<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        成本数据
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索商家编码" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach ($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>成本类型：</label>
                  <select class="form-control" name="filter_type">
                    <option value="*">全部类型</option>
                    <?php foreach ($types as $type_id => $type_name) { ?>
                    <?php if (($filter_type !== '') && ($type_id == $filter_type)) { ?>
                    <option value="<?php echo $type_id; ?>" selected="selected"><?php echo $type_name; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $type_id; ?>"><?php echo $type_name; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>起始年月：</label>
                  <select class="form-control" name="filter_date_start">
                    <option value="*">不限年月</option>
                    <?php foreach($months as $month) { ?>
                    <?php if ($month == $filter_date_start) { ?>
                    <option value="<?php echo $month; ?>" selected="selected"><?php echo $month; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $month; ?>"><?php echo $month; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>截止年月：</label>
                  <select class="form-control" name="filter_date_end">
                    <option value="*">不限年月</option>
                    <?php foreach($months as $month) { ?>
                    <?php if ($month == $filter_date_end) { ?>
                    <option value="<?php echo $month; ?>" selected="selected"><?php echo $month; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $month; ?>"><?php echo $month; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">成本列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">店铺</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">店铺</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'month') { ?>
                <a href="<?php echo $sort_month; ?>" class="<?php echo strtolower($order); ?>">成本日期</a>
                <?php } else { ?>
                <a href="<?php echo $sort_month; ?>">成本日期</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'cost_count') { ?>
                <a href="<?php echo $sort_count; ?>" class="<?php echo strtolower($order); ?>">商品种类</a>
                <?php } else { ?>
                <a href="<?php echo $sort_count; ?>">商品种类</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'cost_quan') { ?>
                <a href="<?php echo $sort_quan; ?>" class="<?php echo strtolower($order); ?>">商品数量</a>
                <?php } else { ?>
                <a href="<?php echo $sort_quan; ?>">商品数量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'cost_total') { ?>
                <a href="<?php echo $sort_total; ?>" class="<?php echo strtolower($order); ?>">商品成本</a>
                <?php } else { ?>
                <a href="<?php echo $sort_total; ?>">商品成本</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'cost_type') { ?>
                <a href="<?php echo $sort_type; ?>" class="<?php echo strtolower($order); ?>">成本类型</a>
                <?php } else { ?>
                <a href="<?php echo $sort_type; ?>">成本类型</a>
                <?php } ?>
              </th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($costlist)) { ?>
              <?php $type_label = ['-1' => 'label-default', '0' => 'label-info', '1' => 'label-primary', '2' => 'label-danger']; ?>
              <?php foreach ($costlist as $cost) { ?>
                <tr>
                  <td><?php echo $cost['cost_store']; ?></td>
                  <td><?php echo $cost['cost_month']; ?></td>
                  <td><?php echo $cost['cost_count']; ?></td>
                  <td><?php echo $cost['cost_quan']; ?></td>
                  <td><?php echo $cost['cost_total']; ?></td>
                  <td class="h4 no-margin">
                    <span class="label <?php echo $type_label[$cost['cost_typeid']] ?? 'label-success'; ?>"><?php echo $cost['cost_type']; ?></span>
                  </td>
                  <td class="text-right">
                    <a class="btn btn-success" href="<?php echo $cost['detail']; ?>" target="_blank">查看详细</a>
                  </td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="7" align="center"> 暂无成本数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_type = $('select[name=\'filter_type\']').val();

      if (filter_type != '*') {
        url += '&filter_type=' + encodeURIComponent(filter_type);
      }

      var filter_date_start = $('select[name=\'filter_date_start\']').val();
  
      if (filter_date_start != '*') {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('select[name=\'filter_date_end\']').val();
  
      if (filter_date_end != '*') {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>