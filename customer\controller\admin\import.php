<?php
class ControllerAdminImport extends Controller {
    public function uploadSales() {
        $data['action'] = $this->url->link('admin/import/importSales', 'token=' . $this->session->data['token']);
        $data['template'] = $this->url->link('admin/import/templateSales', 'token=' . $this->session->data['token']);

        $data['sale_date'] = date($this->language->get('date_format'), strtotime("-1 days"));

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('import/upload_sales.tpl', $data));
    }

    public function importSales() {
        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->load->language('import');
            $this->load->model('admin/import');
            $this->load->model('admin/excel');
            $this->load->model('admin/setting');

            $upload_info = $this->model_admin_excel->upload('storesale');

            if (isset($upload_info['name'])) {
                $sale_datas = array();
                $upload_info['error'] = '';
                $sale_date = $this->request->post['sale_date'];

                $import_total = 0;
                $import_total_success = 0;

                $excel_datas = $this->model_admin_excel->import($upload_info['name']);
                $titles = array_shift($excel_datas);

                if (implode(',', $titles) == $this->language->get('text_sales_title')) {
                    $stores = $this->model_admin_setting->getStores();

                    foreach ($excel_datas as $excel_data) {
                        if (isset($excel_data['A']) && trim($excel_data['A'])) {
                            $import_total++;
                            $store_id = '0';

                            if ((int)$excel_data['S'] == 0) {
                                continue;
                            }
                            
                            foreach ($stores as $store) {
                                if ($store['name'] == trim($excel_data['X'])) {
                                    $store_id = $store['store_id'];
                                    break;
                                }
                            }

                            $sale_datas[] = array(
                                'store_id'  => $store_id,
                                'store_name'=> $excel_data['X'],
                                'bsku'      => $excel_data['A'],
                                'gsku'      => $excel_data['D'],
                                'quantity'  => $excel_data['S'],
                                'total'     => $excel_data['U'],
                                'sale_date' => $sale_date
                            );
                        }
                    }
                } else {
                    $upload_info['error'] = $this->language->get('error_template');
                }

                @unlink($upload_info['name']);

                $exist = $this->model_admin_import->getSalesExist($sale_date);
                
                if ($exist) {
                    $upload_info['error'] = $this->language->get('error_exist');
                }
            }

            if (isset($upload_info['error']) && $upload_info['error']) {
                $json['error'] = $upload_info['error'] . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
            } else {
                if (!empty($sale_datas)) {
                    foreach ($sale_datas as $sale_data) {
                        $this->model_admin_import->addStoreSales($sale_data);
                        $import_total_success++;
                    }

                    $this->log->write($sale_date);

                    $json['success'] = $this->language->get('text_upload_success') . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
                } else {
                    $json['error'] = $this->language->get('error_upload');
                }
            }
        }

        $this->response->setOutJson($json);
    }

    public function templateSales() {
        $file = DIR_DOWNLOAD . '导入店铺销量模板.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }

    public function uploadShipment() {
        $data['action'] = $this->url->link('admin/import/importShipment', 'token=' . $this->session->data['token']);
        $data['template'] = $this->url->link('admin/import/templateShipment', 'token=' . $this->session->data['token']);

        $data['shipment_date'] = date($this->language->get('date_format'), strtotime("-1 days"));

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('import/upload_shipment.tpl', $data));
    }

    public function importShipment() {
        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->load->language('import');
            $this->load->model('admin/import');
            $this->load->model('admin/excel');
            $this->load->model('admin/setting');

            $upload_info = $this->model_admin_excel->upload('storeship');

            if (isset($upload_info['name'])) {
                $ship_datas = array();
                $upload_info['error'] = '';
                $ship_date = $this->request->post['shipment_date'];

                $import_total = 0;
                $import_total_success = 0;

                $excel_datas = $this->model_admin_excel->import($upload_info['name']);
                $titles = array_shift($excel_datas);

                if (implode(',', $titles) == $this->language->get('text_shipment_title')) {
                    $stores = $this->model_admin_setting->getStores();
                    $wares = $this->model_admin_setting->getWarehouses();

                    foreach ($excel_datas as $excel_data) {
                        if (isset($excel_data['A']) && trim($excel_data['A'])) {
                            $import_total++;
                            $store_id = $warehouse_id = '0';
                            
                            foreach ($stores as $store) {
                                if ($store['name'] == trim($excel_data['X'])) {
                                    $store_id = $store['store_id'];
                                    break;
                                }
                            }

                            foreach ($wares as $ware) {
                                if ($ware['name'] == trim($excel_data['Y'])) {
                                    $warehouse_id = $ware['warehouse_id'];
                                    break;
                                }
                            }

                            $ship_datas[] = array(
                                'store_id'  => $store_id,
                                'store_name'=> $excel_data['X'],
                                'ware_id'   => $warehouse_id,
                                'ware_name' => $excel_data['Y'],
                                'bsku'      => $excel_data['A'],
                                'gsku'      => $excel_data['D'],
                                'ship_quan' => $excel_data['R'],
                                'ship_total'=> $excel_data['T'],
                                'return_quan' => $excel_data['AA'],
                                'return_total'=> $excel_data['AH'],
                                'ship_date' => $ship_date
                            );
                        }
                    }
                } else {
                    $upload_info['error'] = $this->language->get('error_template');
                }

                @unlink($upload_info['name']);

                $exist = $this->model_admin_import->getShipmentExist($ship_date);
                
                if ($exist) {
                    $upload_info['error'] = $this->language->get('error_exist');
                }
            }

            if (isset($upload_info['error']) && $upload_info['error']) {
                $json['error'] = $upload_info['error'] . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
            } else {
                if (!empty($ship_datas)) {
                    foreach ($ship_datas as $ship_data) {
                        $this->model_admin_import->addStoreShipment($ship_data);
                        $import_total_success++;
                    }

                    $this->log->write($ship_date . 'ship');

                    $json['success'] = $this->language->get('text_upload_success') . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
                } else {
                    $json['error'] = $this->language->get('error_upload');
                }
            }
        }

        $this->response->setOutJson($json);
    }

    public function templateShipment() {
        $file = DIR_DOWNLOAD . '导入店铺发货模板.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }
}