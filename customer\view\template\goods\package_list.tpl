<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        包材管理
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索编码/名称" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>供应商：</label>
                  <select class="form-control" name="filter_provider"<?php if (isset($isprovider)) { ?> disabled="disabled"<?php } ?>>
                    <option value="*">全部供应商</option>
                    <?php foreach ($providers as $provider) { ?>
                    <?php if ($provider['provider_no'] == $filter_provider) { ?>
                    <option value="<?php echo $provider['provider_no']; ?>" selected="selected"><?php echo $provider['provider_name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $provider['provider_no']; ?>"><?php echo $provider['provider_name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>筛选条件：</label>
                  <select class="form-control" name="filter_rule">
                    <?php foreach($rules as $rule_code => $rule_name) { ?>
                    <?php if ($rule_code == $filter_rule) { ?>
                    <option value="<?php echo $rule_code; ?>" selected="selected"><?php echo $rule_name; ?></option>
                    <?php } else{ ?>
                    <option value="<?php echo $rule_code; ?>"><?php echo $rule_name; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          
          <div class="pull-right">
            <button type="button" id="button-filter" class="btn bg-purple"><i class="glyphicon glyphicon-search"></i> 筛选</button>
          </div>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">包材列表</p>
          <div class="box-tools">
            <?php if (isset($isprovider)) { ?><a class="btn btn-sm btn-success" href="<?php echo $upload; ?>">批量出库</a><?php } ?>
          </div>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th style="max-width: 200px;">图片</th>
              <th>产品名称
                <?php if ($sort == 'bsku') { ?>
                  <a href="<?php echo $sort_bsku; ?>" class="<?php echo strtolower($order); ?>">编码</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_bsku; ?>">编码</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'provider_no') { ?>
                  <a href="<?php echo $sort_provider; ?>" class="<?php echo strtolower($order); ?>">供应商</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_provider; ?>">供应商</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'pending_quan') { ?>
                  <a href="<?php echo $sort_pending; ?>" class="<?php echo strtolower($order); ?>">正在生产</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_pending; ?>">正在生产</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'boxstock_quan') { ?>
                  <a href="<?php echo $sort_boxstock; ?>" class="<?php echo strtolower($order); ?>">彩盒库存</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_boxstock; ?>">彩盒库存</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'outbox') { ?>
                  <a href="<?php echo $sort_outbox; ?>" class="<?php echo strtolower($order); ?>">彩盒不足</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_outbox; ?>">彩盒不足</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'packstock_quan') { ?>
                  <a href="<?php echo $sort_packstock; ?>" class="<?php echo strtolower($order); ?>">保丽龙库存</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_packstock; ?>">保丽龙库存</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'outpack') { ?>
                  <a href="<?php echo $sort_outpack; ?>" class="<?php echo strtolower($order); ?>">保丽龙不足</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_outpack; ?>">保丽龙不足</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'date_modified') { ?>
                  <a href="<?php echo $sort_modified; ?>" class="<?php echo strtolower($order); ?>">最后变动时间</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_modified; ?>">最后变动时间</a>
                <?php } ?>
              </th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($packages)) { ?>
            <?php foreach ($packages as $package) { ?>
            <tr data-id="<?php echo $package['bsku']; ?>">
              <td><img width="100" src="<?php echo $package['img']; ?>" class="img-thumbnail"></td>
              <td><?php echo $package['name']; ?><br><?php echo $package['bsku']; ?></td>
              <td><?php echo $package['provider']; ?></td>
              <td><?php echo $package['pending']; ?></td>
              <td><?php echo $package['boxstock']; ?></td>
              <td><?php echo $package['outbox']; ?></td>
              <?php if ($package['ispack']) { ?>
              <td><?php echo $package['packstock']; ?></td>
              <td><?php echo $package['outpack']; ?></td>
              <?php } else { ?>
              <td>--</td>
              <td>--</td>
              <?php } ?>
              <td><?php echo $package['modified']; ?></td>
              <td class="text-right">
                <?php if (!empty($package['package_id'])) { ?>
                <button class="btn btn-info" type="button" data-id="<?php echo $package['package_id']; ?>" data-toggle="modal" data-target="#detail-modal">详情</button>
                <?php } ?>
                <?php if (isset($isprovider)) { ?>
                <button class="btn btn-primary" type="button" data-toggle="modal" data-target="#info-modal">入库</button>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">出库</button>
                <?php } ?>
              </td>
            </tr>
            <?php } ?>
            <?php } else{ ?>
            <td colspan="10" align="center"> 暂无包材数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 详情 -->
      <div class="modal modal-default fade" id="detail-modal">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">出入库详情</h4>
              </div>
              <div class="modal-body" id="package-detail"></div>
              <div class="modal-footer"></div>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>

      <!-- 入库 -->
      <div class="modal modal-primary fade" id="info-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $add; ?>" method="post" enctype="multipart/form-data" id="form-info" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">包材入库</h4>
              </div>
              <div class="modal-body">
                <input id="info-id" name="bsku" type="hidden" value="">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-in">入库数量：</label>
                  <div class="col-sm-8">
                    <input type="number" name="in_quan" value="" placeholder="请输入入库数量" id="input-in" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-in">入库类型：</label>
                  <div class="col-sm-8">
                    <div class="radio">
                      <label><input type="radio" name="in_type" value="box"> 彩盒</label>
                    </div>
                    <div class="radio">
                      <label><input type="radio" name="in_type" value="pack"> 保丽龙</label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">关闭</button>
                <button id="info-yes" type="button" class="btn btn-outline">提交</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>

      <!-- 出库 -->
      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $add; ?>" method="post" enctype="multipart/form-data" id="form-del" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">包材出库</h4>
              </div>
              <div class="modal-body">
                <input id="del-id" name="bsku" type="hidden" value="">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-out">出库数量：</label>
                  <div class="col-sm-8">
                    <input type="number" name="out_quan" value="" placeholder="请输入出库数量" id="input-out" class="form-control" />
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">关闭</button>
                <button id="del-yes" type="button" class="btn btn-outline">提交</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_provider = $('select[name=\'filter_provider\']').val();

      if (filter_provider != '*') {
        url += '&filter_provider=' + encodeURIComponent(filter_provider);
      }

      var filter_rule = $('select[name=\'filter_rule\']').val();

      if (filter_rule != '*') {
        url += '&filter_rule=' + encodeURIComponent(filter_rule);
      }

      location.href = url;
    });

    $('#info-modal').on('show.bs.modal', function(event) {
      $('#info-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#info-yes').on('click', () => {$('#form-info').submit()})

    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})

    $('#detail-modal').on('show.bs.modal', function(event) {
      $('#package-detail').load('<?php echo $detail; ?>&package_id=' + $(event.relatedTarget).data('id'));
    })

    $('#package-detail').delegate('.pagination a', 'click', function(e) {
      e.preventDefault();

      $('#package-detail').load(this.href);
    });
  })()
</script>
<?php echo $footer; ?>