<?php
class ControllerAdminShop extends Controller {
    private $error = array();

    public function getShopIds() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
            $data_filter_store = explode(',',$this->request->get['filter_store']);
        } else {
            $filter_store = '';
            $data_filter_store = [];
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['import'] = $this->url->link('admin/shop/getShopIdImport', 'token=' . $this->session->data['token']);
        $data['export'] = $this->url->link('admin/shop/export', 'token=' . $this->session->data['token'] . $url);
        $data['add'] = $this->url->link('admin/shop/getShopIdAdd', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/shop/getShopIdDelete', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $this->load->model('admin/bill');
        $data['stores'] = $this->model_admin_bill->getStores();
        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $filter_data = array(
            'filter_name'  =>$filter_name,
            'filter_store' =>$filter_store,
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );

        $this->load->model('admin/shop');
        $results = $this->model_admin_shop->getshopIds($filter_data);


        foreach ($results as $result) {
            $data['shops'][] = array(
                'shop_platform_id'    => $result['shop_platform_id'],
                'shop_name'             => $stores[$result['store_id']],
                'platform_id'         => $result['platform_id'],
                'custodian'           => $result['custodian'],
                'real_name'           => $result['real_name'],
                'date_added'          => $result['date_added'],
                'edit'             => (($result['custodian'] == $this->user->user_id) || ($this->user->user_group_id == 1)) ? $this->url->link('admin/shop/getShopIdEdit', 'token=' . $this->session->data['token'] . '&shop_platform_id=' . $result['shop_platform_id'] . $url) : ''
            );
        }

        $total = $this->model_admin_shop->getTotalshopIds($filter_data);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/shop/getShopIds', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));
        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_store_json'] = json_encode($data_filter_store);

        $data['nofilter'] = $this->url->link('admin/shop/getShopIds', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('shop/list.tpl', $data));
    }

    public function getShopIdImport() {
        $data['action'] = $this->url->link('admin/shop/import', 'token=' . $this->session->data['token']);
        $data['template'] = $this->url->link('admin/shop/template', 'token=' . $this->session->data['token']);
        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('shop/import.tpl', $data));
    }

    public function import() {
        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            if (isset($_FILES['file']) && $_FILES['file']['error'] == 0 && !empty($this->request->post['store_id'])) {
                $this->load->helper('office/autoload');

                $excelPath = $_FILES['file']['tmp_name']; // 获取上传文件的临时路径

                try {
                    //识别文件类型
                    $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($excelPath);
                    //创建读取器
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
                    $reader->setReadDataOnly(true); // 只读数据，不读样式
                    //加载文件
                    $spreadsheet = $reader->load($excelPath);
                    //获取第一个工作表
                    $objWorksheet = $spreadsheet->getSheet(0);
                    //获取所有数据
                    $data = $objWorksheet->toArray();
                } catch (\PhpOffice\PhpSpreadsheet\Reader\Exception $e) {
                    die('读取文件出错: '.$e->getMessage());
                } catch (Exception $e) {
                    die('发生错误: '.$e->getMessage());
                }

                array_shift($data);

                $this->load->model('admin/shop');
                $add_num = 0;
                foreach ($data as $v) {
                    if (!empty($v[0])) {
                        $add_data = [
                            'store_id' => $this->request->post['store_id'],
                            'platform_id' => $v[0],
                        ];
                        $shop_platform_id = $this->model_admin_shop->addShopId($add_data,$this->user->user_id);
                        if (!empty($shop_platform_id)) $add_num += 1;
                    }
                }
                $json['success'] = '成功导入'.$add_num.'条数据';
            } else {
                $json['error'] = '导入失败';
            }
        }
        $this->response->setOutJson($json);
    }

    public function template() {
        $file = DIR_DOWNLOAD . '导入店铺链接模板.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }

    public function export() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_store'		=> $filter_store,
        );

        $this->load->model('admin/bill');
        $data['stores'] = $this->model_admin_bill->getStores();
        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $export_head = array('店铺', '平台链接id', '管理人');
        $export_data = array();
        $export_data[] = $export_head;

        $this->load->model('admin/shop');
        $results = $this->model_admin_shop->getshopIds($filter_data);


        foreach ($results as $result) {
            $export_data[] = array(
                $stores[$result['store_id']],
                $result['platform_id'],
                $result['real_name'],
            );
        }

        if (!empty($export_data)) {
            $this->load->model('admin/excel');
            $this->model_admin_excel->export('平台链接id' . date('Y-m-d'), $export_data, array(), '.xlsx');
        }

        $this->getShopIds();
    }

    public function getShopIdAdd() {
        $this->load->model('admin/shop');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            if (!empty($this->request->post['custodian'])) {
                $this->model_admin_shop->addShopId($this->request->post,$this->request->post['custodian']);
            } else {
                $this->model_admin_shop->addShopId($this->request->post,$this->user->user_id);
            }

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/shop/getShopIds', 'token=' . $this->session->data['token'] . $url));
        }

        $this->Form();
    }

    public function getShopIdEdit() {
        $this->load->model('admin/shop');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            if (!empty($this->request->post['custodian'])) {
                $this->model_admin_shop->editShopId($this->request->get['shop_platform_id'], $this->request->post,$this->request->post['custodian']);
            } else {
                $this->model_admin_shop->editShopId($this->request->get['shop_platform_id'], $this->request->post,$this->user->user_id);
            }

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/shop/getShopIds', 'token=' . $this->session->data['token'] . $url));
        }

        $this->Form();
    }

    protected function Form() {
        $data['text_form'] = !isset($this->request->get['shop_platform_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['shop_platform_id'])) {
            $data['action'] = $this->url->link('admin/shop/getShopIdAdd', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/shop/getShopIdEdit', 'token=' . $this->session->data['token'] . '&shop_platform_id=' . $this->request->get['shop_platform_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/shop/getShopIds', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['shop_platform_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $shopid_info = $this->model_admin_shop->getShopId($this->request->get['shop_platform_id']);
        }

        if (isset($this->request->post['store_id'])) {
            $data['store_id'] = $this->request->post['store_id'];
        } elseif (!empty($shopid_info)) {
            $data['store_id'] = $shopid_info['store_id'];
        } else {
            $data['store_id'] = '17';
        }

        if (isset($this->request->post['platform_id'])) {
            $data['platform_id'] = $this->request->post['platform_id'];
        } elseif (!empty($shopid_info)) {
            $data['platform_id'] = $shopid_info['platform_id'];
        } else {
            $data['platform_id'] = '';
        }

        if (isset($this->request->post['custodian'])) {
            $data['custodian'] = $this->request->post['custodian'];
            $data['user_json'] = $this->request->post['custodian'];
        } elseif (!empty($shopid_info)) {
            $data['custodian'] = $shopid_info['custodian'];
            $data['user_json'] = $shopid_info['custodian'];
        } else {
            $data['custodian'] = '';
            $data['user_json'] = '';
        }

        $data['user_group_id'] = $this->user->user_group_id;

        $data['users'] = $this->model_admin_shop->getUsers();

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('shop/form.tpl', $data));
    }

    public function getShopIdDelete() {
        $this->load->model('admin/shop');

        if (isset($this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $shop_platform_id) {
                $this->model_admin_shop->deleteShopId($shop_platform_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/shop/getShopIds', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getShopIds();
    }


    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'admin/shop')) {
            $this->error['warning'] = $this->language->get('error_permission');
            return false;
        }

        if (empty($this->request->post['store_id'])) {
            $this->error['warning'] = '店铺不能为空！';
            return false;
        }

        if (empty($this->request->post['platform_id'])) {
            $this->error['warning'] = '平台链接id不能为空！';
            return false;
        }

//        if (empty($this->request->post['custodian'])) {
//            $this->error['warning'] = '管理人不能为空！';
//            return false;
//        }
        return !$this->error;
    }
}