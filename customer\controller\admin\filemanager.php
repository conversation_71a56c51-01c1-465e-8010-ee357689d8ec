<?php
class ControllerAdminFileManager extends Controller {
	private $error = array();

	public function index() {
        $data = array();

        $data['uploadToken'] = QiNiu::getUploadToken();

        $data['list'] = $this->url->link('admin/fileManager/getList', 'token=' . $this->session->data['token']);
        $data['addFolder'] = $this->url->link('admin/fileManager/addFolder', 'token=' . $this->session->data['token']);
        $data['addTags'] = $this->url->link('admin/fileManager/addTags', 'token=' . $this->session->data['token']);
        $data['addFile'] = $this->url->link('admin/fileManager/addFile', 'token=' . $this->session->data['token']);
        $data['delete'] = $this->url->link('admin/fileManager/delete', 'token=' . $this->session->data['token']);        
        $data['download'] = $this->url->link('admin/fileManager/download', 'token=' . $this->session->data['token']);
        $data['checkAddPower'] = $this->url->link('admin/fileManager/checkAddPower', 'token=' . $this->session->data['token']);

        $this->response->setOutput($this->load->view('common/filemanager.tpl', $data));
    }
	
	public function getList() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = basename(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		} else {
			$filter_name = '';
		}

        if (isset($this->request->get['filter_folder'])) {
            $filter_folder = $this->request->get['filter_folder'];
        } else {
            $filter_folder = '';
        }

		if (isset($this->request->get['page'])) {
			$page = (int)$this->request->get['page'];
		} else {
			$page = 1;
		}

		$this->config->set('config_limit', 12);

		$filter_data = array(
			'filter_name'	=> $filter_name,
			'filter_folder'	=> $filter_folder,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);

        if ($this->user->union_id) {
            $role_query = $this->db->query("SELECT role_id FROM _union_role WHERE status = '1' AND FIND_IN_SET('" . $this->db->escape($this->user->union_id) . "',union_id)");
            if (!empty($role_query->rows)) {
                $role_ids = array_column($role_query->rows,'role_id');
            }

            $department_query = $this->db->query("SELECT department_id FROM _union_department WHERE status = '1' AND FIND_IN_SET('" . $this->db->escape($this->user->union_id) . "',union_id)");
            if (!empty($department_query->rows)) {
                $department_ids = array_column($department_query->rows,'department_id');
            }

            if (!empty($role_ids) || !empty($department_ids)) {
                $file_power_sql = "SELECT * FROM _union_file_power WHERE status = '1' AND (";
                if (!empty($role_ids)) {
                    foreach ($role_ids as $k => $v) {
                        if ($k != 0) {
                            $file_power_sql .= " OR";
                        }
                        $file_power_sql .= " FIND_IN_SET('" . $this->db->escape($v) . "',role_ids)";
                    }
                }

                if (!empty($department_ids)) {
                    foreach ($department_ids as $k => $v) {
                        if ($k != 0 || !empty($role_ids)) {
                            $file_power_sql .= " OR";
                        }
                        $file_power_sql .= " FIND_IN_SET('" . $this->db->escape($v) . "',department_ids)";
                    }
                }

                $file_power_sql .= ")";

                $file_power_query = $this->db->query($file_power_sql);

                if (!empty($file_power_query->rows)) {
                    $folder_ids = [];
                    $all_folder_ids = [];
                    foreach ($file_power_query->rows as $v) {
                        $folder_ids = array_merge($folder_ids,explode(',',$v['folder_ids']));
                        $all_folder_ids = array_merge($all_folder_ids,explode(',',$v['all_folder_ids']));
                    }

                    $filter_data['folder_ids'] = array_unique($folder_ids);
                    $filter_data['all_folder_ids'] = array_unique($all_folder_ids);
                }
            }
        }

		$data['files'] = [];

		$this->load->model('admin/filemanager');
		$results = $this->model_admin_filemanager->getFileList($filter_data);

        $domain = [
            'roogoapp' => 'http://public.roogo.cn/',
            'roogo' => HTTP_IMAGE,
        ];
		foreach ($results as $result) {
			if ($result['file_thumb']) {
                $thumb = $domain[$result['bucket']].$result['file_thumb'];
            } elseif (substr($result['file_type'], 0, 6) == 'image/' && strpos($result['file_type'],'vnd.adobe.photoshop')  === false) {
                $thumb = $domain[$result['bucket']].$result['file_key'].'?imageView2/0/w/80/h/80';
            } elseif (substr($result['file_type'], 0, 6) == 'video/') {
                $thumb = $domain[$result['bucket']].$result['file_key'].'?vframe/jpg/offset/1/mode/1/w/80';
			} else {
				$thumb = '';
			}

			if ((bool)$result['is_folder']) {
				$href = $this->url->link('admin/fileManager/getList', 'token=' . $this->session->data['token'] . '&filter_name=' . $filter_name . '&filter_folder=' . $result['file_id']);
			} else {
                $href = $domain[$result['bucket']].$result['file_key'];
            }

			$data['files'][] = [
				'file_id'	=> $result['file_id'],
				'file_name'	=> $result['file_name'],
				'is_folder'	=> (bool)$result['is_folder'],
				'thumb'		=> $thumb,
				'href'		=> $href,
                'tags'      => $result['tags'] ? $this->model_admin_filemanager->getINTagsName($result['tags']) : '',
			];
		}

        $parent_folders = [];
        if (!empty($filter_folder)) {
            $file_info = $this->model_admin_filemanager->getFile($filter_folder);
            if (!empty($file_info['parent_folder_ids'])) {
                $parent_folders = $this->model_admin_filemanager->getAllParentFolderName($file_info['parent_folder_ids']);
            }
        }
        $data['parent_folders'] = $parent_folders;
        $data['parent_folders_url'] = $this->url->link('admin/fileManager/getList', 'token=' . $this->session->data['token'] . '&filter_folder=');

		$total = $this->model_admin_filemanager->getTotalFiles($filter_data);

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		$parent_folder = $this->model_admin_filemanager->getParentFolder($filter_folder);
		$data['parent'] = $this->url->link('admin/fileManager/getList', 'token=' . $this->session->data['token'] . $url . '&filter_folder=' . $parent_folder);

		if (isset($this->request->get['filter_folder'])) {
			$url .= '&filter_folder=' . $this->request->get['filter_folder'];
		}

		$data['refresh'] = $this->url->link('admin/fileManager/getList', 'token=' . $this->session->data['token'] . $url . '&page=' . $page);

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/fileManager/getList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

		$data['filter_name'] = $filter_name;
		$data['filter_folder'] = $filter_folder;

        $data['default_tags'] = $this->model_admin_filemanager->getDefaultTags();

		$this->response->setOutput($this->load->view('common/filemanager_list.tpl', $data));
	}
	
	public function addFile() {
		$json = array();

		if (isset($this->request->get['folder'])) {
            $folder = $this->request->get['folder'];
        } else {
            $folder = '';
        }

        $is_add_power = false;
        $this->load->model('admin/filemanager');
        $parent_file_info = $this->model_admin_filemanager->getFile($folder);
        if (!empty($parent_file_info) && !empty($parent_file_info['parent_folder_ids'])) {
            if (count(explode(',',$parent_file_info['parent_folder_ids'])) > 2) {
                $is_add_power = true;
            }
        }

        if (empty($is_add_power) && ($this->user->user_group_id != 1)) {
            $json['error'] = '暂无权限，请联系技术部添加！';
        } else {
            // Sanitize the folder name
            $file = preg_replace('[/\\?%*&:|"<>]', '', basename(html_entity_decode($this->request->post['filename'], ENT_QUOTES, 'UTF-8')));

            if (!empty($file)) {
                $this->load->model('admin/filemanager');
                $exist = $this->model_admin_filemanager->getFileExist($folder, $file);

                if (!empty($exist)) {
                    $this->model_admin_filemanager->deleteFile($exist, $this->user->union_id);
                }

                $file_info = [
                    'union_id'	=> $this->user->union_id,
                    'folder_id' => $folder,
                    'file_name' => $file,
                    'file_type' => $this->request->post['filetype'],
                    'bucket' 	=> $this->config->get('config_qiniu')['bucket'],
                    'file_key' 	=> $this->request->post['filekey'],
                    'file_hash' => $this->request->post['filehash'],
                    'file_thumb'=> '',
                    'file_size' => $this->request->post['filesize']
                ];
                $this->model_admin_filemanager->addFile($file_info);

                $json['success'] = '文件创建成功！';
            } else {
                $json['error'] = '文件名称不能为空！';
            }
        }

		$this->response->setOutJson($json);
	}

    public function addTags() {
        $json = array();

        // Sanitize the folder name
        $tags = $this->request->post['tags'];

        if (!empty($tags) && !empty($this->request->post['files'])) {
            $this->load->model('admin/filemanager');
            foreach ($tags as $tag) {
                $tag = preg_replace('[/\\?%*&:|"<>]', '', basename(html_entity_decode($tag, ENT_QUOTES, 'UTF-8')));
                if (strpos($tag,'file_tags_id_')  !== false) {
                    $file_tags_id = str_replace('file_tags_id_','',$tag);
                } else {
                    $file_tags_id = $this->model_admin_filemanager->addDefaultTags($tag);
                }
                foreach ($this->request->post['files'] as $file) {
                    $file_info = $this->model_admin_filemanager->getFile($file);
                    if (!empty($file_info['tags'])) {
                        if (strpos($file_info['tags'],','.$file_tags_id.',')  === false) {
                            $this->model_admin_filemanager->setFileTags($file,$file_info['tags'].$file_tags_id.',');
                        }
                    } else {
                        $this->model_admin_filemanager->setFileTags($file,','.$file_tags_id.',');
                    }
                }
            }
            $json['success'] = '标签添加成功！';
        } else {
            $json['error'] = '标签不能为空！';
        }

        $this->response->setOutJson($json);
    }
	
	public function addFolder() {
		$json = array();

		if (isset($this->request->get['folder'])) {
            $folder = $this->request->get['folder'];
        } else {
            $folder = '';
        }

        $is_add_power = false;
        $this->load->model('admin/filemanager');
        $parent_file_info = $this->model_admin_filemanager->getFile($folder);
        if (!empty($parent_file_info) && !empty($parent_file_info['parent_folder_ids'])) {
            if (count(explode(',',$parent_file_info['parent_folder_ids'])) > 2) {
                $is_add_power = true;
            }
        }

        if (empty($is_add_power) && ($this->user->user_group_id != 1)) {
            $json['error'] = '暂无权限，请联系技术部添加！';
        } else {
            // Sanitize the folder name
            $file = preg_replace('[/\\?%*&:|"<>]', '', basename(html_entity_decode($this->request->post['filename'], ENT_QUOTES, 'UTF-8')));

            if (!empty($file)) {
                $exist = $this->model_admin_filemanager->getFileExist($folder, $file);

                if (empty($exist)) {
                    $file_info = [
                        'union_id'	=> $this->user->union_id,
                        'folder_id' => $folder,
                        'file_name' => $file,
                        'file_type' => 'folder',
                        'bucket' 	=> $this->config->get('config_qiniu')['bucket'],
                        'file_key' 	=> '',
                        'file_hash' => '',
                        'file_thumb'=> '',
                        'file_size' => '0'
                    ];
                    $this->model_admin_filemanager->addFile($file_info);

                    $json['success'] = '文件夹创建成功！';
                } else {
                    $json['error'] = '文件夹已存在！';
                }
            } else {
                $json['error'] = '文件夹名称不能为空！';
            }
        }

		$this->response->setOutJson($json);
	}
	
	public function delete() {
		$json = array();

		if (isset($this->request->post['path'])) {
			$this->load->model('admin/filemanager');
			$union_id = $this->user->union_id;

			foreach ($this->request->post['path'] as $file_id) {
				$this->model_admin_filemanager->deleteFile($file_id, $union_id);
			}

			$json['success'] = '文件删除成功！';
		} else {
			$json['error'] = '请选择要删除的文件！';
		}

		$this->response->setOutJson($json);
	}

	public function download() {
		if (isset($this->request->get['file_id'])) {
            $file_id = $this->request->get['file_id'];
        } else {
            $file_id = '';
        }

        $this->load->model('admin/filemanager');
        $file_info = $this->model_admin_filemanager->getFile($file_id);

        if (!empty($file_info) && !(bool)$file_info['is_folder']) {
            $domain = [
                'roogoapp' => 'http://public.roogo.cn/',
                'roogo' => HTTP_IMAGE,
            ];
        	$this->response->redirect($domain[$file_info['bucket']].$file_info['file_key']);
        }
	}

    public function checkAddPower() {
        $json = [];
        if (isset($this->request->get['folder'])) {
            $folder_id = $this->request->get['folder'];
        } else {
            $folder_id = '';
        }

        $is_add_power = false;
        $this->load->model('admin/filemanager');
        $parent_file_info = $this->model_admin_filemanager->getFile($folder_id);
        if (!empty($parent_file_info) && !empty($parent_file_info['parent_folder_ids'])) {
            if (count(explode(',',$parent_file_info['parent_folder_ids'])) > 2) {
                $is_add_power = true;
            }
        }

        if (empty($is_add_power) && ($this->user->user_group_id != 1)) {
            $json['error'] = '暂无权限，请联系技术部添加！';
        }

        $this->response->setOutJson($json);
    }
}
