<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        店铺费用
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索名称" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>分摊店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>计算方式：</label>
                  <select class="form-control" name="filter_calculate">
                    <option value="*">全部方式</option>
                    <?php if ($filter_calculate == 'total') { ?>
                    <option value="total" selected="selected">固定金额</option>
                    <?php } else { ?>
                    <option value="total">固定金额</option>
                    <?php } ?>
                    <?php if ($filter_calculate == 'percent') { ?>
                    <option value="percent" selected="selected">销售额百分比</option>
                    <?php } else { ?>
                    <option value="percent">销售额百分比</option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>分摊方式：</label>
                  <select class="form-control" name="filter_method">
                    <option value="*">全部方式</option>
                    <?php if ($filter_method == 'monthly') { ?>
                    <option value="monthly" selected="selected">每月平均扣款</option>
                    <?php } else { ?>
                    <option value="monthly">每月平均扣款</option>
                    <?php } ?>
                    <?php if ($filter_method == 'daily') { ?>
                    <option value="daily" selected="selected">每日扣款</option>
                    <?php } else { ?>
                    <option value="daily">每日扣款</option>
                    <?php } ?>
                  </select>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">          
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">费用列表</p>
          <div class="box-tools">
            <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
          </div>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>费用名称</th>
              <th>分摊金额</th>
              <th>分摊店铺</th>
              <th>分摊方式</th>
              <th>增加时间</th>
              <th class="text-right">排序数值</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($fees)) { ?>
            <?php foreach ($fees as $fee) { ?>
            <tr data-id="<?php echo $fee['fee_id']; ?>">
              <td><?php echo $fee['name']; ?></td>
              <td>
                <?php if ($fee['calculate'] == 'total') { ?>
                ¥<?php echo $fee['total']; ?>
                <?php } elseif ($fee['calculate'] == 'percent') { ?>
                销售额 X <?php echo $fee['percent']; ?>%
                <?php } ?>
              </td>
              <td><?php echo implode('<br>', $fee['stores']); ?></td>
              <td>
                <?php if ($fee['method'] == 'monthly') { ?>
                每月平均扣款
                <?php } elseif ($fee['method'] == 'daily') { ?>
                每日扣款
                <?php } ?>
              </td>
              <td><?php echo $fee['date_added']; ?></td>
              <td class="text-right"><?php echo $fee['order']; ?></td>
              <td class="text-right">
                <a class="btn btn-success" href="<?php echo $fee['edit']; ?>" title="">修改</a>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">删除</button>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="6" align="center"> 暂无费用数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 删除 -->
      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-del">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除此记录吗？</p>
                <input id="del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';
  
      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_calculate = $('select[name=\'filter_calculate\']').val();

      if (filter_calculate != '*') {
        url += '&filter_calculate=' + encodeURIComponent(filter_calculate);
      }

      var filter_method = $('select[name=\'filter_method\']').val();

      if (filter_method != '*') {
        url += '&filter_method=' + encodeURIComponent(filter_method);
      }

      location.href = url;
    });
    
    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
  })()
</script>
<?php echo $footer; ?>