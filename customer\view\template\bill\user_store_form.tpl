<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">权限名称：</label>
              <div class="col-sm-8">
                <input type="text" name="name" value="<?php echo $user_store_name; ?>" placeholder="请输入权限标题" id="input-name" class="form-control" />
                <div class="text-danger" style="display: none">请请输入权限标题</div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-mode">选择成员：</label>
              <div class="col-sm-8">
                <select id="w1" class="form-control select-province"  name="users[]" style="width: 80%;float: left " multiple data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">
                  <?php foreach ($users as $key=>$user) { ?>
                  <option value="<?php echo $user['union_id']; ?>"><?php echo $user['real_name']; ?></option>
                  <?php } ?>
                </select>
                <div class="text-danger" style="display: none">请选择成员</div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-mode">选择店铺：</label>
              <div class="col-sm-8">
                <select id="w2" class="form-control select-province"  name="stores[]" style="width: 80%;float: left " multiple data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">
                  <?php foreach($stores as $store) { ?>
                  <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                  <?php } ?>
                </select>
                <div class="text-danger" style="display: none">请选择店铺</div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>

<script>
  var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
  window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"35px","placeholder":"请选择成员","language":"zh-CN"};

  if (jQuery('#w1').data('select2')) { jQuery('#w1').select2('destroy'); }
  jQuery.when(jQuery('#w1').select2(select2_5eaa6d36)).done(initS2Loading('w1','s2options_c4acac00'));

  var filter_user = '<?php echo $filter_user_json; ?>'
  $("#w1").val($.parseJSON(filter_user)).trigger("change");


  var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
  window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"35px","placeholder":"请选择店铺","language":"zh-CN"};

  if (jQuery('#w2').data('select2')) { jQuery('#w2').select2('destroy'); }
  jQuery.when(jQuery('#w2').select2(select2_5eaa6d36)).done(initS2Loading('w2','s2options_c4acac00'));

  var filter_store = '<?php echo $filter_store_json; ?>'
  $("#w2").val($.parseJSON(filter_store)).trigger("change");

</script>

<script type="text/javascript">
  function toVaild() {
    var isValid = true;
    var inputName = $("#input-name").val()
    if (inputName == '') {
      isValid = false;
      $("#input-name").siblings('.text-danger').show();
    } else {
      $("#input-name").siblings('.text-danger').hide();
    }

    var selectedValues = $("#w1").select2('data');
    if (selectedValues.length > 0) {
      $("#w1").siblings('.text-danger').hide();
    } else {
      isValid = false;
      $("#w1").siblings('.text-danger').show();
    }

    var selectedValues2 = $("#w2").select2('data');
    if (selectedValues2.length > 0) {
      $("#w2").siblings('.text-danger').hide();
    } else {
      isValid = false;
      $("#w2").siblings('.text-danger').show();
    }

    if (isValid) {
      return true;
    } else {
      return false;
    }
  }
</script>
<?php echo $footer; ?>