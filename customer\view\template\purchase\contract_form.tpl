<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-contract_number">合同号：</label>
              <div class="col-sm-8">
                <input type="text" name="contract_number" value="<?php if(!empty($purchase_contract['contract_number'])){ ?><?php echo $purchase_contract['contract_number']; ?><?php } ?>" placeholder="请输入合同号" id="input-contract_number" class="form-control" />
              </div>
            </div>

            <div class="form-group" style="display: none">
              <label class="col-sm-2 control-label" for="input-warehouse_date">入仓时间：</label>
              <div class="col-sm-8">
                <input type="text" name="warehouse_date" value="<?php if(!empty($purchase_contract['warehouse_date'])){ ?><?php echo $purchase_contract['warehouse_date']; ?><?php } ?>" placeholder="请输入入仓时间" id="input-warehouse_date" class="form-control" />
                <div class="text-danger">例：货物送至南安17:00前入仓</div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-sign_date">签订时间：</label>
              <div class="col-sm-8">
                <input type="text" name="sign_date" id="input-sign_date" class="form-control pull-right reservation" placeholder="选择签订时间" value="<?php if(!empty($purchase_contract['sign_date'])){ ?><?php echo $purchase_contract['sign_date']; ?><?php } ?>">
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-delivery_date">交货时间：</label>
              <div class="col-sm-8">
                <input type="text" name="delivery_date" id="input-delivery_date" class="form-control pull-right reservation" placeholder="选择交货时间" value="<?php if(!empty($purchase_contract['delivery_date'])){ ?><?php echo $purchase_contract['delivery_date']; ?><?php } ?>">
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="select-calculate">选择供应商：</label>
              <div class="col-sm-8">
                <select name="provider_no" id="select-provider" class="form-control">
                  <option value="0">请选择供应商</option>
                  <?php foreach($providers as $provider){ ?>
                  <option value="<?php echo $provider['provider_no']; ?>" <?php if(!empty($purchase_contract['provider_no']) && ($purchase_contract['provider_no'] == $provider['provider_no'])){ ?>selected="selected"<?php } ?>><?php echo $provider['provider_name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label">外箱唛：</label>
              <div class="col-sm-8">
                <div class="row" <?php if(empty($purchase_contract['carton_mark'])){ ?>style="display: none"<?php } ?> id="carton_mark_div">
                  <div class="col-sm-3">
                    <div class="thumbnail">
                      <img id="carton_mark_img" src="<?php if(!empty($purchase_contract['carton_mark'])){ ?><?php echo $purchase_contract['carton_mark']; ?><?php } ?>" style="max-height: 100px">
                      <input id="carton_mark_input" type="hidden" name="carton_mark" value="<?php if(!empty($purchase_contract['carton_mark'])){ ?><?php echo $purchase_contract['carton_mark']; ?><?php } ?>">
                    </div>
                  </div>
                </div>
                <button type="button" data-field="carton_mark" class="btn-upload-carton-mark btn btn-success"><i class="fa fa-upload"></i> 上传</button>
              </div>
            </div>

            <hr style="width: 80%">

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-product">选择产品：</label>
              <div class="col-sm-8">
                <input type="text" value="" placeholder="输入产品编码或名称查找（选择产品前请先选择供应商）" id="input-product" class="form-control" />
                <div class="all-specs">
                  <?php if(!empty($purchase_contract['specs'])){ ?>
                  <?php foreach($purchase_contract['specs'] as $spec_key=>$spec){ ?>
                    <div style="margin-top: 5px;min-height: 120px;overflow:hidden">
                      <input name="specs[<?php echo $spec_key; ?>][spec_no]" value="<?php echo $spec['spec_no']; ?>" type="hidden">
                      <input name="specs[<?php echo $spec_key; ?>][goods_id]" value="<?php echo $spec['goods_id']; ?>" type="hidden">
                      <input name="specs[<?php echo $spec_key; ?>][spec_name]" value="<?php echo $spec['spec_name']; ?>" type="hidden">
                      <input name="specs[<?php echo $spec_key; ?>][img_url]" value="<?php echo $spec['img_url']; ?>" class="input-img-upload" type="hidden">
                      <a class="btn btn-danger" href="javascript:;" onclick="$(this).parent().remove()" style="float: left;margin-right: 10px">删除产品</a>
                      <img style="float: left" width="100" <?php if(empty($spec['img_url'])){ ?>height="100"<?php }else{ ?> src="<?php echo $spec['img_url']; ?>"<?php } ?> class="img-thumbnail img-upload">
                      <div style="float: left;margin-left: 5px;padding-top: 5px;width: 15%;margin-right: 10px;min-height:190px"><p style="overflow: hidden;text-overflow: ellipsis; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 3;margin-bottom: 5px"><?php echo $spec['spec_name']; ?></p><p style="margin-bottom: 5px"><?php echo $spec['spec_no']; ?></p></div>
                      <div style="position: center;padding-left:120px">
                        <input name="specs[<?php echo $spec_key; ?>][price]" value="<?php echo $spec['price']; ?>" type="text" class="form-control" placeholder="请输入单价" title="单价/套" style="width: 20%;display: inline-block;margin: 5px"><input name="specs[<?php echo $spec_key; ?>][size]" value="<?php echo $spec['size']; ?>" type="text" class="form-control" placeholder="请输入尺寸" title="尺寸" style="width: 20%;display: inline-block;margin: 5px"><input name="specs[<?php echo $spec_key; ?>][weight]" value="<?php echo $spec['weight']; ?>" type="text" class="form-control" placeholder="请输入净重" title="重量（净重）" style="width: 20%;display: inline-block;margin: 5px">
                        <input name="specs[<?php echo $spec_key; ?>][carton_num]" value="<?php echo $spec['carton_num']; ?>" type="text" class="form-control" placeholder="请输入每箱数量" title="件/箱" style="width: 10%;display: inline-block;margin: 5px"><input name="specs[<?php echo $spec_key; ?>][num]" value="<?php echo $spec['num']; ?>" type="text" class="form-control" placeholder="请输入数量（套）" title="数量（套）" style="width: 10%;display: inline-block;margin: 5px"><input name="specs[<?php echo $spec_key; ?>][packaging]" value="<?php echo $spec['packaging']; ?>" list="packaginglist" type="text" class="form-control" placeholder="请输入包装方式" onmouseover="this.title=this.value" style="width: 40%;display: inline-block;margin: 5px">
                        <datalist id="packaginglist">
                          <?php foreach($packaging_list as $packaging){ ?>
                          <option><?php echo $packaging; ?></option>
                          <?php } ?>
                        </datalist>
                        <input name="specs[<?php echo $spec_key; ?>][accessories]" value="<?php echo $spec['accessories']; ?>" list="accessorieslist" type="text" class="form-control" placeholder="请输入配件" onmouseover="this.title=this.value" style="width: 30%;display: inline-block;margin: 5px">
                        <datalist id="accessorieslist">
                          <?php foreach($accessories_list as $accessories){ ?>
                          <option><?php echo $accessories; ?></option>
                          <?php } ?>
                        </datalist>
                        <input name="specs[<?php echo $spec_key; ?>][attention]" value="<?php echo $spec['attention']; ?>" list="attentionlist" type="text" class="form-control" placeholder="请输入注意事项" onmouseover="this.title=this.value" style="width: 30%;display: inline-block;margin: 5px">
                        <datalist id="attentionlist">
                          <?php foreach($attention_list as $attention){ ?>
                          <option><?php echo $attention; ?></option>
                          <?php } ?>
                        </datalist>
                        <input name="specs[<?php echo $spec_key; ?>][new_img]" value="<?php echo $spec['img_url']; ?>" type="text" class="form-control new-img—blur" placeholder="请输入商品图片地址" onmouseover="this.title=this.value" style="width: 60%;display: inline-block;margin: 5px">
                      </div>
                    </div>
                  <?php } ?>
                  <?php } ?>
              </div>
            </div>
          </div>

          <?php if(!empty($purchase_contract['manner_of_packing'])){ ?>
            <div class="all-field-condition" style="display: none">
            <?php foreach($purchase_contract['manner_of_packing'] as $manner_of_packing_key=>$manner_of_packing){ ?>
              <div class="field-condition" style="padding-top: 20px;border-top: 1px solid #f39c12">
                <div class="form-group">
                  <label class="col-sm-2 control-label">包装方式：</label>
                  <div class="col-sm-8 keyword">
                    <div>
                      <textarea name="manner_of_packing[<?php echo $manner_of_packing_key; ?>][0]"  placeholder="请输入关键字" id="input-manner_of_packing" class="form-control" style="width: 70%;float: left" ><?php echo $manner_of_packing[0]; ?></textarea>
                      <?php if($manner_of_packing_key == 0){ ?>
                      <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                        <a class="btn btn-primary" href="javascript:;" onclick="addbzfs()">添加包装方式</a>
                      </div>
                      <?php } else { ?>
                      <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                        <a class="btn btn-danger" href="javascript:;" onclick="delbzfs($(this))">删除包装方式</a>
                      </div>
                      <?php } ?>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label">加粗：</label>
                  <div class="col-sm-8">
                    <div class="radio">
                      <label><input type="radio" name="manner_of_packing[<?php echo $manner_of_packing_key; ?>][1]" value="1" <?php if($manner_of_packing[1]==1){ ?>checked<?php } ?>>是</label>
                      <label style="margin-left: 10px"><input type="radio" name="manner_of_packing[<?php echo $manner_of_packing_key; ?>][1]" value="2" <?php if($manner_of_packing[1]==2){ ?>checked<?php } ?>>否</label>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label">背景颜色：</label>
                  <div class="col-sm-8">
                    <div class="radio">
                      <label><input type="radio" name="manner_of_packing[<?php echo $manner_of_packing_key; ?>][2]" value="0" <?php if($manner_of_packing[2]==0){ ?>checked<?php } ?>>否</label>
                      <label style="margin-left: 10px"><input type="radio" name="manner_of_packing[<?php echo $manner_of_packing_key; ?>][2]" value="1" <?php if($manner_of_packing[2]==1){ ?>checked<?php } ?>>黄色</label>
                      <label style="margin-left: 10px"><input type="radio" name="manner_of_packing[<?php echo $manner_of_packing_key; ?>][2]" value="2" <?php if($manner_of_packing[2]==2){ ?>checked<?php } ?>>绿色</label>
                      <label style="margin-left: 10px"><input type="radio" name="manner_of_packing[<?php echo $manner_of_packing_key; ?>][2]" value="3" <?php if($manner_of_packing[2]==3){ ?>checked<?php } ?>>蓝色</label>
                    </div>
                  </div>
                </div>
              </div>
            <?php } ?>
            </div>
          <?php }else{ ?>
            <div class="all-field-condition" style="display: none">
              <div class="field-condition" style="padding-top: 20px;border-top: 1px solid #f39c12">
                <div class="form-group">
                  <label class="col-sm-2 control-label">包装方式：</label>
                  <div class="col-sm-8 keyword">
                    <div>
                      <textarea name="manner_of_packing[0][0]"  placeholder="请输入关键字" id="input-manner_of_packing" class="form-control" style="width: 70%;float: left" ></textarea>
                      <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                        <a class="btn btn-primary" href="javascript:;" onclick="addbzfs()">添加包装方式</a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label">加粗：</label>
                  <div class="col-sm-8">
                    <div class="radio">
                      <label><input type="radio" name="manner_of_packing[0][1]" value="1" >是</label>
                      <label style="margin-left: 10px"><input type="radio" name="manner_of_packing[0][1]" value="2"  checked>否</label>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label">背景颜色：</label>
                  <div class="col-sm-8">
                    <div class="radio">
                      <label><input type="radio" name="manner_of_packing[0][2]" value="0" checked>否</label>
                      <label style="margin-left: 10px"><input type="radio" name="manner_of_packing[0][2]" value="1">黄色</label>
                      <label style="margin-left: 10px"><input type="radio" name="manner_of_packing[0][2]" value="2">绿色</label>
                      <label style="margin-left: 10px"><input type="radio" name="manner_of_packing[0][2]" value="3">蓝色</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <?php } ?>

          <div class="form-group" style="display: none">
            <label class="col-sm-2 control-label">双层汽泡袋+牛皮纸内盒包装：</label>
            <div class="col-sm-8">
              <div class="row">
                <?php if(!empty($purchase_contract['inner_box'])) { ?>
                <?php foreach($purchase_contract['inner_box'] as $img) { ?>
                <div class="col-sm-3">
                  <div class="thumbnail">
                    <img src="<?php echo $img; ?>?imageView2/1/w/100">
                    <input type="hidden" name="inner_box[]" value="<?php echo $img; ?>">
                    <div class="caption text-center">
                      <button type="button" onclick="$(this).parent().parent().parent().remove()" class="btn btn-default">删除</button>
                    </div>
                  </div>
                </div>
                <?php } ?>
                <?php } ?>
              </div>
              <button type="button" data-field="inner_box" class="btn-upload-inner-box btn btn-success"><i class="fa fa-upload"></i> 上传</button>
            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
              <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
            </div>
          </div>
        </form>
      </div>
      <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      singleDatePicker:true,
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('YYYY-MM-DD'));
    })
  })()
</script>

<script type="text/javascript">
  var spec_key = <?php echo $count; ?>;
  var manner_of_packing_key = <?php echo $count_manner_of_packing; ?>;
  var packaging_list_json = '<?php echo $packaging_list_json; ?>';
  var accessories_list_json = '<?php echo $accessories_list_json; ?>';
  var attention_list_json = '<?php echo $attention_list_json; ?>';
  // Autocomplete */
  (function($) {
    $.fn.autocomplete = function(option) {
      return this.each(function() {
        var $this = $(this);
        var $dropdown = $('<ul class="dropdown-menu" />');

        this.timer = null;
        this.items = {};

        $.extend(this, option);

        $this.attr('autocomplete', 'off');

        // Focus
        $this.on('focus', function() {
          this.request();
        });

        // Blur
        $this.on('blur', function() {
          setTimeout(function(object) {
            object.hide();
          }, 200, this);
        });

        // Keydown
        $this.on('keydown', function(event) {
          switch(event.keyCode) {
            case 27: // escape
              this.hide();
              break;
            default:
              this.request();
              break;
          }
        });

        // Click
        this.click = function(event) {
          event.preventDefault();

          var value = $(event.target).parent().attr('data-value');

          if (value && this.items[value]) {
            this.select(this.items[value]);
          }
        }

        // Show
        this.show = function() {
          var pos = $this.position();

          $dropdown.css({
            'min-width': '50%',
            top: pos.top + $this.outerHeight(),
            left: pos.left
          });

          $dropdown.show();
        }

        // Hide
        this.hide = function() {
          $dropdown.hide();
        }

        // Request
        this.request = function() {
          clearTimeout(this.timer);

          this.timer = setTimeout(function(object) {
            if ($(object).val()) object.source($(object).val(), $.proxy(object.response, object));
          }, 200, this);
        }

        // Response
        this.response = function(json) {
          var html = '';
          var i = 0;
          if (json.length) {
            for (i = 0; i < json.length; i++) {
              if (i > 10) break;
              // update element items
              this.items[json[i]['spec_no']] = json[i];

              html += '<li data-value="' + json[i]['spec_no'] + '"><a href="#">' + json[i]['spec_no'] + json[i]['spec_name'] + '</a></li>';
            }
          }

          if (html) {
            this.show();
          } else {
            this.hide();
          }

          $dropdown.html(html);
        }

        $dropdown.on('click', '> li > a', $.proxy(this.click, this));
        $this.after($dropdown);
      });
    }
  })(window.jQuery);

  $(document).ready(function () {
    $('#input-product').autocomplete({
      'source': function(request, response) {
        var provider_no = $('select[name=\'provider_no\']').val();
        $.ajax({
          url: '<?php echo $get_specs; ?>&filter_name=' + encodeURIComponent(request)+'&provider_no='+provider_no,
          dataType: 'json',
          success: function(json) {
            response(json);
            // response($.map(json, function(item) {
            //   return item
            // }));
          }
        });
      },
      'select': function(item) {
        $('#input-product').val('');
        var weight_g = ''
        if (item.content_weight) {
          weight_g = item.content_weight
        }

        var last_price = item.last_price ? (Math.round(item.last_price * 100) / 100).toFixed(2) : '';

        var carton_num = ''
        if (item.prop2) {
          carton_num = item.prop2.match(/\d+/g)
          if (carton_num == null) {
            carton_num = 0
          }
        }

        var size = ''
        if (item.prop5) {
          size = item.prop5
        }

        var packaging = ''
        if (item.packaging) {
          packaging = item.packaging
        }

        var accessories = ''
        if (item.accessories) {
          accessories = item.accessories
        }

        var attention = ''
        if (item.attention) {
          attention = item.attention
        }

        var spec_html = '<div style="margin-top: 5px;min-height: 120px;overflow:hidden">' +
                '<input name="specs['+spec_key+'][spec_no]" type="hidden" value="'+item.spec_no+'"> ' +
                '<input name="specs['+spec_key+'][goods_id]" type="hidden" value="'+item.goods_id+'"> ' +
                '<input name="specs['+spec_key+'][spec_name]" value="'+item.spec_name+'" type="hidden">' +
                '<input name="specs['+spec_key+'][img_url]" value="'+item.img_url+'" class="input-img-upload" type="hidden">' +
                '<a class="btn btn-danger" href="javascript:;" onclick="$(this).parent().remove()" style="float: left;margin-right: 10px">删除产品</a>' +
                '<img style="float: left" width="100" '
        if (item.img_url) {
          spec_html += 'src="'+item.img_url+'"'
        } else {
          spec_html += 'height="100"'
        }
        spec_html += ' class="img-thumbnail img-upload">' +
                '<div style="float: left;margin-left: 5px;padding-top: 5px;width: 15%;margin-right: 10px;min-height: 190px"><p style="overflow: hidden;text-overflow: ellipsis; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 4;margin-bottom: 5px">'+item.spec_name+'</p><p style="margin-bottom: 5px">'+item.spec_no+'</p></div>' +
                '<div style="position: center;padding-left:120px">' +
                '<input name="specs['+spec_key+'][price]" value="'+last_price+'" type="text" class="form-control" placeholder="请输入单价" title="单价/套" style="width: 20%;display: inline-block;margin: 5px"><input name="specs['+spec_key+'][size]" value="'+size+'" type="text" class="form-control" placeholder="请输入尺寸" title="尺寸" style="width: 20%;display: inline-block;margin: 5px"><input name="specs['+spec_key+'][weight]" value="'+weight_g+'" type="text" class="form-control" placeholder="请输入净重" title="重量（净重）" style="width: 20%;display: inline-block;margin: 5px">' +
                '<input name="specs['+spec_key+'][carton_num]" value="'+carton_num+'" type="text" class="form-control" placeholder="请输入每箱数量" title="件/箱" style="width: 10%;display: inline-block;margin: 5px"><input name="specs['+spec_key+'][num]" type="text" class="form-control" placeholder="请输入数量（套）" title="数量（套）" style="width: 10%;display: inline-block;margin: 5px"><input name="specs['+spec_key+'][packaging]" value="'+packaging+'" list="packaginglist" type="text" class="form-control" placeholder="请输入包装方式" onmouseover="this.title=this.value" style="width: 40%;display: inline-block;margin: 5px">' +
                '<datalist id="packaginglist">'
        $.each($.parseJSON(packaging_list_json), function(i,val){
          spec_html += '<option>'+val+'</option>'
        });
        spec_html += '</datalist>' +
                '<input name="specs['+spec_key+'][accessories]" value="'+accessories+'" list="accessorieslist" type="text" class="form-control" placeholder="请输入配件" onmouseover="this.title=this.value" style="width: 30%;display: inline-block;margin: 5px">'+
                '<datalist id="accessorieslist">'
        $.each($.parseJSON(accessories_list_json), function(i,val){
          spec_html += '<option>'+val+'</option>'
        });
        spec_html += '</datalist>' +
                '<input name="specs['+spec_key+'][attention]" value="'+attention+'" type="text" list="attentionlist" class="form-control" placeholder="请输入注意事项" onmouseover="this.title=this.value" style="width: 30%;display: inline-block;margin: 5px">' +
                '<datalist id="attentionlist">'
        $.each($.parseJSON(attention_list_json), function(i,val){
          spec_html += '<option>'+val+'</option>'
        });
        spec_html += '</datalist>' +
                '<select  style="width: 40%;margin: 5px;display: inline-block" class="form-control">'
        if (item.provider_goods_list) {
          $.each(item.provider_goods_list, function(i,val){
            spec_html += '<option>'+val.provider_name+'</option>'
          });
        }
        spec_html += '</select>'
                '</div>' +
                '</div>'
        // if (item.img_url == '' || item.img_url == null) {
          spec_html += '<input name="specs['+spec_key+'][new_img]" value="" type="text" class="form-control new-img—blur" placeholder="请输入商品图片地址" onmouseover="this.title=this.value" style="width: 60%;display: inline-block;margin: 5px">'
        // }
        $('.all-specs').prepend(spec_html);
        spec_key += 1;
      }
    });

    document.getElementById('select-provider').addEventListener('change', function() {
      // 当选项变更时，这里的代码会被执行
      $.ajax({
        url: '<?php echo $get_provider_carton_mark; ?>&provider_no=' + this.value,
        dataType: 'json',
        success: function(json) {
          if (json['value']) {
            $("#carton_mark_img").attr("src", json['value']);
            $("#carton_mark_input").val(json['value'])
            $("#carton_mark_div").show();
          } else {
            $("#carton_mark_img").attr("src","");
            $("#carton_mark_input").val("")
            $("#carton_mark_div").hide();
          }
        }
      });
    });

  });

  $("body").on("blur",".new-img—blur",function(){
    var value = $(this).val();
    $(this).parent().parent().find('img').attr("src", value);
  });

  function toVaild() {
    var contract_number = document.getElementById("input-contract_number").value;
    if (contract_number == "") {
      confirm('请输入合同号')
      return false;
    }

    // var warehouse_date = document.getElementById("input-warehouse_date").value;
    // if (warehouse_date == "") {
    //   confirm('请输入入仓时间')
    //   return false;
    // }

    var sign_date = document.getElementById("input-sign_date").value;
    if (sign_date == "") {
      confirm('请选择签订时间')
      return false;
    }

    var delivery_date = document.getElementById("input-delivery_date").value;
    if (delivery_date == "") {
      confirm('请选择交货时间')
      return false;
    }

    var provider_no = $('select[name=\'provider_no\']').val();
    if (provider_no == "0") {
      confirm('请选择供应商')
      return false;
    }

    // var carton_mark = document.getElementById("carton_mark_input").value;
    // if (carton_mark == "") {
    //   confirm('请上传外箱唛')
    //   return false;
    // }

    // var arr_inner_box =[];
    // $("input[name='inner_box[]']").each(function(){
    //   arr_inner_box.push($(this).val());
    // })
    // if (arr_inner_box.length == 0) {
    //   confirm('请上传双层汽泡袋+牛皮纸内盒包装')
    //   return false;
    // }
    // if (arr_inner_box.length > 2) {
    //   confirm('双层汽泡袋+牛皮纸内盒包装只能上传两张')
    //   return false;
    // }

    return true;
  }

  function addbzfs() {
    manner_of_packing_key += 1;
    var addHtml =
            '<div class="field-condition" style="padding-top: 20px;border-top: 1px solid #f39c12">' +
            '<div class="form-group">' +
            '<label class="col-sm-2 control-label">包装方式：</label>' +
            '<div class="col-sm-8 keyword">' +
            '<div>' +
            '<textarea name="manner_of_packing['+manner_of_packing_key+'][0]"  placeholder="请输入关键字" id="input-manner_of_packing" class="form-control" style="width: 70%;float: left" ></textarea>' +
            '<div class="box-tools" style="width: 15%;float: left;margin-left: 8px">'+
            '<a class="btn btn-danger" href="javascript:;" onclick="delbzfs($(this))">删除包装方式</a>'+
            '</div>'+
            '</div>' +
            '</div>' +
            '</div>' +
            '<div class="form-group">' +
            '<label class="col-sm-2 control-label">加粗：</label>' +
            '<div class="col-sm-8">' +
            '<div class="radio">' +
            '<label><input type="radio" name="manner_of_packing['+manner_of_packing_key+'][1]" value="1" >是</label>' +
            '<label style="margin-left: 10px"><input type="radio" name="manner_of_packing['+manner_of_packing_key+'][1]" value="2"  checked>否</label>' +
            '</div>' +
            '</div>' +
            '</div>' +
            '<div class="form-group">' +
            '<label class="col-sm-2 control-label">背景颜色：</label>' +
            '<div class="col-sm-8">' +
            '<div class="radio">' +
            '<label><input type="radio" name="manner_of_packing['+manner_of_packing_key+'][2]" value="0" checked>否</label>' +
            '<label style="margin-left: 10px"><input type="radio" name="manner_of_packing['+manner_of_packing_key+'][2]" value="1">黄色</label>' +
            '<label style="margin-left: 10px"><input type="radio" name="manner_of_packing['+manner_of_packing_key+'][2]" value="2">绿色</label>' +
            '<label style="margin-left: 10px"><input type="radio" name="manner_of_packing['+manner_of_packing_key+'][2]" value="3">蓝色</label>' +
            '</div>' +
            '</div>' +
            '</div>' +
            '</div>';
    $(".all-field-condition").append(addHtml);
  }

  function delbzfs(obj) {
    $(obj).parent().parent().parent().parent().parent().remove();
  }

  $('.content').on('click', '.btn-upload-carton-mark', function() {
    $('#form-upload').remove();

    var target = $(this);

    $('body').prepend('<form enctype="multipart/form-data" id="form-upload" style="display: none;"><input type="file" name="file" value="" accept="image/*, video/*" /><input type="hidden" name="token" value="" /></form>');

    $('#form-upload input[name=\'file\']').trigger('click');

    if (typeof timer != 'undefined') {
      clearInterval(timer);
    }

    timer = setInterval(function() {
      if ($('#form-upload input[name=\'file\']').val() != '') {
        clearInterval(timer);

        $.ajax({
          url: '<?php echo $getToken; ?>',
          type: 'get',
          dataType: 'json',
          success: function(json) {
            $('#form-upload input[name=\'token\']').val(json.uploadToken);
            $.ajax({
              url: 'https://up-z2.qiniup.com',
              type: 'post',
              dataType: 'json',
              data: new FormData($('#form-upload')[0]),
              cache: false,
              contentType: false,
              processData: false,
              beforeSend: function() {
                target.find('i').replaceWith('<i class="fa fa-circle-o-notch fa-spin"></i>');
                target.prop('disabled', true);
              },
              complete: function() {
                target.find('i').replaceWith('<i class="fa fa-upload"></i>');
                target.prop('disabled', false);
              },
              success: function(res) {
                var spec_image = json.httpHost + res.key
                target.siblings().filter(".row").find('img').attr("src", spec_image);
                target.siblings().filter(".row").find('input').val(spec_image);
                target.siblings().filter(".row").show();
              },
              error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
              }
            });
          },
          error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
          }
        });
      }
    }, 500);
  });

  $('.content').on('click', '.img-upload', function() {
    $('#form-upload').remove();

    var target = $(this);

    $('body').prepend('<form enctype="multipart/form-data" id="form-upload" style="display: none;"><input type="file" name="file" value="" accept="image/*" /><input type="hidden" name="token" value="" /></form>');

    $('#form-upload input[name=\'file\']').trigger('click');

    if (typeof timer != 'undefined') {
      clearInterval(timer);
    }

    timer = setInterval(function() {
      if ($('#form-upload input[name=\'file\']').val() != '') {
        clearInterval(timer);

        $.ajax({
          url: '<?php echo $getToken; ?>',
          type: 'get',
          dataType: 'json',
          success: function(json) {
            $('#form-upload input[name=\'token\']').val(json.uploadToken);
            $.ajax({
              url: 'https://up-z2.qiniup.com',
              type: 'post',
              dataType: 'json',
              data: new FormData($('#form-upload')[0]),
              cache: false,
              contentType: false,
              processData: false,
              beforeSend: function() {
                target.find('i').replaceWith('<i class="fa fa-circle-o-notch fa-spin"></i>');
                target.prop('disabled', true);
              },
              complete: function() {
                target.find('i').replaceWith('<i class="fa fa-upload"></i>');
                target.prop('disabled', false);
              },
              success: function(res) {
                var spec_image = json.httpHost + res.key
                target.siblings('.input-img-upload').val(spec_image);
                target.attr("src", spec_image);
                target.attr("height", "auto");
                target.attr("max-height", "110");
              },
              error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText)
              }
            });
          },
          error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
          }
        });
      }
    }, 500);
  });

  $('.content').on('click', '.btn-upload-inner-box', function() {
    $('#form-upload').remove();

    var target = $(this);

    $('body').prepend('<form enctype="multipart/form-data" id="form-upload" style="display: none;"><input type="file" name="file" value="" accept="image/*, video/*" /><input type="hidden" name="token" value="" /></form>');

    $('#form-upload input[name=\'file\']').trigger('click');

    if (typeof timer != 'undefined') {
      clearInterval(timer);
    }

    timer = setInterval(function() {
      if ($('#form-upload input[name=\'file\']').val() != '') {
        clearInterval(timer);

        $.ajax({
          url: '<?php echo $getToken; ?>',
          type: 'get',
          dataType: 'json',
          success: function(json) {
            $('#form-upload input[name=\'token\']').val(json.uploadToken);
            $.ajax({
              url: 'https://up-z2.qiniup.com',
              type: 'post',
              dataType: 'json',
              data: new FormData($('#form-upload')[0]),
              cache: false,
              contentType: false,
              processData: false,
              beforeSend: function() {
                target.find('i').replaceWith('<i class="fa fa-circle-o-notch fa-spin"></i>');
                target.prop('disabled', true);
              },
              complete: function() {
                target.find('i').replaceWith('<i class="fa fa-upload"></i>');
                target.prop('disabled', false);
              },
              success: function(res) {
                target.prev().append('<div class="col-sm-3"><div class="thumbnail"><img src="' + json.httpHost + res.key + '?imageView2/1/w/100" alt="..."><input type="hidden" name="' + target.data('field') + '[]" value="'+ json.httpHost + res.key + '"><div class="caption text-center"><button type="button" onclick="$(this).parent().parent().parent().remove()" class="btn btn-default">删除</button></div></div></div>');
              },
              error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
              }
            });
          },
          error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
          }
        });
      }
    }, 500);
  });
</script>
<?php echo $footer; ?>