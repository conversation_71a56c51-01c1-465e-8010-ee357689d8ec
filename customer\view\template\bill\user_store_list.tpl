<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <h1>
            毛利率查看权限
            <small></small>
        </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
        <?php if ($success) { ?>
        <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
        <?php } ?>
        <?php if ($warning) { ?>
        <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
        <?php } ?>
        <div class="box box-primary">
            <div class="box-body">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>人员：</label>
                        <select id="w1" class="form-control" name="filter_user" multiple>
                            <?php foreach ($users as $key=>$user) { ?>
                            <option value="<?php echo $user['union_id']; ?>"><?php echo $user['real_name']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>所属店铺：</label>
                        <select id="w2" class="form-control" name="filter_store" multiple>
                            <?php foreach($stores as $store) { ?>
                            <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <!-- /.box-body -->
            <div class="box-footer">
                <div class="pull-right">
                    <button type="button" id="button-filter" class="btn bg-purple"><i class="glyphicon glyphicon-search"></i> 筛选</button>
                </div>
            </div>
        </div>
        <div class="box box-success">
            <div class="box-header with-border">
                <p class="box-title">权限列表</p>
                <div class="box-tools">
                    <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
                </div>
            </div>
            <div class="box-body table-responsive no-padding">
                <table class="table text-middle table-bordered table-hover table-striped">
                    <tbody><tr>
                        <th>权限名称</th>
                        <th style="width: 600px">人员</th>
                        <th style="width: 600px">店铺</th>
                        <th class="text-right">操作</th>
                    </tr>
                    <?php if (!empty($userStores)) { ?>
                    <?php foreach ($userStores as $userStore) { ?>
                    <tr data-id="<?php echo $userStore['user_store_pover_id']; ?>">
                        <td><?php echo $userStore['name']; ?></td>
                        <td><?php echo $userStore['users']; ?></td>
                        <td><?php echo $userStore['stores']; ?></td>
                        <td class="text-right">
                            <a class="btn btn-success" href="<?php echo $add; ?>&user_store_pover_id=<?php echo $userStore['user_store_pover_id']; ?>" title="" target="_blank">编辑</a>
                            <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">删除</button>
                        </td>
                    </tr>
                    <?php } ?>
                    <?php } else{ ?>
                    <td colspan="10" align="center"> 暂无权限数据 </td>
                    <?php } ?>
                    </tbody></table>
            </div>
        </div>

        <!-- 删除 -->
        <div class="modal modal-danger fade" id="del-modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-del">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title">删除</h4>
                        </div>
                        <div class="modal-body">
                            <p>确定删除此记录吗？</p>
                            <input id="del-id" name="selected[]" type="hidden" value="">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                            <button id="del-yes" type="button" class="btn btn-outline">是</button>
                        </div>
                    </form>
                </div>
                <!-- /.modal-content -->
            </div>
            <!-- /.modal-dialog -->
        </div>
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>

<script>
    var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
    window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"35px","placeholder":"请选择人员","language":"zh-CN"};

    if (jQuery('#w1').data('select2')) { jQuery('#w1').select2('destroy'); }
    jQuery.when(jQuery('#w1').select2(select2_5eaa6d36)).done(initS2Loading('w1','s2options_c4acac00'));

    var filter_user = '<?php echo $filter_user_json; ?>'
    $("#w1").val($.parseJSON(filter_user)).trigger("change");

    var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
    window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"35px","placeholder":"请选择店铺","language":"zh-CN"};

    if (jQuery('#w2').data('select2')) { jQuery('#w2').select2('destroy'); }
    jQuery.when(jQuery('#w2').select2(select2_5eaa6d36)).done(initS2Loading('w2','s2options_c4acac00'));

    var filter_store = '<?php echo $filter_store_json; ?>'
    $("#w2").val($.parseJSON(filter_store)).trigger("change");
</script>
<style type="text/css">
    .table a.asc:after {
        content: " \f106";
        font-family: FontAwesome;
    }
    .table a.desc:after {
        content: " \f107";
        font-family: FontAwesome;
    }
    .select2-container--krajee .select2-selection--multiple .select2-search--inline .select2-search__field {
        height: 26px;
    }
    /*.select2-selection {*/
/*  height: 34px;*/
/*}*/
    /*.select2-container .select2-selection--multiple .select2-selection__rendered {*/
/*  padding-top: 0;*/
/*}*/
</style>

<script type="text/javascript">
    (function () {
        // 筛选
        $('#button-filter').on('click', function() {
            url = '<?php echo $nofilter; ?>';

            var filter_user = $('select[name=\'filter_user\']').val();

            if (filter_user != '') {
                url += '&filter_user=' + encodeURIComponent(filter_user);
            }

            var filter_store = $('select[name=\'filter_store\']').val();

            if (filter_store != '') {
                url += '&filter_store=' + encodeURIComponent(filter_store);
            }
            location.href = url;
        });

        $('#del-modal').on('show.bs.modal', function(event) {
            $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
        })
        $('#del-yes').on('click', () => {$('#form-del').submit()})
    })()
</script>
<?php echo $footer; ?>