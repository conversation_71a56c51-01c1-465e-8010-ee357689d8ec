<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">费用名称：</label>
              <div class="col-sm-8">
                <input type="text" name="fee_name" value="<?php echo $fee_name; ?>" placeholder="请输入费用名称" id="input-name" class="form-control" />
              </div>
            </div> 
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-calculate">计算方式：</label>
              <div class="col-sm-8">
                <select name="calculate" id="input-calculate" class="form-control">
                  <option value="">请选择费用计算方式</option>
                  <option value="total"<?php if ($calculate == 'total') { ?> selected="selected"<?php } ?>>固定金额</option>
                  <option value="percent"<?php if ($calculate == 'percent') { ?> selected="selected"<?php } ?>>销售额百分比</option>
                </select>
              </div>
            </div>
            <div id="fee-total" class="form-group">
              <label class="col-sm-2 control-label" for="input-total">金额：</label>
              <div class="col-sm-8">
                <input type="text" name="fee_total" value="<?php echo (float)$fee_total; ?>" placeholder="请输入金额" id="input-total" class="form-control" />
              </div>
            </div>
            <div id="fee-percent" class="form-group">
              <label class="col-sm-2 control-label" for="input-percent">百分比：</label>
              <div class="col-sm-8">
                <input type="text" name="fee_percent" value="<?php echo $fee_percent; ?>" placeholder="请输入百分比" id="input-percent" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">分摊店铺：</label>
              <div class="col-sm-8">
                <div class="well well-sm" style="height: 150px; overflow: auto;">
                  <?php foreach ($stores as $store) { ?>
                  <?php if (in_array($store['store_id'], $store_ids)) { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="store_ids[]" value="<?php echo $store['store_id']; ?>" checked="checked"><?php echo $store['name']; ?></label>
                  </div>
                  <?php } else { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="store_ids[]" value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></label>
                  </div>
                  <?php } ?>
                  <?php } ?>
                </div>
              </div>
            </div> 
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-method">分摊方式：</label>
              <div class="col-sm-8">
                <select name="method" id="input-method" class="form-control">
                  <option value="">请选择分摊方式</option>
                  <option value="monthly"<?php if ($method == 'monthly') { ?> selected="selected"<?php } ?>>每月平均扣款</option>
                  <option value="daily"<?php if ($method == 'daily') { ?> selected="selected"<?php } ?>>每日扣款</option>
                </select>
              </div>
            </div>
            <div id="fee-percent" class="form-group">
              <label class="col-sm-2 control-label" for="input-order">排序数值：</label>
              <div class="col-sm-8">
                <input type="number" name="sort_order" value="<?php echo $sort_order; ?>" placeholder="请输入排序，数字越大排前面" id="input-order" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
$('#input-calculate').on('change', function() {
  if ($(this).val() == 'total') {
    $('#fee-total').show();
  } else {
    $('#fee-total').hide();
    $('#input-total').val('0');
  }

  if ($(this).val() == 'percent') {
    $('#fee-percent').show();
  } else {
    $('#fee-percent').hide();
    $('#input-percent').val('0');
  }
});

$('#input-calculate').trigger('change');
</script>
<?php echo $footer; ?>