<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        账单列表
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <option <?php if ($store['type'] == 1) { ?>style="color: #3dd5f3"<?php }else if ($store['type'] == 2) { ?>style="color: #ff0000"<?php } ?> value="<?php echo $store['store_id']; ?>" <?php if ($store['store_id'] == $filter_store) { ?>selected="selected" <?php } ?>><?php echo $store['name']; ?></option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>账单时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>类别：</label>
                <select class="form-control" name="filter_bill_key_id">
                  <option value="*">全部类别</option>
                  <?php foreach($bill_keys as $bill_key) { ?>
                  <?php if ($bill_key['alipay_bill_sort_id'] == $filter_bill_key_id) { ?>
                  <option value="<?php echo $bill_key['alipay_bill_sort_id']; ?>" selected="selected"><?php echo $bill_key['name']; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $bill_key['alipay_bill_sort_id']; ?>"><?php echo $bill_key['name']; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
          <button type="button" id="button-add" class="btn btn-success pull-right" style="margin-right: 10px"><i class="glyphicon glyphicon-plus"></i> 添加</button>
          <button type="button" id="button-del" class="btn btn-danger pull-right" style="margin-right: 10px"><i class="glyphicon glyphicon-trash"></i> 删除</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">账单列表</h3>
          <div class="box-tools">
            <!-- <a class="btn btn-sm btn-primary" href="<?php //echo $add; ?>">添加</a> -->
          </div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">店铺</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">店铺</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'month') { ?>
                <a href="<?php echo $month; ?>" class="<?php echo strtolower($order); ?>">账单时间</a>
                <?php } else { ?>
                <a href="<?php echo $month; ?>">账单时间</a>
                <?php } ?>
              </th>
              <th>类别</th>
              <th>收入</th>
              <th>支出</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($bills)) { ?>
              <?php foreach ($bills as $bill) { ?>
                <tr>
                  <td><?php echo $bill['store']; ?></td>
                  <td><?php echo $bill['date']; ?></td>
                  <td><?php echo $bill['keys']; ?></td>
                  <td><?php echo $bill['sum_income']; ?></td>
                  <td><?php echo $bill['sum_disburse']; ?></td>
                  <td class="text-right">
                    <a class="btn btn-success" href="<?php echo $bill['detail']; ?>" title="" target="_blank">查看详情</a>
                  </td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      var filter_bill_key_id = $('select[name=\'filter_bill_key_id\']').val();

      if (filter_bill_key_id != '*') {
        url += '&filter_bill_key_id=' + encodeURIComponent(filter_bill_key_id);
      }
      location.href = url;
    });

    $('#button-add').on('click', function() {
      url = '<?php echo $addtosort; ?>';

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&store_id=' + encodeURIComponent(filter_store);
      } else {
        confirm('请选择店铺')
        return false
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();

      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }else {
        confirm('请选择账单时间')
        return false
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();

      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }else {
        confirm('请选择账单时间')
        return false
      }

      var filter_bill_key_id = $('select[name=\'filter_bill_key_id\']').val();

      if (filter_bill_key_id != '*') {
        url += '&alipay_bill_sort_id=' + encodeURIComponent(filter_bill_key_id);
      }
      window.open(url, '_blank');
    });

    $('#button-del').on('click', function() {
      url = '<?php echo $deltosort; ?>';
      window.open(url, '_blank');
    });
  })()
</script>
<?php echo $footer; ?>