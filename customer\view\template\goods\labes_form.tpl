<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">维度名称：</label>
              <div class="col-sm-8">
                <input type="text" name="name" value="<?php echo $labels['name']; ?>" placeholder="请输入维度名称" id="input-name" class="form-control" />
                <div class="text-danger" style="display: none">请请输入维度名称</div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-quan">状态：</label>
              <div class="col-sm-8">
                <div class="radio">
                  <label><input type="radio" name="status" value="1"  <?php if(!empty($labels['status']) && $labels['status'] == 1){ ?>checked<?php } ?>>正常</label>
                  <label style="margin-left: 10px"><input type="radio" name="status" value="0"  <?php if(empty($labels['status']) || (!empty($labels['status']) && $labels['status'] == 0)){ ?>checked<?php } ?>>隐藏</label>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-quan">标签：</label>
              <div class="col-sm-8 keyword">
                <?php if($labels['tag']) { ?>
                <?php foreach ($labels['tag'] as $keykey =>$keyval ) { ?>
                <?php if($keykey==0) { ?>
                <div style="display: flex; align-items: center; gap: 5px;padding: 6px 12px 6px 12px">
                  <div><input type="checkbox" name="oldtag[<?php echo $keyval['label_id']; ?>][status]" <?php if($keyval['status'] == 1) { ?>checked<?php } ?> /></div>
                  <input type="text" name="oldtag[<?php echo $keyval['label_id']; ?>][name]" value="<?php echo $keyval['name']; ?>" placeholder="请输入标签" id="input-keyword" class="form-control" />
                  <div class="box-tools">
                    <a class="btn btn-primary" href="javascript:;" onclick="addkey($(this))">添加标签</a>
                  </div>
                </div>
                <?php } else { ?>
                <div style="display: flex; align-items: center; gap: 5px;padding: 6px 12px 6px 12px">
                  <div><input type="checkbox" name="oldtag[<?php echo $keyval['label_id']; ?>][status]" <?php if($keyval['status'] == 1) { ?>checked<?php } ?> /></div>
                  <input type="text" name="oldtag[<?php echo $keyval['label_id']; ?>][name]" value="<?php echo $keyval['name']; ?>" placeholder="请输入标签" id="input-keyword" class="form-control" />
                  <div class="box-tools">
                    <a class="btn btn-danger" href="javascript:;" onclick="delkey($(this))">删除</a>
                  </div>
                </div>
                <?php } ?>
                <?php } ?>
                <?php } else { ?>
                <div style="display: flex; align-items: center; gap: 5px;padding: 6px 12px 6px 12px">
                  <div><input type="checkbox" name="tag[0][status]" checked /></div>
                  <input type="text" name="tag[0][name]" value="" placeholder="请输入标签" id="input-keyword" class="form-control" />
                  <div class="box-tools">
                    <a class="btn btn-primary" href="javascript:;" onclick="addkey($(this))">添加标签</a>
                  </div>
                </div>
                <?php } ?>
              </div>
            </div>



            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>


<script type="text/javascript">
  //初始化
  $('input[type="checkbox"]').iCheck({
    checkboxClass: 'icheckbox_flat-blue',
  });
  function toVaild() {
    var isValid = true;
    var inputName = $("#input-name").val()
    if (inputName == '') {
      isValid = false;
      $("#input-name").siblings('.text-danger').show();
    } else {
      $("#input-name").siblings('.text-danger').hide();
    }

    if (isValid) {
      return true;
    } else {
      return false;
    }
  }

  var key = 0;
  function addkey(obj) {
    key += 1;
    var addHtml =
            '<div style="display: flex; align-items: center; gap: 5px;padding: 6px 12px 6px 12px">' +
            '<div><input type="checkbox" name="tag['+key+'][status]" checked /></div>' +
            '<input type="text" name="tag['+key+'][name]" value="" placeholder="请输入标签" id="input-keyword" class="form-control"/>'+
            '<div class="box-tools">' +
            '<a class="btn btn-danger" href="javascript:;" onclick="delkey($(this))">删除</a>' +
            '</div>' +
            '</div>';
    $(obj).parent().parent().parent().append(addHtml);

    $('input[type="checkbox"]').iCheck({
      checkboxClass: 'icheckbox_flat-blue',
    });
  }

  function delkey(obj) {
    $(obj).parent().parent().remove();
  }
</script>
<?php echo $footer; ?>