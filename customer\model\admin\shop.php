<?php
class ModelAdminShop extends Model {
    public function addShopId($data,$user_id) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "shop_platform SET store_id = '" . (int)$data['store_id'] . "', platform_id = '" . $this->db->escape($data['platform_id']) . "', custodian = '" . $this->db->escape($user_id) . "', date_added = NOW()");

        $shop_platform_id = $this->db->getLastId();

        return $shop_platform_id;
    }

    public function editShopId($shop_platform_id,$data,$user_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "shop_platform SET store_id = '" . (int)$data['store_id'] . "', platform_id = '" . $this->db->escape($data['platform_id']) . "', custodian = '" . $this->db->escape($user_id) . "' WHERE shop_platform_id = '" . (int)$shop_platform_id . "'");
    }

    public function deleteShopId($shop_platform_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "shop_platform WHERE shop_platform_id = '" . (int)$shop_platform_id . "'");
    }
	public function getshopIds($data = array()) {
		$sql = "SELECT sp.*,u.real_name FROM " . DB_PREFIX . "shop_platform as sp LEFT JOIN " . DB_PREFIX . "union_info as ui ON sp.custodian = ui.user_id LEFT JOIN _union as u ON ui.union_id = u.union_id WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(sp.platform_id, u.real_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND FIND_IN_SET(sp.store_id, '" . $this->db->escape($data['filter_store']) . "')";
        }

		$sql .= " ORDER BY sp.shop_platform_id DESC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalshopIds($data = array()) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "shop_platform as sp LEFT JOIN " . DB_PREFIX . "union_info as ui ON sp.custodian = ui.user_id LEFT JOIN _union as u ON ui.union_id = u.union_id WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(sp.platform_id, u.real_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND FIND_IN_SET(sp.store_id, '" . $this->db->escape($data['filter_store']) . "')";
        }

        $sql .= " ORDER BY sp.shop_platform_id DESC";

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

    public function getShopId($shop_platform_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "shop_platform WHERE shop_platform_id = '" . $shop_platform_id . "'");

        return $query->row;
    }

    public function getUsers() {
        $sql = "SELECT *, (SELECT real_name FROM _union WHERE union_id = ui.union_id) AS real_name, (SELECT name FROM `" . DB_PREFIX . "user_group` WHERE user_group_id = ui.user_group_id) AS user_group FROM `" . DB_PREFIX . "union_info` ui WHERE ui.status = '1'";

        $sql .= " ORDER BY user_id DESC";

        $query = $this->db->query($sql);

        return $query->rows;
    }
}