<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        防伪码
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="产品名称编码" value="<?php echo $filter_name; ?>">
                </div>
              </div>

            <div class="col-md-4">
              <div class="form-group">
                <label>供应商：</label>
                <select class="form-control" name="filter_provider">
                  <option value="*">全部供应商</option>
                  <?php foreach($providers as $provider_key => $provider) { ?>
                  <?php if ($provider_key == $filter_provider) { ?>
                  <option value="<?php echo $provider_key; ?>" selected="selected"><?php echo $provider; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $provider_key; ?>"><?php echo $provider; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="col-md-4">
              <div class="form-group">
                <label>分配时间：</label>
                <div class="input-group">
                  <div class="input-group-addon">
                    <i class="glyphicon glyphicon-calendar"></i>
                  </div>
                  <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                  <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                  <?php } else{ ?>
                  <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                  <?php } ?>
                  <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                  <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                </div>
              </div>
            </div>

          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <a class="btn btn-success" href="<?php echo $allocation; ?>"><i class="glyphicon glyphicon-tasks"></i> 分配数量</a>
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>

      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">防伪码列表</h3>
          <div class="box-tools">
            <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
          </div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>供应商</th>
              <th>前缀</th>
              <th>位数</th>
              <th>开始值</th>
              <th>结束值</th>
              <th>范围</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($antifakes)) { ?>
            <?php foreach ($antifakes as $antifake) { ?>
            <tr data-id="<?php echo $antifake['antifake_id']; ?>">
              <td> <?php if (!empty($providers[$antifake['provider_no']])) { ?><?php echo $providers[$antifake['provider_no']]; ?><?php } ?></td>
              <td><?php echo $antifake['prefix']; ?></td>
              <td><?php echo $antifake['digit']; ?></td>
              <td><?php echo $antifake['start_num']; ?></td>
              <td><?php echo $antifake['end_num']; ?></td>
              <td><?php echo $antifake['antifake_range']; ?></td>
              <td class="text-right">
                <a class="btn btn-success" href="<?php echo $edit; ?>&antifake_id=<?php echo $antifake['antifake_id']; ?>" title="">修改</a>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#user-del-modal">删除</button>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="13" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 删除 -->
      <div class="modal modal-danger fade" id="user-del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delAntifake; ?>" method="post" enctype="multipart/form-data" id="form-user">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除吗？</p>
                <input id="user-del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="user-del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_provider = $('select[name=\'filter_provider\']').val();

      if (filter_provider != '*') {
        url += '&filter_provider=' + encodeURIComponent(filter_provider);
      }

      var filter_start = $('input[name=\'filter_start\']').val();

      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();

      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });

    $('#user-del-modal').on('show.bs.modal', function(event) {
      $('#user-del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#user-del-yes').on('click', () => {$('#form-user').submit()})
  })()
</script>

<?php echo $footer; ?>