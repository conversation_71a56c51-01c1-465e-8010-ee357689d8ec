<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        店铺产品
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="输入产品编码或名称查找" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select id="w1" class="form-control" name="filter_store" multiple>
                    <?php foreach($stores as $store) { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>筛选产品：</label>
                  <select class="form-control" name="filter_rule">
                    <option value="*">全部产品</option>
                    <?php foreach($rules as $rid => $rname) { ?>
                    <?php if ($rid == $filter_rule) { ?>
                    <option value="<?php echo $rid; ?>" selected="selected"><?php echo $rname; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $rid; ?>"><?php echo $rname; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">产品列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table id="stock" class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>图片</th>
              <th>产品名称<?php if ($sort == 'bsku') { ?>
                  <a href="<?php echo $sort_bsku; ?>" class="<?php echo strtolower($order); ?>">编码</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_bsku; ?>">编码</a>
                <?php } ?>
              </th>
              <th><?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">所属店铺</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">所属店铺</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'stock_quan') { ?>
                  <a href="<?php echo $sort_stock; ?>" class="<?php echo strtolower($order); ?>">剩余库存</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_stock; ?>">剩余库存</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'order_quan') { ?>
                  <a href="<?php echo $sort_order; ?>" class="<?php echo strtolower($order); ?>">总备货</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_order; ?>">总备货</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'sale_quan') { ?>
                  <a href="<?php echo $sort_sale; ?>" class="<?php echo strtolower($order); ?>">总销量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_sale; ?>">总销量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == '1dsales') { ?>
                  <a href="<?php echo $sort_1d; ?>" class="<?php echo strtolower($order); ?>">1天销</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_1d; ?>">1天销</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == '3dsales') { ?>
                  <a href="<?php echo $sort_3d; ?>" class="<?php echo strtolower($order); ?>">3天销</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_3d; ?>">3天销</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == '7dsales') { ?>
                  <a href="<?php echo $sort_7d; ?>" class="<?php echo strtolower($order); ?>">7天销</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_7d; ?>">7天销</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == '15dsales') { ?>
                  <a href="<?php echo $sort_15d; ?>" class="<?php echo strtolower($order); ?>">15天销</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_15d; ?>">15天销</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == '30dsales') { ?>
                  <a href="<?php echo $sort_30d; ?>" class="<?php echo strtolower($order); ?>">30天销</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_30d; ?>">30天销</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == '60dsales') { ?>
                  <a href="<?php echo $sort_60d; ?>" class="<?php echo strtolower($order); ?>">60天销</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_60d; ?>">60天销</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == '90dsales') { ?>
                  <a href="<?php echo $sort_90d; ?>" class="<?php echo strtolower($order); ?>">90天销</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_90d; ?>">90天销</a>
                <?php } ?>
              </th>
              <th width="150" class="text-left">备货量</th>
              <th class="text-left">其他店备货</th>
            </tr>
            <?php if (!empty($stocks)) { ?>
            <?php foreach ($stocks as $stock) { ?>
            <tr>
              <td><img width="100" src="<?php echo $stock['img_url']; ?>" class="img-thumbnail"></td>
              <td>
                <?php echo $stock['spec_name']; ?><br>
                <?php echo $stock['bsku']; ?><br>
                <?php echo $stock['bat_quan'] ? '装箱：'.$stock['bat_quan'] : ''; ?>
              </td>
              <td><?php echo $stock['storename']; ?></td>
              <td><?php echo $stock['stock_quan']; ?></td>
              <td><?php echo $stock['order_quan']; ?></td>
              <td><?php echo $stock['sale_quan']; ?></td>
              <td><?php echo $stock['1dsales']; ?></td>
              <td><?php echo $stock['3dsales']; ?></td>
              <td><?php echo $stock['7dsales']; ?></td>
              <td><?php echo $stock['15dsales']; ?></td>
              <td><?php echo $stock['30dsales']; ?></td>
              <td><?php echo $stock['60dsales']; ?></td>
              <td><?php echo $stock['90dsales']; ?></td>
              <td class="text-left">
                <?php if (!empty($stock['stop'])) { ?>
                  不翻单
                <?php } else { ?>
                  <?php if (!empty($store_ids) && in_array($stock['store_id'], $store_ids)) { ?>
                    <input type="number" name="plan[]" value="<?php echo $stock['plan_quan']; ?>" data-store="<?php echo $stock['store_id']; ?>" data-sku="<?php echo $stock['bsku']; ?>" data-org="<?php echo $stock['plan_quan']; ?>" placeholder="备货量" class="input-plan form-control" />
                  <?php } else { ?>
                    <?php echo $stock['plan_quan']; ?>
                  <?php } ?>
                <?php } ?>
              </td>
              <td><?php echo $stock['other_quan']; ?></td>
            </tr>
            <?php } ?>
            <tr>
              <th colspan="5" style="text-align: right;"> 销量累计： </th>
              <th><?php echo $sumsales['sale_quan']; ?></th>
              <th><?php echo $sumsales['1dsales']; ?></th>
              <th><?php echo $sumsales['3dsales']; ?></th>
              <th><?php echo $sumsales['7dsales']; ?></th>
              <th><?php echo $sumsales['15dsales']; ?></th>
              <th><?php echo $sumsales['30dsales']; ?></th>
              <th><?php echo $sumsales['60dsales']; ?></th>
              <th><?php echo $sumsales['90dsales']; ?></th>
              <th colspan="2"></th>
            </tr>
            <?php } else { ?>
            <tr><td colspan="15" align="center"> 暂无产品数据 </td></tr>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>

<script>
  var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
  window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"35px","placeholder":"请选择店铺","language":"zh-CN"};

  if (jQuery('#w1').data('select2')) { jQuery('#w1').select2('destroy'); }
  jQuery.when(jQuery('#w1').select2(select2_5eaa6d36)).done(initS2Loading('w1','s2options_c4acac00'));

  var filter_store = '<?php echo $filter_store_json; ?>'
  $("#w1").val($.parseJSON(filter_store)).trigger("change");
</script>

<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
/*.select2-selection {*/
/*  height: 34px;*/
/*}*/
/*.select2-container .select2-selection--multiple .select2-selection__rendered {*/
/*  padding-top: 0;*/
/*}*/
</style>
<script type="text/javascript">
(function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }
  
      var filter_rule = $('select[name=\'filter_rule\']').val();

      if (filter_rule != '*') {
        url += '&filter_rule=' + encodeURIComponent(filter_rule);
      }

      location.href = url;
    });
})()
$('.input-plan').on('blur', function () {
  var obj = $(this);
  if (obj.val() == obj.data('org')) return;
  if (!obj.val() || (obj.val() == '0')) {
    var action = '<?php echo $delPlan; ?>';
  } else {
    var action = '<?php echo $addPlan; ?>';
  }
  $.ajax({
    url: action + '&store_id=' + obj.data('store'),
    type: 'post',
    data: {bsku: obj.data('sku'), plan_quan: obj.val()},
    dataType: 'json',
    success: function(json) {
      $('.alert-danger, .alert-success').remove();

      if (json['error']) {
        $('.box-success').before('<div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 没有该店铺权限 </div>');

        obj.val(obj.data('org'));
      }

      if (json['success']) {
        $('.box-success').before('<div class="alert alert-success alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> ' + obj.data('sku') + '更改成功 </div>');
        
        obj.data('org', obj.val());
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
});
</script>
<?php echo $footer; ?>