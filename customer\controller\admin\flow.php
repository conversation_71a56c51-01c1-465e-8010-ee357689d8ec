<?php
class ControllerAdminFlow extends Controller {
	private $error = array();

	public function exportProduce() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_provider'])) {
			$filter_provider = $this->request->get['filter_provider'];
		} else {
			$filter_provider = '';
		}

		if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

		$data['node_fields'] = array(
			'complete_quan39' => '注浆数量',
			'complete_quan40' => '修抛数量',
			'complete_quan41' => '补坯数量',
			'complete_quan42' => '喷漆数量',
			'complete_quan43' => '彩绘数量',
			'complete_quan44' => '包装数量',
			'complete_quan45' => '出货数量',
		);

		$data['providers'] = array(
			'82' => '优源',
			'19' => '豪丽',
		);

		$data['states'] = array(
			'1' => '正常',
			'2' => '超时',
		);

		$data['flows'] = array();

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_provider'	=> $filter_provider,
			'filter_state'		=> $filter_state,
			'filter_date_start'	=> $filter_date_start,
			'filter_date_end'	=> $filter_date_end
		);

		$this->load->model('admin/flow');
		$results = $this->model_admin_flow->getProduces($filter_data);

		foreach ($results as $result) {
			$data['flows'][$result['workflow_id']] = array(
				'workid'	=> $result['workflow_id'],
				'workname'	=> $result['workflow_name'],
				'provider'	=> $data['providers'][$result['user_id']] ?? '',
				'workstate'	=> $data['states'][$result['state']] ?? '',
				'workdate'	=> date('Y-m-d', $result['start_time'])
			);
		}

		$fields = $this->model_admin_flow->getWorkFields(array_keys($data['flows']));
		$submit = $this->model_admin_flow->getWorkSubmit(array_keys($data['flows']));

		foreach ($fields as $workflow_id => $field) {
			$data['flows'][$workflow_id]['workproduct'] = $field['product_name'] ?? '';
			$data['flows'][$workflow_id]['workorder'] = $field['order_no'] ?? '';
			$data['flows'][$workflow_id]['workfinish'] = $field['finish_time45'] ?? '';
			$data['flows'][$workflow_id]['worktotal'] = $field['complete_total'] ?? '';
			$data['flows'][$workflow_id]['worktime41'] = $field['finish_time41'] ?? '';
			$data['flows'][$workflow_id]['worktime43'] = $field['finish_time43'] ?? '';
			$data['flows'][$workflow_id]['worktime44'] = $field['finish_time44'] ?? '';

			foreach ($data['node_fields'] as $code => $node_field) {
				if (isset($submit[$workflow_id][$code])) {
					$data['flows'][$workflow_id]['fields'][$code] = $submit[$workflow_id][$code];
				} else {
					$data['flows'][$workflow_id]['fields'][$code] = $field[$code] ?? '0';
				}
			}
		}

		$export_head = array('合同编号', '供应商', '产品名称', '下单数量', '白坯工期', '彩绘工期', '包装工期', '生产状态', '注浆完成数量', '修抛完成数量', '补坯完成数量', '喷漆完成数量', '彩绘完成数量', '包装完成数量', '出货完成数量');		
		$export_data = array();
        $export_data[] = $export_head;

		foreach ($data['flows'] as $flow) {
			$export_data[] = array(
				$flow['workorder'],
				$flow['provider'],
				$flow['workproduct'],
				$flow['worktotal'],
				$flow['worktime41'],
				$flow['worktime43'],
				$flow['worktime44'],
				$flow['workstate'],
				$flow['fields']['complete_quan39'],
				$flow['fields']['complete_quan40'],
				$flow['fields']['complete_quan41'],
				$flow['fields']['complete_quan42'],
				$flow['fields']['complete_quan43'],
				$flow['fields']['complete_quan44'],
				$flow['fields']['complete_quan45']
			);
		}

        if (!empty($export_data)) {
            $this->load->model('admin/excel');
            $this->model_admin_excel->export('生产流程表' . date('Y-m-d'), $export_data, array(), '.xlsx');
        }

        $this->getProduce();
	}

	public function getProduce() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_provider'])) {
			$filter_provider = $this->request->get['filter_provider'];
		} else {
			$filter_provider = '';
		}

		if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . $this->request->get['filter_provider'];
		}

		if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$data['export'] = $this->url->link('admin/flow/exportProduce', 'token=' . $this->session->data['token'] . $url);
		$data['submit'] = $this->url->link('admin/flow/addNodeSubmit', 'token=' . $this->session->data['token'] . $url);

		$data['node_fields'] = array(
			'complete_quan39' => '注浆数量',
			'complete_quan40' => '修抛数量',
			'complete_quan41' => '补坯数量',
			'complete_quan42' => '喷漆数量',
			'complete_quan43' => '彩绘数量',
			'complete_quan44' => '包装数量',
			'complete_quan45' => '出货数量',
		);

		$data['providers'] = array(
			'82' => '优源',
			'19' => '豪丽',
		);

		$data['states'] = array(
			'1' => '正常',
			'2' => '超时',
		);

		$data['flows'] = array();

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_provider'	=> $filter_provider,
			'filter_state'		=> $filter_state,
			'filter_date_start'	=> $filter_date_start,
			'filter_date_end'	=> $filter_date_end,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);

		$this->load->model('admin/flow');
		$results = $this->model_admin_flow->getProduces($filter_data);

		foreach ($results as $result) {
			$data['flows'][$result['workflow_id']] = array(
				'workid'	=> $result['workflow_id'],
				'workname'	=> $result['workflow_name'],
				'provider'	=> $data['providers'][$result['user_id']] ?? '',
				'workstate'	=> $data['states'][$result['state']] ?? '',
				'workdate'	=> date('Y-m-d', $result['start_time'])
			);
		}

		$fields = $this->model_admin_flow->getWorkFields(array_keys($data['flows']));
		$submit = $this->model_admin_flow->getWorkSubmit(array_keys($data['flows']));

		foreach ($fields as $workflow_id => $field) {
			$data['flows'][$workflow_id]['workproduct'] = $field['product_name'] ?? '';
			$data['flows'][$workflow_id]['workorder'] = $field['order_no'] ?? '';
			$data['flows'][$workflow_id]['workfinish'] = $field['finish_time45'] ?? '';
			$data['flows'][$workflow_id]['worktotal'] = $field['complete_total'] ?? '';
			$data['flows'][$workflow_id]['worktime41'] = $field['finish_time41'] ?? '';
			$data['flows'][$workflow_id]['worktime43'] = $field['finish_time43'] ?? '';
			$data['flows'][$workflow_id]['worktime44'] = $field['finish_time44'] ?? '';

			foreach ($data['node_fields'] as $code => $node_field) {
				if (isset($submit[$workflow_id][$code])) {
					$data['flows'][$workflow_id]['fields'][$code] = $submit[$workflow_id][$code];
				} else {
					$data['flows'][$workflow_id]['fields'][$code] = $field[$code] ?? '0';
				}
			}
		}

		$total = $this->model_admin_flow->getTotalProduces($filter_data);

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . $this->request->get['filter_provider'];
		}

		if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/flow/getProduce', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

		$data['filter_name'] = $filter_name;
		$data['filter_provider'] = $filter_provider;
		$data['filter_state'] = $filter_state;
		$data['filter_date_start'] = $filter_date_start;
		$data['filter_date_end'] = $filter_date_end;

		$data['nofilter'] = $this->url->link('admin/flow/getProduce', 'token=' . $this->session->data['token']);

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('flow/produce_list.tpl', $data));
	}

	public function addNodeSubmit() {
		$this->load->model('admin/flow');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && isset($this->request->post['sumbit'])) {
        	if ($this->user->union_id == '106') {
        		$actioin_user = '173';
        	} elseif ($this->user->union_id == '107') {
        		$actioin_user = '175';
        	} elseif ($this->user->union_id == '139') {
        		$actioin_user = '82';
        	} else {
        		$actioin_user = '1';
        	}

        	$node_fields = array(
				'complete_quan39' => [
					'node_id' => 39, 'action_id' => 51, 'users' => [40 => $actioin_user]
				],
				'complete_quan40' => [
					'node_id' => 40, 'action_id' => 52, 'users' => [41 => $actioin_user]
				],
				'complete_quan41' => [
					'node_id' => 41, 'action_id' => 53, 'users' => [0 => $actioin_user, 42 => $actioin_user]
				],
				'complete_quan42' => [
					'node_id' => 42, 'action_id' => 54, 'users' => [43 => $actioin_user]
				],
				'complete_quan43' => [
					'node_id' => 43, 'action_id' => 55, 'users' => [0 => $actioin_user, 44 => $actioin_user]
				],
				'complete_quan44' => [
					'node_id' => 44, 'action_id' => 56, 'users' => [0 => $actioin_user, 45 => $actioin_user]
				],
				'complete_quan45' => [
					'node_id' => 45, 'action_id' => 57, 'users' => [0 => $actioin_user, 3 => 48, 17 => 48]
				],
			);

			$work_fields = $this->model_admin_flow->getWorkFields(array_keys($this->request->post['sumbit']));

			foreach ($this->request->post['sumbit'] as $workflow_id => $fields) {
				if (empty($fields['complete_quan45'])) $fields['complete_quan45'] = '0';
				if (empty($fields['complete_quan44'])) $fields['complete_quan44'] = $fields['complete_quan45'];
				if (empty($fields['complete_quan43'])) $fields['complete_quan43'] = $fields['complete_quan44'];
				if (empty($fields['complete_quan42'])) $fields['complete_quan42'] = $fields['complete_quan43'];
				if (empty($fields['complete_quan41'])) $fields['complete_quan41'] = $fields['complete_quan42'];
				if (empty($fields['complete_quan40'])) $fields['complete_quan40'] = $fields['complete_quan41'];
				if (empty($fields['complete_quan39'])) $fields['complete_quan39'] = $fields['complete_quan40'];

				foreach ($node_fields as $code => $node) {
					if (!empty($fields[$code]) && (!isset($work_fields[$workflow_id][$code]) || ($work_fields[$workflow_id][$code] != $fields[$code]))) {
						$this->db->query("UPDATE cr_worknode_submit SET status = '-1' WHERE workflow_id = '" . (int)$workflow_id . "' AND node_id = '" . (int)$node['node_id'] . "' AND status = '0'");

						$this->db->query("INSERT INTO cr_worknode_submit SET workflow_id = '" . (int)$workflow_id . "', node_id = '" . (int)$node['node_id'] . "', action_id = '" . (int)$node['action_id'] . "', user_id = '" . (int)$actioin_user . "', fields = '" . $this->db->escape(json_encode([$code => $fields[$code]])) . "', users = '" . $this->db->escape(json_encode($node['users'])) . "', status = '0', date_added = NOW()");
					}
				}
			}

            $url = '';

            if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_provider'])) {
				$url .= '&filter_provider=' . $this->request->get['filter_provider'];
			}

			if (isset($this->request->get['filter_state'])) {
	            $url .= '&filter_state=' . $this->request->get['filter_state'];
	        }

	        if (isset($this->request->get['filter_date_start'])) {
	            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
	        }

	        if (isset($this->request->get['filter_date_end'])) {
	            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
	        }

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

            $this->response->redirect($this->url->link('admin/flow/getProduce', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getProduce();
	}
}
