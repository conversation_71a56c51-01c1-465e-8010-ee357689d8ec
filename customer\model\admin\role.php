<?php
class ModelAdminRole extends Model {
	public function addRole($data) {
        if (!empty($data['union_id'])) {
            $union_id = implode(',', $data['union_id']);
        } else {
            $union_id = '';
        }
		$this->db->query("INSERT INTO _union_role SET role_name = '" . $this->db->escape($data['role_name']) . "', status = '" . (int)$data['status'] . "',union_id = '" . $this->db->escape($union_id) . "', date_added = NOW()");
	}

	public function editRole($id, $data) {
        if (!empty($data['union_id'])) {
            $union_id = implode(',', $data['union_id']);
        } else {
            $union_id = '';
        }
        $this->db->query("UPDATE  _union_role SET role_name = '" . $this->db->escape($data['role_name']) . "',union_id = '" . $this->db->escape($union_id) . "',status = '" . (int)$data['status'] . "' WHERE role_id = '" . (int)$id . "'");
	}

	
	public function deleteRole($id) {

		$this->db->query("UPDATE _union_role SET status = '-1' WHERE role_id = '" . (int)$id . "'");
		// $this->db->query("DELETE FROM `" . DB_PREFIX . "union_info` WHERE user_id = '" . (int)$user_id . "'");
	}

	public function getRole($id) {
		$query = $this->db->query("SELECT * FROM _union_role  WHERE role_id = '" . (int)$id . "'");
		return $query->row;
	}
	
	public function getRoleByRoleName($rolename) {
		$query = $this->db->query("SELECT * FROM _union_role WHERE role_name = '" . $this->db->escape($rolename) . "'");

		return $query->row;
	}

	public function getRoles($data = array()) {
		$sql = "SELECT * FROM `_union_role` where status != '-1'";

		$sql .= " ORDER BY role_id DESC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalRoles() {
		$sql = "SELECT COUNT(*) AS total FROM `_union_role`";
		
		$query = $this->db->query($sql);

		return $query->row['total'];
	}
    public function getUser($data = array()){
        $sql = "SELECT * FROM _union  ";
        $query = $this->db->query($sql);
        return $query->rows;
    }
}