<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="select-calculate">选择供应商：</label>
              <div class="col-sm-8">
                <select name="provider_no" id="select-provider" class="form-control">
                  <?php foreach($providers as $provider){ ?>
                  <option value="<?php echo $provider['provider_no']; ?>" <?php if(!empty($provider_no) && ($provider_no == $provider['provider_no'])){ ?>selected="selected"<?php } ?>><?php echo $provider['provider_name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-prefix">前缀：</label>
              <div class="col-sm-8">
                <input type="text" name="prefix" value="<?php echo $prefix; ?>" placeholder="请输入前缀" id="input-prefix" class="form-control" />
                <div class="text-danger" style="display: none">请请输入前缀</div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-digit">位数：</label>
              <div class="col-sm-8">
                <input type="text" name="digit" value="<?php echo $digit; ?>" placeholder="请输入位数" id="input-digit" class="form-control" />
                <div class="text-danger" style="display: none">请请输入位数</div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-start_num">开始值：</label>
              <div class="col-sm-8">
                <input type="text" name="start_num" value="<?php echo $start_num; ?>" placeholder="请输入开始值" id="input-start_num" class="form-control" />
                <div class="text-danger" style="display: none">请请输入开始值</div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-end_num">结束值：</label>
              <div class="col-sm-8">
                <input type="text" name="end_num" value="<?php echo $end_num; ?>" placeholder="请输入结束值" id="input-end_num" class="form-control" />
                <div class="text-danger" style="display: none">请请输入结束值</div>
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script type="text/javascript">
  function toVaild() {
    var isValid = true;
    var inputName = $("#input-name").val()
    if (inputName == '') {
      isValid = false;
      $("#input-name").siblings('.text-danger').show();
    } else {
      $("#input-name").siblings('.text-danger').hide();
    }

    var selectedValues = $("#w1").select2('data');
    if (selectedValues.length > 0) {
      $("#w1").siblings('.text-danger').hide();
    } else {
      isValid = false;
      $("#w1").siblings('.text-danger').show();
    }

    var selectedValues2 = $("#w2").select2('data');
    if (selectedValues2.length > 0) {
      $("#w2").siblings('.text-danger').hide();
    } else {
      isValid = false;
      $("#w2").siblings('.text-danger').show();
    }

    if (isValid) {
      return true;
    } else {
      return false;
    }
  }
</script>
<?php echo $footer; ?>