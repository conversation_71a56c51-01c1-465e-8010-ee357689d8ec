<?php
class ControllerAdminCustomer extends Controller {
    private $error = array();

    public function add() {
        $this->load->language('customer');
        $this->load->model('admin/customer');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_customer->addCustomer($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_group'])) {
                $url .= '&filter_group=' . $this->request->get['filter_group'];
            }

            if (isset($this->request->get['filter_channel'])) {
                $url .= '&filter_channel=' . urlencode(html_entity_decode($this->request->get['filter_channel'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_product'])) {
                $url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_state'])) {
                $url .= '&filter_state=' . urlencode(html_entity_decode($this->request->get['filter_state'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_first_start'])) {
                $url .= '&filter_first_start=' . $this->request->get['filter_first_start'];
            }

            if (isset($this->request->get['filter_first_end'])) {
                $url .= '&filter_first_end=' . $this->request->get['filter_first_end'];
            }

            if (isset($this->request->get['filter_last_start'])) {
                $url .= '&filter_last_start=' . $this->request->get['filter_last_start'];
            }

            if (isset($this->request->get['filter_last_end'])) {
                $url .= '&filter_last_end=' . $this->request->get['filter_last_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function edit() {
        $this->load->language('customer');
        $this->load->model('admin/customer');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_customer->editCustomer($this->request->get['customer_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_group'])) {
                $url .= '&filter_group=' . $this->request->get['filter_group'];
            }

            if (isset($this->request->get['filter_channel'])) {
                $url .= '&filter_channel=' . urlencode(html_entity_decode($this->request->get['filter_channel'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_product'])) {
                $url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_state'])) {
                $url .= '&filter_state=' . urlencode(html_entity_decode($this->request->get['filter_state'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_first_start'])) {
                $url .= '&filter_first_start=' . $this->request->get['filter_first_start'];
            }

            if (isset($this->request->get['filter_first_end'])) {
                $url .= '&filter_first_end=' . $this->request->get['filter_first_end'];
            }

            if (isset($this->request->get['filter_last_start'])) {
                $url .= '&filter_last_start=' . $this->request->get['filter_last_start'];
            }

            if (isset($this->request->get['filter_last_end'])) {
                $url .= '&filter_last_end=' . $this->request->get['filter_last_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function editShop() {
        $this->load->model('admin/customer');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && isset($this->request->post['customer_id'])) {
            $this->model_admin_customer->editCustomerShop($this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_group'])) {
                $url .= '&filter_group=' . $this->request->get['filter_group'];
            }

            if (isset($this->request->get['filter_channel'])) {
                $url .= '&filter_channel=' . urlencode(html_entity_decode($this->request->get['filter_channel'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_product'])) {
                $url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_state'])) {
                $url .= '&filter_state=' . urlencode(html_entity_decode($this->request->get['filter_state'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_first_start'])) {
                $url .= '&filter_first_start=' . $this->request->get['filter_first_start'];
            }

            if (isset($this->request->get['filter_first_end'])) {
                $url .= '&filter_first_end=' . $this->request->get['filter_first_end'];
            }

            if (isset($this->request->get['filter_last_start'])) {
                $url .= '&filter_last_start=' . $this->request->get['filter_last_start'];
            }

            if (isset($this->request->get['filter_last_end'])) {
                $url .= '&filter_last_end=' . $this->request->get['filter_last_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getList();
    }

    public function editOrderGroup() {
        $this->load->model('admin/customer');

        if ($this->validateDelete()) {
            $this->model_admin_customer->editOrderGroup();

            $this->session->data['success'] = $this->language->get('text_edit_success');
        }

        $this->getList();
    }

    public function upload() {
        $data['action'] = $this->url->link('admin/customer/import', 'token=' . $this->session->data['token']);
        $data['template'] = $this->url->link('admin/customer/template', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('customer/upload_form.tpl', $data));
    }

    public function import() {
        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->load->language('customer');
            $this->load->model('admin/customer');
            $this->load->model('admin/excel');
            $this->load->model('admin/setting');

            $upload_info = $this->model_admin_excel->upload('customer');

            if (isset($upload_info['name'])) {
                $customer_datas = array();
                $upload_info['error'] = '';

                $import_total = 0;
                $import_total_success = 0;

                $excel_datas = $this->model_admin_excel->import($upload_info['name']);
                $titles = array_shift($excel_datas);

                if (implode(',', $titles) == $this->language->get('text_template_title')) {
                    $stores = $this->model_admin_setting->getStores();

                    foreach ($excel_datas as $excel_data) {
                        if (isset($excel_data['A']) && trim($excel_data['A'])) {
                            $import_total++;
                            $store_id = '';

                            foreach ($stores as $store) {
                                if ($store['name'] == trim($excel_data['A'])) {
                                    $store_id = $store['store_id'];
                                    break;
                                }
                            }

                            if (!empty($store_id)) {
                                $repeat = $this->model_admin_customer->getRepeatCustomer($store_id, trim($excel_data['B']));;

                                if (!empty($repeat)) {
                                    if (in_array($store_id, array('1', '2', '3', '4', '5', '6', '7'))) {
                                        $upload_info['error'] .= $excel_data['B'] . $this->language->get('error_customer_exist') . '<br>';
                                    } else {
                                        if (strtotime($excel_data['J'])) {
                                            $order_date = date('Y-m-d', strtotime($excel_data['J']));
                                        } else {
                                            $order_date = date('Y-m-d');
                                        }

                                        $this->model_admin_customer->addOrder(array('customer_id' => $repeat['customer_id'], 'order_times' => 1, 'order_total' => (float)$excel_data['H'], 'order_date' => $order_date, 'order_product' => ''));
                                    }
                                } else {
                                    $customer_datas[] = array(
                                        'customer_group_id' => 0,
                                        'store_id'  => $store_id,
                                        'nickname'  => $excel_data['B'],
                                        'realname'  => $excel_data['C'],
                                        'telephone' => $excel_data['D'],
                                        'address'   => $excel_data['E'],
                                        'channel'   => $excel_data['F'],
                                        'product'   => $excel_data['G'],
                                        'order_total' => (float)$excel_data['H'],
                                        'state'     => $excel_data['I'],
                                        'order_date'=> str_replace('/', '-', $excel_data['J']),
                                        'remark'    => $excel_data['K']
                                    );
                                }
                            } else {
                                $upload_info['error'] .= $excel_data['A'] . $this->language->get('error_store_notfound') . '<br>';
                            }
                        }
                    }
                } else {
                    $upload_info['error'] = $this->language->get('error_template');
                }

                @unlink($upload_info['name']);
            }

            if (isset($upload_info['error']) && $upload_info['error']) {
                $json['error'] = $upload_info['error'] . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
            } else {
                if (!empty($customer_datas)) {
                    foreach ($customer_datas as $customer_data) {
                        $this->model_admin_customer->addCustomer($customer_data);
                        $import_total_success++;
                    }

                    $json['success'] = $this->language->get('text_upload_success') . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
                } else {
                    $json['error'] = $this->language->get('error_upload');
                }
            }
        }

        $this->response->setOutJson($json);
    }

    public function template() {
        $file = DIR_DOWNLOAD . '客户导入模板.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }

    public function getReport() {
        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_report'])) {
            $filter_report = $this->request->get['filter_report'];
        } else {
            $filter_report = 'group';
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $this->load->model('admin/customer');
        $data['reports'] = $this->model_admin_customer->getReportType();

        $filter_data = array(
            'filter_store'  => $filter_store,
            'filter_report' => $filter_report
        );

        $data['summary'] = $this->model_admin_customer->getSummary($filter_data);

        $data['filter_report'] = $filter_report;
        $data['filter_store'] = $filter_store;

        $data['nofilter'] = $this->url->link('admin/customer/getReport', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('customer/customer_report.tpl', $data));
    }

    public function getList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_group'])) {
            $filter_group = $this->request->get['filter_group'];
        } else {
            $filter_group = '';
        }

        if (isset($this->request->get['filter_channel'])) {
            $filter_channel = $this->request->get['filter_channel'];
        } else {
            $filter_channel = '';
        }

        if (isset($this->request->get['filter_product'])) {
            $filter_product = $this->request->get['filter_product'];
        } else {
            $filter_product = '';
        }

        if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '';
        }

        if (isset($this->request->get['filter_first_start'])) {
            $filter_first_start = $this->request->get['filter_first_start'];
        } else {
            $filter_first_start = '';
        }

        if (isset($this->request->get['filter_first_end'])) {
            $filter_first_end = $this->request->get['filter_first_end'];
        } else {
            $filter_first_end = '';
        }

        if (isset($this->request->get['filter_last_start'])) {
            $filter_last_start = $this->request->get['filter_last_start'];
        } else {
            $filter_last_start = '';
        }

        if (isset($this->request->get['filter_last_end'])) {
            $filter_last_end = $this->request->get['filter_last_end'];
        } else {
            $filter_last_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'customer_id';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_group'])) {
            $url .= '&filter_group=' . $this->request->get['filter_group'];
        }

        if (isset($this->request->get['filter_channel'])) {
            $url .= '&filter_channel=' . urlencode(html_entity_decode($this->request->get['filter_channel'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . urlencode(html_entity_decode($this->request->get['filter_state'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_first_start'])) {
            $url .= '&filter_first_start=' . $this->request->get['filter_first_start'];
        }

        if (isset($this->request->get['filter_first_end'])) {
            $url .= '&filter_first_end=' . $this->request->get['filter_first_end'];
        }

        if (isset($this->request->get['filter_last_start'])) {
            $url .= '&filter_last_start=' . $this->request->get['filter_last_start'];
        }

        if (isset($this->request->get['filter_last_end'])) {
            $url .= '&filter_last_end=' . $this->request->get['filter_last_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/customer/add', 'token=' . $this->session->data['token'] . $url);
        $data['editShop'] = $this->url->link('admin/customer/editShop', 'token=' . $this->session->data['token'] . $url);
        $data['autoGroup'] = $this->url->link('admin/customer/editOrderGroup', 'token=' . $this->session->data['token'] . $url);
        $data['getGroup'] = $this->url->link('admin/customer/getGroupByStore', 'token=' . $this->session->data['token']);

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $this->load->model('admin/customer');
        $data['groups'] = $this->model_admin_customer->getGroups();
        $data['channels'] = $this->model_admin_customer->getChannels();
        // $data['products'] = $this->model_admin_customer->getProducts();
        $data['states'] = $this->model_admin_customer->getStates();

        $stores = $groups = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        foreach ($data['groups'] as $group) {
            $groups[$group['customer_group_id']] = $group['group_name'];
        }

        $data['customers'] = array();

        $filter_data = array(
            'filter_name'       => $filter_name,
            'filter_group'      => $filter_group,
            'filter_store'      => $filter_store,
            'filter_channel'    => $filter_channel,
            'filter_product'    => $filter_product,
            'filter_state'      => $filter_state,
            'filter_first_start'=> $filter_first_start,
            'filter_first_end'  => $filter_first_end,
            'filter_last_start' => $filter_last_start,
            'filter_last_end'   => $filter_last_end,
            'sort'              => $sort,
            'order'             => $order,
            'start'             => ($page - 1) * $this->config->get('config_limit'),
            'limit'             => $this->config->get('config_limit'),
        );
        
        $results = $this->model_admin_customer->getCustomers($filter_data);

        foreach ($results as $result) {
            $data['customers'][] = array(
                'customer_id'   => $result['customer_id'],
                'nickname'      => $result['nickname'],
                'shop_name'     => $result['shop_name'],
                'groupname'     => isset($groups[$result['customer_group_id']]) ? $groups[$result['customer_group_id']] : '',
                'storename'     => isset($stores[$result['store_id']]) ? $stores[$result['store_id']] : '',
                'realname'      => $result['realname'],
                'telephone'     => $result['telephone'],
                'channel'       => $result['channel'],
                'product'       => $result['product'],
                'order_total'   => $result['order_total'],
                'state'         => $result['state'],
                'date_first'    => date($this->language->get('date_format'), strtotime($result['date_first'])),
                'date_last'     => date($this->language->get('date_format'), strtotime($result['date_last'])),
                'total_last'    => $result['order_last'],
                'edit'          => $this->url->link('admin/customer/edit', 'token=' . $this->session->data['token'] . '&customer_id=' . $result['customer_id'] . $url)
            );
        }

        $total = $this->model_admin_customer->getTotalCustomers($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_group'])) {
            $url .= '&filter_group=' . $this->request->get['filter_group'];
        }

        if (isset($this->request->get['filter_channel'])) {
            $url .= '&filter_channel=' . urlencode(html_entity_decode($this->request->get['filter_channel'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . urlencode(html_entity_decode($this->request->get['filter_state'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_first_start'])) {
            $url .= '&filter_first_start=' . $this->request->get['filter_first_start'];
        }

        if (isset($this->request->get['filter_first_end'])) {
            $url .= '&filter_first_end=' . $this->request->get['filter_first_end'];
        }

        if (isset($this->request->get['filter_last_start'])) {
            $url .= '&filter_last_start=' . $this->request->get['filter_last_start'];
        }

        if (isset($this->request->get['filter_last_end'])) {
            $url .= '&filter_last_end=' . $this->request->get['filter_last_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_name'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=nickname' . $url);
        $data['sort_group'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=customer_group_id' . $url);
        $data['sort_store'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
        $data['sort_realname'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=realname' . $url);
        $data['sort_telephone'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=telephone' . $url);
        $data['sort_channel'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=channel' . $url);        
        $data['sort_product'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=product' . $url);
        $data['sort_state'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=state' . $url);
        $data['sort_total'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=order_total' . $url);
        $data['sort_first'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=date_first' . $url);
        $data['sort_last'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . '&sort=date_last' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_group'])) {
            $url .= '&filter_group=' . $this->request->get['filter_group'];
        }

        if (isset($this->request->get['filter_channel'])) {
            $url .= '&filter_channel=' . urlencode(html_entity_decode($this->request->get['filter_channel'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . urlencode(html_entity_decode($this->request->get['filter_state'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_first_start'])) {
            $url .= '&filter_first_start=' . $this->request->get['filter_first_start'];
        }

        if (isset($this->request->get['filter_first_end'])) {
            $url .= '&filter_first_end=' . $this->request->get['filter_first_end'];
        }

        if (isset($this->request->get['filter_last_start'])) {
            $url .= '&filter_last_start=' . $this->request->get['filter_last_start'];
        }

        if (isset($this->request->get['filter_last_end'])) {
            $url .= '&filter_last_end=' . $this->request->get['filter_last_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_group'] = $filter_group;
        $data['filter_store'] = $filter_store;
        $data['filter_channel'] = $filter_channel;
        $data['filter_product'] = $filter_product;
        $data['filter_state'] = $filter_state;
        $data['filter_first_start'] = $filter_first_start;
        $data['filter_first_end'] = $filter_first_end;
        $data['filter_last_start'] = $filter_last_start;
        $data['filter_last_end'] = $filter_last_end;

        $data['nofilter'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('customer/customer_list.tpl', $data));
    }

    protected function getForm() {
        $data['text_form'] = !isset($this->request->get['customer_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_group'])) {
            $url .= '&filter_group=' . $this->request->get['filter_group'];
        }

        if (isset($this->request->get['filter_channel'])) {
            $url .= '&filter_channel=' . urlencode(html_entity_decode($this->request->get['filter_channel'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . urlencode(html_entity_decode($this->request->get['filter_state'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_first_start'])) {
            $url .= '&filter_first_start=' . $this->request->get['filter_first_start'];
        }

        if (isset($this->request->get['filter_first_end'])) {
            $url .= '&filter_first_end=' . $this->request->get['filter_first_end'];
        }

        if (isset($this->request->get['filter_last_start'])) {
            $url .= '&filter_last_start=' . $this->request->get['filter_last_start'];
        }

        if (isset($this->request->get['filter_last_end'])) {
            $url .= '&filter_last_end=' . $this->request->get['filter_last_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['customer_id'])) {
            $data['action'] = $this->url->link('admin/customer/add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/customer/edit', 'token=' . $this->session->data['token'] . '&customer_id=' . $this->request->get['customer_id'] . $url);

            $data['summary'] = $this->model_admin_customer->getOrderSummary($this->request->get['customer_id']);

            $data['orders'] = $this->url->link('admin/customer/orders', 'token=' . $this->session->data['token'] . '&customer_id=' . $this->request->get['customer_id']);
            $data['addorder'] = $this->url->link('admin/customer/addOrder', 'token=' . $this->session->data['token'] . '&customer_id=' . $this->request->get['customer_id']);
        }

        $data['cancel'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token'] . $url);
        $data['getGroup'] = $this->url->link('admin/customer/getGroupByStore', 'token=' . $this->session->data['token'] . '&noall=1');

        if (isset($this->request->get['customer_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $customer_info = $this->model_admin_customer->getCustomer($this->request->get['customer_id']);
        }

        $data['channels'] = $this->model_admin_customer->getChannels();
        $data['states'] = $this->model_admin_customer->getStates();

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        if (isset($this->request->post['nickname'])) {
            $data['nickname'] = $this->request->post['nickname'];
        } elseif (!empty($customer_info)) {
            $data['nickname'] = $customer_info['nickname'];
        } else {
            $data['nickname'] = '';
        }

        if (isset($this->request->post['customer_group_id'])) {
            $data['customer_group_id'] = $this->request->post['customer_group_id'];
        } elseif (!empty($customer_info)) {
            $data['customer_group_id'] = $customer_info['customer_group_id'];
        } else {
            $data['customer_group_id'] = '';
        }

        if (isset($this->request->post['store_id'])) {
            $data['store_id'] = $this->request->post['store_id'];
        } elseif (!empty($customer_info)) {
            $data['store_id'] = $customer_info['store_id'];
        } else {
            $data['store_id'] = '';
        }

        if (isset($this->request->post['realname'])) {
            $data['realname'] = $this->request->post['realname'];
        } elseif (!empty($customer_info)) {
            $data['realname'] = $customer_info['realname'];
        } else {
            $data['realname'] = '';
        }

        if (isset($this->request->post['telephone'])) {
            $data['telephone'] = $this->request->post['telephone'];
        } elseif (!empty($customer_info)) {
            $data['telephone'] = $customer_info['telephone'];
        } else {
            $data['telephone'] = '';
        }

        if (isset($this->request->post['address'])) {
            $data['address'] = $this->request->post['address'];
        } elseif (!empty($customer_info)) {
            $data['address'] = $customer_info['address'];
        } else {
            $data['address'] = '';
        }

        if (isset($this->request->post['channel'])) {
            $data['channel'] = $this->request->post['channel'];
        } elseif (!empty($customer_info)) {
            $data['channel'] = $customer_info['channel'];
        } else {
            $data['channel'] = '';
        }

        if (isset($this->request->post['product'])) {
            $data['product'] = $this->request->post['product'];
        } elseif (!empty($customer_info)) {
            $data['product'] = $customer_info['product'];
        } else {
            $data['product'] = '';
        }

        if (isset($this->request->post['state'])) {
            $data['state'] = $this->request->post['state'];
        } elseif (!empty($customer_info)) {
            $data['state'] = $customer_info['state'];
        } else {
            $data['state'] = '';
        }

        if (isset($this->request->post['remark'])) {
            $data['remark'] = $this->request->post['remark'];
        } elseif (!empty($customer_info)) {
            $data['remark'] = $customer_info['remark'];
        } else {
            $data['remark'] = '';
        }

        if (!empty($customer_info)) {
            $data['order_times'] = $customer_info['order_times'];
        } else {
            $data['order_times'] = '';
        }

        if (!empty($customer_info)) {
            $data['order_total'] = $customer_info['order_total'];
        } else {
            $data['order_total'] = '';
        }

        if (!empty($customer_info)) {
            $data['date_first'] = date($this->language->get('date_format'), strtotime($customer_info['date_first']));
        } else {
            $data['date_first'] = '';
        }

        if (!empty($customer_info)) {
            $data['date_last'] = date($this->language->get('date_format'), strtotime($customer_info['date_last']));
        } else {
            $data['date_last'] = '';
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('customer/customer_form.tpl', $data));
    }

    public function orders() {
        if (!isset($this->request->get['customer_id'])) {
            $this->request->get['customer_id'] = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $data['orders'] = array();
        $this->config->set('config_limit', 10);

        $filter_data = array(
            'start'   => ($page - 1) * $this->config->get('config_limit'),
            'limit'   => $this->config->get('config_limit')
        );

        $this->load->model('admin/customer');        
        $results = $this->model_admin_customer->getOrders($this->request->get['customer_id'], $filter_data);

        foreach ($results as $result) {
            $data['orders'][] = array(
                'order_times'   => $result['order_times'],
                'order_total'   => $result['order_total'],
                'order_date'    => date($this->language->get('date_format'), strtotime($result['order_date'])),
                'order_product' => $result['order_product'],
                'date_added'    => $result['date_added']
            );
        }

        $total = $this->model_admin_customer->getTotalOrders($this->request->get['customer_id']);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/customer/orders', 'token=' . $this->session->data['token'] . '&customer_id=' . $this->request->get['customer_id'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $this->response->setOutput($this->load->view('customer/customer_order.tpl', $data));
    }

    public function addOrder() {
        $json = array();

        if (empty($this->request->post['order_total']) || empty($this->request->post['order_date'])) {
            $json['error'] = $this->language->get('error_input_empty');
        }

        if (!$this->user->hasPermission('modify', 'admin/customer')) {
            $json['error'] = $this->language->get('error_permission');
        }

        if (!isset($json['error'])) {
            $user_tran = array('customer_id' => $this->request->get['customer_id'], 'order_times' => $this->request->post['order_times'], 'order_total' => str_replace(',', '',$this->request->post['order_total']), 'order_date' => $this->request->post['order_date'], 'order_product' => $this->request->post['order_product']);

            $this->load->model('admin/customer');
            $this->model_admin_customer->addOrder($user_tran);

            $json['success'] = $this->language->get('text_add_success');
        }

        $this->response->setOutJson($json);
    }

    public function addGroup() {
        $this->load->model('admin/customer');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateGroupForm()) {
            $this->model_admin_customer->addGroup($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/customer/getGroups', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getGroupForm();
    }

    public function editGroup() {
        $this->load->model('admin/customer');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateGroupForm()) {
            $this->model_admin_customer->editGroup($this->request->get['customer_group_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/customer/getGroups', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getGroupForm();
    }

    public function deleteGroup() {
        $this->load->model('admin/customer');

        if (isset($this->request->post['selected']) && $this->validateGroupDelete()) {
            foreach ($this->request->post['selected'] as $customer_group_id) {
                $this->model_admin_customer->deleteGroup($customer_group_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/customer/getGroups', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getGroups();
    }

    public function getGroups() {
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/customer/addGroup', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/customer/deleteGroup', 'token=' . $this->session->data['token'] . $url);

        $data['groups'] = array();

        $filter_data = array(
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );

        $this->load->model('admin/customer');
        $results = $this->model_admin_customer->getGroups($filter_data);

        foreach ($results as $result) {
            $data['groups'][] = array(
                'group_id'    => $result['customer_group_id'],
                'group_name'  => $result['group_name'],
                'storename'   => $result['storename'],
                'order_total' => $result['order_total'],
                'date_added'  => $result['date_added'],
                'edit'        => $this->url->link('admin/customer/editGroup', 'token=' . $this->session->data['token'] . '&customer_group_id=' . $result['customer_group_id'] . $url)
            );
        }

        $total = $this->model_admin_customer->getTotalGroups();

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/customer/getGroups', 'token=' . $this->session->data['token'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('customer/group_list.tpl', $data));
    }

    protected function getGroupForm() {
        $data['text_form'] = !isset($this->request->get['customer_group_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        
        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['customer_group_id'])) {
            $data['select_store'] = true;

            $data['action'] = $this->url->link('admin/customer/addGroup', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['select_store'] = false;

            $data['action'] = $this->url->link('admin/customer/editGroup', 'token=' . $this->session->data['token'] . '&customer_group_id=' . $this->request->get['customer_group_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/customer/getGroups', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['customer_group_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $group_info = $this->model_admin_customer->getGroup($this->request->get['customer_group_id']);
        }

        if (isset($this->request->post['group_name'])) {
            $data['group_name'] = $this->request->post['group_name'];
        } elseif (!empty($group_info)) {
            $data['group_name'] = $group_info['group_name'];
        } else {
            $data['group_name'] = '';
        }

        if (isset($this->request->post['order_total'])) {
            $data['order_total'] = $this->request->post['order_total'];
        } elseif (!empty($group_info)) {
            $data['order_total'] = $group_info['order_total'];
        } else {
            $data['order_total'] = 0;
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        if (isset($this->request->post['store_id'])) {
            $data['store_id'] = $this->request->post['store_id'];
        } elseif (!empty($group_info)) {
            $data['store_id'] = $group_info['store_id'];
        } else {
            $data['store_id'] = 0;
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('customer/group_form.tpl', $data));
    }

    public function offSalesAdd() {
        $this->load->model('admin/customer');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateSalesForm()) {
            $sales_id = $this->model_admin_customer->addSales($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_state'])) {
                $url .= '&filter_state=' . $this->request->get['filter_state'];
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getSalesForm();
    }

    public function offSalesEdit() {
        $this->load->model('admin/customer');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateSalesForm()) {
            $this->model_admin_customer->editSales($this->request->get['sales_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_state'])) {
                $url .= '&filter_state=' . $this->request->get['filter_state'];
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getSalesForm();
    }

    public function offSalesList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'customer_id';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/customer/offSalesAdd', 'token=' . $this->session->data['token'] . $url);

        $this->load->model('admin/customer');
        $data['states'] = $this->model_admin_customer->getSalesStates();
        
        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $data['sales'] = array();

        $filter_data = array(
            'filter_name'       => $filter_name,
            'filter_store'      => $filter_store,
            'filter_state'      => $filter_state,
            'filter_date_start' => $filter_date_start,
            'filter_date_end'   => $filter_date_end,
            'sort'              => $sort,
            'order'             => $order,
            'start'             => ($page - 1) * $this->config->get('config_limit'),
            'limit'             => $this->config->get('config_limit'),
        );
        
        $results = $this->model_admin_customer->getSalesList($filter_data);

        foreach ($results as $result) {
            $data['sales'][] = array(
                'sales_id'      => $result['sales_id'],
                'order_no'      => $result['order_no'],
                'storename'     => isset($stores[$result['store_id']]) ? $stores[$result['store_id']] : '',
                'customer'      => $result['customer'],
                'contact'       => $result['contact'],
                'sales_manage'  => $result['sales_manage'],
                'sales_paid'    => $result['sales_paid'],
                'sales_total'   => $result['sales_total'],
                'sales_date'    => $result['sales_date'],
                'paid_date'     => $result['paid_date'],
                'delivery_date' => $result['delivery_date'],
                'state'         => isset($data['states'][$result['state']]) ? $data['states'][$result['state']] : '',
                'edit'          => $this->url->link('admin/customer/offSalesEdit', 'token=' . $this->session->data['token'] . '&sales_id=' . $result['sales_id'] . $url)
            );
        }

        $total = $this->model_admin_customer->getTotalSales($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_order'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=order_no' . $url);
        $data['sort_store'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
        $data['sort_customer'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=customer' . $url);
        $data['sort_contact'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=contact' . $url);
        $data['sort_manage'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=sales_manage' . $url);        
        $data['sort_paid'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=sales_paid' . $url);
        $data['sort_state'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=state' . $url);
        $data['sort_total'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=sales_total' . $url);
        $data['sort_salesdate'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=sales_date' . $url);
        $data['sort_paiddate'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=paid_date' . $url);
        $data['sort_delivery'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . '&sort=delivery_date' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_state'] = $filter_state;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('customer/sales_list.tpl', $data));
    }

    protected function getSalesForm() {
        $data['text_form'] = !isset($this->request->get['sales_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['sales_id'])) {
            $data['action'] = $this->url->link('admin/customer/offSalesAdd', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/customer/offSalesEdit', 'token=' . $this->session->data['token'] . '&sales_id=' . $this->request->get['sales_id'] . $url);

            $data['bill'] = $this->url->link('admin/customer/getSalesBills', 'token=' . $this->session->data['token'] . '&sales_id=' . $this->request->get['sales_id']);
            $data['addbill'] = $this->url->link('admin/customer/addSalesBill', 'token=' . $this->session->data['token'] . '&sales_id=' . $this->request->get['sales_id']);
        }

        $data['cancel'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['sales_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $sales_info = $this->model_admin_customer->getSales($this->request->get['sales_id']);
        }

        $data['states'] = $this->model_admin_customer->getSalesStates();

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        if (isset($this->request->post['order_no'])) {
            $data['order_no'] = $this->request->post['order_no'];
        } elseif (!empty($sales_info)) {
            $data['order_no'] = $sales_info['order_no'];
        } else {
            $data['order_no'] = '';
        }

        if (isset($this->request->post['sales_total'])) {
            $data['sales_total'] = $this->request->post['sales_total'];
        } elseif (!empty($sales_info)) {
            $data['sales_total'] = $sales_info['sales_total'];
        } else {
            $data['sales_total'] = '';
        }

        if (isset($this->request->post['store_id'])) {
            $data['store_id'] = $this->request->post['store_id'];
        } elseif (!empty($sales_info)) {
            $data['store_id'] = $sales_info['store_id'];
        } else {
            $data['store_id'] = '';
        }

        if (isset($this->request->post['customer'])) {
            $data['customer'] = $this->request->post['customer'];
        } elseif (!empty($sales_info)) {
            $data['customer'] = $sales_info['customer'];
        } else {
            $data['customer'] = '';
        }

        if (isset($this->request->post['contact'])) {
            $data['contact'] = $this->request->post['contact'];
        } elseif (!empty($sales_info)) {
            $data['contact'] = $sales_info['contact'];
        } else {
            $data['contact'] = '';
        }

        if (isset($this->request->post['sales_manage'])) {
            $data['sales_manage'] = $this->request->post['sales_manage'];
        } elseif (!empty($sales_info)) {
            $data['sales_manage'] = $sales_info['sales_manage'];
        } else {
            $data['sales_manage'] = $this->user->real_name;
        }

        if (isset($this->request->post['sales_date'])) {
            $data['sales_date'] = $this->request->post['sales_date'];
        } elseif (!empty($sales_info)) {
            $data['sales_date'] = $sales_info['sales_date'];
        } else {
            $data['sales_date'] = '';
        }

        if (isset($this->request->post['delivery_date'])) {
            $data['delivery_date'] = $this->request->post['delivery_date'];
        } elseif (!empty($sales_info)) {
            $data['delivery_date'] = $sales_info['delivery_date'];
        } else {
            $data['delivery_date'] = '';
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('customer/sales_form.tpl', $data));
    }

    public function getSalesBills() {
        if (!isset($this->request->get['sales_id'])) {
            $this->request->get['sales_id'] = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $data['bills'] = array();
        $this->config->set('config_limit', 10);

        $filter_data = array(
            'start'   => ($page - 1) * $this->config->get('config_limit'),
            'limit'   => $this->config->get('config_limit')
        );

        $this->load->model('admin/customer');        
        $results = $this->model_admin_customer->getSalesBills($this->request->get['sales_id'], $filter_data);

        foreach ($results as $result) {
            $data['bills'][] = array(
                'bill_paid'     => $result['bill_paid'],
                'bill_date'     => date($this->language->get('date_format'), strtotime($result['bill_date'])),
                'bill_account'  => $result['bill_account'],
                'date_added'    => $result['date_added']
            );
        }

        $total = $this->model_admin_customer->getTotalSalesBills($this->request->get['sales_id']);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/customer/getSalesBills', 'token=' . $this->session->data['token'] . '&sales_id=' . $this->request->get['sales_id'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $this->response->setOutput($this->load->view('customer/sales_bill.tpl', $data));
    }

    public function addSalesBill() {
        $json = array();

        if (empty($this->request->post['bill_paid']) || empty($this->request->post['bill_date']) || empty($this->request->post['bill_account'])) {
            $json['error'] = $this->language->get('error_input_empty');
        }

        if (!isset($json['error'])) {
            $user_tran = array('sales_id' => $this->request->get['sales_id'], 'bill_paid' => str_replace(',', '',$this->request->post['bill_paid']), 'bill_date' => $this->request->post['bill_date'], 'bill_account' => $this->request->post['bill_account']);

            $this->load->model('admin/customer');
            $this->model_admin_customer->addSalesBill($user_tran);

            $json['success'] = $this->language->get('text_add_success');
        }

        $this->response->setOutJson($json);
    }

    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'admin/customer')) {
            $this->error['warning'] = $this->language->get('error_permission');
            return false;
        }

        if (empty($this->request->post['nickname'])) {
            $this->error['warning'] = $this->language->get('error_empty_nickname');
            return false;
        }

        if (empty($this->request->post['store_id'])) {
            $this->error['warning'] = $this->language->get('error_empty_store');
            return false;
        }

        if (empty($this->request->post['customer_group_id'])) {
            $this->error['warning'] = $this->language->get('error_empty_group');
            return false;
        }

        if (empty($this->request->post['realname'])) {
            $this->error['warning'] = $this->language->get('error_empty_realname');
            return false;
        }

        if (empty($this->request->post['telephone'])) {
            $this->error['warning'] = $this->language->get('error_empty_telephone');
            return false;
        }

        if (empty($this->request->post['channel'])) {
            $this->error['warning'] = $this->language->get('error_empty_channel');
            return false;
        }

        if (empty($this->request->post['state'])) {
            $this->error['warning'] = $this->language->get('error_empty_state');
            return false;
        }

        $repeat_info = $this->model_admin_customer->getRepeatCustomer($this->request->post['store_id'], $this->request->post['nickname']);

        if (!isset($this->request->get['customer_id'])) {
            if ($repeat_info) {
                $this->error['warning'] = $this->language->get('error_customer_exist');
            }
        } else {
            if ($repeat_info && ($this->request->get['customer_id'] != $repeat_info['customer_id'])) {
                $this->error['warning'] = $this->language->get('error_customer_exist');
            }
        }

        return !$this->error;
    }

    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'admin/customer')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }

    protected function validateGroupForm() {
        if (!$this->user->hasPermission('modify', 'admin/customer/group')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['group_name']) < 1) || (utf8_strlen($this->request->post['group_name']) > 32)) {
            $this->error['warning'] = $this->language->get('error_name_length');
        }
        
        return !$this->error;
    }

    protected function validateGroupDelete() {
        if (!$this->user->hasPermission('modify', 'admin/customer/group')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }

    protected function validateSalesForm() {
        if (!$this->user->hasPermission('modify', 'admin/customer')) {
            $this->error['warning'] = $this->language->get('error_permission');
            return false;
        }

        if (empty($this->request->post['order_no']) || empty($this->request->post['store_id']) || empty($this->request->post['sales_total']) || empty($this->request->post['customer']) || empty($this->request->post['sales_manage']) || empty($this->request->post['sales_date']) || empty($this->request->post['delivery_date'])) {
            $this->error['warning'] = $this->language->get('error_input_empty');
            return false;
        }

        return !$this->error;
    }
    
    public function getGroupByStore() {
        if (isset($this->request->get['noall'])) {
            $output = '<option value="">请选择客户级别</option>';
        } else {
            $output = '<option value="*">全部级别</option>';
        }

        $this->load->model('admin/customer');
        $results = $this->model_admin_customer->getGroupByStore($this->request->get['store_id']);

        foreach ($results as $result) {
            $output .= '<option value="' . $result['customer_group_id'] . '"';

            if ($this->request->get['group_id'] == $result['customer_group_id']) {
                $output .= ' selected="selected"';
            }

            $output .= '>' . $result['group_name'] . '</option>';
        }

        $this->response->setOutput($output);
    }
}