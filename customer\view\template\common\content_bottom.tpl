  <!-- Main Footer -->
  <footer class="main-footer">
    <!-- To the right -->
    <div class="pull-right hidden-xs"></div>
    <!-- Default to the left -->
    
  </footer>
</div>
<!-- ./wrapper -->

<!-- REQUIRED JS SCRIPTS -->

<!-- jQuery 3 -->
<script src="static/bower_components/jquery/dist/jquery.min.js"></script>
<!-- Bootstrap 3.3.7 -->
<script src="static/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
<!-- PACE -->
<script src="static/bower_components/pace/pace.min.js"></script>
<!-- date-range-picker -->
<script src="static/bower_components/moment/min/moment.min.js"></script>
<script src="static/bower_components/moment/locale/zh-cn.js"></script>
<script src="static/bower_components/bootstrap-daterangepicker/daterangepicker.js"></script>
<!-- iCheck 1.0.1 -->
<script src="static/plugins/iCheck/icheck.min.js"></script>
<!-- AdminLTE App -->
<script src="static/dist/js/adminlte.min.js"></script>
<script src="static/dist/js/common.js"></script>
<script type="text/javascript">
	var current_url = location.href;

	$('.sidebar-menu li').each(function(){
		if (current_url.indexOf($(this).find('a').data('scope')) >= 0) {
			$('.sidebar-menu li').removeClass('active');
      $(this).addClass('active');
      $(this).parents('.treeview').addClass('active');
      $(this).parents('.treeview-alipay').find('.alipay').css('border-left','3px solid rgb(0, 166, 90)');
    }
  });

  $('.gourl').click(function(event){
    event.preventDefault();
    location.href = $(this).data('url');
  });
  
function getURLVar(key) {
    var value = [];

    var query = String(document.location).split('?');

    if (query[1]) {
        var part = query[1].split('&');

        for (i = 0; i < part.length; i++) {
            var data = part[i].split('=');

            if (data[0] && data[1]) {
                value[data[0]] = data[1];
            }
        }

        if (value[key]) {
            return value[key];
        } else {
            return '';
        }
    }
}

// File Manager
$(document).on('click', '[data-roogo=\'file\']', function (e) {
    var element = this;

    $('#modal-file').remove();

    $.ajax({
        url: '<?php echo $fileManager; ?>' + '&filter=' + encodeURIComponent($(element).attr('data-filter')),
        dataType: 'html',
        success: function (html) {
            $('body').append(html);

            $('#modal-file').modal('show');
        }
    });
});

</script>