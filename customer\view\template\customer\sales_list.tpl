<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        订单付款信息
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="订单号/客户/联系方式/业务员" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>状态：</label>
                  <select class="form-control" name="filter_state">
                    <option value="*">全部状态</option>
                    <?php foreach($states as $state_id => $state) { ?>
                    <?php if ($state_id == $filter_state) { ?>
                    <option value="<?php echo $state_id; ?>" selected="selected"><?php echo $state; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $state_id; ?>"><?php echo $state; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>下单时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" id="last" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" id="last" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start-last" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end-last" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">订单列表</h3>
          <div class="box-tools">
            <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
          </div>
        </div>
        <div class="box-body table-responsive no-padding">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'order_no') { ?>
                  <a href="<?php echo $sort_order; ?>" class="<?php echo strtolower($order); ?>">订单号</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_order; ?>">订单号</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">所属店铺</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">所属店铺</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'customer') { ?>
                  <a href="<?php echo $sort_customer; ?>" class="<?php echo strtolower($order); ?>">客户</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_customer; ?>">客户</a>
                <?php } ?>
              </th>
              <!-- <th>
                <?php if ($sort == 'contact') { ?>
                  <a href="<?php echo $sort_contact; ?>" class="<?php echo strtolower($order); ?>">联系方式</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_contact; ?>">联系方式</a>
                <?php } ?>
              </th> -->
              <th>
                <?php if ($sort == 'sales_manage') { ?>
                  <a href="<?php echo $sort_manage; ?>" class="<?php echo strtolower($order); ?>">业务员</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_manage; ?>">业务员</a>
                <?php } ?>
              </th>

              <th>
                <?php if ($sort == 'sales_total') { ?>
                  <a href="<?php echo $sort_total; ?>" class="<?php echo strtolower($order); ?>">订单金额</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_total; ?>">订单金额</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'sales_paid') { ?>
                  <a href="<?php echo $sort_paid; ?>" class="<?php echo strtolower($order); ?>">付款金额</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_paid; ?>">付款金额</a>
                <?php } ?>
              </th> 
              <th>
                <?php if ($sort == 'sales_date') { ?>
                  <a href="<?php echo $sort_salesdate; ?>" class="<?php echo strtolower($order); ?>">销售日期</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_salesdate; ?>">销售日期</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'paid_date') { ?>
                  <a href="<?php echo $sort_paiddate; ?>" class="<?php echo strtolower($order); ?>">最后付款日期</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_paiddate; ?>">最后付款日期</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'delivery_date') { ?>
                  <a href="<?php echo $sort_delivery; ?>" class="<?php echo strtolower($order); ?>">交货日期</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_delivery; ?>">交货日期</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'state') { ?>
                  <a href="<?php echo $sort_state; ?>" class="<?php echo strtolower($order); ?>">状态</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_state; ?>">状态</a>
                <?php } ?>
              </th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($sales)) { ?>
            <?php foreach ($sales as $sale) { ?>
            <tr data-id="<?php echo $sale['sales_id']; ?>">
              <td><?php echo $sale['order_no']; ?></td>
              <td><?php echo $sale['storename']; ?></td>
              <td><?php echo $sale['customer']; ?></td>
              <!-- <td><?php echo $sale['contact']; ?></td> -->
              <td><?php echo $sale['sales_manage']; ?></td>
              <td><?php echo $currency_symbol; ?> <?php echo $sale['sales_total']; ?></td>
              <td><?php echo $currency_symbol; ?> <?php echo $sale['sales_paid']; ?></td>
              <td><?php echo $sale['sales_date']; ?></td>
              <td><?php echo $sale['paid_date']; ?></td>
              <td><?php echo $sale['delivery_date']; ?></td>
              <td><?php echo $sale['state']; ?></td>
              <td class="text-right">
                <a class="btn btn-success" href="<?php echo $sale['edit']; ?>">更新</a>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="11" align="center"> 暂无订单数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter_date-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter_date-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter_date-start').val('')
      $('#filter_date-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }
      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_state = $('select[name=\'filter_state\']').val();

      if (filter_state != '*') {
        url += '&filter_state=' + encodeURIComponent(filter_state);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>