<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        考核管理
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box">
        <div class="box-header">
          <h3 class="box-title">用户列表</h3>

          <div class="box-tools">
            <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
            <a class="btn btn-sm btn-danger" data-toggle="modal" data-target="#user-month-modal">生成月度评分</a>
          </div>
        </div>
        <!-- /.box-header -->
        <div class="box-body table-responsive no-padding">
          <table class="table table-hover table-striped">
            <tbody><tr>
              <th>名字</th>
              <th>考核指标</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($users)) { ?>
            <?php foreach ($users as $user) { ?>
            <tr data-id="<?php echo $user['user_id']; ?>">
              <td><?php echo $user['realname']; ?></td>
              <td><?php echo implode(', ', $user['useritems']); ?></td>
              <td class="text-right">
                <a class="btn btn-success" href="<?php echo $user['edit']; ?>" title="">修改</a>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#user-del-modal">删除</button>
              </td>
            </tr>
            <?php } ?>
            <?php } else{ ?>
            <td colspan="3" align="center"> 暂无用户 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
        <!-- /.box-body -->
      </div>
      <!-- 信息 -->
      <div class="modal modal-info fade" id="user-month-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $month; ?>" method="post" enctype="multipart/form-data" id="form-month">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">生成评分</h4>
              </div>
              <div class="modal-body">
                <p>确定生成月度评分吗？</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="user-month-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
      <!-- 删除 -->
      <div class="modal modal-danger fade" id="user-del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-user">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除此帐号吗？</p>
                <input id="user-del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="user-del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function() {
    $('#user-del-modal').on('show.bs.modal', function(event) {
      $('#user-del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#user-del-yes').on('click', () => {$('#form-user').submit()})

    $('#user-month-yes').on('click', () => {$('#form-month').submit()})
  })()
</script>
<?php echo $footer; ?>