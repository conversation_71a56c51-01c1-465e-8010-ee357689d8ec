<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" class="form-horizontal">
            <div class="form-group">
              <label class="col-sm-2 control-label">账单日期：</label>
              <div class="col-sm-8">
                <input type="text" value="<?php echo $bill_date; ?>" class="form-control" disabled="disabled" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-store">所属店铺：</label>
              <div class="col-sm-8">
                <select name="store_id" id="input-store" class="form-control" onchange="javascript: location.href = '<?php echo $action; ?>&store_id=' + this.value;" readonly="readonly">
                  <?php foreach ($stores as $store) { ?>
                  <?php if ($store['store_id'] == $store_id) { ?>
                  <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-sales">销售额：</label>
              <div class="col-sm-8">
                <input type="text" name="sales" value="<?php echo $sales; ?>" placeholder="请输入销售额" id="input-sales" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-advert">广告费用：</label>
              <div class="col-sm-8">
                <input type="text" name="advert" value="<?php echo $advert; ?>" placeholder="请输入广告费用" id="input-advert" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-cost">产品成本：</label>
              <div class="col-sm-8">
                <input type="text" name="cost" value="<?php echo $cost; ?>" placeholder="请输入产品成本" id="input-cost" class="form-control" />
              </div>
            </div>
            <?php foreach ($fee_detail as $fee_row => $fee) { ?>
            <div class="form-group">
              <label class="col-sm-2 control-label"><?php echo $fee['fee_name']; ?>：</label>
              <div class="col-sm-8">
                <input type="text" name="fee_detail[<?php echo $fee_row; ?>][amount]" id="amount<?php echo $fee['fee_id']; ?>" value="<?php echo $fee['amount']; ?>" class="form-control" readonly="readonly" />
                <input type="hidden" name="fee_detail[<?php echo $fee_row; ?>][fee_id]" value="<?php echo $fee['fee_id']; ?>" />
                <input type="hidden" name="fee_detail[<?php echo $fee_row; ?>][fee_name]" value="<?php echo $fee['fee_name']; ?>" />
                <input type="hidden" name="fee_detail[<?php echo $fee_row; ?>][total]" value="<?php echo $fee['total']; ?>" />
                <input type="hidden" name="fee_detail[<?php echo $fee_row; ?>][percent]" value="<?php echo $fee['percent']; ?>" />
              </div>
            </div>
            <?php } ?>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
var fees = {};
<?php foreach ($fee_detail as $fee) { ?>
fees[<?php echo $fee['fee_id']; ?>] = {total: <?php echo $fee['total']; ?>, percent: <?php echo $fee['percent']; ?>, amount: <?php echo $fee['amount']; ?>};
<?php } ?>

$('#input-sales').on('input', function() {
  var sales = parseFloat($(this).val());
  if (isNaN(sales)) sales = 0;
  $.each(fees, function(i, v){
    fees[i].amount = Math.round(v.total * 100 + sales * v.percent) / 100;
    $('#amount' + i).val(fees[i].amount.toFixed(2));
  });
});

$('#input-sales').trigger('input');
</script>
<?php echo $footer; ?>