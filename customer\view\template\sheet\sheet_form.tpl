<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
          <div class="nav-tabs-custom">
            <ul class="nav nav-tabs">
              <li><a href="#tab-general" data-toggle="tab">基本信息</a></li>
            </ul>
            <div class="tab-content">
              <div class="tab-pane" id="tab-general">

              <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-group">表类型：</label>
                  <div class="col-sm-8">
                    <select class="form-control" name="sheet_type" id="sheetType">

                      <option value="0" <?php if($filter_flow_id == 0) echo "selected"; ?>>待办事项</option>
                      <option value="1" <?php if($filter_flow_id > 0) echo "selected"; ?> >流程图</option>
                    </select>
                  </div>
                </div>

                <div class="form-group" id="oneSheetGroup">
                  <label class="col-sm-2 control-label" for="input-store">一级多维表：</label>
                  <div class="col-sm-8">
                    <select class="form-control" name="filter_one_sheet" onchange="$('#secondCate').load('<?php echo $getSecondCate; ?>&filter_one_sheet=' + this.value );">
                    <?php  if ($name){ ?>
                          <option value="<?php echo $filter_one_sheet; ?>" selected="selected"><?php if($filter_second_sheet) echo $filter_one_sheet_name ?></option>
                    <?php } else { ?>
                      <option value=""></option>
                      <?php foreach($sheetOneList as $list) { ?>
                      <?php if ($list['sheet_id'] == $filter_one_sheet) { ?>
                      <option value="<?php echo $list['sheet_id']; ?>" selected="selected"><?php echo $list['name']; ?></option>
                      <?php } else { ?>
                      <option value="<?php echo $list['sheet_id']; ?>"><?php echo $list['name']; ?></option>
                      <?php } ?>
                      <?php } ?>
                     <?php } ?>
                    </select>
                  </div>
                </div>

                <div class="form-group" id="secondSheetGroup">
                  <label class="col-sm-2 control-label" for="input-group">二级多维表：</label>
                  <div class="col-sm-8">
                    <select class="form-control" name="filter_second_sheet" id="secondCate">
                      <?php if ($name) { ?>
                      <option value="<?php echo $filter_second_sheet; ?>" selected="selected"><?php if($type == 2) echo $filter_second_sheet_name; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-realname">名称*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="name" value="<?php echo $name; ?>" placeholder="请输入名称" id="input-realname" class="form-control" />
                  </div>
                </div>

                <div class="form-group" id="flowSelectGroup">
                  <label class="col-sm-2 control-label" for="input-store">选择流程*：</label>
                  <div class="col-sm-8">
                    <select class="form-control" name="flow_id">
                    <option value = 0 ></option>
                      <?php foreach($flow_list as $list) { ?>
                      <?php if ($list['flow_id'] == $filter_flow_id) { ?>
                      <option value="<?php echo $list['flow_id']; ?>" selected="selected"><?php echo $list['flow_name']; ?></option>
                      <?php } else { ?>
                      <option value="<?php echo $list['flow_id']; ?>"><?php echo $list['flow_name']; ?></option>
                      <?php } ?>
                      <?php } ?>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <div class="col-sm-offset-2 col-sm-8">
                    <button class="btn btn-primary" type="submit">提交保存</button>
                    <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
            
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script src="<?php echo HTTP_SERVER; ?>static/js/echarts.min.js"></script>
<script type="text/javascript">
$('.nav-tabs li:first a').tab('show');

// 页面加载时初始化
$(document).ready(function() {
    // 获取表单元素
    var sheetType = $('#sheetType');
    var flowSelectGroup = $('#flowSelectGroup');
    var oneSheetGroup = $('#oneSheetGroup');
    var secondSheetGroup = $('#secondSheetGroup');
    var flow_id = <?php echo $filter_flow_id; ?>
    
    // 初始化时根据默认值设置显示状态
    toggleFormGroups(sheetType.val());
    
    // 监听表类型变化
    sheetType.change(function() {
        toggleFormGroups($(this).val());
    });
    
    // 控制表单组显示/隐藏的函数
    function toggleFormGroups(type) {
        if(type == '1' || flow_id > 0) { // 流程图
            flowSelectGroup.show();
          
        } else if(type == '0') { // 待办事项
            flowSelectGroup.hide();
            oneSheetGroup.show();
            secondSheetGroup.show();
        }
    }
});

$('#input-group').load('<?php echo $getSecondCate; ?>&filter_one_sheet=<?php echo $filter_one_sheet; ?>');

<?php if (!empty($summary)) { ?>
var myChart = echarts.init(document.getElementById('chartMain'));
myChart.clear();
myChart.setOption(<?php echo json_encode($summary, 320); ?>);
<?php } ?>

<?php if (isset($orders)) { ?>
$('#orders').delegate('.pagination a', 'click', function(e) {
  e.preventDefault();

  $('#orders').load(this.href);
});

$('#orders').load('<?php echo $orders; ?>');

// 日期显示
$('#input-date').daterangepicker({
  autoApply: true,
  autoUpdateInput: false,
  singleDatePicker: true,
  timePicker: false,
  locale: {
    format: 'YYYY-MM-DD',
    applyLabel: '确定',
    cancelLabel: '清除'
  }
})
$('#input-date').on('apply.daterangepicker', function(ev, picker) {
  $(this).val(picker.startDate.format('YYYY-MM-DD'))
})
$('#input-date').on('cancel.daterangepicker', function(ev, picker) {
  $(this).val('')
})

$('#button-order').on('click', function(e) {
  e.preventDefault();

  $.ajax({
    url: '<?php echo $addorder; ?>',
    type: 'post',
    dataType: 'json',
    data: 'order_times=' + encodeURIComponent($('#tab-orders select[name=\'order_times\']').val()) + '&order_total=' + encodeURIComponent($('#tab-orders input[name=\'order_total\']').val()) + '&order_date=' + encodeURIComponent($('#tab-orders input[name=\'order_date\']').val()) + '&order_product=' + encodeURIComponent($('#tab-orders input[name=\'order_product\']').val()),
    beforeSend: function() {
      $('#button-order').button('loading');
    },
    complete: function() {
      $('#button-order').button('reset');
    },
    success: function(json) {
      $('.alert').remove();

      if (json['error']) {
        $('#tab-orders').prepend('<div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
      }

      if (json['success']) {
        $('#tab-orders').prepend('<div class="alert alert-success"><i class="glyphicon glyphicon-info-sign"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');

        $('#orders').load('<?php echo $orders; ?>');

        $('#tab-orders input[name=\'order_total\']').val('');
        $('#tab-orders input[name=\'order_product\']').val('');
      }
    }
  });
});
<?php } ?>
</script>
<?php echo $footer; ?>