<?php
class ModelAdminExcel extends Model {
	public function upload($othername){
		$info = array();
		
		if (!empty($this->request->files['file']['name']) && is_file($this->request->files['file']['tmp_name'])) {
			$filename = basename(html_entity_decode($this->request->files['file']['name'], ENT_QUOTES, 'UTF-8'));

			if ((utf8_strlen($filename) < 3) || (utf8_strlen($filename) > 64)) {
				$info['error'] = $this->language->get('error_filename');
			}
		
			$allowed = array('xls', 'xlsx', 'csv');

			if (!in_array(strtolower(substr(strrchr($filename, '.'), 1)), $allowed)) {
				$info['error'] = $this->language->get('error_filetype');
			}

			if ($this->request->files['file']['error'] != UPLOAD_ERR_OK) {
				$info['error'] = $this->language->get('error_upload_' . $this->request->files['file']['error']);
			}
		} else {
			$info['error'] = $this->language->get('error_upload');
		}

		if (!isset($info['error'])) {
			if (is_uploaded_file($this->request->files['file']['tmp_name']) && file_exists($this->request->files['file']['tmp_name'])) {
				$path = $this->createPath(true);
								
				$info['name'] = $path . $othername . token(32) . strrchr($filename, '.');
				move_uploaded_file($this->request->files['file']['tmp_name'], $info['name']);
			}			
		}
		
		return $info;	
	}
	
	public function import($filename){
		$this->load->helper('PHPExcel/Classes/PHPExcel');
		
		$FileType = PHPExcel_IOFactory::identify($filename);
		$Reader = PHPExcel_IOFactory::createReader($FileType);
		//$Reader->setReadDataOnly(true);
		$PHPExcel = $Reader->load($filename);
		
		$sheetData = $PHPExcel->getActiveSheet()->toArray(null,true,true,true);
		
		return $sheetData;
	}
	
	public function createPath($upload) {
		if ($upload) {
			$path = DIR_UPLOAD;
		} else {
			$path = DIR_DOWNLOAD;
		}		
		
		//$dates = explode('/', date('Y/m/d',time()));
		$dates[] = date('Ym');
				
		foreach ($dates as $value) {
			$path = $path . $value . '/';
				
			if (!file_exists($path)) {
				mkdir($path, 0777);
			}
		}
		
		return $path;
	}

	public function export($filename, $results = array(), $datatypes = array(), $type = '.xls') {		
		$this->load->helper('PHPExcel/Classes/PHPExcel');
		
		$PHPExcel = new PHPExcel();
		
		$i = 1;
		foreach ($results as $result) {
			$j = 0;

			foreach ($result as $value) {
				if ($i == 1) {
					$PHPExcel->setActiveSheetIndex(0)->setCellValueExplicitByColumnAndRow($j, $i, $value)->getStyleByColumnAndRow($j, $i)->getFont()->setBold(true);
				} elseif (!empty($value)) {
					if (isset($datatypes[$j]) && $datatypes[$j] == 'numbric') {
						$datatype = PHPExcel_Cell_DataType::TYPE_NUMERIC;
					} else {
						$datatype = PHPExcel_Cell_DataType::TYPE_STRING;
					}

					if (isset($datatypes[$j]) && $datatypes[$j] == 'link') {
						$PHPExcel->setActiveSheetIndex(0)->setCellValueExplicitByColumnAndRow($j, $i, $value['text'], $datatype, true)->getHyperlink()->setUrl($value['link']);
					} else {
						$PHPExcel->setActiveSheetIndex(0)->setCellValueExplicitByColumnAndRow($j, $i, $value, $datatype);
						// $PHPExcel->setActiveSheetIndex(0)->setCellValueByColumnAndRow($j, $i, $value);
					}
				} elseif (isset($datatypes[$j]) && $datatypes[$j] == 'numbric') {
					$PHPExcel->setActiveSheetIndex(0)->setCellValueExplicitByColumnAndRow($j, $i, 0, PHPExcel_Cell_DataType::TYPE_NUMERIC);
				}

				$j++;
			}

			$i++;
		}	
			
		$PHPExcel->getActiveSheet()->setTitle('Sheet1');
		$PHPExcel->setActiveSheetIndex(0);
		if ($type == '.xlsx') {
			$PHPExcelWriter = PHPExcel_IOFactory::createWriter($PHPExcel, 'Excel2007');
		} else {
			$PHPExcelWriter = PHPExcel_IOFactory::createWriter($PHPExcel, 'Excel5');
		}

		header('Content-Type: application/octet-stream');
		header('Content-Disposition: attachment; filename="' . $filename . $type . '"');
		header('Expires: 0');
		header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		header('Pragma: public');

		if (ob_get_level()) ob_end_clean();

		$PHPExcelWriter->save('php://output');		
	}
}
?>