<?php
class ModelAdminBillCheck extends Model {
    //支付宝账单-关键字列表
	public function getAlipayBillSortKeyAll($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "alipay_bill_sort_key WHERE 1";
        if (!empty($data['filter_name'])) {
            $sql .= " AND name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }
        if (!empty($data['filter_type'])) {
            $sql .= " AND type = " . $this->db->escape($data['filter_type']);
        }

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

		return $query->rows;
	}

    //支付宝账单-关键字总数
    public function getAlipayBillSortKeyTotal($data = array()) {
        $sql = "SELECT count(*) AS total FROM " . DB_PREFIX . "alipay_bill_sort_key WHERE 1";
        if (!empty($data['filter_name'])) {
            $sql .= " AND name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }
        if (!empty($data['filter_type'])) {
            $sql .= " AND type = " . $this->db->escape($data['filter_type']);
        }

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    //支付宝账单-查询关键字
    public function getAlipayBillSortKey($id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "alipay_bill_sort_key WHERE alipay_bill_sort_id = " . $id);

        return $query->row;
    }

    //支付宝账单-所有关键字
    public function getAllAlipayBillSortKey() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "alipay_bill_sort_key ORDER BY sort DESC");

        return $query->rows;
    }

    //支付宝账单-添加关键字
    public function addAlipayBillSortKey($data  = array()) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "alipay_bill_sort_key SET sort = '" . $this->db->escape($data['sort']). "',export_sort = '" . $this->db->escape($data['export_sort']). "', type = '" . (int)$data['type'] . "', keyword = '" . $this->db->escape(json_encode(array_values($data['fields']),320)) . "', name = '". $data['name'] ."', date_added = NOW()");
    }

    //支付宝账单-编辑关键字
    public function editAlipayBillSortKey($data = array(),$id) {
        $this->db->query("UPDATE " . DB_PREFIX . "alipay_bill_sort_key SET sort = '" . $this->db->escape($data['sort']). "',export_sort = '" . $this->db->escape($data['export_sort']). "', type = '" . (int)$data['type'] . "', keyword = '" . $this->db->escape(json_encode(array_values($data['fields']),320)) . "', name = '". $data['name'] ."' WHERE alipay_bill_sort_id = '" . (int)$id . "'");
    }

    //支付宝账单-删除关键字
    public function delAlipayBillSortKey($id) {
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "alipay_bill_sort_key WHERE alipay_bill_sort_id = " . $id);
    }

    //支付宝账单-列表
    public function getAlipayBillSortAll($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "alipay_bill_statistic WHERE 1";
        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id =" . $this->db->escape($data['filter_store']);
        }

        if (!empty($data['filter_bill_key_id'])) {
            $sql .= " AND alipay_bill_sort_id =" . $this->db->escape($data['filter_bill_key_id']);
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND month >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND month <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //支付宝账单-总数
    public function getAlipayBillSortAllTotal($data = array()) {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "alipay_bill_statistic WHERE 1";
        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id =" . $this->db->escape($data['filter_store']);
        }

        if (!empty($data['filter_bill_key_id'])) {
            $sql .= " AND alipay_bill_sort_id =" . $this->db->escape($data['filter_bill_key_id']);
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND month >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND month <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    //支付宝账单-查询分类详情
    public function getAlipayBillSortDetail($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "alipay_bill WHERE 1";
        if (!empty($data['filter_alipay_bill_sort_id'])) {
            $sql .= " AND alipay_bill_sort_id = " . $this->db->escape($data['filter_alipay_bill_sort_id']);
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = " . $this->db->escape($data['filter_store']);
        }

        if (!empty($data['filter_month'])) {
            $sql .= " AND month = " . $this->db->escape($data['filter_month']);
        }

        if (!empty($data['filter_accounting_serial_number'])) {
            $sql .= " AND accounting_serial_number LIKE '%" . $this->db->escape($data['filter_accounting_serial_number']) . "%'";
        }

        if (!empty($data['filter_service_serial_number'])) {
            $sql .= " AND service_serial_number LIKE '%" . $this->db->escape($data['filter_service_serial_number']) . "%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND occurrence_time >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND occurrence_time <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $sql .= " ORDER BY occurrence_time DESC";

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //支付宝账单-查询分类详情总数
    public function getAlipayBillSortDetailTotal($data = array()) {
        $sql = "SELECT count(*) as count FROM " . DB_PREFIX . "alipay_bill WHERE 1";
        if (!empty($data['filter_alipay_bill_sort_id'])) {
            $sql .= " AND alipay_bill_sort_id = " . $this->db->escape($data['filter_alipay_bill_sort_id']);
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = " . $this->db->escape($data['filter_store']);
        }

        if (!empty($data['filter_month'])) {
            $sql .= " AND month = " . $this->db->escape($data['filter_month']);
        }

        if (!empty($data['filter_accounting_serial_number'])) {
            $sql .= " AND accounting_serial_number LIKE '%" . $this->db->escape($data['filter_accounting_serial_number']) . "%'";
        }

        if (!empty($data['filter_service_serial_number'])) {
            $sql .= " AND service_serial_number LIKE '%" . $this->db->escape($data['filter_service_serial_number']) . "%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND occurrence_time >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND occurrence_time <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['count'];
    }

    //支付宝账单-查询未分类数据
    public function getAlipayBillNotSortList($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "alipay_bill WHERE alipay_bill_sort_id=0";

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = " . $this->db->escape($data['filter_store']);
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND occurrence_time >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND occurrence_time <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        if (!empty($data['filter_remark'])) {
            $sql .= " AND remark LIKE '%" . $this->db->escape($data['filter_remark'])."%'";
        }

        if (!empty($data['filter_business_type'])) {
            $business_types = $this->getAlipayBillBusinessTypes();
            $sql .= " AND business_type = '" . $this->db->escape($business_types[$data['filter_business_type']])."'";
        }

        $sql .= " ORDER BY occurrence_time DESC";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //支付宝账单-添加类别（未分类账单添加）
    public function addAlipayBillSort($data  = array()) {
        $query = $this->db->query("INSERT INTO " . DB_PREFIX . "alipay_bill_sort_key SET sort = '0',export_sort = '0', type = '3', keyword = '" . $this->db->escape(json_encode(array_values($data['fields']),320)) . "', name = '". $data['name'] ."', date_added = NOW()");

        $alipay_bill_sort_id = $this->db->getLastId();

        return $alipay_bill_sort_id;
    }

    //支付宝账单-添加账单到类别
    public function addAlipayBillToSore($data = array(),$ids) {
        $sql = "UPDATE " . DB_PREFIX . "alipay_bill SET alipay_bill_sort_id=".$data['filter_alipay_bill_sort_id'].",to_statistic=1 WHERE alipay_bill_sort_id=0";

        $sql .= " AND store_id = " . $this->db->escape($data['filter_store']);

        $sql .= " AND occurrence_time >= '" . $this->db->escape($data['filter_date_start']) . "'";

        $sql .= " AND occurrence_time <= '" . $this->db->escape($data['filter_date_end']) . "'";

        $sql .= " AND accounting_serial_number in(" . $this->db->escape($ids).")";

        $query = $this->db->query($sql);
    }

    //支付宝账单-修改账单统计
    public function setAlipayBillStatistic($data = array(),$ids) {
        $query_month = $this->db->query("SELECT month FROM  " . DB_PREFIX . "alipay_bill WHERE accounting_serial_number in(" . $this->db->escape($ids).") GROUP BY month");
        foreach ($query_month->rows as $month) {
            $query_statistic = $this->db->query("SELECT alipay_bill_statistic_id FROM  " . DB_PREFIX . "alipay_bill_statistic WHERE store_id='" . $this->db->escape($data['filter_store']). "' AND alipay_bill_sort_id='" . $this->db->escape($data['filter_alipay_bill_sort_id']). "' AND month='" . $this->db->escape($month['month']). "'");
            $sum =  $this->db->query("SELECT store_id,alipay_bill_sort_id,month,sum(income) as sum_income,sum(disburse) as sum_disburse FROM " . DB_PREFIX . "alipay_bill WHERE to_statistic <> 0 AND store_id='" . $this->db->escape($data['filter_store']). "' AND alipay_bill_sort_id='" . $this->db->escape($data['filter_alipay_bill_sort_id']). "' AND month='" . $this->db->escape($month['month']). "'");
            if (empty($query_statistic->row)) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "alipay_bill_statistic SET store_id = '" . $sum->row['store_id'] . "',alipay_bill_sort_id = '" . $sum->row['alipay_bill_sort_id'] . "',month = '" . $sum->row['month'] . "',sum_income = '" . $sum->row['sum_income'] . "',sum_disburse = '" . $sum->row['sum_disburse'] . "', date_added = NOW()");
            } else {
                $this->db->query("UPDATE  " . DB_PREFIX . "alipay_bill_statistic SET sum_income = '" . $sum->row['sum_income'] . "',sum_disburse = '" . $sum->row['sum_disburse'] . "'  WHERE alipay_bill_statistic_id='" . $this->db->escape($query_statistic->row['alipay_bill_statistic_id']). "'");
            }
            $this->db->query("UPDATE " . DB_PREFIX . "alipay_bill SET to_statistic = '1' WHERE  store_id = '" . $sum->row['store_id'] . "' AND alipay_bill_sort_id = '" . $sum->row['alipay_bill_sort_id'] . "' AND month = '" . $sum->row['month'] . "'");
        }
    }

    //支付宝账单-修改账单统计2
    public function setAlipayBillStatisticTwo($data = array(),$ids) {
        $query_month = $this->db->query("SELECT month FROM  " . DB_PREFIX . "alipay_bill WHERE accounting_serial_number in(" . $this->db->escape($ids).") GROUP BY month");
        foreach ($query_month->rows as $month) {
            $query_statistic = $this->db->query("SELECT alipay_bill_statistic_id FROM  " . DB_PREFIX . "alipay_bill_statistic WHERE store_id='" . $this->db->escape($data['filter_store']). "' AND alipay_bill_sort_id='" . $this->db->escape($data['filter_alipay_bill_sort_id']). "' AND month='" . $this->db->escape($month['month']). "'");
            $sum =  $this->db->query("SELECT store_id,alipay_bill_sort_id,month,sum(income) as sum_income,sum(disburse) as sum_disburse FROM " . DB_PREFIX . "alipay_bill WHERE alipay_bill_sort_id <> 0 AND store_id='" . $this->db->escape($data['filter_store']). "' AND alipay_bill_sort_id='" . $this->db->escape($data['filter_alipay_bill_sort_id']). "' AND month='" . $this->db->escape($month['month']). "'");
            if (empty($query_statistic->row)) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "alipay_bill_statistic SET store_id = '" . $sum->row['store_id'] . "',alipay_bill_sort_id = '" . $sum->row['alipay_bill_sort_id'] . "',month = '" . $sum->row['month'] . "',sum_income = '" . $sum->row['sum_income'] . "',sum_disburse = '" . $sum->row['sum_disburse'] . "', date_added = NOW()");
            } else {
                $this->db->query("UPDATE  " . DB_PREFIX . "alipay_bill_statistic SET sum_income = '" . $sum->row['sum_income'] . "',sum_disburse = '" . $sum->row['sum_disburse'] . "'  WHERE alipay_bill_statistic_id='" . $this->db->escape($query_statistic->row['alipay_bill_statistic_id']). "'");
            }
            $this->db->query("UPDATE " . DB_PREFIX . "alipay_bill SET to_statistic = '1' WHERE  store_id = '" . $sum->row['store_id'] . "' AND alipay_bill_sort_id = '" . $sum->row['alipay_bill_sort_id'] . "' AND month = '" . $sum->row['month'] . "'");
        }
    }

    //支付宝账单-添加费用归属（后台备注）
    public function editAlipayBillCostAffiliation($data = array()) {
        $query = $this->db->query("UPDATE " . DB_PREFIX . "alipay_bill SET cost_affiliation='".$data['cost_affiliation']."' WHERE accounting_serial_number=".$data['accounting_serial_number']);
    }

    //支付宝账单-从类别中删除
    public function delAlipayBillSortGather($id) {
        $query_bill = $this->db->query("SELECT store_id,alipay_bill_sort_id,month,income,disburse FROM " . DB_PREFIX . "alipay_bill  WHERE accounting_serial_number=".$id);
        $query_statistic = $this->db->query("SELECT alipay_bill_statistic_id,sum_income,sum_disburse FROM  " . DB_PREFIX . "alipay_bill_statistic WHERE store_id='" . $this->db->escape($query_bill->row['store_id']). "' AND alipay_bill_sort_id='" . $this->db->escape($query_bill->row['alipay_bill_sort_id']). "' AND month='" . $this->db->escape($query_bill->row['month']). "'");
        $sum_income = $query_statistic->row['sum_income'] - $query_bill->row['income'];
        $sum_disburse = $query_statistic->row['sum_disburse'] - $query_bill->row['disburse'];
        if ($sum_income == 0 && $sum_disburse == 0) {
            $this->db->query("DELETE FROM " . DB_PREFIX . "alipay_bill_statistic  WHERE alipay_bill_statistic_id='" . $this->db->escape($query_statistic->row['alipay_bill_statistic_id']). "'");
        } else {
            $this->db->query("UPDATE  " . DB_PREFIX . "alipay_bill_statistic SET sum_income = '" . $sum_income . "',sum_disburse = '" . $sum_disburse . "'  WHERE alipay_bill_statistic_id='" . $this->db->escape($query_statistic->row['alipay_bill_statistic_id']). "'");
        }

        $this->db->query("UPDATE " . DB_PREFIX . "alipay_bill SET alipay_bill_sort_id=0,to_statistic=0 WHERE accounting_serial_number=".$id);
    }

    //支付宝账单-上传文件
    public function addAlipayBillFile($file_url,$name,$original_name) {
        $query = $this->db->query("INSERT INTO " . DB_PREFIX . "alipay_bill_file SET link = '" . $this->db->escape($file_url). "',name = '" . $this->db->escape($name). "',original_name = '" . $this->db->escape($original_name). "', date_added = NOW()");
        return $query;
    }

    //支付宝账单-查询上传文件
    public function getAlipayBillFile($file_url) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "alipay_bill_file WHERE link= '". $this->db->escape($file_url). "'");

        return $query->num_rows;
    }

    //支付宝账单-查询上传文件列表
    public function getAlipayBillFileList() {
        $sql = "SELECT * FROM " . DB_PREFIX . "alipay_bill_file";

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //支付宝账单-查询上传文件总数
    public function getAlipayBillFileTotal() {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "alipay_bill_file";

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    //支付宝账单-删除上传文件
    public function delAlipayBillUpload($id) {
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "alipay_bill_file WHERE status=0 AND alipay_bill_file_id = " . $id);
    }

    //支付宝账单-字段筛选
    public function getAlipayBillSortKeyFields() {
        $fields = [
            1 => ['账务流水号','accounting_serial_number'],
            2 => ['业务流水号','service_serial_number'],
            3 => ['商户订单号','merchant_order_number'],
            4 => ['商品名称','product_name'],
            5 => ['发生时间','occurrence_time'],
            6 => ['对方账号','reciprocal_account'],
            7 => ['收入金额（+元）','income'],
            8 => ['支出金额（-元）','disburse'],
            9 => ['账户余额（元）','balance'],
            10 => ['交易渠道','transaction_channel'],
            11 => ['业务类型','business_type'],
            12 => ['备注','remark'],
        ];
        return $fields;
    }

    //支付宝账单-关系运算
    public function getAlipayBillSortKeyOperations() {
        $operation = [
            '等于',
            '不等于',
            '大于',
            '小于',
            '大于等于',
            '小于等于',
            '包含',
            '不包含',
        ];
        return $operation;
    }

    //支付宝账单-支付宝账号关联店铺id
    public function getAlipayBillSortKeyStore() {
        $store = [
            '20884015410411300156' => 17,
            '20888318986780800156' => 14,
            '20886116687244810156' => 13,
            '20884212700582050156' => 15,
            '20880425887694580156' => 16,
            '20880217495963460156' => 21,
            '20881210147079350156' => 25,
            '20880220391108220156' => 26,
            '20887225999994250156' => 28,
            '20883427002893880156' => 2,
            '20881026101091050156' => 5,
            '20880225412595650156' => 3,
            '20884224843861910156' => 1,
            '20889310249902490156' => 7,
            '20883423400359050156' => 6,
            '20882226117615740156' => 4,
            '20886421988822570156' => 42,
        ];
        return $store;
    }

    public function getAlipayBillBusinessTypes() {
        $business_types = [
            1 => '保证金',
            2 => '理财申购',
            3 => '其他',
            4 => '提现',
            5 => '退款',
            6 => '在线支付',
            7 => '在线支付（购汇支付）',
            8 => '转账',
        ];

        return $business_types;
    }

    //支付宝账单-支付宝账号关联店铺列表
    public function getStores($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "store WHERE FIND_IN_SET(store_id, '" . implode(',',array_values($this->getAlipayBillSortKeyStore())) . "')";
        $query = $this->db->query($sql);

        foreach ($query->rows as $k=>$v) {
            $type = 0;
            $NotSortListWhere['filter_store'] = $v['store_id'];
            $NotSortListWhere['filter_date_start'] = date('Y-m-01 00:00:00', strtotime('-1 month'));
            $NotSortListWhere['filter_date_end'] = date('Y-m-t 23:59:59', strtotime('-1 month'));
            $getAlipayBillNotSortList = $this->getAlipayBillNotSortList($NotSortListWhere);
            if (!empty($getAlipayBillNotSortList)) {
                $type = 1;
            } else {
                $count_bill = $this->db->query("SELECT count(*) as total FROM " . DB_PREFIX . "alipay_bill WHERE store_id='".$v['store_id']."' AND month='".date('Ym', strtotime('-1 month'))."'");
                if ($count_bill->row['total'] == 0) {
                    $type = 2;
                }
            }
            $query->rows[$k]['type'] = $type;
        }
        return $query->rows;
    }

    //支付宝账单-导出列表
    public function getAlipayBillSortExportAll($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "alipay_bill_export WHERE 1";

        if (!empty($data['filter_store'])) {
            $sql .= " AND stores like '%," . $this->db->escape($data['filter_store']).",%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND s_month >=" . $this->db->escape($data['filter_date_start']);
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND e_month <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $sql .= " ORDER BY date_added DESC";

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //支付宝账单-导出总数
    public function getAlipayBillSortExportTotal($data = array()) {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "alipay_bill_export WHERE 1";

        if (!empty($data['filter_store'])) {
            $sql .= " AND stores like '%," . $this->db->escape($data['filter_store']).",%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND s_month >=" . $this->db->escape($data['filter_date_start']);
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND e_month <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }
        $sql .= " ORDER BY date_added DESC";

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    //支付宝账单-添加导出
    public function addAlipayBillSortExport($data = array()) {
        $query = $this->db->query("INSERT INTO " . DB_PREFIX . "alipay_bill_export SET s_month = '" . $this->db->escape(date("Ym",strtotime($data['date_start']))). "',e_month = '" . $this->db->escape(date("Ym",strtotime($data['date_end']))). "',stores = '," . $this->db->escape(implode(',',$data['stores'])). ",', date_added = NOW()");
        return $query;
    }

    //支付宝账单-删除分类数据
    public function addAlipayBillSortDel($data = array()) {
        $query = $this->db->query("UPDATE " . DB_PREFIX . "alipay_bill SET alipay_bill_sort_id=0,to_statistic=0 WHERE store_id in(".implode(',',$data['stores']).") AND month>='".date("Ym",strtotime($data['date_start']))."' AND month<='".date("Ym",strtotime($data['date_end']))."'");
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "alipay_bill_statistic WHERE store_id in(".implode(',',$data['stores']).") AND month>='".date("Ym",strtotime($data['date_start']))."' AND month<='".date("Ym",strtotime($data['date_end']))."'");
        return $query;
    }


    //快递费-查询上传文件
    public function getExpressFeeFile($file_url) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "express_fee_file WHERE link= '". $this->db->escape($file_url). "'");

        return $query->num_rows;
    }

    //快递费-上传文件
    public function addExpressFeeFile($file_url,$name,$original_name) {
        $query = $this->db->query("INSERT INTO " . DB_PREFIX . "express_fee_file SET link = '" . $this->db->escape($file_url). "',name = '" . $this->db->escape($name). "',original_name = '" . $this->db->escape($original_name). "', date_added = NOW()");
        return $query;
    }

    //快递费-查询上传文件列表
    public function getExpressFeeFileList() {
        $sql = "SELECT * FROM " . DB_PREFIX . "express_fee_file";

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //快递费-查询上传文件总数
    public function getExpressFeeFileTotal() {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "express_fee_file";

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    //快递费-删除上传文件
    public function delExpressFeeUpload($id) {
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "express_fee_file WHERE  express_fee_file_id = " . $id);
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "express_fee WHERE  express_fee_file_id = " . $id);
    }

    //快递费-查询所有快递费用报价
    public function getExpressFeeStandardAll(){
        $query = $this->db->query("SELECT t.name,s.s_day,s.e_day,s.type,s.express_fee_standard_id,s.sort FROM " . DB_PREFIX . "express_fee_standard as s LEFT JOIN " . DB_PREFIX . "express_fee_template as t ON s.name=t.express_fee_template_id ORDER BY s.name,s.sort DESC");

        return $query->rows;
    }

    //快递费-添加快递报价
    public function addExpressFeeStandard($data = array()) {
        if ($data['type'] == 2) {
            $data['volume_ratio'] = 0;
        }
        $this->db->query("INSERT INTO " . DB_PREFIX . "express_fee_standard SET name = '" . $this->db->escape($data['name']). "', type = '" . (int)$data['type'] . "', weight = '" . $this->db->escape(json_encode(array_values($data['weight']),320)) . "',starting_weight = '" . $this->db->escape($data['starting_weight']). "', first_weight = '" . $this->db->escape($data['first_weight']). "',continue_weight = '" . $this->db->escape($data['continue_weight']). "',volume_ratio = '" . $this->db->escape($data['volume_ratio']). "',s_day = '" . $this->db->escape($data['s_day']). "',e_day = '" . $this->db->escape($data['e_day']). "',sort = '" . $this->db->escape($data['sort']). "', date_added = NOW()");
    }

    //快递费-修改快递报价
    public function editExpressFeeStandard($data = array(),$id) {
        if ($data['type'] == 2) {
            $data['volume_ratio'] = 0;
        }
        $expressFeeStandard = $this->getexpressFeeStandard($id);
        $data['provinces'] = '';
        if (!empty($expressFeeStandard['provinces'])) {
            $new_provinces = array();
            $provinces = json_decode($expressFeeStandard['provinces'],true);
            foreach ($provinces as $key=>$val) {
                $new_provinces[$key]['province'] = $val['province'];
                foreach (array_values($data['weight']) as $k=>$v) {
                    $new_provinces[$key]['price'][$k] = !empty($val['price'][$k]) ? $val['price'][$k] : '';
                }
                $new_provinces[$key]['starting_price'] = $val['starting_price'];
                $new_provinces[$key]['weight_type'] = $val['weight_type'];
                $new_provinces[$key]['starting_price_type'] = $val['starting_price_type'];
                $new_provinces[$key]['first_follow_weight'] = $val['first_follow_weight'];
            }
            $data['provinces'] = json_encode($new_provinces);
        }
        $this->db->query("UPDATE " . DB_PREFIX . "express_fee_standard SET name = '" . $this->db->escape($data['name']). "', type = '" . (int)$data['type'] . "', weight = '" . $this->db->escape(json_encode(array_values($data['weight']),320)) . "',starting_weight = '" . $this->db->escape($data['starting_weight']). "', first_weight = '". $this->db->escape($data['first_weight']) ."',continue_weight = '" . $this->db->escape($data['continue_weight']). "',volume_ratio = '" . $this->db->escape($data['volume_ratio']). "',s_day = '" . $this->db->escape($data['s_day']). "',e_day = '" . $this->db->escape($data['e_day']). "',sort = '" . $this->db->escape($data['sort']). "',provinces = '" . $this->db->escape($data['provinces']). "' WHERE express_fee_standard_id = '" . (int)$id . "'");
    }

    //快递费-复制快递报价
    public function copyExpressFeeStandard($data = array(),$id) {
        if ($data['type'] == 2) {
            $data['volume_ratio'] = 0;
        }
        $expressFeeStandard = $this->getexpressFeeStandard($id);

        $this->db->query("INSERT INTO " . DB_PREFIX . "express_fee_standard SET name = '" . $this->db->escape($data['name']). "', type = '" . (int)$data['type'] . "', weight = '" . $this->db->escape(json_encode(array_values($data['weight']),320)) . "',starting_weight = '" . $this->db->escape($data['starting_weight']). "', first_weight = '" . $this->db->escape($data['first_weight']). "',continue_weight = '" . $this->db->escape($data['continue_weight']). "',volume_ratio = '" . $this->db->escape($data['volume_ratio']). "',s_day = '" . $this->db->escape($data['s_day']). "',e_day = '" . $this->db->escape($data['e_day']). "',sort = '" . $this->db->escape($data['sort']). "',provinces = '" . $this->db->escape($expressFeeStandard['provinces']). "', date_added = NOW()");
    }

    //快递费-添加省份费用详情
    public function editExpressFeeStandardProvince($data = array(),$id) {
        $provinces = !empty($data['provinces']) ? $this->db->escape(json_encode(array_values($data['provinces']),320)) : '';
        $this->db->query("UPDATE " . DB_PREFIX . "express_fee_standard SET  provinces = '" . $provinces . "' WHERE express_fee_standard_id = '" . (int)$id . "'");
    }

    //快递费-查询快递报价
    public function getExpressFeeStandard($id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "express_fee_standard WHERE express_fee_standard_id=".$id);

        return $query->row;
    }

    //快递费-删除快递报价
    public function delExpressFeeStandard($id) {
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "express_fee_standard WHERE express_fee_standard_id = " . $id);
    }

    //快递费-查询所有快递加价
    public function getExpressFeeSurchargesAll() {
        $query = $this->db->query("SELECT t.name,s.express_fee_surcharges_id,s.s_day,s.e_day,s.month FROM " . DB_PREFIX . "express_fee_surcharges as s LEFT JOIN " . DB_PREFIX . "express_fee_template as t ON s.name=t.express_fee_template_id ORDER BY s.name,s.month DESC");

        return $query->rows;
    }

    //快递费-添加快递加价
    public function addExpressFeeSurcharges($data = array()) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "express_fee_surcharges SET name = '" . $this->db->escape($data['name']). "',month = '" . $this->db->escape($data['month']). "',s_day = '" . $this->db->escape($data['s_day']). "',e_day = '" . $this->db->escape($data['e_day']). "', date_added = NOW()");
    }

    //快递费-修改快递加价
    public function editExpressFeeSurcharges($data = array(),$id) {
        $this->db->query("UPDATE " . DB_PREFIX . "express_fee_surcharges SET name = '" . $this->db->escape($data['name']). "',month = '" . $this->db->escape($data['month']). "',s_day = '" . $this->db->escape($data['s_day']). "',e_day = '" . $this->db->escape($data['e_day']). "' WHERE express_fee_surcharges_id = '" . (int)$id . "'");
    }

    //快递费-查询快递报价
    public function getExpressFeeSurcharges($id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "express_fee_surcharges WHERE express_fee_surcharges_id=".$id);

        return $query->row;
    }

    //快递费-添加省份加价详情
    public function editExpressFeeSurchargesProvince($data = array(),$id) {
        $provinces = !empty($data['provinces']) ? $this->db->escape(json_encode(array_values($data['provinces']),320)) : '';
        $this->db->query("UPDATE " . DB_PREFIX . "express_fee_surcharges SET  provinces = '" . $provinces . "' WHERE express_fee_surcharges_id = '" . (int)$id . "'");
    }

    //快递费-删除快递加价
    public function delExpressFeeSurcharges($id) {
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "express_fee_surcharges WHERE express_fee_surcharges_id = " . $id);
    }

    //快递费-快递模板列表
    public function getExpressFeeTemplateAll() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "express_fee_template ORDER BY express_fee_template_id DESC");

        return $query->rows;
    }

    //快递费-添加快递模板
    public function addExpressFeeTemplate($data = array()) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "express_fee_template SET name = '" . $this->db->escape($data['name']). "',is_surcharges = '" . $this->db->escape($data['is_surcharges']). "', setting = '" . $this->db->escape(json_encode($data['setting'],320)) . "', date_added = NOW()");
    }

    //快递费-修改快递模板
    public function editExpressFeeTemplate($data = array(),$id) {
        $this->db->query("UPDATE " . DB_PREFIX . "express_fee_template SET name = '" . $this->db->escape($data['name']). "',is_surcharges = '" . $this->db->escape($data['is_surcharges']). "', setting = '" . $this->db->escape(json_encode($data['setting'],320)) . "' WHERE express_fee_template_id = '" . (int)$id . "'");
    }

    //快递费-查询快递模板
    public function getExpressFeeTemplate($id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "express_fee_template WHERE express_fee_template_id=".$id);

        return $query->row;
    }

    //快递费-删除快递模板
    public function delExpressFeeTemplate($id) {
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "express_fee_template WHERE express_fee_template_id = " . $id);
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "express_fee_standard WHERE name = " . $id);
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "express_fee_surcharges WHERE name = " . $id);
    }

    //快递费-列表
    public function getexpressFeeListAll($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "express_fee WHERE 1";
        if (!empty($data['filter_template_id'])) {
            $sql .= " AND express_fee_template_id =" . $this->db->escape($data['filter_template_id']);
        }
        if (!empty($data['filter_date_start'])) {
            $sql .= " AND delivery_date >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND delivery_date <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }
        if (!empty($data['filter_status'])) {
            $sql .= " AND status = " . $this->db->escape($data['filter_status']);
        } else {
            $sql .= " AND status <> 0";
        }

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //快递费-总数
    public function getexpressFeeListAllTotal($data = array()) {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "express_fee WHERE 1";
        if (!empty($data['filter_template_id'])) {
            $sql .= " AND express_fee_template_id =" . $this->db->escape($data['filter_template_id']);
        }
        if (!empty($data['filter_date_start'])) {
            $sql .= " AND delivery_date >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND delivery_date <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }
        if (!empty($data['filter_status'])) {
            $sql .= " AND status = " . $this->db->escape($data['filter_status']);
        } else {
            $sql .= " AND status <> 0";
        }

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        $query = $this->db->query($sql);


        return $query->row['total'];
    }

    //快递费-状态列表
    public function getExpressFeeStatus() {
        $status = [
            1 => '正常',
            2 => '快递费金额错误',
            3 => '加价金额错误',
            4 => '快递费和加价金额错误',
            5 => '不是系统订单',
            6 => '未设置快递标准',
        ];
        return $status;
    }

    //快递费-省份列表
    public function getExpressFeeProvince() {
        $province = [
            1 => '福建',
            2 => '浙江',
            3 => '江苏',
            4 => '广东',
            5 => '深圳',
            6 => '上海',
            7 => '安徽',
            8 => '江西',
            9 => '湖南',
            10 => '湖北',
            11 => '山东',
            12 => '天津',
            13 => '北京',
            14 => '河南',
            15 => '河北',
            16 => '广西',
            17 => '陕西',
            18 => '山西',
            19 => '贵州',
            20 => '四川',
            21 => '重庆',
            22 => '云南',
            23 => '海南',
            24 => '辽宁',
            25 => '吉林',
            26 => '黑龙江',
            27 => '甘肃',
            28 => '宁夏',
            29 => '青海',
            30 => '内蒙古',
            31 => '新疆',
            32 => '西藏',
        ];
        return $province;
    }

    //快递费-导出列表
    public function getExpressFeeExportAll($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "express_fee_export WHERE 1";

        if (!empty($data['filter_template_id'])) {
            $sql .= " AND templates like '%," . $this->db->escape($data['filter_template_id']).",%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND s_month >=" . $this->db->escape($data['filter_date_start']);
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND e_month <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $sql .= " ORDER BY date_added DESC";

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //快递费-导出总数
    public function getExpressFeeExportTotal($data = array()) {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "express_fee_export WHERE 1";

        if (!empty($data['filter_template_id'])) {
            $sql .= " AND templates like '%," . $this->db->escape($data['filter_template_id']).",%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND s_month >=" . $this->db->escape($data['filter_date_start']);
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND e_month <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }
        $sql .= " ORDER BY date_added DESC";

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    //快递费-添加导出
    public function addExpressFeeExport($data = array()) {
        $query = $this->db->query("INSERT INTO " . DB_PREFIX . "express_fee_export SET s_month = '" . $this->db->escape(date("Ym",strtotime($data['date_start']))). "',e_month = '" . $this->db->escape(date("Ym",strtotime($data['date_end']))). "',templates = '," . $this->db->escape(implode(',',$data['templates'])). ",', date_added = NOW()");
        return $query;
    }



    //货款核对-上传文件
    public function addGoodsPriceFile($file_url,$name,$original_name) {
        $query = $this->db->query("INSERT INTO " . DB_PREFIX . "goods_price_file SET link = '" . $this->db->escape($file_url). "',name = '" . $this->db->escape($name). "',original_name = '" . $this->db->escape($original_name). "', date_added = NOW()");
        return $query;
    }

    //货款核对-查询上传文件
    public function getGoodsPriceFile($file_url) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "goods_price_file WHERE link= '". $this->db->escape($file_url). "'");

        return $query->num_rows;
    }

    //货款核对-查询上传文件列表
    public function getGoodsPriceFileList() {
        $sql = "SELECT * FROM " . DB_PREFIX . "goods_price_file";

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //货款核对-查询上传文件总数
    public function getGoodsPriceFileTotal() {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "goods_price_file";

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    //货款核对-删除上传文件
    public function delGoodsPriceUpload($id) {
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "goods_price_file WHERE status=0 AND goods_price_file_id = " . $id);
    }

    //货款核对-工厂模板列表
    public function getGoodsPriceTemplateList() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "goods_price_template ORDER BY goods_price_template_id DESC");

        return $query->rows;
    }

    //货款核对-添加工厂模板
    public function addGoodsPriceTemplate($data = array()) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "goods_price_template SET name = '" . $this->db->escape($data['name']). "', setting = '" . $this->db->escape(json_encode($data['setting'],320)) . "', date_added = NOW()");
    }

    //货款核对-修改工厂模板
    public function editGoodsPriceTemplate($data = array(),$id) {
        $this->db->query("UPDATE " . DB_PREFIX . "goods_price_template SET name = '" . $this->db->escape($data['name']). "', setting = '" . $this->db->escape(json_encode($data['setting'],320)) . "' WHERE goods_price_template_id = '" . (int)$id . "'");
    }

    //货款核对-查询工厂模板
    public function getGoodsPriceTemplate($id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "goods_price_template WHERE goods_price_template_id=".$id);

        return $query->row;
    }

    //货款核对-删除工厂模板
    public function delGoodsPriceTemplate($id) {
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "goods_price_template WHERE goods_price_template_id = " . $id);
    }

    //货款核对-查询货款列表
    public function getgoodsPriceListAll($data=array()) {
        $sql = "SELECT goods_price_statistics_id,status,supplier,check_date,bsku,quantity,total_prices,import_quantity,import_total_prices, spec_name, img_url,name FROM " . DB_PREFIX . "goods_price_statistics gps LEFT JOIN (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl ON (gps.bsku = sl.spec_no) LEFT JOIN (SELECT goods_price_template_id,name FROM " . DB_PREFIX . "goods_price_template) gpt ON (gps.supplier = gpt.goods_price_template_id) WHERE 1";
        if (!empty($data['filter_template_id'])) {
            $sql .= " AND supplier =" . $this->db->escape($data['filter_template_id']);
        }
        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id =" . $this->db->escape($data['filter_store']);
        }
        if (!empty($data['filter_date_start'])) {
            $sql .= " AND check_date >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND check_date <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }
        if (!empty($data['filter_status'])) {
            $sql .= " AND status = " . $this->db->escape($data['filter_status']);
        } else {
            $sql .= " AND status <> 0";
        }

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY check_date";
        }

        if (!empty($data['order']) && ($data['order'] == 'ASC')) {
            $sql .= " ASC";
        } else {
            $sql .= " DESC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //货款核对-查询货款列表总数
    public function getgoodsPriceListAllTotal($data=array()) {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "goods_price_statistics WHERE 1";
        if (!empty($data['filter_template_id'])) {
            $sql .= " AND supplier =" . $this->db->escape($data['filter_template_id']);
        }
        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id =" . $this->db->escape($data['filter_store']);
        }
        if (!empty($data['filter_date_start'])) {
            $sql .= " AND check_date >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND check_date <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }
        if (!empty($data['filter_status'])) {
            $sql .= " AND status = " . $this->db->escape($data['filter_status']);
        } else {
            $sql .= " AND status <> 0";
        }


        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY check_date";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        $query = $this->db->query($sql);
        return $query->row['total'];
    }

    //货款核对-查询货款
    public function getgoodsPriceList ($supplier,$check_date,$bsku) {
        $sql = "SELECT status,supplier,check_date,bsku,quantity,total_prices,name FROM " . DB_PREFIX . "goods_price_statistics gps LEFT JOIN (SELECT goods_price_template_id,name FROM " . DB_PREFIX . "goods_price_template) gpt ON (gps.supplier = gpt.goods_price_template_id) WHERE supplier='".$supplier."' AND check_date='".$check_date."' AND bsku='".$bsku."'";

        $query = $this->db->query($sql);
        return $query->row;
    }

    //货款核对-查询货款对应入库单
    public function getgoodsPriceListStockin ($supplier,$check_date,$bsku) {
        $sql = "SELECT * FROM " . DB_PREFIX . "goods_price_stockin WHERE is_check=0 AND supplier='".$supplier."' AND (check_date='".$check_date."' OR remark LIKE'%".$check_date."%') AND bsku='".$bsku."'";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    //货款核对-查询选中入库单数量和总金额
    public function getgoodsPriceListCheck($supplier,$check_date,$bsku,$ids=array()) {
        $sql = "SELECT bsku,sum(quantity) as quantity,sum(total_prices) as total_prices FROM " . DB_PREFIX . "goods_price_stockin WHERE is_check=0 AND supplier='".$supplier."' AND check_date='".$check_date."' AND bsku='".$bsku."' AND stockin_id in('".implode("','",$ids)."')";

        $query = $this->db->query($sql);
        return $query->row;
    }

    //货款核对-核对
    public function getgoodsPriceCheck($supplier,$check_date,$bsku,$ids=array()) {
        $total_prices_stockin_sql = '';
        $total_prices_sql = '';
        if (!empty($supplier)) {
            $template = $this->getGoodsPriceTemplate($supplier);
            $total_prices_stockin_sql .= " AND supplier = '".$template['name']."'";
            $total_prices_sql .= " AND supplier = '".$supplier."'";
        }
        if (!empty($check_date)) {
//            $total_prices_stockin_sql .= " AND remark LIKE'%".$data['check_date']."%'";
            $total_prices_stockin_sql .= " AND check_date ='".$check_date."'";
            $total_prices_sql .= " AND check_date = '".$check_date."'";
        }
        if (!empty($bsku)) {
            $total_prices_stockin_sql .= " AND bsku = '".$bsku."'";
            $total_prices_sql .= " AND bsku = '".$bsku."'";
        }
        if (!empty($ids)) {
            $total_prices_stockin_sql .= " AND stockin_id in('".implode("','",$ids)."')";
        }

        $this->db->query("UPDATE " . DB_PREFIX . "goods_price_stockin SET is_check = 1 WHERE is_check=0" . $total_prices_stockin_sql);
        $this->db->query("UPDATE " . DB_PREFIX . "goods_price_statistics SET status = 1 WHERE status = 2" . $total_prices_sql);
    }

    //货款核对-查看详情
    public function getGoodsPriceDetailList($supplier,$check_date,$bsku) {
        $sql = "SELECT goods_price_id,supplier,check_date,bsku,quantity,total_prices,spec_name, img_url FROM " . DB_PREFIX . "goods_price gp LEFT JOIN (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl ON (gp.bsku = sl.spec_no) WHERE  supplier='".$supplier."' AND check_date='".$check_date."' AND bsku='".$bsku."'";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    //货款核对-修改sku
    public function editGoodsPriceSku($goods_price_id,$supplier,$check_date,$bsku,$new_sku) {
        $total_prices_stockin_sql = '';
        $total_prices_sql = '';
        $total_prices_stockin_sql2 = '';
        $total_prices_sql2 = '';
        if (!empty($supplier)) {
            $template = $this->getGoodsPriceTemplate($supplier);
            $total_prices_stockin_sql .= " AND supplier = '".$template['name']."'";
            $total_prices_sql .= " AND supplier = '".$supplier."'";
        }
        if (!empty($check_date)) {
//            $total_prices_stockin_sql .= " AND remark LIKE'%".$data['check_date']."%'";
            $total_prices_stockin_sql .= " AND check_date ='".$check_date."'";
            $total_prices_sql .= " AND check_date = '".$check_date."'";
        }
        if (!empty($bsku) && !empty($new_sku)) {
            $total_prices_stockin_sql2 .= " AND (bsku = '".$bsku."' OR bsku = '".$new_sku."')";
            $total_prices_sql2 .= " AND (bsku = '".$bsku."' OR bsku = '".$new_sku."')";
            $this->db->query("UPDATE " . DB_PREFIX . "goods_price_stockin SET is_check = 0 WHERE is_check=1" . $total_prices_stockin_sql.$total_prices_stockin_sql2);
            $this->db->query("UPDATE " . DB_PREFIX . "goods_price SET status = 0 WHERE status = 1" . $total_prices_sql.$total_prices_sql2);
            $this->db->query("DELETE FROM " . DB_PREFIX . "goods_price_statistics WHERE 1" . $total_prices_sql.$total_prices_sql2);
            $this->db->query("UPDATE " . DB_PREFIX . "goods_price SET bsku='".$new_sku."' WHERE goods_price_id=" . $goods_price_id);
        }
        $this->getgoodsPriceStockin();
    }

    //货款核对-核对
    public function getgoodsPriceStockin() {
        $query = $this->db->query("SELECT supplier,check_date,bsku,sum(quantity) as quantity,sum(total_prices) as total_prices FROM " . DB_PREFIX . "goods_price WHERE status = 0 GROUP BY supplier,check_date,bsku");
        if (!empty($query->rows)) {
            foreach ($query->rows as $goodsPrice) {
                $sql = "SELECT bsku,sum(quantity) as quantity,sum(total_prices) as total_prices FROM " . DB_PREFIX . "goods_price_stockin WHERE is_check=0";
                $total_prices_stockin_sql = '';
                $total_prices_sql = '';
                if (!empty($goodsPrice['supplier'])) {
                    $template = $this->getGoodsPriceTemplate($goodsPrice['supplier']);
                    $total_prices_stockin_sql .= " AND supplier = '".$template['name']."'";
                    $total_prices_sql .= " AND supplier = '".$goodsPrice['supplier']."'";

                }
                if (!empty($goodsPrice['check_date'])) {
//            $total_prices_stockin_sql .= " AND remark LIKE'%".$goodsPrice['check_date']."%'";
                    $total_prices_stockin_sql .= " AND check_date ='".$goodsPrice['check_date']."'";
                    $total_prices_sql .= " AND check_date = '".$goodsPrice['check_date']."'";
                }
                if (!empty($goodsPrice['bsku'])) {
                    $total_prices_stockin_sql .= " AND bsku = '".$goodsPrice['bsku']."'";
                    $total_prices_sql .= " AND bsku = '".$goodsPrice['bsku']."'";
                }

                $query = $this->db->query($sql.$total_prices_stockin_sql);
                if (!empty($query->row) && $query->row['quantity'] == $goodsPrice['quantity'] && $query->row['total_prices'] == $goodsPrice['total_prices']) {
                    $this->db->query("UPDATE " . DB_PREFIX . "goods_price_stockin SET is_check = 1 WHERE is_check=0" . $total_prices_stockin_sql);

                    $this->db->query("UPDATE " . DB_PREFIX . "goods_price SET status = 1 WHERE status = 0" . $total_prices_sql);

                    $this->db->query("INSERT INTO " . DB_PREFIX . "goods_price_statistics SET bsku = '" . $goodsPrice['bsku'] . "',quantity = '" . $goodsPrice['quantity'] . "',total_prices = '" . $goodsPrice['total_prices'] . "',check_date = '" . $goodsPrice['check_date'] . "',supplier = '" . $goodsPrice['supplier'] . "',status=1,import_quantity = '" . $goodsPrice['quantity'] . "',import_total_prices = '" . $goodsPrice['total_prices'] . "', date_added = NOW()");
                } else {
                    $this->db->query("UPDATE " . DB_PREFIX . "goods_price SET status = 1 WHERE status = 0" . $total_prices_sql);

                    $this->db->query("INSERT INTO " . DB_PREFIX . "goods_price_statistics SET bsku = '" . $goodsPrice['bsku'] . "',quantity = '" . $goodsPrice['quantity'] . "',total_prices = '" . $goodsPrice['total_prices'] . "',check_date = '" . $goodsPrice['check_date'] . "',supplier = '" . $goodsPrice['supplier'] . "',status=2,import_quantity = '" . $goodsPrice['quantity'] . "',import_total_prices = '" . $goodsPrice['total_prices'] . "', date_added = NOW()");
                }
            }
        }
    }

    //货款核对-修改总数量，总金额
    public function editGoodsPriceImport($goods_price_statistics_id,$quantity,$total_prices) {
        $this->db->query("UPDATE " . DB_PREFIX . "goods_price_statistics SET status = 1,quantity='".$quantity."',total_prices='".$total_prices."' WHERE goods_price_statistics_id=" . $goods_price_statistics_id);
    }

    //货款核对-导出列表
    public function getGoodsPriceExportAll($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "goods_price_export WHERE 1";

        if (!empty($data['filter_template_id'])) {
            $sql .= " AND templates like '%," . $this->db->escape($data['filter_template_id']).",%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND s_month >=" . $this->db->escape($data['filter_date_start']);
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND e_month <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $sql .= " ORDER BY date_added DESC";

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    //货款核对-导出总数
    public function getGoodsPriceExportTotal($data = array()) {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "goods_price_export WHERE 1";

        if (!empty($data['filter_template_id'])) {
            $sql .= " AND templates like '%," . $this->db->escape($data['filter_template_id']).",%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND s_month >=" . $this->db->escape($data['filter_date_start']);
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND e_month <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }
        $sql .= " ORDER BY date_added DESC";

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    //货款核对-添加导出
    public function addGoodsPriceExport($data = array()) {
        $query = $this->db->query("INSERT INTO " . DB_PREFIX . "goods_price_export SET s_month = '" . $this->db->escape(date("Ym",strtotime($data['date_start']))). "',e_month = '" . $this->db->escape(date("Ym",strtotime($data['date_end']))). "',templates = '," . $this->db->escape(implode(',',$data['templates'])). ",', date_added = NOW()");
        return $query;
    }

}