<?php
class ModelAdminRemainder extends Model {
    public function getInList($data = array()) {
        $sql = "SELECT store_id, stockin_date, bsku, stockin_quan, stockin_cost, sale_quan, (stockin_quan - sale_quan) AS remain_quan, spec_name, img_url FROM " . DB_PREFIX . "remainder_in ri LEFT JOIN (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl ON (ri.bsku = sl.spec_no) WHERE status = '1'";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(spec_name, bsku) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND stockin_date >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND stockin_date <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $sort_data = array(
            'store_id',
            'bsku',
            'stockin_date',
            'stockin_cost',
            'stockin_quan',
            'sale_quan',
            'remain_quan'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY stockin_date";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getInTotal($data = array()) {
        $sql = "SELECT count(stockin_id) AS total FROM " . DB_PREFIX . "remainder_in ri LEFT JOIN (SELECT spec_no, spec_name FROM wdt_spec_list WHERE deleted = '0') sl ON (ri.bsku = sl.spec_no) WHERE status = '1'";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(spec_name, bsku) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND stockin_date >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND stockin_date <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getOutList($data = array()) {
        $sql = "SELECT store_id, stockout_month, COUNT(bsku) AS stockout_count, SUM(stockout_quan) AS stockout_quan, MIN(status) AS status FROM " . DB_PREFIX . "remainder_out WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND bsku LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND stockout_month >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND stockout_month <= '" . (int)$data['filter_date_end'] . "'";
        }

        $sql .= " GROUP BY store_id, stockout_month";

        $sort_data = array(
            'store_id',
            'stockout_month',
            'stockout_count',
            'stockout_quan'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY stockout_month";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getOutTotal($data = array()) {
        $sql = "SELECT count(stockout_id) AS total FROM " . DB_PREFIX . "remainder_out WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND bsku LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND stockout_month >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND stockout_month <= '" . (int)$data['filter_date_end'] . "'";
        }

        $sql .= " GROUP BY store_id, stockout_month";

        $query = $this->db->query($sql);

        return $query->num_rows;
    }

    public function getOutDetail($store_id, $stockout_month) {
        $sql = "SELECT stockout_id, bsku, stockout_month, stockout_quan, stockout_cost, status, spec_name, img_url FROM " . DB_PREFIX . "remainder_out ro LEFT JOIN (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl ON (ro.bsku = sl.spec_no) WHERE store_id = '" . (int)$store_id . "' AND stockout_month = '" . (int)$stockout_month . "'";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        $sql .= " ORDER BY stockout_id ASC";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getHandleOuts($store_id, $stockout_month) {
        $sql = "SELECT stockout_id, store_id, bsku, stockout_month, stockout_cost FROM " . DB_PREFIX . "remainder_out WHERE store_id = '" . (int)$store_id . "' AND stockout_month = '" . (int)$stockout_month . "' AND status = '0'";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        $sql .= " ORDER BY stockout_id ASC LIMIT 100";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function updateOutDetail($out_info) {
        $stockin_ids = $cost_rows = [];
        $sale_stock = 0;
        $stockout_quan = (int)$out_info['stockout_quan'];
        
        $query = $this->db->query("SELECT stockin_id, store_id, stockin_cost, stockin_quan - sale_quan AS stock_quan, status FROM " . DB_PREFIX . "remainder_in WHERE bsku = '" . $this->db->escape($out_info['bsku']) . "' AND status > '0' AND stockin_quan > sale_quan ORDER BY stockin_date ASC, stockin_id ASC");

        $queue = [];

        foreach ($query->rows as $row) {
            if ($out_info['store_id'] == $row['store_id']) {
                array_unshift($queue, $row);
            } else {
                array_push($queue, $row);
            }
        }

        foreach ($queue as $row) {
            $diff = $stockout_quan - $sale_stock;

            if ($diff == 0) {
                break;
            } else {
                $stockin_ids[] = $row['stockin_id'];
                $out_quantity = (int)min($diff, (int)$row['stock_quan']);
                $out_total = moneyformat($row['stockin_cost']) * $out_quantity;

                if ($row['status'] == '1') {
                    if ($out_info['store_id'] == $row['store_id']) {
                        $cost_rows[] = ['store_id' => $out_info['store_id'], 'quan' => $out_quantity, 'total' => $out_total, 'type' => '1'];
                    } else {
                        $cost_rows[] = ['store_id' => $out_info['store_id'], 'quan' => $out_quantity, 'total' => $out_total, 'type' => '3'];
                    }
                } elseif ($row['status'] == '2') {
                    if ($out_info['store_id'] == $row['store_id']) {
                        $cost_rows[] = ['store_id' => $out_info['store_id'], 'quan' => $out_quantity, 'total' => $out_total, 'type' => '0'];
                    } else {
                        $cost_rows[] = ['store_id' => $out_info['store_id'], 'quan' => $out_quantity, 'total' => $out_total, 'type' => '4'];
                        
                        $cost_rows[] = ['store_id' => $row['store_id'], 'quan' => $out_quantity, 'total' => -$out_total, 'type' => '2'];
                    }
                }

                $this->db->query("UPDATE " . DB_PREFIX . "remainder_in SET sale_quan = sale_quan + '" . (int)$out_quantity . "' WHERE stockin_id = '" . (int)$row['stockin_id'] . "'");

                $sale_stock += $out_quantity;
            }
        }

        if ($stockout_quan > $sale_stock) {
            $less = (int)($stockout_quan - $sale_stock);
            $cost_rows[] = ['store_id' => $out_info['store_id'], 'quan' => $less, 'total' => moneyformat($out_info['stockout_cost']) * $less, 'type' => '-1'];
        }

        foreach ($cost_rows as $row) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "remainder_cost SET store_id = '" . (int)$row['store_id'] . "', bsku = '" . $this->db->escape($out_info['bsku']) . "', cost_quan = '" . (int)$row['quan'] . "', month = '" . (int)$out_info['stockout_month'] . "', cost_total = '" . (float)$row['total'] . "', cost_type = '" . (int)$row['type'] . "', date_added = NOW()");
        }

        $this->db->query("UPDATE " . DB_PREFIX . "remainder_out SET stockin_ids = '" . $this->db->escape(implode(',', $stockin_ids)) . "', stockout_quan = '" . (int)$stockout_quan . "', status = '1' WHERE stockout_id = '" . (int)$out_info['stockout_id'] . "'");
    }

    public function addOverTimeCost($store_id, $stockout_month) {
        if (strlen($stockout_month) == 6) {
            $date_format = substr($stockout_month, 0, 4) . '-' . substr($stockout_month, 4, 2) . '-01';
        } else {
            $stockout_month = date('Ym', strtotime('-1 month'));
            $date_format = date('Y-m-01', strtotime('-1 month'));
        }
        
        $over_date = date('Y-m-d', strtotime('+1 month', strtotime($date_format)));

        $this->db->query("INSERT INTO " . DB_PREFIX . "remainder_cost SELECT NULL, store_id, bsku, SUM(stockin_quan - sale_quan), SUM((stockin_quan - sale_quan) * stockin_cost), '" . (int)$stockout_month . "', '2', NOW() FROM " . DB_PREFIX . "remainder_in WHERE store_id = '" . (int)$store_id . "' AND stockin_date < '" . $this->db->escape($over_date) . "' AND status = '1' AND stockin_quan > sale_quan GROUP BY store_id, bsku");

        $this->db->query("UPDATE " . DB_PREFIX . "remainder_in SET status = '2' WHERE store_id = '" . (int)$store_id . "' AND stockin_date < '" . $this->db->escape($over_date) . "' AND status = '1' AND stockin_quan > sale_quan");
    }

    public function getCostList($data = array()) {
        $sql = "SELECT store_id, month, cost_type, COUNT(bsku) AS cost_count, SUM(cost_quan) AS cost_quan, SUM(cost_total) AS cost_total FROM " . DB_PREFIX . "remainder_cost WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND bsku LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (isset($data['filter_type']) && ($data['filter_type'] !== '')) {
            $sql .= " AND cost_type = '" . (int)$data['filter_type'] . "'";
        } else {
            $sql .= " AND cost_type > '0'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND month >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND month <= '" . (int)$data['filter_date_end'] . "'";
        }

        $sql .= " GROUP BY store_id, month, cost_type";

        $sort_data = array(
            'store_id',
            'month',
            'cost_type',
            'cost_count',
            'cost_quan',
            'cost_total'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY month";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getCostTotal($data = array()) {
        $sql = "SELECT count(cost_id) AS total FROM " . DB_PREFIX . "remainder_cost WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND bsku LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (isset($data['filter_type']) && ($data['filter_type'] !== '')) {
            $sql .= " AND cost_type = '" . (int)$data['filter_type'] . "'";
        } else {
            $sql .= " AND cost_type > '0'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND month >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND month <= '" . (int)$data['filter_date_end'] . "'";
        }

        $sql .= " GROUP BY store_id, month, cost_type";

        $query = $this->db->query($sql);

        return $query->num_rows;
    }

    public function getCostDetail($store_id, $month, $cost_type) {
        $sql = "SELECT bsku, cost_quan, cost_total, month, cost_type, spec_name, img_url FROM " . DB_PREFIX . "remainder_cost rc LEFT JOIN (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl ON (rc.bsku = sl.spec_no) WHERE store_id = '" . (int)$store_id . "' AND month = '" . (int)$month . "' AND cost_type = '" . (int)$cost_type . "'";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        $sql .= " ORDER BY cost_id ASC";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getMonths() {
        $months = array();

        for ($i=1; $i <= 24; $i++) { 
            $months[] = date('Ym', strtotime('-' . $i . ' month'));
        }

        return $months;
    }

    public function getCostType() {
        return [
            '-1' => '库存超卖',
            '0'  => '抵消预扣',
            '1'  => '库存销售',
            '2'  => '库存预扣',
            '3'  => '共卖库存',
            '4'  => '共卖预扣',
        ];
    }
}