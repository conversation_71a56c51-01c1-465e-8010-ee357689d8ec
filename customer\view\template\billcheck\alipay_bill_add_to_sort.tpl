<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      未分类账单
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <div class="box box-primary">
      <div class="box-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label>备注：</label>
              <input type="text" class="form-control" name="filter_remark" placeholder="搜索备注" value="<?php echo $filter_remark; ?>">
            </div>
          </div>

          <?php if(!empty($business_types)){ ?>
          <div class="col-md-4">
            <div class="form-group">
              <label>业务类型：</label>
              <select class="form-control" name="filter_business_type">
                <option value="0">全部类型</option>
                <?php foreach($business_types as $business_type_k => $business_type_v) { ?>
                <?php if ($business_type_k == $business_type) { ?>
                <option value="<?php echo $business_type_k; ?>" selected="selected"><?php echo $business_type_v; ?></option>
                <?php } else { ?>
                <option value="<?php echo $business_type_k; ?>"><?php echo $business_type_v; ?></option>
                <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          <?php } ?>
        </div>
      </div>
      <!-- /.box-body -->
      <div class="box-footer">
        <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
      </div>
    </div>
    <?php if ($warning) { ?>
    <div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?> </div>
    <?php } ?>
    <div class="box box-primary">
      <!-- /.box-header -->
      <div class="box-body">
        <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-order" class="form-horizontal">
          <div class="table-responsive">
            <table id="plans" class="table table-striped table-bordered table-hover" style="table-layout: fixed">
              <thead>
              <tr>
                <th width="30"><input id="selectAll" class="flat" type="checkbox"></th>
                <td style="width: 60px" class="text-left">账务流水号</td>
                <td style="width: 60px" class="text-left">业务流水号</td>
                <td style="width: 60px" class="text-left">商户订单号</td>
                <td style="width: 60px" class="text-left">商品名称</td>
                <td style="width: 60px" class="text-left">发生时间</td>
                <td style="width: 60px" class="text-left">对方账号</td>
                <td style="width: 60px" class="text-left">收入金额（+元）</td>
                <td style="width: 60px" class="text-left">支出金额（-元）</td>
                <td style="width: 60px" class="text-left">账户余额（元）</td>
                <td style="width: 60px" class="text-left">交易渠道</td>
                <td style="width: 80px" class="text-left">业务类型</td>
                <td style="width: 200px" class="text-left">备注</td>
                <?php if(empty($data['alipay_bill_sort_id'])) { ?>
                <td style="width: 60px" class="text-left">操作</td>
                <?php } ?>
              </tr>
              </thead>
              <tbody>
              <?php if (!empty($details)) { ?>
                <?php foreach ($details as $detail) { ?>
                <tr data-id="<?php echo $detail['accounting_serial_number']; ?>" style="word-wrap: break-word;">
                  <td><input class="flat" type="checkbox" name="selected[]" value="<?php echo $detail['accounting_serial_number']; ?>"></td>
                  <td class="text-left"><?php echo $detail['accounting_serial_number']; ?></td>
                  <td class="text-left"><?php echo $detail['service_serial_number']; ?></td>
                  <td class="text-left"><?php echo $detail['merchant_order_number']; ?></td>
                  <td class="text-left"><?php echo $detail['product_name']; ?></td>
                  <td class="text-left"><?php echo $detail['occurrence_time']; ?></td>
                  <td class="text-left"><?php echo $detail['reciprocal_account']; ?></td>
                  <td class="text-left"><?php echo $detail['income']; ?></td>
                  <td class="text-left"><?php echo $detail['disburse']; ?></td>
                  <td class="text-left"><?php echo $detail['balance']; ?></td>
                  <td class="text-left"><?php echo $detail['transaction_channel']; ?></td>
                  <td class="text-left"><?php echo $detail['business_type']; ?></td>
                  <td class="text-left"><?php echo $detail['remark']; ?></td>
                  <?php if(empty($data['alipay_bill_sort_id'])) { ?>
                  <td class="text-right">
                    <button class="btn btn-success" type="button" data-toggle="modal" data-target="#add-modal">选择类别</button>
                  </td>
                  <?php } ?>
                </tr>
                <?php } ?>
              <?php } ?>
              </tbody>
            </table>
          </div>
          <?php if(!empty($data['alipay_bill_sort_id'])) { ?>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" type="submit">确认</button>
              <a href="<?php echo $action; ?>" class="btn btn-default">重置数据</a>
            </div>
          </div>
          <?php } ?>
        </form>
      </div>
      <!-- /.box-body -->
    </div>

    <!-- 添加到类别 -->
    <div class="modal modal-warning fade" id="add-modal">
      <div class="modal-dialog">
        <div class="modal-content">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-add" class="form-horizontal">
            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title">请填写类别信息</h4>
            </div>
            <div class="modal-body">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-quan">操作：</label>
                <div class="col-sm-8">
                  <div class="radio">
                    <label><input type="radio" name="add_sort_type" checked onchange="to_sort_type($(this))" value="1" checked>选择类别</label>
                    <label style="margin-left: 10px"><input type="radio" name="add_sort_type" onchange="to_sort_type($(this))" value="2">新增类别</label>
                  </div>
                </div>
              </div>
              <div class="form-group type-1">
                <label class="col-sm-2 control-label" for="input-store">选择类别：</label>
                <div class="col-sm-8">
                  <select name="alipay_bill_sort_id" id="input-sort" class="form-control">
                    <option value="">请选择类别</option>
                    <?php if(!empty($bill_keys)){ ?>
                    <?php foreach($bill_keys as $bill_key) { ?>
                    <option value="<?php echo $bill_key['alipay_bill_sort_id']; ?>"><?php echo $bill_key['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="form-group type-2" style="display: none">
                <label class="col-sm-2 control-label" for="input-product">新增类别：</label>
                <div class="col-sm-8">
                  <input type="text" value="" placeholder="输入类别" name="add_sort" id="input-add_sort" class="form-control" />
                </div>
              </div>
              <input id="add-id" name="accounting_serial_number" type="hidden" value="">
            <div class="modal-footer">
              <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">取消</button>
              <button id="add-yes" type="button" class="btn btn-outline">提交保存</button>
            </div>
          </form>
        </div>
        <!-- /.modal-content -->
      </div>
      <!-- /.modal-dialog -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript"><!--
  function to_sort_type(obj) {
    if (obj.val() == 1) {
      $(".type-1").show()
      $(".type-2").hide()
    } else {
      $(".type-1").hide()
      $(".type-2").show()
    }
  }

  (function () {
    // 全选操作
    $('#selectAll').on('ifChecked', function() {
      $('input.flat').iCheck('check')
    })
    $('#selectAll').on('ifUnchecked', function() {
      $('input.flat').iCheck('uncheck')
    })

    $('#add-modal').on('show.bs.modal', function(event) {
      $('#input-sort').val('');
      $('#add-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#add-yes').on('click', () => {$('#form-add').submit()})

    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_remark = $('input[name=\'filter_remark\']').val();

      if (filter_remark) {
        url += '&filter_remark=' + encodeURIComponent(filter_remark);
      }

      var filter_business_type = $('select[name=\'filter_business_type\']').val();

      if (filter_business_type != '') {
        url += '&filter_business_type=' + encodeURIComponent(filter_business_type);
      }


      location.href = url;
    });
  })()

  //--></script>
<?php echo $footer; ?>