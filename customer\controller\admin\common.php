<?php
class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends Controller {
    public function router() {
        if (isset($this->request->get['method'])) {
            $route = trim($this->request->get['method'], '/');
        } else if (isset($this->request->get['route'])) {
            $route = trim($this->request->get['route'], '/');
        } else {
            $route = 'admin/common/dashboard';
        }

        $this->request->get['route'] = $route;

        return new Action(strtolower($route));
    }

    public function dashboard() {
        $query = $this->db->query("SELECT route FROM " . DB_PREFIX . "user_group WHERE user_group_id = '" . (int)$this->user->user_group_id . "'");

        if (!empty($query->row['route'])) {
            $this->response->redirect($this->url->link($query->row['route'], 'token=' . $this->session->data['token']));
        } else {
            $this->response->redirect($this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token']));
        }
    }

    public function login() {
        $data['error_warning'] = ''; 

        // if ((isset($this->session->data['token']) && !isset($this->request->get['token'])) || ((isset($this->request->get['token']) && (isset($this->session->data['token']) && ($this->request->get['token'] != $this->session->data['token']))))) {
        //     $data['error_warning'] = $this->language->get('error_token');
        // }

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $union = false;

            if (!$union && !empty($this->request->post['username']) && !empty($this->request->post['password'])) {
                $union = $this->user->loginByPwd($this->request->post['username'], $this->request->post['password']);
            }

            if (!$union && !empty($this->request->post['telephone']) && !empty($this->request->post['code']) && !empty($this->session->data['code_check']) && $this->session->data['code_check'] = md5($this->request->post['telephone'] . $this->request->post['code'])) {
                $union = $this->user->loginBySms($this->request->post['telephone'], $this->request->post['code']);
            }

            if ($union && empty($this->request->get['redirectURL'])) {
                $this->request->get['redirectURL'] = urlencode($this->url->link('admin/common/dashboard'));
            }

            $data['error_warning'] = $this->language->get('error_login');
        }

        $data['action'] = $this->url->link('admin/common/login');        
        $data['getcode'] = $this->url->link('admin/common/getSmsCode');

        if (!empty($this->request->get['redirectURL'])) {
            $redirect = urldecode($this->request->get['redirectURL']);

            $data['action'] .= strpos($data['action'], '?') === false ? '?' : '&';
            $data['action'] .= 'redirectURL=' . urlencode($redirect);

            if (!$this->user->login_token && !empty($this->request->cookie['autoToken'])) {
                $this->user->loginByToken($this->request->cookie['autoToken']);
            }

            if ($this->user->login_token) {
                $redirect .= strpos($redirect, '?') === false ? '?' : '&';
                $redirect .= 'login_token=' . $this->user->login_token;

                $this->response->redirect($redirect);
            }
        }

        if ($this->user->isLogged() && isset($this->request->get['token']) && ($this->request->get['token'] == $this->session->data['token'])) {
            $this->response->redirect($this->url->link('admin/common/dashboard', 'token=' . $this->session->data['token']));
        }

        if (isset($this->request->post['telephone'])) {
            $data['telephone'] = $this->request->post['telephone'];
        } else {
            $data['telephone'] = '';
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('common/login.tpl', $data));
    }

    public function logout() {
        $this->user->logout();

        $this->response->redirect($this->url->link('admin/common/login'));
    }

    public function password() {
        $this->load->model('admin/user');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validatePassword()) {
            $this->model_admin_user->editPassword($this->user->union_id, $this->request->post['password']);

            $this->session->data['success'] = $this->language->get('text_edit_success');
        }

        if (isset($this->session->data['warning'])) {
            $data['warning'] = $this->session->data['warning'];

            unset($this->session->data['warning']);
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['action'] = $this->url->link('admin/common/password', 'token=' . $this->session->data['token']);

        if (isset($this->request->post['oldPassword'])) {
            $data['oldPassword'] = $this->request->post['oldPassword'];
        } else {
            $data['oldPassword'] = '';
        }

        if (isset($this->request->post['password'])) {
            $data['password'] = $this->request->post['password'];
        } else {
            $data['password'] = '';
        }

        if (isset($this->request->post['confirm'])) {
            $data['confirm'] = $this->request->post['confirm'];
        } else {
            $data['confirm'] = '';
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('common/password.tpl', $data));
    }

    public function errorLog() {
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $file = DIR_LOGS . $this->config->get('config_error_filename');

            $handle = fopen($file, 'w+');

            fclose($handle);

            $this->session->data['success'] = $this->language->get('text_delete_success');
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['clear'] = $this->url->link('admin/common/errorLog', 'token=' . $this->session->data['token']);
        
        $data['warning'] = '';
        $data['log'] = '';

        $file = DIR_LOGS . $this->config->get('config_error_filename');

        if (file_exists($file)) {
            $size = filesize($file);

            if ($size >= 5242880) {
                $suffix = array(
                    'B',
                    'KB',
                    'MB',
                    'GB',
                    'TB',
                    'PB',
                    'EB',
                    'ZB',
                    'YB'
                );

                $i = 0;

                while (($size / 1024) > 1) {
                    $size = $size / 1024;
                    $i++;
                }

                $data['warning'] = sprintf($this->language->get('error_log_size'), basename($file), round(substr($size, 0, strpos($size, '.') + 4), 2) . $suffix[$i]);
            } else {
                $data['log'] = file_get_contents($file, FILE_USE_INCLUDE_PATH, null);
            }
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('common/error_log.tpl', $data));
    }

    protected function validatePassword() {
        $user_info = $this->model_admin_user->getUser($this->user->user_id);
        $str_pwd = sha1($user_info['salt'] . sha1($this->request->post['oldPassword']));

        if ($user_info['password'] != $str_pwd) {
            $this->session->data['warning'] = $this->language->get('error_password_verify');
            return false;
        }

        if ((utf8_strlen($this->request->post['password']) < 4) || (utf8_strlen($this->request->post['password']) > 20)) {
            $this->session->data['warning'] = $this->language->get('error_password_length');
            return false;
        }

        if ($this->request->post['password'] != $this->request->post['confirm']) {
            $this->session->data['warning'] = $this->language->get('error_password_confirm');
            return false;
        }

        return true;
    }

    public function permission() {
        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('common/permission.tpl', $data));
    }

    public function notFound() {
        $data['logout'] = $this->url->link('admin/common/logout', 'token=' . $this->session->data['token']);
        
        $data['header'] = $this->load->controller('admin/template/header');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('common/not_found.tpl', $data));
    }

    public function getSmsCode() {
        $json = array();

        if (!empty($this->request->post['telephone'])) {
            $telephone = $this->request->post['telephone'];
            $available = $this->user->isAvailable($telephone);
            
            if ($available) {
                $rand = (string)mt_rand(1000, 9999);
                $this->session->data['code_check'] = md5($telephone . $rand);
                $this->load->helper('qcloudsms/autoload');

                try {
                    $cred = new TencentCloud\Common\Credential('AKIDscN4iCbq2JeqwvRlS5X1azVr10ylk9b1', 'PdkroZ9fWCkBRegWBNODvhIjz4lIdqzy');
                    $client = new TencentCloud\Sms\V20210111\SmsClient($cred, 'ap-guangzhou');
                    $req = new TencentCloud\Sms\V20210111\Models\SendSmsRequest();
                    $req->SmsSdkAppId = '1400158166';
                    $req->SignName = '如果家居';
                    $req->TemplateId = '225212';
                    $req->TemplateParamSet = array((string)$rand);
                    $req->PhoneNumberSet = array('+86' . $telephone);
                    $res = $client->SendSms($req);
                    $result = $res->serialize();

                    if (isset($result['SendStatusSet'][0])) {
                        if ($result['SendStatusSet'][0]['Code'] == 'Ok') {
                            $json['success'] = true;
                        } else {
                            $json['message'] = $result['SendStatusSet'][0]['Message'];
                        }

                        $this->db->query("INSERT INTO _union_sms SET union_id = '" . (int)$available . "', sms_code = '" . $this->db->escape($rand) . "', serial_no = '" . $this->db->escape($result['SendStatusSet'][0]['SerialNo']) . "', phone_number = '" . $this->db->escape($result['SendStatusSet'][0]['PhoneNumber']) . "', message = '" . $this->db->escape($result['SendStatusSet'][0]['Message']) . "', request_id = '" . $this->db->escape($result['RequestId']) . "', status = '0', date_added = NOW()");
                    }
                } catch (TencentCloud\Common\Exception\TencentCloudSDKException $e) {
                    $this->log->write($e);
                }
            } else {
                $json['disable'] = true;
            }
        }

        $this->response->setOutJson($json);
    }

    public function getUploadToken() {
        $json = array();
        $json['uploadToken'] = QiNiu::getUploadToken();
        $json['httpHost'] = HTTP_IMAGE;

        $this->response->setOutJson($json);
    }

    public function checkPermission() {
        if (isset($this->request->get['method']) || isset($this->request->get['route'])) {
            if (isset($this->request->get['method'])) {
                $part = explode('/', trim($this->request->get['method'], '/'));
            } else if (isset($this->request->get['route'])) {
                $part = explode('/', trim($this->request->get['route'], '/'));
            }

            $route = '';

            if (isset($part[0])) {
                $route .= $part[0];
            }

            if (isset($part[1])) {
                $route .= '/' . $part[1];
            }

            // if (isset($part[2])) {
            //     $route .= '/' . $part[2];
            // }


            $ignore = array(
                'admin/common',
                'admin/fileManager',
                'goods/auth',
                'goods/list'
            );
       

            if (!in_array($route, $ignore) && !$this->user->hasPermission('access', $route)) {
                return new Action('admin/common/permission');
            }
        }
    }

    public function checkLogin() {
        if (isset($this->request->get['method'])) {
            $route = trim($this->request->get['method'], '/');
        } else if (isset($this->request->get['route'])) {
            $route = trim($this->request->get['route'], '/');
        } else {
            $route = '';
        }

        $ignore = array(
            'admin/common/login',
            'admin/common/getSmsCode',
            'admin/common/notFound',
            'goods/auth',
            'goods/list',
        );

        if (in_array($route, $ignore)) {
            return;
        }


        if ($this->user->isLogged() && isset($this->request->get['token']) && isset($this->session->data['token']) && ($this->request->get['token'] == $this->session->data['token'])) {
            if ($route) {
                $this->user->addUnionAccess($route, $this->request->request);
            }
        } else {
            $logged = false;

            unset($this->request->get['method']);
            unset($this->request->get['route']);
            unset($this->request->get['token']);

            if (isset($this->request->get['login_token'])) {
                $login_token = $this->request->get['login_token'];
                unset($this->request->get['login_token']);

                $logged = $this->user->loginUnion($login_token, $this->url->link($route, http_build_query($this->request->get)));

                if (!$logged) {
                    return new Action('admin/common/notFound');
                }
            } elseif ($this->user->isLogged() && isset($this->session->data['token']) && isset($this->session->data['login_token'])) {
                $union_query = $this->db->query("SELECT union_login_id, union_id FROM _union_login WHERE login_token = '" . $this->db->escape($this->session->data['login_token']) . "' AND session_token = '" . $this->db->escape($this->session->data['token']) . "' AND date_added >= DATE_SUB(NOW(), INTERVAL 120 MINUTE)");

                if ($union_query->num_rows) {
                    $logged = true;
                }
            }

            if ($logged) {
                $this->request->get['token'] = $this->session->data['token'];

                $this->response->redirect($this->url->link($route, http_build_query($this->request->get)));
            } else {
                $redirect = defined('HTTP_LOGIN') ? HTTP_LOGIN : 'http://crm.roogo.cn/admin/common/login';
                $redirect .= strpos($redirect, '?') === false ? '?' : '&';
                $redirect .= 'redirectURL=' . urlencode($this->url->link($route, http_build_query($this->request->get)));

                $this->response->redirect($redirect);
            }
        }
    }
}