<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        库存列表
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="输入产品编码或名称查找" value="<?php echo $filter_name; ?>">
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">产品列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <div class="table-responsive">
          <table id="stock" class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>图片</th>
              <th>产品名称<?php if ($sort == 'bsku') { ?>
                  <a href="<?php echo $sort_bsku; ?>" class="<?php echo strtolower($order); ?>">编码</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_bsku; ?>">编码</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'stock_quan') { ?>
                  <a href="<?php echo $sort_stock; ?>" class="<?php echo strtolower($order); ?>">总库存</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_stock; ?>">总库存</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'order_quan') { ?>
                  <a href="<?php echo $sort_order; ?>" class="<?php echo strtolower($order); ?>">总备货</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_order; ?>">总备货</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'sale_quan') { ?>
                  <a href="<?php echo $sort_sale; ?>" class="<?php echo strtolower($order); ?>">总销量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_sale; ?>">总销量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'avaliable_num') { ?>
                  <a href="<?php echo $sort_avaliable; ?>" class="<?php echo strtolower($order); ?>">可发库存</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_avaliable; ?>">可发库存</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'order_num') { ?>
                  <a href="<?php echo $sort_ordered; ?>" class="<?php echo strtolower($order); ?>">待审核量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_ordered; ?>">待审核量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'purchase_num') { ?>
                  <a href="<?php echo $sort_purchase; ?>" class="<?php echo strtolower($order); ?>">采购在途</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_purchase; ?>">采购在途</a>
                <?php } ?>
              </th>
            </tr>
            <?php if (!empty($stocks)) { ?>
            <?php foreach ($stocks as $stock) { ?>
            <tr>
              <td><img width="100" src="<?php echo $stock['img_url']; ?>" class="img-thumbnail"></td>
              <td><?php echo $stock['spec_name']; ?><br><?php echo $stock['bsku']; ?></td>
              <td><div class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  <?php echo $stock['stock_quan']; ?>
                  <span class="caret"></span>
                </a>
                <ul class="dropdown-menu">
                  <?php foreach ($stock['stores'] as $list) { ?>
                  <li><a href="javascript:void(0);"><?php echo $list['store']; ?>：<?php echo $list['stock']; ?></a></li>
                  <?php } ?>
                </ul>
              </div></td>
              <td><?php echo $stock['order_quan']; ?></td>
              <td><?php echo $stock['sale_quan']; ?></td>
              <td><?php echo (int)$stock['avaliable_num']; ?></td>
              <td><?php echo (int)$stock['order_num']; ?></td>
              <td><?php echo (int)$stock['purchase_num']; ?></td>              
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="8" align="center"> 暂无产品数据 </td>
            <?php } ?>
          </tbody></table>
          </div>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
(function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      location.href = url;
    });
})()
</script>
<?php echo $footer; ?>