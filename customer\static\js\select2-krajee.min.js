/*!
 * @copyright Copyright &copy; <PERSON><PERSON><PERSON>, Krajee.com, 2014 - 2016
 * @version 2.0.8
 *
 * Additional enhancements for Select2 widget extension for Yii 2.0.
 *
 * Author: <PERSON><PERSON><PERSON>
 * For more JQuery plugins visit http://plugins.krajee.com
 * For more Yii related demos visit http://demos.krajee.com
 */var initS2ToggleAll=function(){},initS2Order=function(){},initS2Loading=function(){},initS2Open=function(){},initS2Unselect=function(){};!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(window.jQuery)}(function(e){"use strict";initS2ToggleAll=function(t){var n=e("#"+t),s="#s2-togall-"+t,l=e(s);n.attr("multiple")&&(n.on("select2:open.krajees2",function(){l.parent().attr("id")!=="parent-"+s&&n.attr("multiple")&&(e("#select2-"+t+"-results").closest(".select2-dropdown").prepend(l),e("#parent-"+s).remove())}).on("change.krajeeselect2",function(){if(n.attr("multiple")){var t=0,s=n.val()?n.val().length:0;l.removeClass("s2-togall-select s2-togall-unselect"),n.find("option:enabled").each(function(){e(this).val().length&&t++}),0===t||s!==t?l.addClass("s2-togall-select"):l.addClass("s2-togall-unselect")}}),l.off(".krajees2").on("click.krajees2",function(){var t=l.hasClass("s2-togall-select"),s=!0,o="selectall";t||(s=!1,o="unselectall"),n.find("option").each(function(){var t=e(this);!t.attr("disabled")&&t.val().length&&t.prop("selected",s)}),n.select2("close").trigger("krajeeselect2:"+o).trigger("change")}))},initS2Open=function(){var t,n,s=e(this),l=e(".select2-container--open"),o=s.parents("[class*='has-']");if(o.length)for(t=o[0].className.split(/\s+/),n=0;n<t.length;n++)t[n].match("has-")&&l.removeClass("has-success has-error has-warning").addClass(t[n]);s.data("unselecting")&&(s.removeData("unselecting"),s.select2("close").trigger("krajeeselect2:cleared"))},initS2Unselect=function(){e(this).data("unselecting",!0)},initS2Order=function(t,n){var s=e("#"+t);n&&n.length&&(e.each(n,function(e,t){s.find('option[value="'+t+'"]').appendTo(s)}),s.find("option:not(:selected)").appendTo(s))},initS2Loading=function(t,n){var s=window[n]||{},l=s.themeCss,o=s.sizeCss,a=s.doOrder,i=s.doReset,r=s.doToggle,c=e("#"+t),d=e(l),u=e(".kv-plugin-loading.loading-"+t),g=e(".group-"+t);c.off(".krajees2"),d.length||c.show(),g.length&&g.removeClass("kv-input-group-hide").removeClass(".group-"+t),u.length&&u.remove(),o&&c.next(l).removeClass(o).addClass(o),i&&c.closest("form").off(".krajees2").on("reset.krajees2",function(){setTimeout(function(){c.trigger("change").trigger("krajeeselect2:reset")},100)}),r&&initS2ToggleAll(t),a&&c.on("select2:select.krajees2 select2:unselect.krajees2",function(t){var n=e(t.params.data.element);n&&n.length&&(n.detach(),c.append(n).find("option:not(:selected)").appendTo(c))}),c.on("select2:open.krajees2",initS2Open).on("select2:unselecting.krajees2",initS2Unselect)}});