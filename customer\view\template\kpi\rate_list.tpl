<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        评分管理
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索考核人或评分人" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>考核指标：</label>
                  <select class="form-control" name="filter_item">
                    <option value="*">全部指标</option>
                    <?php foreach ($items as $item) { ?>
                    <?php if ($item['item_id'] == $filter_item) { ?>
                    <option value="<?php echo $item['item_id']; ?>" selected="selected"><?php echo $item['item_name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $item['item_id']; ?>"><?php echo $item['item_name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>状态：</label>
                  <select class="form-control" name="filter_status">
                    <option value="*">全部状态</option>
                    <?php if ($filter_status === '0') { ?>
                    <option value="0" selected="selected">待评分</option>
                    <?php } else { ?>
                    <option value="0">待评分</option>
                    <?php } ?>
                    <?php if ($filter_status == '1') { ?>
                    <option value="1" selected="selected">已评分</option>
                    <?php } else { ?>
                    <option value="1">已评分</option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>起始年月：</label>
                  <select class="form-control" name="filter_date_start">
                    <option value="*">不限年月</option>
                    <?php foreach($months as $month) { ?>
                    <?php if ($month == $filter_date_start) { ?>
                    <option value="<?php echo $month; ?>" selected="selected"><?php echo $month; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $month; ?>"><?php echo $month; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>截止年月：</label>
                  <select class="form-control" name="filter_date_end">
                    <option value="*">不限年月</option>
                    <?php foreach($months as $month) { ?>
                    <?php if ($month == $filter_date_end) { ?>
                    <option value="<?php echo $month; ?>" selected="selected"><?php echo $month; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $month; ?>"><?php echo $month; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">评分列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>考核指标</th>
              <th>考核人</th>
              <th>评分人</th>
              <th>所属年月</th>
              <th>分数</th>
              <th>说明</th>
              <th class="text-right">状态</th>
            </tr>
            <?php if (!empty($rates)) { ?>
              <?php foreach ($rates as $rate) { ?>
                <tr data-id="<?php echo $rate['score_id']; ?>">
                  <td><?php echo $rate['item']; ?></td>
                  <td><?php echo $rate['user']; ?></td>
                  <td><?php echo $rate['rater']; ?></td>
                  <td><?php echo $rate['month']; ?></td>
                  <?php if ($rate['status'] == '1') { ?>
                  <td><?php echo $rate['score']; ?></td>
                  <td><?php echo $rate['remark']; ?></td>
                  <!-- <td><?php echo $rate['date_added']; ?></td> -->
                  <td class="text-right">已评分</td>
                  <?php } else { ?>
                  <td>--</td>
                  <td>--</td>
                  <td class="text-right">
                    <?php if ($rate['israter']) { ?>             
                    <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">提交评分</button>
                    <?php } else { ?>
                    待评分
                    <?php } ?>
                  </td>
                  <?php } ?>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="7" align="center"> 暂无评分数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-del" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">请填写评分信息</h4>
              </div>
              <div class="modal-body">
                <input type="hidden" name="score_id" id="score-id" value="">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-date-start">分数*：</label>
                  <div class="col-sm-8">
                    <input type="number" name="score" value="" placeholder="请填写分数0-100" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">说明：</label>
                  <div class="col-sm-8">
                    <textarea name="remark" rows="5" placeholder="请填写评分说明，非必填" class="form-control"></textarea>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">关闭</button>
                <button id="del-yes" type="button" class="btn btn-outline">提交</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_item = $('select[name=\'filter_item\']').val();

      if (filter_item != '*') {
        url += '&filter_item=' + encodeURIComponent(filter_item);
      }

      var filter_date_start = $('select[name=\'filter_date_start\']').val();
  
      if (filter_date_start != '*') {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('select[name=\'filter_date_end\']').val();
  
      if (filter_date_end != '*') {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      var filter_status = $('select[name=\'filter_status\']').val();

      if (filter_status != '*') {
        url += '&filter_status=' + encodeURIComponent(filter_status);
      }

      location.href = url;
    });

    $('#del-modal').on('show.bs.modal', function(event) {
      $('#score-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
  })()
</script>
<?php echo $footer; ?>