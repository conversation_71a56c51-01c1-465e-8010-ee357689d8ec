<?php
class ModelAdminSetting extends Model {
	public function getStore($store_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "store WHERE store_id = '" . (int)$store_id . "'");

		return $query->row;
	}

	public function getStores($data = array()) {
		$sql = "SELECT * FROM " . DB_PREFIX . "store WHERE state = '1'";

		if ($this->user->store_ids && !isset($data['all'])) {
			$sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalStores() {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "store WHERE state = '1'";

		if ($this->user->store_ids && !isset($data['all'])) {
			$sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
		}
		
		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function getWarehouses($data = array()) {
		$sql = "SELECT warehouse_id, warehouse_no, name FROM wdt_warehouse";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}
}