<?php
class ModelAdminUser extends Model {	
	public function addUser($data) {
		if (!empty($data['store_ids'])) {
			$store_ids = implode(',', $data['store_ids']);
		} else {
			$store_ids = '';
		}

		if (isset($data['union_id'])) {
			$union_id = $data['union_id'];
		} else {
			$this->db->query("INSERT INTO _union SET real_name = '" . $this->db->escape($data['real_name']) . "', telephone = '" . $this->db->escape($data['username']) . "', salt = '" . $this->db->escape($salt = token(9)) . "', password = '" . $this->db->escape(sha1($salt . sha1($data['password']))) . "', initpwd = '" . $this->db->escape($data['password']) . "', token = '', date_added = NOW()");

			$union_id = $this->db->getLastId();
		}

		$this->db->query("INSERT INTO " . DB_PREFIX . "union_info SET union_id = '" . (int)$union_id . "', user_group_id = '" . (int)$data['user_group_id'] . "', username = '" . $this->db->escape($data['username']) . "', store_ids = '" . $this->db->escape($store_ids) . "', kpi_item_ids = '', status = '" . (int)$data['status'] . "', date_added = NOW(), date_modified = NOW()");
	}

	public function editUser($user_id, $data) {
		if (!empty($data['store_ids'])) {
			$store_ids = implode(',', $data['store_ids']);
		} else {
			$store_ids = '';
		}

		$this->db->query("UPDATE " . DB_PREFIX . "union_info SET user_group_id = '" . (int)$data['user_group_id'] . "', username = '" . $this->db->escape($data['username']) . "', store_ids = '" . $this->db->escape($store_ids) . "', status = '" . (int)$data['status'] . "', date_modified = NOW() WHERE user_id = '" . (int)$user_id . "'");

		if ($data['password']) {
			$this->db->query("UPDATE _union SET real_name = '" . $this->db->escape($data['real_name']) . "', telephone = '" . $this->db->escape($data['username']) . "', salt = '" . $this->db->escape($salt = token(9)) . "', password = '" . $this->db->escape(sha1($salt . sha1($data['password']))) . "' WHERE union_id = '" . (int)$data['union_id'] . "'");
		} else {
			$this->db->query("UPDATE _union SET real_name = '" . $this->db->escape($data['real_name']) . "', telephone = '" . $this->db->escape($data['username']) . "' WHERE union_id = '" . (int)$data['union_id'] . "'");
		}
	}

	public function editPassword($union_id, $password) {
		$this->db->query("UPDATE _union SET salt = '" . $this->db->escape($salt = token(9)) . "', password = '" . $this->db->escape(sha1($salt . sha1($password))) . "' WHERE union_id = '" . (int)$union_id . "'");
	}
	
	public function deleteUser($user_id) {
		$this->db->query("UPDATE " . DB_PREFIX . "union_info SET status = '-1' WHERE user_id = '" . (int)$user_id . "'");
		// $this->db->query("DELETE FROM `" . DB_PREFIX . "union_info` WHERE user_id = '" . (int)$user_id . "'");
	}

	public function getUser($user_id) {
		$query = $this->db->query("SELECT * FROM _union u LEFT JOIN " . DB_PREFIX . "union_info ui ON (u.union_id = ui.union_id) WHERE ui.user_id = '" . (int)$user_id . "'");

		return $query->row;
	}
	
	public function getUserByUsername($username) {
		$query = $this->db->query("SELECT u.union_id, ui.user_id FROM _union u LEFT JOIN " . DB_PREFIX . "union_info ui ON (u.union_id = ui.union_id) WHERE u.telephone = '" . $this->db->escape($username) . "'");

		return $query->row;
	}

	public function getUsers($data = array()) {
		$sql = "SELECT *, (SELECT real_name FROM _union WHERE union_id = ui.union_id) AS real_name, (SELECT name FROM `" . DB_PREFIX . "user_group` WHERE user_group_id = ui.user_group_id) AS user_group FROM `" . DB_PREFIX . "union_info` ui WHERE ui.status = '1'";

		$sql .= " ORDER BY user_id DESC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalUsers() {
		$sql = "SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "union_info` WHERE status = '1'";
		
		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function getTotalUsersByGroupId($user_group_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "union_info` WHERE user_group_id = '" . (int)$user_group_id . "'");

		return $query->row['total'];
	}

	public function addUserGroup($data) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "user_group SET name = '" . $this->db->escape($data['name']) . "', permission = '" . (isset($data['permission']) ? $this->db->escape(json_encode($data['permission'])) : '') . "'");
	}

	public function editUserGroup($user_group_id, $data) {
		$this->db->query("UPDATE " . DB_PREFIX . "user_group SET name = '" . $this->db->escape($data['name']) . "', permission = '" . (isset($data['permission']) ? $this->db->escape(json_encode($data['permission'])) : '') . "' WHERE user_group_id = '" . (int)$user_group_id . "'");
	}

	public function deleteUserGroup($user_group_id) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "user_group WHERE user_group_id = '" . (int)$user_group_id . "'");
	}

	public function getUserGroup($user_group_id) {
		$query = $this->db->query("SELECT DISTINCT * FROM " . DB_PREFIX . "user_group WHERE user_group_id = '" . (int)$user_group_id . "'");

		$user_group = array(
			'name'       => $query->row['name'],
			'permission' => json_decode($query->row['permission'], true)
		);

		return $user_group;
	}

	public function getUserGroups($data = array()) {
		$sql = "SELECT * FROM " . DB_PREFIX . "user_group";

		$sql .= " ORDER BY name";

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalUserGroups() {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "user_group");

		return $query->row['total'];
	}

	public function addPermission($user_group_id, $type, $route) {
		$user_group_query = $this->db->query("SELECT DISTINCT * FROM " . DB_PREFIX . "user_group WHERE user_group_id = '" . (int)$user_group_id . "'");

		if ($user_group_query->num_rows) {
			$data = json_decode($user_group_query->row['permission'], true);

			$data[$type][] = $route;

			$this->db->query("UPDATE " . DB_PREFIX . "user_group SET permission = '" . $this->db->escape(json_encode($data)) . "' WHERE user_group_id = '" . (int)$user_group_id . "'");
		}
	}

	public function removePermission($user_group_id, $type, $route) {
		$user_group_query = $this->db->query("SELECT DISTINCT * FROM " . DB_PREFIX . "user_group WHERE user_group_id = '" . (int)$user_group_id . "'");

		if ($user_group_query->num_rows) {
			$data = json_decode($user_group_query->row['permission'], true);

			$data[$type] = array_diff($data[$type], array($route));

			$this->db->query("UPDATE " . DB_PREFIX . "user_group SET permission = '" . $this->db->escape(json_encode($data)) . "' WHERE user_group_id = '" . (int)$user_group_id . "'");
		}
	}
}