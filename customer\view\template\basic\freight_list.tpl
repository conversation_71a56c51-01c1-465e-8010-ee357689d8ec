<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      运费试算
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <div class="box box-primary">
      <div class="box-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label>订单号：</label>
              <input type="text" class="form-control" name="filter_name" placeholder="订单号" value="<?php echo $filter_name; ?>">
            </div>
          </div>

          <div class="col-md-3">
            <div class="form-group">
              <label>状态：</label>
              <select class="form-control" name="filter_status">
                <option value="*" <?php if($filter_status=='') { ?>selected="selected"<?php } ?>>全部状态</option>
                <option value="0" <?php if($filter_status=='0') { ?>selected="selected"<?php } ?>>待试算</option>
                <option value="1" <?php if($filter_status=='1') { ?>selected="selected"<?php } ?>>已试算</option>
                <option value="2" <?php if($filter_status=='2') { ?>selected="selected"<?php } ?>>已确认</option>
              </select>
            </div>
          </div>

          <div class="col-md-3">
            <div class="form-group">
              <label>创建时间：</label>
              <div class="input-group">
                <div class="input-group-addon">
                  <i class="glyphicon glyphicon-calendar"></i>
                </div>
                <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                <?php } else{ ?>
                <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                <?php } ?>
                <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
              </div>
            </div>
          </div>

        </div>
      </div>
      <!-- /.box-body -->
      <div class="box-footer">
        <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
      </div>
    </div>
    <div class="box box-success">
      <div class="box-header with-border">
        <h3 class="box-title">试算列表</h3>
        <div class="box-tools">
          <button id="button-crowd" class="btn btn-danger" type="button">批量确认</button>
          <a class="btn btn-primary" href="<?php echo $add; ?>">新增试算</a>
        </div>
      </div>
      <div class="box-body">
        <div class="box-body">
          <form action="<?php echo $affirm; ?>" method="post" enctype="multipart/form-data" id="form-batch-affirm" class="form-horizontal">
            <table class="table text-middle table-bordered table-hover table-striped">
              <tbody><tr>
                <th width="30"><input id="selectAll" class="flat" type="checkbox"></th>
                <th>订单号</th>
                <th>状态</th>
                <th>新建时间</th>
                <th class="text-right">操作</th>
                </th>
              </tr>
              <?php if (!empty($freights)) { ?>
              <?php foreach ($freights as $freight) { ?>
              <tr data-id="<?php echo $freight['freight_id']; ?>">
                <td><?php if($freight['status']==1) { ?><input class="flat" type="checkbox" name="selected[]" value="<?php echo $freight['freight_id']; ?>"><?php } ?></td>
                <td><?php echo $freight['order_no']; ?></td>
                <td><?php if($freight['status']==0){ ?>待试算<?php } else if($freight['status']==1) { ?>已试算<?php } else if($freight['status']==2) { ?>已确认<?php } ?></td>
                <td><?php echo $freight['date_added']; ?></td>
                <td class="text-right">
                  <?php if($freight['status']==1) { ?>
                  <button class="btn btn-info" type="button" data-toggle="modal" data-target="#affirm-modal">确认</button>
                  <?php } ?>
                  <a class="btn btn-success" href="<?php echo $add; ?>&freight_id=<?php echo $freight['freight_id']; ?>" title="">明细</a>
                </td>
              </tr>
              <?php } ?>
              <?php } else { ?>
              <td colspan="7" align="center"> 暂无试算数据 </td>
              <?php } ?>
              </tbody></table>
        </div>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-8">
            <button class="btn btn-danger" type="submit">批量确认</button>
            <!--<a href="<?php echo $action; ?>" class="btn btn-default">重置数据</a>-->
          </div>
        </div>
        </form>
      </div>

      <div class="box-footer clearfix">
        <div class="flex ai__c jc__sb">
          <div><?php echo $results; ?></div>
          <?php echo $pagination; ?>
        </div>
      </div>
    </div>

    <!-- 确认 -->
    <div class="modal modal-info fade" id="affirm-modal">
      <div class="modal-dialog">
        <div class="modal-content">
          <form action="<?php echo $affirm; ?>" method="post" enctype="multipart/form-data" id="form-affirm">
            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title">确认</h4>
            </div>
            <div class="modal-body">
              <p>确定确认吗？此操作不可恢复！</p>
              <input id="affirm-id" name="selected[]" type="hidden" value="">
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
              <button id="affirm-yes" type="button" class="btn btn-outline">是</button>
            </div>
          </form>
        </div>
        <!-- /.modal-content -->
      </div>
      <!-- /.modal-dialog -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
  .table a.asc:after {
    content: " \f106";
    font-family: FontAwesome;
  }
  .table a.desc:after {
    content: " \f107";
    font-family: FontAwesome;
  }
</style>
<script type="text/javascript">
  (function () {
    // 全选操作
    $('#selectAll').on('ifChecked', function() {
      $('input.flat').iCheck('check')
    })
    $('#selectAll').on('ifUnchecked', function() {
      $('input.flat').iCheck('uncheck')
    })
    // 日期筛选
    $('.reservation').daterangepicker({
      timePicker: true, // 显示时间选择器
      timePicker24Hour: true, // 使用24小时制
      timePickerSeconds: true, // 显示秒
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除',
        format: 'YYYY-MM-DD HH:mm:ss'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD HH:mm:ss'))
      $(this).val(picker.startDate.format('YYYY-MM-DD HH:mm:ss') + ' - ' + picker.endDate.format('YYYY-MM-DD HH:mm:ss'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();

      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_status = $('select[name=\'filter_status\']').val();

      if (filter_status != '*') {
        url += '&filter_status=' + encodeURIComponent(filter_status);
      }

      var filter_start = $('input[name=\'filter_start\']').val();

      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();

      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });
    $('input.flat').iCheck('uncheck');
  })()
</script>

<script type="text/javascript">
  (function () {
    $('#affirm-modal').on('show.bs.modal', function(event) {
      $('#affirm-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#affirm-yes').on('click', () => {$('#form-affirm').submit()})
  })()
</script>
<script type="text/javascript"><!--
  $('#button-crowd').on('click', function() {
    $('#form-batch-affirm').submit()
  });
  //--></script>
<?php echo $footer; ?>