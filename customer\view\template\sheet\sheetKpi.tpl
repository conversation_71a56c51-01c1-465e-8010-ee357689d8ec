<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        多维表绩效
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>姓名：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="请输入姓名" value="<?php echo $filter_name; ?>">
                </div>
              </div>
          

              <div class="col-md-3">
                        <div class="form-group">
                            <label>发布时间：</label>
                            <div class="input-group">
                                <div class="input-group-addon">
                                    <i class="glyphicon glyphicon-calendar"></i>
                                </div>
                                <?php if (!empty($filter_months)) { ?>
                                <input type="text" class="form-control pull-right" id="reservation"
                                       placeholder="月份"
                                       value="<?php echo $filter_months; ?>">
                                <?php } else{ ?>
                                <input type="text" class="form-control pull-right" id="reservation"
                                       placeholder="月份"
                                       value="<?php echo $filter_months; ?>">
                                <?php } ?>
                                <input type="text" class="hidden" name="filter_months" id="filter-start-time"
                                       placeholder="" value="<?php echo $filter_months; ?>">
                            </div>
                        </div>
                    </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
        <a class="btn btn-danger" href="<?php echo $exportKpi; ?>"><i class="glyphicon glyphicon-download-alt"></i> 下载数据</a>
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">列表</p>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'name') { ?>
                  <a href="<?php echo $sort_name; ?>" class="<?php echo strtolower($order); ?>">名称</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_name; ?>">名称</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'months') { ?>
                  <a href="<?php echo $sort_months; ?>" class="<?php echo strtolower($order); ?>">月份</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_months; ?>">月份</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'score') { ?>
                  <a href="<?php echo $sort_score; ?>" class="<?php echo strtolower($order); ?>">绩效分数</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_score; ?>">绩效分数</a>
                <?php } ?>
              </th>
               <th>
                <?php if ($sort == 'extra_info') { ?>
                  <a href="<?php echo $sort_extra_info; ?>" class="<?php echo strtolower($order); ?>">岗位津贴</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_extra_info; ?>">岗位津贴</a>
                <?php } ?>
              </th>
              <th>
             扣款
              </th>

                     <th>
                应发岗位津贴
              </th>
                <th class="text-right">操作</th>
            </tr>
            <?php if (isset($sheet) && !empty($sheet)) { ?>
            <?php foreach ($sheet as $v) { ?>
            <tr data-id="<?php echo $v['id']; ?>">

              <td><?php echo $v['real_name']; ?></td>
              <td><?php echo $v['months']; ?></td>
              <td><?php echo $v['score']; ?></td>
              <td><?php echo $v['extra_info']; ?></td>
              <td><?php echo $v['deduct_money']; ?></td>
              <td><?php echo $v['extra_info_score']; ?></td>
              
              <td class="text-right">
                <?php if($v['score'] != 100) { ?>
                  <a class="btn btn-success" href="<?php echo $v['detail']; ?>" title="">查看扣分</a>
                <?php } ?>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <tr>
                <td colspan="4" align="center">暂无数据</td>
            </tr>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after, .table a.undesc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after, .table a.unasc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">

  (function () {

      // 日期筛选
  $('#reservation').daterangepicker({
    autoUpdateInput: false,
    singleDatePicker: true,
    showDropdowns: true,    // 显示年月下拉选择
    autoApply: true,        // 选择月份后自动确认
    startDate: moment(), // 默认显示今天
    locale: {
        format: 'YYYY-MM',  // 仅显示年月
        applyLabel: '确定',
        cancelLabel: '清除'
    }
});

// 事件处理（返回年月格式）
$('#reservation').on('apply.daterangepicker', function(ev, picker) {
    $(this).val(picker.startDate.format('YYYY-MM')); // 格式如 "2046-01"
    $('#filter-start-time').val(picker.startDate.format('YYYY-MM'));
});

    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      
       var filter_months = $('input[name=\'filter_months\']').val();
      if (filter_months) {
        url += '&filter_months=' + encodeURIComponent(filter_months);
      }






 
      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>