<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        生产流程
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索合同号或产品名称" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>供应商：</label>
                  <select class="form-control" name="filter_provider">
                    <option value="*">全部供应商</option>
                    <?php foreach ($providers as $provider_id => $provider) { ?>
                    <?php if ($provider_id == $filter_provider) { ?>
                    <option value="<?php echo $provider_id; ?>" selected="selected"><?php echo $provider; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $provider_id; ?>"><?php echo $provider; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>状态：</label>
                  <select class="form-control" name="filter_state">
                    <option value="*">全部状态</option>
                    <?php foreach ($states as $state_id => $state) { ?>
                    <?php if ($state_id == $filter_state) { ?>
                    <option value="<?php echo $state_id; ?>" selected="selected"><?php echo $state; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $state_id; ?>"><?php echo $state; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>创建时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <a class="btn btn-danger" href="<?php echo $export; ?>"><i class="glyphicon glyphicon-download-alt"></i> 下载数据</a>
          
          <div class="pull-right">
            <button type="button" id="button-filter" class="btn bg-purple"><i class="glyphicon glyphicon-search"></i> 筛选</button>
          </div>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">流程列表</p>
          <div class="box-tools">
            <a class="btn btn-sm btn-danger" data-toggle="modal" data-target="#info-modal">批量更新</a>
          </div>
        </div>
        <div class="box-body table-responsive no-padding">
          <form action="<?php echo $submit; ?>" method="post" enctype="multipart/form-data" id="form-flows">
          <table id="flows" class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>合同号 / 日期</th>
              <th>供应商</th>
              <th>产品名称 / 数量</th>
              <th>完成工期</th>
              <th>状态</th>
              <th>白坯完成</th>
              <th>彩绘完成</th>
              <th>包装出货</th>
            </tr>
            <?php if (!empty($flows)) { ?>
            <?php foreach ($flows as $flow) { ?>
            <tr data-id="<?php echo $flow['workid']; ?>">
              <td><?php echo $flow['workorder']; ?><br><?php echo $flow['workfinish']; ?></td>
              <td><?php echo $flow['provider']; ?></td>
              <td><?php echo $flow['workproduct']; ?><br><?php echo $flow['worktotal']; ?></td>
              <td>
                白坯：<?php echo $flow['worktime41']; ?><br>
                彩绘：<?php echo $flow['worktime43']; ?><br>
                包装：<?php echo $flow['worktime44']; ?>
              </td>
              <td class="h4 no-margin">
                <span class="label label-primary"><?php echo $flow['workstate']; ?></span>
              </td>
              <td>
                <?php echo $node_fields['complete_quan39']; ?>：<input type="number" name="sumbit[<?php echo $flow['workid']; ?>][complete_quan39]" value="<?php echo $flow['fields']['complete_quan39']; ?>"><br>
                <?php echo $node_fields['complete_quan40']; ?>：<input type="number" name="sumbit[<?php echo $flow['workid']; ?>][complete_quan40]" value="<?php echo $flow['fields']['complete_quan40']; ?>"><br>
                <?php echo $node_fields['complete_quan41']; ?>：<input type="number" name="sumbit[<?php echo $flow['workid']; ?>][complete_quan41]" value="<?php echo $flow['fields']['complete_quan41']; ?>"><br>
              </td>
              <td>
                <?php echo $node_fields['complete_quan42']; ?>：<input type="number" name="sumbit[<?php echo $flow['workid']; ?>][complete_quan42]" value="<?php echo $flow['fields']['complete_quan42']; ?>"><br>
                <?php echo $node_fields['complete_quan43']; ?>：<input type="number" name="sumbit[<?php echo $flow['workid']; ?>][complete_quan43]" value="<?php echo $flow['fields']['complete_quan43']; ?>"><br>
              </td>
              <td>
                <?php echo $node_fields['complete_quan44']; ?>：<input type="number" name="sumbit[<?php echo $flow['workid']; ?>][complete_quan44]" value="<?php echo $flow['fields']['complete_quan44']; ?>"><br>
                <?php echo $node_fields['complete_quan45']; ?>：<input type="number" name="sumbit[<?php echo $flow['workid']; ?>][complete_quan45]" value="<?php echo $flow['fields']['complete_quan45']; ?>"><br>
              </td>
            </tr>
            <?php } ?>
            <?php } else{ ?>
            <td colspan="8" align="center"> 暂无流程数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 更新 -->
      <div class="modal modal-primary fade" id="info-modal">
        <div class="modal-dialog">
          <div class="modal-content">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">更新</h4>
              </div>
              <div class="modal-body">
                <p>确定批量更新记录吗？</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="info-yes" type="button" class="btn btn-outline">是</button>
              </div>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
  #flows input {
    display: inline;
    width: 75px;
  }
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_provider = $('select[name=\'filter_provider\']').val();

      if (filter_provider != '*') {
        url += '&filter_provider=' + encodeURIComponent(filter_provider);
      }

      var filter_state = $('select[name=\'filter_state\']').val();

      if (filter_state != '*') {
        url += '&filter_state=' + encodeURIComponent(filter_state);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });

    $('#info-yes').on('click', () => {$('#form-flows').submit()})
  })()
</script>
<?php echo $footer; ?>