<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      <?php echo $text_form; ?>
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <?php if ($warning) { ?>
    <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
    <?php } ?>
    <div class="box box-warning">
      <div class="box-header">
        <h3 class="box-title"></h3>
      </div>
      <!-- /.box-header -->
      <div class="box-body">
        <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-product">破损产品*：</label>
            <div class="col-sm-8">
              <input type="text" value="" placeholder="输入产品编码或名称查找" id="input-product" class="form-control" />
              <input type="hidden" name="bsku" value="<?php echo $bsku; ?>" />
              <input type="hidden" name="spec_name" value="<?php echo $spec_name; ?>" />
              <div class="text-danger">请选择破损产品</div>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-quan">破损数量*：</label>
            <div class="col-sm-8">
              <input type="number" name="quantity" value="<?php echo $quantity; ?>" placeholder="请输入破损数量" id="input-quan" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-supplier">所属供应商*：</label>
            <div class="col-sm-8">
              <select name="supplier" id="input-supplier" class="form-control">
                <option value="">请先选择破损产品</option>
              </select>
            </div>
          </div>
          <div class="form-group hide">
            <label class="col-sm-2 control-label" for="input-costfee">产品成本：</label>
            <div class="col-sm-8">
              <input type="text" name="cost_fee" value="<?php echo $cost_fee; ?>" placeholder="请输入产品成本" id="input-costfee" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">破损图片*：</label>
            <div class="col-sm-8">
              <div class="row">
                <?php foreach($images as $img) { ?>
                <div class="col-sm-3">
                  <div class="thumbnail">
                    <img src="<?php echo HTTP_IMAGE . $img; ?>?imageView2/1/w/100" alt="...">
                    <input type="hidden" name="images[]" value="<?php echo $img; ?>">
                    <div class="caption text-center">
                      <button type="button" onclick="$(this).parent().parent().parent().remove()" class="btn btn-default">删除</button>
                    </div>
                  </div>
                </div>
                <?php } ?>
              </div>
              <button type="button" data-field="images" class="btn-upload btn btn-success"><i class="fa fa-upload"></i> 上传</button>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-belong">责任原因*：</label>
            <div class="col-sm-8">
              <select name="belong" id="input-belong" class="form-control">
                <option value="">选择责任原因</option>
                <?php foreach($reasons as $row) { ?>
                <?php foreach($row['subs'] as $sub) { ?>
                <?php $subname = $row['name'] . '-' . $sub; ?>
                <?php if ($belong == $subname) { ?>
                <option value="<?php echo $subname; ?>" selected="selected"><?php echo $subname; ?></option>
                <?php } else{ ?>
                <option value="<?php echo $subname; ?>"><?php echo $subname; ?></option>
                <?php } ?>
                <?php } ?>
                <?php } ?>
              </select>
              <input type="hidden" name="responsible" value="<?php echo $responsible; ?>">
              <input type="hidden" name="reason" value="<?php echo $reason; ?>">
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-solution">处理方案*：</label>
            <div class="col-sm-8">
              <select name="solution" id="input-solution" class="form-control">
                <option value="">请选择处理方案</option>
                <?php foreach($solutions as $row) { ?>
                <option value="<?php echo $row; ?>"<?php if ($solution == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-store">所属店铺*：</label>
            <div class="col-sm-8">
              <select name="store_id" id="input-store" class="form-control">
                <option value="">请选择所属店铺</option>
                <?php foreach ($stores as $store) { ?>
                <?php if ($store['store_id'] == $store_id) { ?>
                <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                <?php } else { ?>
                <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-buyerlocation">所在地*：</label>
            <div class="col-sm-8">
              <select name="buyer_location" id="input-buyerlocation" class="form-control">
                <option value="">请选择客户所在地</option>
                <?php foreach($locations as $row) { ?>
                <option value="<?php echo $row; ?>"<?php if ($buyer_location == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-buyerid">原始单号：</label>
            <div class="col-sm-8">
              <input type="text" name="buyer_id" value="<?php echo $buyer_id; ?>" placeholder="请输入原始单号" id="input-buyerid" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-orderno">订单编号：</label>
            <div class="col-sm-8">
              <input type="text" name="order_no" value="<?php echo $order_no; ?>" placeholder="请输入订单编号" id="input-orderno" class="form-control" />
            </div>
          </div>
          <div id="responsible-ship">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-orderfee">订单金额：</label>
              <div class="col-sm-8">
                <input type="text" name="order_fee" value="<?php echo $order_fee; ?>" placeholder="请输入订单金额" id="input-orderfee" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">订单图片：</label>
              <div class="col-sm-8">
                <div class="row">
                  <?php foreach($order_img as $img) { ?>
                  <div class="col-sm-3">
                    <div class="thumbnail">
                      <img src="<?php echo HTTP_IMAGE . $img; ?>?imageView2/1/w/100" alt="...">
                      <input type="hidden" name="order_img[]" value="<?php echo $img; ?>">
                      <div class="caption text-center">
                        <button type="button" onclick="$(this).parent().parent().parent().remove()" class="btn btn-default">删除</button>
                      </div>
                    </div>
                  </div>
                  <?php } ?>
                </div>
                <button type="button" data-field="order_img" class="btn-upload btn btn-success"><i class="fa fa-upload"></i> 上传</button>
              </div>
            </div>
          </div>
          <div id="responsible-intra">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-handler">责任人：</label>
              <div class="col-sm-8">
                <input type="text" name="handler" value="<?php echo $handler; ?>" placeholder="请输入责任人" id="input-handler" class="form-control" />
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-refundfee">退款金额：</label>
            <div class="col-sm-8">
              <input type="text" name="refund_fee" value="<?php echo $refund_fee; ?>" placeholder="请输入退款金额" id="input-refundfee" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-lossfee">补偿金额：</label>
            <div class="col-sm-8">
              <input type="text" name="loss_fee" value="<?php echo $loss_fee; ?>" placeholder="请输入补偿金额" id="input-lossfee" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-shipname">快递公司：</label>
            <div class="col-sm-8">
              <select name="ship_name" id="input-shipname" class="form-control">
                <option value="">请选择快递公司</option>
                <?php foreach($shipnames as $row) { ?>
                <option value="<?php echo $row; ?>"<?php if ($ship_name == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-shipno">快递单号：</label>
            <div class="col-sm-8">
              <input type="text" name="ship_no" value="<?php echo $ship_no; ?>" placeholder="请输入快递单号" id="input-shipno" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-shipfee">快递运费：</label>
            <div class="col-sm-8">
              <input type="text" name="ship_fee" value="<?php echo $ship_fee; ?>" placeholder="请输入快递运费" id="input-shipfee" class="form-control" />
            </div>
          </div>
          <div id="solution-reship">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-reshipname">补发快递：</label>
              <div class="col-sm-8">
                <select name="reship_name" id="input-reshipname" class="form-control">
                  <option value="">请选择补发快递</option>
                  <?php foreach($shipnames as $row) { ?>
                  <option value="<?php echo $row; ?>"<?php if ($reship_name == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-reshipno">补发快递单号：</label>
              <div class="col-sm-8">
                <input type="text" name="reship_no" value="<?php echo $reship_no; ?>" placeholder="请输入补发快递单号" id="input-reshipno" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-reshipfee">补发运费：</label>
              <div class="col-sm-8">
                <input type="text" name="reship_fee" value="<?php echo $reship_fee; ?>" placeholder="请输入补发运费" id="input-reshipfee" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-remark">备注：</label>
              <div class="col-sm-8">
                <input type="text" name="remark" value="<?php echo $remark; ?>" placeholder="请输入备注" id="input-remark" class="form-control" />
              </div>
            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" type="submit">提交保存</button>
              <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
            </div>
          </div>
        </form>
      </div>
      <!-- /.box-body -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  var providers = [];
  // Autocomplete */
  (function($) {
    $.fn.autocomplete = function(option) {
      return this.each(function() {
        var $this = $(this);
        var $dropdown = $('<ul class="dropdown-menu" />');

        this.timer = null;
        this.items = {};

        $.extend(this, option);

        $this.attr('autocomplete', 'off');

        // Focus
        $this.on('focus', function() {
          this.request();
        });

        // Blur
        $this.on('blur', function() {
          setTimeout(function(object) {
            object.hide();
          }, 200, this);
        });

        // Keydown
        $this.on('keydown', function(event) {
          switch(event.keyCode) {
            case 27: // escape
              this.hide();
              break;
            default:
              this.request();
              break;
          }
        });

        // Click
        this.click = function(event) {
          event.preventDefault();

          var value = $(event.target).parent().attr('data-value');

          if (value && this.items[value]) {
            this.select(this.items[value]);
          }
        }

        // Show
        this.show = function() {
          var pos = $this.position();

          $dropdown.css({
            'min-width': '50%',
            top: pos.top + $this.outerHeight(),
            left: pos.left
          });

          $dropdown.show();
        }

        // Hide
        this.hide = function() {
          $dropdown.hide();
        }

        // Request
        this.request = function() {
          clearTimeout(this.timer);

          this.timer = setTimeout(function(object) {
            if ($(object).val()) object.source($(object).val(), $.proxy(object.response, object));
          }, 200, this);
        }

        // Response
        this.response = function(json) {
          var html = '';
          var category = {};
          var name;
          var i = 0, j = 0;

          if (json.length) {
            for (i = 0; i < json.length; i++) {
              if (i > 10) break;
              // update element items
              this.items[json[i]['value']] = json[i];

              if (!json[i]['category']) {
                // ungrouped items
                html += '<li data-value="' + json[i]['value'] + '"><a href="#">' + json[i]['value'] + json[i]['label'] + '</a></li>';
              } else {
                // grouped items
                name = json[i]['category'];
                if (!category[name]) {
                  category[name] = [];
                }

                category[name].push(json[i]);
              }
            }

            for (name in category) {
              html += '<li class="dropdown-header">' + name + '</li>';

              for (j = 0; j < category[name].length; j++) {
                html += '<li data-value="' + category[name][j]['value'] + '"><a href="#">&nbsp;&nbsp;&nbsp;' + category[name][j]['label'] + '</a></li>';
              }
            }
          }

          if (html) {
            this.show();
          } else {
            this.hide();
          }

          $dropdown.html(html);
        }

        $dropdown.on('click', '> li > a', $.proxy(this.click, this));
        $this.after($dropdown);
      });
    }
  })(window.jQuery);

  $(document).ready(function () {
    $('#input-product').autocomplete({
      'source': function(request, response) {
        $.ajax({
          url: '<?php echo $autoProvider; ?>&filter_name=' + encodeURIComponent(request),
          dataType: 'json',
          success: function(json) {
            response(json);
            // response($.map(json, function(item) {
            //   return item
            // }));
          }
        });
      },
      'select': function(item) {
        $('#input-product').val('');
        $('input[name="bsku"]').val(item['value']);
        $('input[name="spec_name"]').val(item['label']);
        $('.text-danger').html('选中产品：' + item['value'] + item['label']);
        providers = item['providers'];

        $('#input-supplier option').remove();
        if (item['providers'].length > 0) {
          if (item['providers'].length > 1) {
            $('#input-supplier').append('<option value="">请选择供应商</option>');
          }
          $.each(item['providers'], function(i, v) {
            $('#input-supplier').append('<option value="' + v.supplier + '">' + v.supplier + '</option>');
          });
          $('#input-supplier').trigger('change');
        } else {
          $('#input-supplier').append('<option value="">暂无供应商</option>');
        }
      }
    });
    <?php if (!empty($bsku)) { ?>
      $.ajax({
        url: '<?php echo $autoProvider; ?>&filter_name=<?php echo $bsku; ?>',
        dataType: 'json',
        success: function(json) {
          if (json.length > 0) {
            var item = json[0];
            $('.text-danger').html('选中产品：' + item['value'] + item['label']);
            providers = item['providers'];

            $('#input-supplier option').remove();
            if (item['providers'].length > 0) {
              if (item['providers'].length > 1) {
                $('#input-supplier').append('<option value="">请选择供应商</option>');
              }
              $.each(item['providers'], function(i, v) {
                $('#input-supplier').append('<option value="' + v.supplier + '">' + v.supplier + '</option>');
              });
              $('#input-supplier').val('<?php echo $supplier; ?>');
              $('#input-supplier').trigger('change');
            } else {
              $('#input-supplier').append('<option value="">暂无供应商</option>');
            }
          }
        }
      });
      <?php } ?>
  });

  $('.content').on('click', '.btn-upload', function() {
    $('#form-upload').remove();

    var target = $(this);

    $('body').prepend('<form enctype="multipart/form-data" id="form-upload" style="display: none;"><input type="file" name="file" value="" accept="image/*, video/*" /><input type="hidden" name="token" value="" /></form>');

    $('#form-upload input[name=\'file\']').trigger('click');

    if (typeof timer != 'undefined') {
      clearInterval(timer);
    }

    timer = setInterval(function() {
      if ($('#form-upload input[name=\'file\']').val() != '') {
        clearInterval(timer);

        $.ajax({
          url: '<?php echo $getToken; ?>',
          type: 'get',
          dataType: 'json',
          success: function(json) {
            $('#form-upload input[name=\'token\']').val(json.uploadToken);
            $.ajax({
              url: 'https://up-z2.qiniup.com',
              type: 'post',
              dataType: 'json',
              data: new FormData($('#form-upload')[0]),
              cache: false,
              contentType: false,
              processData: false,
              beforeSend: function() {
                target.find('i').replaceWith('<i class="fa fa-circle-o-notch fa-spin"></i>');
                target.prop('disabled', true);
              },
              complete: function() {
                target.find('i').replaceWith('<i class="fa fa-upload"></i>');
                target.prop('disabled', false);
              },
              success: function(res) {
                target.prev().append('<div class="col-sm-3"><div class="thumbnail"><img src="' + json.httpHost + res.key + '?imageView2/1/w/100" alt="..."><input type="hidden" name="' + target.data('field') + '[]" value="' + res.key + '"><div class="caption text-center"><button type="button" onclick="$(this).parent().parent().parent().remove()" class="btn btn-default">删除</button></div></div></div>');
              },
              error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
              }
            });
          },
          error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
          }
        });
      }
    }, 500);
  });

  $('#input-supplier').on('change', function() {
    $('input[name="cost_fee"]').val('');
    var supplier = $(this).val();
    $.each(providers, function(i, v) {
      if (v.supplier == supplier) {
        $('input[name="cost_fee"]').val(v.price);
      }
    });
  });

  $('#input-belong').on('change', function() {
    var responsible = $(this).val();

    if (responsible.indexOf('快递责任') != -1) {
      $('#responsible-ship').show();
    } else {
      $('#responsible-ship').hide();
    }

    if (responsible.indexOf('内部责任') != -1) {
      $('#responsible-intra').show();
    } else {
      $('#responsible-intra').hide();
    }
  });

  $('#input-belong').trigger('change');
</script>
<?php echo $footer; ?>