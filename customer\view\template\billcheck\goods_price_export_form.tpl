<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        添加
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">选择工厂：</label>
              <div class="col-sm-8">
                <input type="checkbox" id="selectAll" onclick="toggleAll(this)">
                <label for="selectAll">全选</label><br>
                <?php foreach($templates as $template) { ?>
                <input type="checkbox" id="template<?php echo $template['goods_price_template_id']; ?>" name="templates[]" value="<?php echo $template['goods_price_template_id']; ?>" class="template">
                <label for="template<?php echo $template['goods_price_template_id']; ?>"><?php echo $template['name']; ?></label><br>
                <?php } ?>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">货款时间：</label>
              <div class="col-sm-8">
                <div class="input-group">
                  <div class="input-group-addon">
                    <i class="glyphicon glyphicon-calendar"></i>
                  </div>
                  <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="">
                  <input type="text" class="hidden" name="date_start" id="filter-start" placeholder="" value="">
                  <input type="text" class="hidden" name="date_end" id="filter-end" placeholder="" value="">
                </div>
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })

  })()

  function toggleAll(source) {
    var checkboxes = document.querySelectorAll('.template');
    for (var i = 0, n = checkboxes.length; i < n; i++) {
      checkboxes[i].checked = source.checked;
    }
  }

  function toVaild() {
    var checkboxes = document.getElementsByName('templates[]');
    var templates = [];
    for (var i = 0; i < checkboxes.length; i++) {
      if (checkboxes[i].checked) {
        templates.push(checkboxes[i].value);
      }
    }
    if (templates.length == 0) {
      confirm('请选择快递')
      return false;
    }

    var date_start = $('input[name=\'date_start\']').val();
    var date_end = $('input[name=\'date_end\']').val();
    if (date_start == "" || date_end == "") {
      confirm('请选择账单时间')
      return false;
    }
    return true;
  }


</script>
<?php echo $footer; ?>