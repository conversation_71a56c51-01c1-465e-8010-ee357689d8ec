<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-mode">借调方式：</label>
              <div class="col-sm-8">
                  <select class="form-control" id="input-mode" name="mode">
                    <?php if ($mode == 'out') { ?>
                    <option value="out" selected="selected">从申请店铺调出到目标店铺</option>
                    <?php } else { ?>
                    <option value="out">从申请店铺调出到目标店铺</option>
                    <?php } ?>
                    <?php if ($mode == 'in') { ?>
                    <option value="in" selected="selected">从目标店铺调出到申请店铺</option>
                    <?php } else { ?>
                    <option value="in">从目标店铺调出到申请店铺</option>
                    <?php } ?>
                  </select>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-product">借调产品：</label>
              <div class="col-sm-8">
                <input type="text" value="" placeholder="输入产品编码或名称查找" id="input-product" class="form-control" />
                <input type="hidden" name="bsku" value="<?php echo $bsku; ?>" />
                <input type="hidden" name="state" value="<?php echo $state; ?>" />
                <div class="text-danger">请选择借调产品</div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-from-store">申请店铺：</label>
              <div class="col-sm-8">
                <select name="from_store_id" id="input-from-store" class="form-control">
                  <option value="">请选择申请店铺</option>
                  <?php foreach ($stores as $store) { ?>
                  <?php if ($store['store_id'] == $from_store_id) { ?>
                  <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-to-store">目标店铺：</label>
              <div class="col-sm-8">
                <select name="to_store_id" id="input-to-store" class="form-control">
                </select>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-quan">借调数量：</label>
              <div class="col-sm-8">
                <input type="number" name="transfer_quan" value="<?php echo $transfer_quan; ?>" placeholder="请输入借调数量" id="input-quan" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
// Autocomplete */
(function($) {
  $.fn.autocomplete = function(option) {
    return this.each(function() {
      var $this = $(this);
      var $dropdown = $('<ul class="dropdown-menu" />');

      this.timer = null;
      this.items = {};

      $.extend(this, option);

      $this.attr('autocomplete', 'off');

      // Focus
      $this.on('focus', function() {
        this.request();
      });

      // Blur
      $this.on('blur', function() {
        setTimeout(function(object) {
          object.hide();
        }, 200, this);
      });

      // Keydown
      $this.on('keydown', function(event) {
        switch(event.keyCode) {
          case 27: // escape
            this.hide();
            break;
          default:
            this.request();
            break;
        }
      });

      // Click
      this.click = function(event) {
        event.preventDefault();

        var value = $(event.target).parent().attr('data-value');

        if (value && this.items[value]) {
          this.select(this.items[value]);
        }
      }

      // Show
      this.show = function() {
        var pos = $this.position();

        $dropdown.css({
          'min-width': '50%',
          top: pos.top + $this.outerHeight(),
          left: pos.left
        });

        $dropdown.show();
      }

      // Hide
      this.hide = function() {
        $dropdown.hide();
      }

      // Request
      this.request = function() {
        clearTimeout(this.timer);

        this.timer = setTimeout(function(object) {
          object.source($(object).val(), $.proxy(object.response, object));
        }, 200, this);
      }

      // Response
      this.response = function(json) {
        var html = '';
        var category = {};
        var name;
        var i = 0, j = 0;

        if (json.length) {
          for (i = 0; i < json.length; i++) {
            // update element items
            this.items[json[i]['value']] = json[i];

            if (!json[i]['category']) {
              // ungrouped items
              html += '<li data-value="' + json[i]['value'] + '"><a href="#">' + json[i]['value'] + json[i]['label'] + '</a></li>';
            } else {
              // grouped items
              name = json[i]['category'];
              if (!category[name]) {
                category[name] = [];
              }

              category[name].push(json[i]);
            }
          }

          for (name in category) {
            html += '<li class="dropdown-header">' + name + '</li>';

            for (j = 0; j < category[name].length; j++) {
              html += '<li data-value="' + category[name][j]['value'] + '"><a href="#">&nbsp;&nbsp;&nbsp;' + category[name][j]['label'] + '</a></li>';
            }
          }
        }

        if (html) {
          this.show();
        } else {
          this.hide();
        }

        $dropdown.html(html);
      }

      $dropdown.on('click', '> li > a', $.proxy(this.click, this));
      $this.after($dropdown);
    });
  }
})(window.jQuery);

$(document).ready(function () {
  $('#input-product').autocomplete({
    'source': function(request, response) {
      $.ajax({
        url: '<?php echo $autoStock; ?>&filter_name=' + encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response(json);
          // response($.map(json, function(item) {
          //   return item
          // }));
        }
      });
    },
    'select': function(item) {
      $('#input-product').val('');
      $('input[name="bsku"]').val(item['value']);
      $('.text-danger').html('选中产品：' + item['value'] + item['label']);

      $('#input-to-store option').remove();
      if (item['stocks'].length > 0) {
        $('#input-to-store').append('<option value="">请选择目标店铺</option>');
        $.each(item['stocks'], function(i, v) {
          $('#input-to-store').append('<option value="' + v.store + '">' + v.stock + '</option>');
        });
      } else {
        $('#input-to-store').append('<option value="">所有店铺暂无库存</option>');
      }
    }
  });
  <?php if (!empty($bsku)) { ?>
  $.ajax({
    url: '<?php echo $autoStock; ?>&filter_name=<?php echo $bsku; ?>',
    dataType: 'json',
    success: function(json) {
      if (json.length > 0) {
        var item = json[0];
        $('.text-danger').html('选中产品：' + item['value'] + item['label']);

        $('#input-to-store option').remove();
        if (item['stocks'].length > 0) {
          $('#input-to-store').append('<option value="">请选择目标店铺</option>');
          $.each(item['stocks'], function(i, v) {
            $('#input-to-store').append('<option value="' + v.store + '">' + v.stock + '</option>');
          });
          $('#input-to-store').val('<?php echo $to_store_id; ?>');
        } else {
          $('#input-to-store').append('<option value="">所有店铺暂无库存</option>');
        }
      }
    }
  });
  <?php } ?>
});
</script>
<?php echo $footer; ?>