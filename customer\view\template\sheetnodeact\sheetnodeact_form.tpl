<?php echo $header; ?><?php echo $content_top; ?>
<div id="content" style="margin-left:13%">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-node" data-toggle="tooltip" title="保存" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="<?php echo $cancel; ?>" data-toggle="tooltip" title="取消" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>流程节点管理</h1>
    </div>
  </div>
  <div class="container-fluid">
    <?php if ($warning) { ?>
    <div class="alert alert-danger" ><i class="fa fa-exclamation-circle"></i> <?php echo $warning; ?>
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    <?php } ?>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> 流程节点表单</h3>
      </div>
      <div class="panel-body">
        <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-node" class="form-horizontal">
          
          <!-- 流程 -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-flow">流程：</label>
            <div class="col-sm-10">
              <select name="flow_id" id="input-flow" class="form-control">
                <option value="">请选择流程</option>
                <?php foreach ($flow_list as $flow) { ?>
                  <?php if ($flow['flow_id'] == $flow_id) { ?>
                    <option value="<?php echo $flow['flow_id']; ?>" selected="selected"><?php echo $flow['flow_name']; ?></option>
                  <?php } else { ?>
                    <option value="<?php echo $flow['flow_id']; ?>"><?php echo $flow['flow_name']; ?></option>
                  <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          
          <!-- 当前环节 -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-current-node">当前环节：</label>
            <div class="col-sm-10">
              <select name="parent_node_id" id="input-current-node" class="form-control">
                <option value="">请选择当前环节</option>
                <?php if (!empty($node_list)) { ?>
                  <?php foreach ($node_list as $node) { ?>
                    <?php if ($node['node_id'] == $parent_node_id) { ?>
                      <option value="<?php echo $node['node_id']; ?>" selected="selected"><?php echo $node['node_name']; ?></option>
                    <?php } else { ?>
                      <option value="<?php echo $node['node_id']; ?>"><?php echo $node['node_name']; ?></option>
                    <?php } ?>
                  <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          
          <!-- 下一环节 -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-branches">下一环节</label>
            <div class="col-sm-10">
              <select name="child_node_id[]" id="input-branches" class="form-control" multiple style="height: 150px; overflow-y: auto;">
                <?php if (!empty($node_list)) { ?>
                  <?php foreach ($node_list as $node) { ?>
                    <?php if (in_array($node['node_id'], $child_node_id)) { ?>
                      <option value="<?php echo $node['node_id']; ?>" selected="selected"><?php echo $node['node_name']; ?></option>
                    <?php } else { ?>
                      <option value="<?php echo $node['node_id']; ?>"><?php echo $node['node_name']; ?></option>
                    <?php } ?>
                  <?php } ?>
                <?php } ?>
              </select>
              <div class="help-block">按住Ctrl键可多选</div>
            </div>
          </div>
          
          <!-- 分支 -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-branches-fork">分支：</label>
            <div class="col-sm-10">
              <select name="branches[]" id="input-branches-fork" class="form-control" multiple style="height: 150px; overflow-y: auto;">
                <?php if (!empty($node_list)) { ?>
                  <?php foreach ($node_list as $node) { ?>
                    <?php if (in_array($node['node_id'], $branches)) { ?>
                      <option value="<?php echo $node['node_id']; ?>" selected="selected"><?php echo $node['node_name']; ?></option>
                    <?php } else { ?>
                      <option value="<?php echo $node['node_id']; ?>"><?php echo $node['node_name']; ?></option>
                    <?php } ?>
                  <?php } ?>
                <?php } ?>
              </select>
              <div class="help-block">按住Ctrl键可多选</div>
            </div>
          </div>
          
        
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" type="submit">提交保存</button>
              <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
<script type="text/javascript">
$(document).ready(function() {
  // 添加调试信息
  console.log('Flow ID:', <?php echo json_encode($flow_id); ?>);
  console.log('Parent Node ID:', <?php echo json_encode($parent_node_id); ?>);
  console.log('Child Node IDs:', <?php echo json_encode($child_node_id); ?>);
  console.log('Branches:', <?php echo json_encode($branches); ?>);
  console.log('Node List:', <?php echo json_encode($node_list); ?>);
  
  // 初始化多选框
  if ($.fn.select2) {
    $('#input-branches').select2();
    $('#input-branches-fork').select2();
  }
  
  // 流程选择变化时，重新加载对应流程的节点列表
  $('#input-flow').change(function() {
    var flowId = $(this).val();
    var nodeFlowUrl = '<?php echo $nodeFlow; ?>';
    if (flowId) {
      // 获取token
      var token = '<?php echo isset($token) ? $token : (isset($this->session->data["token"]) ? $this->session->data["token"] : ""); ?>';
      console.log('Token:', token);
      console.log('Flow ID:', flowId);
      
      $.ajax({
        url: nodeFlowUrl + token + '&flow_id=' + flowId,
        dataType: 'json',
        beforeSend: function() {
          $('#input-current-node').prop('disabled', true);
          $('#input-branches').prop('disabled', true);
          $('#input-branches-fork').prop('disabled', true);
        },
        complete: function() {
          $('#input-current-node').prop('disabled', false);
          $('#input-branches').prop('disabled', false);
          $('#input-branches-fork').prop('disabled', false);
        },
        success: function(json) {
          console.log('API Response:', json);
          
          // 获取已选择的选项
          var selectedChildNodes = <?php echo !empty($child_node_id) ? json_encode($child_node_id) : '[]'; ?>;
          var selectedBranches = <?php echo !empty($branches) ? json_encode($branches) : '[]'; ?>;
          var currentNodeId = <?php echo !empty($parent_node_id) ? json_encode($parent_node_id) : '0'; ?>;
          
          console.log('Selected Child Nodes:', selectedChildNodes);
          console.log('Selected Branches:', selectedBranches);
          console.log('Current Node ID:', currentNodeId);
          
          // 更新当前环节选项
          var html = '<option value="">请选择当前环节</option>';
          if (json.length) {
            for (i = 0; i < json.length; i++) {
              var selected = (json[i]['node_id'] == currentNodeId) ? ' selected="selected"' : '';
              html += '<option value="' + json[i]['node_id'] + '"' + selected + '>' + json[i]['node_name'] + '</option>';
            }
          }
          $('#input-current-node').html(html);
          
          // 更新下一环节选项
          var html = '';
          if (json.length) {
            for (i = 0; i < json.length; i++) {
              var selected = '';
              if (selectedChildNodes && selectedChildNodes.includes(json[i]['node_id'])) {
                selected = ' selected="selected"';
              }
              html += '<option value="' + json[i]['node_id'] + '"' + selected + '>' + json[i]['node_name'] + '</option>';
            }
          }
          $('#input-branches').html(html);
          
          // 更新分支选项
          var html = '';
          if (json.length) {
            for (i = 0; i < json.length; i++) {
              var selected = '';
              if (selectedBranches && selectedBranches.includes(json[i]['node_id'])) {
                selected = ' selected="selected"';
              }
              html += '<option value="' + json[i]['node_id'] + '"' + selected + '>' + json[i]['node_name'] + '</option>';
            }
          }
          $('#input-branches-fork').html(html);
          
          // 刷新Select2
          if ($.fn.select2) {
            $('#input-branches').select2();
            $('#input-branches-fork').select2();
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          console.error('API Error:', xhr.status, thrownError);
          console.error('Response Text:', xhr.responseText);
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    } else {
      $('#input-current-node').html('<option value="">请选择当前环节</option>');
      $('#input-branches').html('');
      $('#input-branches-fork').html('');
    }
  });
  
  // 页面加载完成后，如果已经选择了流程，则自动触发change事件
  var selectedFlowId = $('#input-flow').val();
  console.log('Initial selected flow ID:', selectedFlowId);
  if (selectedFlowId) {
    $('#input-flow').trigger('change');
  }
});
</script>

<?php echo $content_bottom; ?><?php echo $footer; ?>
