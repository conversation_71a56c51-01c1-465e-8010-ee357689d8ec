<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        查看加价-<?php if(!empty($template['name'])){ ?><?php echo $template['name']; ?><?php } ?><br>日期：<?php if(!empty($expressFeeSurcharges['s_day'])){ ?><?php echo $expressFeeSurcharges['s_day']; ?><?php } ?>-<?php if(!empty($expressFeeSurcharges['e_day'])){ ?><?php echo $expressFeeSurcharges['e_day']; ?><?php } ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">收费标准列表</h3>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody>
               <tr>
                 <th>
                   <a href="javascript:;">省份</a>
                 </th>
                 <th>
                   <a href="javascript:;">市/区</a>
                 </th>
                 <th>
                   <a href="javascript:;">加价金额</a>
                 </th>
              </tr>

            <?php if (!empty($expressFeeSurcharges['provinces'])) { ?>
              <?php foreach ($expressFeeSurcharges['provinces'] as $provinces) { ?>
                <tr>
                  <td><?php echo $provinces['province']; ?></td>
                  <td><?php echo $provinces['city']; ?></td>
                  <td><?php echo $provinces['price']; ?></td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
      </div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {

  })()
</script>
<?php echo $footer; ?>