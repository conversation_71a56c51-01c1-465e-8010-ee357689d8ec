<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        店铺日损益
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>状态：</label>
                  <select class="form-control" name="filter_state">
                    <option value="*">全部状态</option>
                    <?php if ($filter_state === '0') { ?>
                    <option value="0" selected="selected">待确认</option>
                    <?php } else { ?>
                    <option value="0">待确认</option>
                    <?php } ?>
                    <?php if ($filter_state == '1') { ?>
                    <option value="1" selected="selected">已确认</option>
                    <?php } else { ?>
                    <option value="1">已确认</option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>账单时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">账单列表</h3>
          <div class="box-tools">
            <!-- <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a> -->
          </div>
        </div>
        <div class="box-body table-responsive no-padding">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">店铺</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">店铺</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'sales') { ?>
                  <a href="<?php echo $sort_sales; ?>" class="<?php echo strtolower($order); ?>">销售额</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_sales; ?>">销售额</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'advert') { ?>
                  <a href="<?php echo $sort_advert; ?>" class="<?php echo strtolower($order); ?>">广告费用</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_advert; ?>">广告费用</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'cost') { ?>
                  <a href="<?php echo $sort_cost; ?>" class="<?php echo strtolower($order); ?>">产品成本</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_cost; ?>">产品成本</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'fee') { ?>
                  <a href="<?php echo $sort_fee; ?>" class="<?php echo strtolower($order); ?>">运营费用</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_fee; ?>">运营费用</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'bill_total') { ?>
                  <a href="<?php echo $sort_total; ?>" class="<?php echo strtolower($order); ?>">损益金额</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_total; ?>">损益金额</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'state') { ?>
                  <a href="<?php echo $sort_state; ?>" class="<?php echo strtolower($order); ?>">状态</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_state; ?>">状态</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'bill_date') { ?>
                  <a href="<?php echo $sort_date; ?>" class="<?php echo strtolower($order); ?>">账单日期</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_date; ?>">账单日期</a>
                <?php } ?>
              </th>
              <th class="text-right">成本占比</th>
              <th class="text-right">损益占比</th>
            </tr>
            <?php if (!empty($bills)) { ?>
            <?php foreach ($bills as $bill) { ?>
            <tr>
              <?php if ($bill['state'] == '1') { ?>
              <td><?php echo $bill['store']; ?></td>
              <td>
                当日：<?php echo $bill['sales']; ?><br>
                本月：<?php echo $bill['msales']; ?><br>
              </td>
              <td>
                当日：<?php echo $bill['advert']; ?><br>
                本月：<?php echo $bill['madvert']; ?><br>
              </td>
              <td>
                当日：<?php echo $bill['cost']; ?><br>
                本月：<?php echo $bill['mcost']; ?><br>
              </td>
              <td>
              <?php if (count($bill['detail']) > 0) { ?>
              <div class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  当日：<?php echo $bill['fee']; ?>
                  <span class="caret"></span>
                </a>
                <ul class="dropdown-menu">
                  <?php foreach ($bill['detail'] as $list) { ?>
                  <li><a href="javascript:void(0);"><?php echo $list['fee_name']; ?>：<?php echo $list['amount']; ?></a></li>
                  <?php } ?>
                </ul>
              </div>
              <?php } else { ?>
              当日：<?php echo $bill['fee']; ?><br>
              <?php } ?>
              本月：<?php echo $bill['mfee']; ?><br>
              </td>
              <td>
                当日：<?php echo $bill['total']; ?><br>
                本月：<?php echo $bill['mtotal']; ?><br>
                <!-- <?php echo $bill['month']; ?> -->
              </td>
              <td>已确认</td>
              <td><?php echo $bill['date']; ?></td>
              <td class="text-right">
                当日：<?php echo $bill['dcost_ratio']; ?>%<br>
                本月：<?php echo $bill['mcost_ratio']; ?>%<br>
              </td>
              <td class="text-right">
                当日：<?php echo $bill['dtotal_ratio']; ?>%<br>
                本月：<?php echo $bill['mtotal_ratio']; ?>%<br>
              </td>
              <?php } else { ?>
              <td><?php echo $bill['store']; ?></td>
              <td>--</td>
              <td>--</td>
              <td>--</td>
              <td>--</td>
              <td>--</td>
              <td>待确认</td>
              <td><?php echo $bill['date']; ?></td>
              <td colspan="2" class="text-right">                
                <a class="btn btn-success" href="<?php echo $bill['edit']; ?>" title="">确认账单</a>
              </td>
              <?php } ?>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="10" align="center"> 暂无账单数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_state = $('select[name=\'filter_state\']').val();

      if (filter_state != '*') {
        url += '&filter_state=' + encodeURIComponent(filter_state);
      }
  
      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>