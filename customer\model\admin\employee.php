<?php
class ModelAdminEmployee extends Model {
	public function addEmployee($data) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "employee SET fullname = '" . $this->db->escape($data['fullname']) . "', telephone = '" . $this->db->escape($data['telephone']) . "', sex = '" . $this->db->escape($data['sex']) . "', birth_date = '" . $this->db->escape($data['birth_date']) . "', nation = '" . $this->db->escape($data['nation']) . "', marriage = '" . $this->db->escape($data['marriage']) . "', politics = '" . $this->db->escape($data['politics']) . "', address = '" . $this->db->escape($data['address']) . "', card_img = '" . $this->db->escape($data['card_img']) . "', card_no = '" . $this->db->escape($data['card_no']) . "', card_name = '" . $this->db->escape($data['card_name']) . "', card_address = '" . $this->db->escape($data['card_address']) . "', card_expire = '" . $this->db->escape($data['card_expire']) . "', contact_name = '" . $this->db->escape($data['contact_name']) . "', contact_relation = '" . $this->db->escape($data['contact_relation']) . "', contact_telephone = '" . $this->db->escape($data['contact_telephone']) . "', job_centre = '" . $this->db->escape($data['job_centre']) . "', job_department = '" . $this->db->escape($data['job_department']) . "', job_post = '" . $this->db->escape($data['job_post']) . "', job_office = '" . $this->db->escape($data['job_office']) . "', job_number = '" . $this->db->escape($data['job_number']) . "', job_work_date = '" . $this->db->escape($data['job_work_date']) . "', job_regular_date = '" . $this->db->escape($data['job_regular_date']) . "', job_estimate_date = '" . $this->db->escape($data['job_estimate_date']) . "', job_leave_date = '" . $this->db->escape($data['job_leave_date']) . "', job_status = '" . $this->db->escape($data['job_status']) . "', job_confidentiality = '" . $this->db->escape($data['job_confidentiality']) . "', edu_degree = '" . $this->db->escape($data['edu_degree']) . "', edu_school = '" . $this->db->escape($data['edu_school']) . "', edu_speciality = '" . $this->db->escape($data['edu_speciality']) . "', edu_graduate_date = '" . $this->db->escape($data['edu_graduate_date']) . "', channel_from = '" . $this->db->escape($data['channel_from']) . "', status = '1', date_added = NOW(), date_modified = NOW()");

		$employee_id = $this->db->getLastId();

		if (isset($data['contracts'])) {
			foreach ($data['contracts'] as $contract) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "employee_contract SET employee_id = '" . (int)$employee_id . "', company = '" . $this->db->escape($contract['company']) . "', sign_date = '" . $this->db->escape($contract['sign_date']) . "', start_date = '" . $this->db->escape($contract['start_date']) . "', end_date = '" . $this->db->escape($contract['end_date']) . "', date_added = NOW()");
			}
		}

		if (isset($data['insurances'])) {
			foreach ($data['insurances'] as $insurance) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "employee_insurance SET employee_id = '" . (int)$employee_id . "', insurance_name = '" . $this->db->escape($insurance['insurance_name']) . "', company = '" . $this->db->escape($insurance['company']) . "', add_date = '" . $this->db->escape($insurance['add_date']) . "', del_date = '" . $this->db->escape($insurance['del_date']) . "'");
			}
		}

		return $employee_id;
	}

	public function editEmployee($employee_id, $data) {
		$this->db->query("UPDATE " . DB_PREFIX . "employee SET fullname = '" . $this->db->escape($data['fullname']) . "', telephone = '" . $this->db->escape($data['telephone']) . "', sex = '" . $this->db->escape($data['sex']) . "', birth_date = '" . $this->db->escape($data['birth_date']) . "', nation = '" . $this->db->escape($data['nation']) . "', marriage = '" . $this->db->escape($data['marriage']) . "', politics = '" . $this->db->escape($data['politics']) . "', address = '" . $this->db->escape($data['address']) . "', card_img = '" . $this->db->escape($data['card_img']) . "', card_no = '" . $this->db->escape($data['card_no']) . "', card_name = '" . $this->db->escape($data['card_name']) . "', card_address = '" . $this->db->escape($data['card_address']) . "', card_expire = '" . $this->db->escape($data['card_expire']) . "', contact_name = '" . $this->db->escape($data['contact_name']) . "', contact_relation = '" . $this->db->escape($data['contact_relation']) . "', contact_telephone = '" . $this->db->escape($data['contact_telephone']) . "', job_centre = '" . $this->db->escape($data['job_centre']) . "', job_department = '" . $this->db->escape($data['job_department']) . "', job_post = '" . $this->db->escape($data['job_post']) . "', job_office = '" . $this->db->escape($data['job_office']) . "', job_number = '" . $this->db->escape($data['job_number']) . "', job_work_date = '" . $this->db->escape($data['job_work_date']) . "', job_regular_date = '" . $this->db->escape($data['job_regular_date']) . "', job_estimate_date = '" . $this->db->escape($data['job_estimate_date']) . "', job_leave_date = '" . $this->db->escape($data['job_leave_date']) . "', job_status = '" . $this->db->escape($data['job_status']) . "', job_confidentiality = '" . $this->db->escape($data['job_confidentiality']) . "', edu_degree = '" . $this->db->escape($data['edu_degree']) . "', edu_school = '" . $this->db->escape($data['edu_school']) . "', edu_speciality = '" . $this->db->escape($data['edu_speciality']) . "', edu_graduate_date = '" . $this->db->escape($data['edu_graduate_date']) . "', channel_from = '" . $this->db->escape($data['channel_from']) . "', date_modified = NOW() WHERE employee_id = '" . (int)$employee_id . "'");

		$this->db->query("DELETE FROM " . DB_PREFIX . "employee_contract WHERE employee_id = '" . (int)$employee_id . "'");

		if (isset($data['contracts'])) {
			foreach ($data['contracts'] as $contract) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "employee_contract SET employee_id = '" . (int)$employee_id . "', company = '" . $this->db->escape($contract['company']) . "', sign_date = '" . $this->db->escape($contract['sign_date']) . "', start_date = '" . $this->db->escape($contract['start_date']) . "', end_date = '" . $this->db->escape($contract['end_date']) . "', date_added = NOW()");
			}
		}

		$this->db->query("DELETE FROM " . DB_PREFIX . "employee_insurance WHERE employee_id = '" . (int)$employee_id . "'");

		if (isset($data['insurances'])) {
			foreach ($data['insurances'] as $insurance) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "employee_insurance SET employee_id = '" . (int)$employee_id . "', insurance_name = '" . $this->db->escape($insurance['insurance_name']) . "', company = '" . $this->db->escape($insurance['company']) . "', add_date = '" . $this->db->escape($insurance['add_date']) . "', del_date = '" . $this->db->escape($insurance['del_date']) . "'");
			}
		}
	}

	public function deleteEmployee($employee_id) {
		$this->db->query("UPDATE " . DB_PREFIX . "employee SET status = '-1' WHERE employee_id = '" . (int)$employee_id . "'");
		// $this->db->query("DELETE FROM " . DB_PREFIX . "employee WHERE employee_id = '" . (int)$employee_id . "'");
	}

	public function getEmployee($employee_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "employee WHERE employee_id = '" . (int)$employee_id . "'");

		return $query->row;
	}

	public function getEmployees($data = array()) {
		$sql = "SELECT employee_id, fullname, telephone, birth_date, job_department, job_post, job_work_date, job_leave_date, job_status, channel_from FROM " . DB_PREFIX . "employee WHERE status = '1'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND CONCAT(fullname, telephone) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_department'])) {
			$sql .= " AND CONCAT(job_centre, '-', job_department) LIKE '%" . $this->db->escape($data['filter_department']) . "%'";
		}

		if (!empty($data['filter_status'])) {
			$sql .= " AND job_status = '" . $this->db->escape($data['filter_status']) . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(job_work_date) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(job_work_date) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

		$sort_data = array(
			'employee_id',
			'fullname',
			'telephone',
			'birth_date',
			'job_department',
			'job_work_date',
			'job_post',
			'job_work_date',
			'job_status',
			'channel_from'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY job_work_date";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalEmployees($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "employee WHERE status = '1'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND CONCAT(fullname, telephone) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_department'])) {
			$sql .= " AND CONCAT(job_centre, '-', job_department) LIKE '%" . $this->db->escape($data['filter_department']) . "%'";
		}

		if (!empty($data['filter_status'])) {
			$sql .= " AND job_status = '" . $this->db->escape($data['filter_status']) . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(job_work_date) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(job_work_date) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function addLog($data) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "employee_log SET employee_id = '" . (int)$data['employee_id'] . "', log_action = '" . $this->db->escape($data['log_action']) . "', log_name = '" . $this->db->escape($data['log_name']) . "', content = '" . $this->db->escape($data['content']) . "', date_added = NOW()");

		if ($data['log_action'] == 'regular') {
			$this->db->query("UPDATE " . DB_PREFIX . "employee SET job_regular_date = '" . $this->db->escape($data['job_regular_date']) . "', job_status = '" . $this->db->escape($data['job_status']) . "', date_modified = NOW() WHERE employee_id = '" . (int)$data['employee_id'] . "'");
		}

		if ($data['log_action'] == 'contract' && isset($data['contracts'])) {
			$this->db->query("DELETE FROM " . DB_PREFIX . "employee_contract WHERE employee_id = '" . (int)$data['employee_id'] . "'");

			foreach ($data['contracts'] as $contract) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "employee_contract SET employee_id = '" . (int)$data['employee_id'] . "', company = '" . $this->db->escape($contract['company']) . "', sign_date = '" . $this->db->escape($contract['sign_date']) . "', start_date = '" . $this->db->escape($contract['start_date']) . "', end_date = '" . $this->db->escape($contract['end_date']) . "', date_added = NOW()");
			}
		}

		if ($data['log_action'] == 'insurance' && isset($data['insurances'])) {
			$this->db->query("DELETE FROM " . DB_PREFIX . "employee_insurance WHERE employee_id = '" . (int)$data['employee_id'] . "'");

			foreach ($data['insurances'] as $insurance) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "employee_insurance SET employee_id = '" . (int)$data['employee_id'] . "', insurance_name = '" . $this->db->escape($insurance['insurance_name']) . "', company = '" . $this->db->escape($insurance['company']) . "', add_date = '" . $this->db->escape($insurance['add_date']) . "', del_date = '" . $this->db->escape($insurance['del_date']) . "'");
			}
		}
	}

	public function getLogs($employee_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "employee_log WHERE employee_id = '" . (int)$employee_id . "'");

		return $query->rows;
	}

	public function getStatuses() {
		return array('正式', '试用', '实习', '离职', '辞退');
	}

	public function getDepartments() {
		return array(
			array('name' => '董事会', 'subs' => array('董事')),
			array('name' => '管理中心', 'subs' => array('总助', '财务部', '人事部', '技术部')),
			array('name' => '研发中心', 'subs' => array('设计部', '设样部', '建模部')),
			array('name' => '品牌中心', 'subs' => array('视觉部', '新媒体运营部', '企划部')),
			array('name' => '供应中心', 'subs' => array('商品部', '品控部', '仓储部')),
			array('name' => '营销中心', 'subs' => array('跨境运营部', '1688事业部', '淘宝运营部', '抖音运营部', '拼多多运营部', '京东运营部', '快手运营部', '客服部')),
			array('name' => '拓彩工厂', 'subs' => array('车间'))
		);
	}

	public function getCompanys() {
		return array('泉州市盈扩电子商务有限公司', '泉州市悠然见旅游文化有限公司', '泉州大笑人生文化传播有限公司', '泉州市宏真创业投资有限公司', '泉州市盈拓创业投资有限公司', '泉州市探索文化传播有限公司', '泉州市如果家居工艺有限公司', '南安海纳百川电子商务有限公司', '福州市盈扩国际贸易有限公司', '厦门如果光年文创科技有限公司');
	}

	public function getOffices() {
		return array('福州市仓山区山亚大厦', '泉州市丰泽区罗格林大楼', '厦门思明区软件园二期', '泉州市丰泽区飞洋家纺', '泉州市南安市如果仓库', '泉州市南安市源昌幸福里');
	}

	public function getDegrees() {
		return array('初中', '高中', '中专', '大专', '大专（函授）', '本科', '本科（函授）', '硕士', '博士');
	}

	public function getChannels() {
		return array('BOSS直聘', '597直聘', '大泉州人才网', '领导推荐', '内部推荐', '员工推荐');
	}

	public function getMarriages() {
		return array('未婚', '已婚', '离异');
	}

	public function getRelations() {
		return array('夫妻', '父子', '父女', '母子', '母女', '兄弟', '兄妹', '姐弟', '姐妹', '其他');
	}

	public function getPoliticses() {
		return array('党员', '团员', '群众', '其他');
	}

	public function getInnames() {
		return array('医疗险', '生育险', '养老险', '失业险', '工伤险', '平安险');
	}

	public function getContract($employee_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "employee_contract WHERE employee_id = '" . (int)$employee_id . "'");

		return $query->rows;
	}

	public function getInsurance($employee_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "employee_insurance WHERE employee_id = '" . (int)$employee_id . "'");

		return $query->rows;
	}

	public function getAge($format, $start_date, $end_date = '') {
		if (!empty($end_date)) {
			$diff = strtotime($end_date) - strtotime($start_date);
		} else {
			$diff = time() - strtotime($start_date);
		}		
		$year = floor($diff / (365 * 86400));
		$diff -= $year * 365 * 86400;
		$month = floor($diff / (30 * 86400));
		return sprintf($format, $year, $month);
	}

	public function getRangeExp($early = 30, $over = 7) {
		return "BETWEEN DATE_SUB(CURDATE(), INTERVAL " . (int)$over . " DAY) AND DATE_ADD(CURDATE(), INTERVAL " . (int)$early . " DAY)";
	}

	// 转正时间提醒
	public function getRegularRemind() {
		$statuses = $this->getStatuses();

		$query = $this->db->query("SELECT e.employee_id, fullname, job_department, job_work_date, job_estimate_date, DATEDIFF(job_estimate_date, CURDATE()) AS diff_days FROM " . DB_PREFIX . "employee e LEFT JOIN (SELECT employee_id, COUNT(*) AS times FROM " . DB_PREFIX . "employee_log WHERE log_action = 'regular' GROUP BY employee_id) el ON (e.employee_id = el.employee_id) WHERE status = '1' AND job_status = '" . $statuses[1] . "' AND job_estimate_date != '' AND ((times IS NULL AND job_estimate_date " . $this->getRangeExp(30) . ") OR (times = '1' AND job_estimate_date " . $this->getRangeExp(15) . ") OR (times = '2' AND job_estimate_date " . $this->getRangeExp(3) . ")) ORDER BY diff_days ASC");

		return $query->rows;
	}

	// 员工司日提醒
	public function getJobAgeRemind() {
		$statuses = $this->getStatuses();
		$age_date_exp = "CONCAT(YEAR(CURDATE()), DATE_FORMAT(job_work_date, '-%m-%d'))";
		$age_exp = "TIMESTAMPDIFF(YEAR, job_work_date, CURDATE())";

		$query = $this->db->query("SELECT employee_id, fullname, job_department, job_work_date, " . $age_date_exp . " AS job_age_date, job_work_date, DATEDIFF(" . $age_date_exp . ", CURDATE()) AS diff_days FROM " . DB_PREFIX . "employee WHERE status = '1' AND job_status = '" . $statuses[0] . "' AND job_work_date != '' AND " . $age_date_exp . " " . $this->getRangeExp() . " AND employee_id NOT IN (SELECT employee_id FROM " . DB_PREFIX . "employee_log WHERE log_action = 'jobage' AND date_added > DATE_SUB(CURDATE(), INTERVAL 60 DAY)) ORDER BY diff_days ASC");

		return $query->rows;
	}

	// 劳动合同提醒
	public function getContractRemind() {
		$statuses = $this->getStatuses();

		$query = $this->db->query("SELECT e.employee_id, e.fullname, e.job_department, e.job_work_date, ec.expire, DATEDIFF(ec.expire, CURDATE()) AS diff_days FROM " . DB_PREFIX . "employee e LEFT JOIN (SELECT employee_id, MAX(CASE end_date WHEN '' THEN '2099-12-31' ELSE end_date END) AS expire FROM " . DB_PREFIX . "employee_contract GROUP BY employee_id) ec ON (e.employee_id = ec.employee_id) WHERE e.status = '1' AND e.job_status = '" . $statuses[0] . "' AND (ec.expire IS NULL OR ec.expire " . $this->getRangeExp() . ")");

		return $query->rows;
	}

	// 医社保提醒
	public function getInsuranceRemind() {
		$statuses = $this->getStatuses();

		$query = $this->db->query("SELECT e.employee_id, e.fullname, e.job_department, e.job_work_date, e.job_regular_date, e.job_leave_date, e.job_status, ei.insurance_name FROM " . DB_PREFIX . "employee e LEFT JOIN " . DB_PREFIX . "employee_insurance ei ON (e.employee_id = ei.employee_id) WHERE e.status = '1' AND ((e.job_status = '" . $statuses[0] . "' AND (ei.add_date IS NULL OR ei.add_date = '')) OR ((e.job_status = '" . $statuses[3] . "' OR e.job_status = '" . $statuses[4] . "') AND ei.del_date = ''))");

		return $query->rows;
	}
}
