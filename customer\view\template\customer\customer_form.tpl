<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
          <div class="nav-tabs-custom">
            <ul class="nav nav-tabs">
              <li><a href="#tab-general" data-toggle="tab">基本信息</a></li>
              <?php if (!empty($summary)) { ?>
              <li><a href="#tab-summary" data-toggle="tab">下单汇总</a></li>
              <?php } ?>
              <?php if (isset($orders)) { ?>
              <li><a href="#tab-orders" data-toggle="tab">下单明细</a></li>
              <?php } ?>
            </ul>
            <div class="tab-content">
              <div class="tab-pane" id="tab-general">
                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">客户*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="nickname" value="<?php echo $nickname; ?>" placeholder="请输入客户名称" id="input-nickname" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-store">业务归属*：</label>
                  <div class="col-sm-8">
                    <select name="store_id" id="input-store" class="form-control" onchange="$('#input-group').load('<?php echo $getGroup; ?>&store_id=' + this.value + '&group_id=<?php echo $customer_group_id; ?>');">
                      <option value="">请选择所属店铺</option>
                      <?php foreach($stores as $store) { ?>
                      <?php if ($store['store_id'] == $store_id) { ?>
                      <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                      <?php } else { ?>
                      <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                      <?php } ?>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-group">客户级别*：</label>
                  <div class="col-sm-8">
                    <select name="customer_group_id" id="input-group" class="form-control"></select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-realname">联系人*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="realname" value="<?php echo $realname; ?>" placeholder="请输入联系人" id="input-realname" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-telephone">联系方式*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="telephone" value="<?php echo $telephone; ?>" placeholder="联系方式（电话和邮箱）" id="input-telephone" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-address">地址：</label>
                  <div class="col-sm-8">
                    <input type="text" name="address" value="<?php echo $address; ?>" placeholder="请输入地址" id="input-address" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-channel">销售渠道*：</label>
                  <div class="col-sm-8">
                    <select name="channel" id="input-channel" class="form-control">
                      <option value="">请选择销售渠道</option>
                      <?php foreach($channels as $row) { ?>
                      <?php if ($row['channel'] == $channel) { ?>
                      <option value="<?php echo $row['channel']; ?>" selected="selected"><?php echo $row['channel']; ?></option>
                      <?php } else { ?>
                      <option value="<?php echo $row['channel']; ?>"><?php echo $row['channel']; ?></option>
                      <?php } ?>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-product">进货类型：</label>
                  <div class="col-sm-8">
                    <input type="text" name="product" value="<?php echo $product; ?>" placeholder="请输入进货类型" id="input-product" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-state">客户状态*：</label>
                  <div class="col-sm-8">
                    <select name="state" id="input-state" class="form-control">
                      <option value="">请选择客户状态</option>
                      <?php foreach($states as $row) { ?>
                      <?php if ($row['state'] == $state) { ?>
                      <option value="<?php echo $row['state']; ?>" selected="selected"><?php echo $row['state']; ?></option>
                      <?php } else { ?>
                      <option value="<?php echo $row['state']; ?>"><?php echo $row['state']; ?></option>
                      <?php } ?>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-remark">备注：</label>
                  <div class="col-sm-8">
                    <input type="text" name="remark" value="<?php echo $remark; ?>" placeholder="请输入备注内容，可不填" id="input-remark" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <div class="col-sm-offset-2 col-sm-8">
                    <button class="btn btn-primary" type="submit">提交保存</button>
                    <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
                  </div>
                </div>
              </div>
              <?php if (!empty($summary)) { ?>
              <div class="tab-pane" id="tab-summary">
                <div class="panel panel-primary">
                  <div class="panel-heading">累计下单数据</div>
                  <div class="panel-body">
                    下单次数：<?php echo $order_times; ?>；
                    下单金额：<?php echo $order_total; ?>；
                    首次下单时间：<?php echo $date_first; ?>；
                    最后下单时间：<?php echo $date_last; ?>；
                  </div>
                </div>
                <div class="panel panel-success">
                  <div class="panel-heading">年度下单数据</div>
                  <div class="panel-body">
                    <div id="chartMain" style="width: 1000px;height:500px;"></div>
                  </div>
                </div>
              </div>
              <?php } ?>
              <?php if (isset($orders)) { ?>
              <div class="tab-pane" id="tab-orders">
                <div id="orders"></div>
                <br />
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-date">下单日期：</label>
                  <div class="col-sm-8">
                    <div class="input-group">
                      <div class="input-group-addon">
                        <i class="glyphicon glyphicon-calendar"></i>
                      </div>
                      <input type="text" name="order_date" value="" placeholder="请选择下单日期" id="input-date" class="form-control" />
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-times">下单次数：</label>
                  <div class="col-sm-8">
                    <select name="order_times" id="input-times" class="form-control">
                      <?php for ($i=1; $i <= 20; $i++) { ?>
                      <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-total">下单金额：</label>
                  <div class="col-sm-8">
                    <input type="text" name="order_total" value="" placeholder="请输入下单金额" id="input-total" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-oproduct">下单产品：</label>
                  <div class="col-sm-8">
                    <input type="text" name="order_product" value="" placeholder="选填，主要下单产品" id="input-oproduct" class="form-control" />
                  </div>
                </div>
                <div class="text-center">
                  <button type="button" id="button-order" data-loading-text="正在添加记录..." class="btn btn-danger"><i class="fa fa-plus-circle"></i> 增加下单记录</button>
                </div>
              </div>
              <?php } ?>
            </div>
          </div>
            
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script src="<?php echo HTTP_SERVER; ?>static/js/echarts.min.js"></script>
<script type="text/javascript">
$('.nav-tabs li:first a').tab('show');

$('#input-group').load('<?php echo $getGroup; ?>&store_id=<?php echo $store_id; ?>&group_id=<?php echo $customer_group_id; ?>');

<?php if (!empty($summary)) { ?>
var myChart = echarts.init(document.getElementById('chartMain'));
myChart.clear();
myChart.setOption(<?php echo json_encode($summary, 320); ?>);
<?php } ?>

<?php if (isset($orders)) { ?>
$('#orders').delegate('.pagination a', 'click', function(e) {
  e.preventDefault();

  $('#orders').load(this.href);
});

$('#orders').load('<?php echo $orders; ?>');

// 日期显示
$('#input-date').daterangepicker({
  autoApply: true,
  autoUpdateInput: false,
  singleDatePicker: true,
  timePicker: false,
  locale: {
    format: 'YYYY-MM-DD',
    applyLabel: '确定',
    cancelLabel: '清除'
  }
})
$('#input-date').on('apply.daterangepicker', function(ev, picker) {
  $(this).val(picker.startDate.format('YYYY-MM-DD'))
})
$('#input-date').on('cancel.daterangepicker', function(ev, picker) {
  $(this).val('')
})

$('#button-order').on('click', function(e) {
  e.preventDefault();

  $.ajax({
    url: '<?php echo $addorder; ?>',
    type: 'post',
    dataType: 'json',
    data: 'order_times=' + encodeURIComponent($('#tab-orders select[name=\'order_times\']').val()) + '&order_total=' + encodeURIComponent($('#tab-orders input[name=\'order_total\']').val()) + '&order_date=' + encodeURIComponent($('#tab-orders input[name=\'order_date\']').val()) + '&order_product=' + encodeURIComponent($('#tab-orders input[name=\'order_product\']').val()),
    beforeSend: function() {
      $('#button-order').button('loading');
    },
    complete: function() {
      $('#button-order').button('reset');
    },
    success: function(json) {
      $('.alert').remove();

      if (json['error']) {
        $('#tab-orders').prepend('<div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
      }

      if (json['success']) {
        $('#tab-orders').prepend('<div class="alert alert-success"><i class="glyphicon glyphicon-info-sign"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');

        $('#orders').load('<?php echo $orders; ?>');

        $('#tab-orders input[name=\'order_total\']').val('');
        $('#tab-orders input[name=\'order_product\']').val('');
      }
    }
  });
});
<?php } ?>
</script>
<?php echo $footer; ?>