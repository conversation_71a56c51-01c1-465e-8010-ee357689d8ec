<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-quan">类别：</label>
              <div class="col-sm-8">
                <input type="text" name="name" value="<?php if(!empty($alipayBillSortKey['name'])){ ?><?php echo $alipayBillSortKey['name']; ?><?php } ?>" placeholder="请输入类别" id="input-name" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-quan">权重：</label>
              <div class="col-sm-8">
                <input type="number" name="sort" value="<?php if(!empty($alipayBillSortKey['sort'])){ ?><?php echo $alipayBillSortKey['sort']; ?><?php } ?>" placeholder="请输入权重" id="input-sort" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-quan">导出权重：</label>
              <div class="col-sm-8">
                <input type="number" name="export_sort" value="<?php if(!empty($alipayBillSortKey['export_sort'])){ ?><?php echo $alipayBillSortKey['export_sort']; ?><?php } ?>" placeholder="请输入导出权重" id="input-export_sort" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-quan">执行方式：</label>
              <div class="col-sm-8">
                <div class="radio">
                  <label><input type="radio" name="type" value="1" onchange="typesty($(this))" <?php if(empty($alipayBillSortKey['type']) || (!empty($alipayBillSortKey['type']) && $alipayBillSortKey['type'] == 1)){ ?>checked<?php } ?>>全部符合</label>
                  <label style="margin-left: 10px"><input type="radio" name="type" value="2" onchange="typesty($(this))" <?php if(!empty($alipayBillSortKey['type']) && $alipayBillSortKey['type'] == 2){ ?>checked<?php } ?>>一条符合</label>
                  <label style="margin-left: 10px"><input type="radio" name="type" value="3" onchange="typesty($(this))" <?php if(!empty($alipayBillSortKey['type']) && $alipayBillSortKey['type'] == 3){ ?>checked<?php } ?>>没有条件</label>
                </div>
              </div>
            </div>
            <div class="all-field-condition" <?php if(!empty($alipayBillSortKey['type']) && $alipayBillSortKey['type'] == 3){ ?>style="display: none"<?php } ?>>
              <?php if(!empty($alipayBillSortKey['keyword'])){ ?>
              <?php foreach($alipayBillSortKey['keyword'] as $keyword_key => $keyword){ ?>
              <div class="field-condition" style="padding-top: 20px;border-top: 1px solid #f39c12">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-mode">选择字段：</label>
                  <div class="col-sm-8">
                    <select class="form-control"  name="fields[<?php echo $keyword_key; ?>][field]" style="width: 80%;float: left">
                      <?php foreach ($fields as $key=>$field) { ?>
                      <option value="<?php echo $key; ?>" <?php if($keyword['field']==$key){ ?>selected<?php } ?>><?php echo $field[0]; ?></option>
                      <?php } ?>
                    </select>
                    <?php if($keyword_key == 0){ ?>
                    <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                      <a class="btn btn-primary" href="javascript:addfield();">添加字段</a>
                    </div>
                    <?php } else { ?>
                    <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                      <a class="btn btn-danger" href="javascript:;" onclick="delfield($(this))">删除字段</a>
                    </div>
                    <?php } ?>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-mode">选择关系运算：</label>
                  <div class="col-sm-8">
                    <select class="form-control"  name="fields[<?php echo $keyword_key; ?>][operation]" style="width: 80%">
                      <?php foreach ($operations as $key=>$operation) { ?>
                      <option value="<?php echo $key; ?>" <?php if($keyword['operation']==$key){ ?>selected<?php } ?>><?php echo $operation; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">执行方式：</label>
                  <div class="col-sm-8">
                    <div class="radio">
                      <label><input type="radio" name="fields[<?php echo $keyword_key; ?>][type]" value="1"  <?php if(!empty($keyword['type']) && $keyword['type'] == 1){ ?>checked<?php } ?>>全部符合</label>
                      <label style="margin-left: 10px"><input type="radio" name="fields[<?php echo $keyword_key; ?>][type]" value="2"  <?php if(empty($keyword['type']) || (!empty($keyword['type']) && $keyword['type'] == 2)){ ?>checked<?php } ?>>一条符合</label>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">关键字：</label>
                  <div class="col-sm-8 keyword">
                    <?php foreach ($keyword['keyword'] as $keykey =>$keyval ) { ?>
                    <?php if($keykey==0) { ?>
                    <div>
                      <input type="text" name="fields[<?php echo $keyword_key; ?>][keyword][]" value="<?php echo $keyval; ?>" placeholder="请输入关键字" id="input-keyword" class="form-control" style="width: 70%;float: left" />
                      <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                        <a class="btn btn-primary" href="javascript:;" onclick="addkey($(this),<?php echo $keyword_key; ?>)">添加关键字</a>
                      </div>
                    </div>
                    <?php } else { ?>
                    <div>
                      <input type="text" name="fields[<?php echo $keyword_key; ?>][keyword][]" value="<?php echo $keyval; ?>" placeholder="请输入关键字" id="input-keyword" class="form-control" style="width: 70%;float: left;margin-top: 5px" />
                      <div class="box-tools" style="width: 15%;float: left;margin-left: 8px;margin-top: 5px">
                        <a class="btn btn-danger" href="javascript:;" onclick="delkey($(this))">删除</a>
                      </div>
                    </div>
                    <?php } ?>
                    <?php } ?>
                  </div>
                </div>
              </div>
              <?php } ?>
              <?php } else { ?>
              <div class="field-condition" style="padding-top: 20px;border-top: 1px solid #f39c12">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-mode">选择字段：</label>
                  <div class="col-sm-8">
                    <select class="form-control"  name="fields[0][field]" style="width: 80%;float: left">
                      <?php foreach ($fields as $key=>$field) { ?>
                      <option value="<?php echo $key; ?>"><?php echo $field[0]; ?></option>
                      <?php } ?>
                    </select>
                    <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                      <a class="btn btn-primary" href="javascript:addfield();">添加字段</a>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-mode">选择关系运算：</label>
                  <div class="col-sm-8">
                    <select class="form-control"  name="fields[0][operation]" style="width: 80%">
                      <?php foreach ($operations as $key=>$operation) { ?>
                      <option value="<?php echo $key; ?>"><?php echo $operation; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">执行方式：</label>
                  <div class="col-sm-8">
                    <div class="radio">
                      <label><input type="radio" name="fields[0][type]" value="1" >全部符合</label>
                      <label style="margin-left: 10px"><input type="radio" name="fields[0][type]" value="2"  checked>一条符合</label>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">关键字：</label>
                  <div class="col-sm-8 keyword">
                    <div>
                      <input type="text" name="fields[0][keyword][]" value="" placeholder="请输入关键字" id="input-keyword" class="form-control" style="width: 70%;float: left" />
                      <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                        <a class="btn btn-primary" href="javascript:;" onclick="addkey($(this),0)">添加关键字</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <?php } ?>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  var num = parseInt('<?php echo $num; ?>');
  var fields = '<?php echo $fields_json; ?>'
  var operations = '<?php echo $operations_json; ?>'
  function toVaild() {
    var sort = document.getElementById("input-name").value;
    // if ($('.glyphicon-info-sign').length) {
    //   confirm('分类不存在')
    //   return false;
    // }
    if (sort == "") {
      confirm('请输入类别')
      return false;
    }
    return true;
  }

  function addfield() {
    num += 1;
    var addHtml ='<div class="field-condition" style="padding-top: 20px;border-top: 1px solid #f39c12">'+
            '<div class="form-group">'+
            '<label class="col-sm-2 control-label" for="input-mode">选择字段：</label>'+
            '<div class="col-sm-8">'+
    '<select class="form-control"  name="fields['+num+'][field]" style="width: 80%;float: left">'

    $.each($.parseJSON(fields), function(i,val){
      addHtml += '<option value="'+i+'">'+val[0]+'</option>'
    });
    addHtml += '</select>'+
    '<div class="box-tools" style="width: 15%;float: left;margin-left: 8px">'+
    '<a class="btn btn-danger" href="javascript:;" onclick="delfield($(this))">删除字段</a>'+
    '</div>'+
    '</div>'+
    '</div>'+
    '<div class="form-group">'+
    '<label class="col-sm-2 control-label" for="input-mode">选择关系运算：</label>'+
    '<div class="col-sm-8">'+
    '<select class="form-control"  name="fields['+num+'][operation]" style="width: 80%">'

    $.each($.parseJSON(operations), function(i,val){
      addHtml += '<option value="'+i+'">'+val+'</option>'
    });
    addHtml += '</select>'+
    '</div>'+
    '</div>'+
    '<div class="form-group">'+
    '<label class="col-sm-2 control-label" for="input-quan">执行方式：</label>'+
    '<div class="col-sm-8">'+
    '<div class="radio">'+
    '<label><input type="radio" name="fields['+num+'][type]" value="1" >全部符合</label>'+
    '<label style="margin-left: 10px"><input type="radio" name="fields['+num+'][type]" value="2"  checked>一条符合</label>'+
    '</div>'+
    '</div>'+
    '</div>'+
    '<div class="form-group">'+
    '<label class="col-sm-2 control-label" for="input-quan">关键字：</label>'+
    '<div class="col-sm-8 keyword">'+
    '<div>'+
    '<input type="text" name="fields['+num+'][keyword][]" value="" placeholder="请输入关键字" id="input-keyword" class="form-control" style="width: 70%;float: left" />'+
    '<div class="box-tools" style="width: 15%;float: left;margin-left: 8px">'+
    '<a class="btn btn-primary" href="javascript:;" onclick="addkey($(this),'+num+')">添加关键字</a>'+
    '</div>'+
    '</div>'+
    '</div>'+
    '</div>'+
    '</div>';
    $(".all-field-condition").append(addHtml);
  }

  function delfield(obj) {
    $(obj).parent().parent().parent().parent().remove();
  }

  function addkey(obj,num) {
    var addHtml =
            '<div>' +
            '<input type="text" name="fields['+num+'][keyword][]" value="" placeholder="请输入关键字" id="input-keyword" class="form-control" style="width: 70%;float: left;margin-top: 5px" />'+
            '<div class="box-tools" style="width: 15%;float: left;margin-left: 8px;margin-top: 5px">' +
            '<a class="btn btn-danger" href="javascript:;" onclick="delkey($(this))">删除</a>' +
            '</div>' +
            '</div>';
    $(obj).parent().parent().parent().append(addHtml);
  }

  function delkey(obj) {
    $(obj).parent().parent().remove();
  }

  function typesty(obj) {
    if (obj.val() == 3) {
      $(".all-field-condition").hide()
    } else {
      $(".all-field-condition").show()
    }
  }

</script>
<?php echo $footer; ?>