<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-user">考核用户：</label>
              <div class="col-sm-8">
                <select name="user_id" id="input-user" class="form-control">
                  <?php foreach ($users as $user) { ?>
                  <?php if ($user['user_id'] == $user_id) { ?>
                  <option value="<?php echo $user['user_id']; ?>" selected="selected"><?php echo $user['real_name']; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $user['user_id']; ?>"><?php echo $user['real_name']; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>          
            <div class="form-group">
              <label class="col-sm-2 control-label">考核指标：</label>
              <div class="col-sm-8">
                <div class="well well-sm" style="height: 250px; overflow: auto;">
                  <?php foreach ($items as $item) { ?>
                  <?php if (in_array($item['item_id'], $kpi_item_ids)) { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="kpi_item_ids[]" value="<?php echo $item['item_id']; ?>" checked="checked"><?php echo $item['item_name']; ?></label>
                  </div>
                  <?php } else { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="kpi_item_ids[]" value="<?php echo $item['item_id']; ?>"><?php echo $item['item_name']; ?></label>
                  </div>
                  <?php } ?>
                  <?php } ?>
                </div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>