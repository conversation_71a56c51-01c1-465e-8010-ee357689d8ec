<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        员工轨迹记录
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-warning">
        <div class="box-body">
          <table class="table table-striped table-bordered table-hover"><tbody>
              <tr>
                <td width="180">姓名：</td>
                <td><?php echo $employee_info['fullname']; ?></td>
              </tr>
              <tr>
                <td width="180">所属部门：</td>
                <td><?php echo $employee_info['job_centre'] . '-' . $employee_info['job_department']; ?></td>
              </tr>
              <tr>
                <td width="180">入职时间：</td>
                <td><?php echo $employee_info['job_work_date']; ?></td>
              </tr>
            </tbody></table>
        </div>
      </div>
      <div class="box box-danger">
        <div class="box-header">
          <h3 class="box-title"><?php echo $log_name; ?></h3>
        </div>
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">            
            <?php $contract_row = 0; ?>
            <?php $insurance_row = 0; ?>
            <?php if ($log_action == 'regular') { ?>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-jobregulardate">实际转正时间：</label>
              <div class="col-sm-8">
                <input type="text" name="job_regular_date" value="<?php echo $employee_info['job_regular_date']; ?>" placeholder="实际转正时间" id="input-jobregulardate" class="input-date form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-jobstatus">任职状态：</label>
              <div class="col-sm-8">
                <select name="job_status" id="input-jobstatus" class="form-control">
                  <?php foreach($statuses as $status) { ?>
                    <?php if ($status == $employee_info['job_status']) { ?>
                    <option value="<?php echo $status; ?>" selected="selected"><?php echo $status; ?></option>
                    <?php } else{ ?>
                    <option value="<?php echo $status; ?>"><?php echo $status; ?></option>
                    <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>
            <?php } elseif ($log_action == 'contract') { ?>
            <div class="form-group">
              <label class="col-sm-2 control-label">合同明细：</label>
              <div class="table-responsive col-sm-8">
                  <table id="contracts" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <td class="text-left">单位名称</td>
                        <td class="text-left">签订时间</td>
                        <td class="text-left">起始时间</td>
                        <td class="text-left">到期时间</td>
                        <td></td>
                      </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($contracts as $contract) { ?>
                      <tr id="contract-row<?php echo $contract_row; ?>">
                        <td class="text-left"><select name="contracts[<?php echo $contract_row; ?>][company]" class="form-control">
                          <?php foreach($companys as $row) { ?>
                          <option value="<?php echo $row; ?>"<?php if ($contract['company'] == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                          <?php } ?>
                        </select></td>
                        <td class="text-left"><input type="text" name="contracts[<?php echo $contract_row; ?>][sign_date]" value="<?php echo ($contract['sign_date'] == '0000-00-00') ? '' : $contract['sign_date']; ?>" placeholder="签订时间" class="input-date form-control" /></td>
                        <td class="text-left"><input type="text" name="contracts[<?php echo $contract_row; ?>][start_date]" value="<?php echo ($contract['start_date'] == '0000-00-00') ? '' : $contract['start_date']; ?>" placeholder="起始时间" class="input-date form-control" /></td>
                        <td class="text-left"><input type="text" name="contracts[<?php echo $contract_row; ?>][end_date]" value="<?php echo ($contract['end_date'] == '0000-00-00') ? '' : $contract['end_date']; ?>" placeholder="到期时间" class="input-date form-control" /></td>
                        <td class="text-left"><button type="button" onclick="$('#contract-row<?php echo $contract_row; ?>').remove();" data-toggle="tooltip" title="删除" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
                      </tr>
                      <?php $contract_row++; ?>
                      <?php } ?>
                    </tbody>
                    <tfoot>
                      <tr>
                        <td colspan="4"></td>
                        <td class="text-left"><button type="button" onclick="addContract();" data-toggle="tooltip" title="增加" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                      </tr>
                    </tfoot>
                  </table>
              </div>
            </div>
            <?php } elseif ($log_action == 'insurance') { ?>
            <div class="form-group">
              <label class="col-sm-2 control-label">保险明细：</label>
              <div class="table-responsive col-sm-8">
                  <table id="insurances" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <td class="text-left">保险类型</td>
                        <td class="text-left">缴纳单位</td>
                        <td class="text-left">增员时间</td>
                        <td class="text-left">减员时间</td>
                        <td></td>
                      </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($insurances as $insurance) { ?>
                      <tr id="insurance-row<?php echo $insurance_row; ?>">
                        <td class="text-left"><select name="insurances[<?php echo $insurance_row; ?>][insurance_name]" class="form-control">
                            <?php foreach ($innames as $inname) { ?>
                              <?php if ($insurance['insurance_name'] == $inname) { ?>
                              <option value="<?php echo $inname; ?>" selected="selected"><?php echo $inname; ?></option>
                              <?php } else{ ?>
                              <option value="<?php echo $inname; ?>"><?php echo $inname; ?></option>
                              <?php } ?>
                            <?php } ?> 
                          </select></td>
                        <td class="text-left"><select name="insurances[<?php echo $insurance_row; ?>][company]" class="form-control">
                          <?php foreach($companys as $row) { ?>
                          <option value="<?php echo $row; ?>"<?php if ($insurance['company'] == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                          <?php } ?>
                        </select></td>
                        <td class="text-left"><input type="text" name="insurances[<?php echo $insurance_row; ?>][add_date]" value="<?php echo ($insurance['add_date'] == '0000-00-00') ? '' : $insurance['add_date']; ?>" placeholder="增员时间" class="input-date form-control" /></td>
                        <td class="text-left"><input type="text" name="insurances[<?php echo $insurance_row; ?>][del_date]" value="<?php echo ($insurance['del_date'] == '0000-00-00') ? '' : $insurance['del_date']; ?>" placeholder="减员时间" class="input-date form-control" /></td>
                        <td class="text-left"><button type="button" onclick="$('#insurance-row<?php echo $insurance_row; ?>').remove();" data-toggle="tooltip" title="删除" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
                      </tr>
                      <?php $insurance_row++; ?>
                      <?php } ?>
                    </tbody>
                    <tfoot>
                      <tr>
                        <td colspan="4"></td>
                        <td class="text-left"><button type="button" onclick="addInsurance();" data-toggle="tooltip" title="增加" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                      </tr>
                    </tfoot>
                  </table>
              </div>
            </div>
            <?php } ?>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">记录说明：</label>
              <div class="col-sm-8">
                <textarea name="content" rows="5" maxlength="500" placeholder="请输入简要说明，最长不能超过500字。" class="form-control"></textarea>
              </div>
            </div>
            <input type="hidden" name="employee_id" value="<?php echo $employee_info['employee_id']; ?>">
            <input type="hidden" name="log_name" value="<?php echo $log_name; ?>">
            <input type="hidden" name="log_action" value="<?php echo $log_action; ?>">
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
// 日期显示
$('.input-date').daterangepicker({
  autoApply: true,
  autoUpdateInput: false,
  singleDatePicker: true,
  timePicker: false,
  timePicker24Hour: false,
  locale: {
    format: 'YYYY-MM-DD',
    applyLabel: '确定',
    cancelLabel: '清除'
  }
})
$('.tab-content').on('apply.daterangepicker', '.input-date', function(ev, picker) {
  $(this).val(picker.startDate.format('YYYY-MM-DD'))
})
$('.tab-content').on('cancel.daterangepicker', '.input-date', function(ev, picker) {
  $(this).val('')
})
</script>
<script type="text/javascript"><!--
var contract_row = <?php echo $contract_row; ?>;
var insurance_row = <?php echo $insurance_row; ?>;

function addContract() {
  html  = '<tr id="contract-row' + contract_row + '">';
  html += '  <td class="text-left"><select name="contracts[' + contract_row + '][company]" value="" class="form-control">';
  <?php foreach($companys as $company) { ?>
  html += '<option value="<?php echo $company; ?>"><?php echo $company; ?></option>';
  <?php } ?>
  html += '</select></td>';
  html += '  <td class="text-left"><input type="text" name="contracts[' + contract_row + '][sign_date]" value="" placeholder="签订时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><input type="text" name="contracts[' + contract_row + '][start_date]" value="" placeholder="起始时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><input type="text" name="contracts[' + contract_row + '][end_date]" value="" placeholder="到期时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><button type="button" onclick="$(\'#contract-row' + contract_row  + '\').remove();" data-toggle="tooltip" title="删除" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
  html += '</tr>';

  $('#contracts tbody').append(html);

  addDatePicker('contract-row' + contract_row);

  contract_row++;
}

function addInsurance() {
  html  = '<tr id="insurance-row' + insurance_row + '">';
  html += '  <td class="text-left"><select name="insurances[' + insurance_row + '][insurance_name]" class="form-control">';
  <?php foreach ($innames as $inname) { ?>
  html += '<option value="<?php echo $inname; ?>"><?php echo $inname; ?></option>';
  <?php } ?>   
  html += '</select></td>';
  html += '  <td class="text-left"><select name="insurances[' + insurance_row + '][company]" value="" class="form-control">';
  <?php foreach($companys as $company) { ?>
  html += '<option value="<?php echo $company; ?>"><?php echo $company; ?></option>';
  <?php } ?>
  html += '</select></td>';
  html += '  <td class="text-left"><input type="text" name="insurances[' + insurance_row + '][add_date]" value="" placeholder="增员时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><input type="text" name="insurances[' + insurance_row + '][del_date]" value="" placeholder="减员时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><button type="button" onclick="$(\'#insurance-row' + insurance_row + '\').remove();" data-toggle="tooltip" title="删除" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
  html += '</tr>';
  
  $('#insurances tbody').append(html);

  addDatePicker('insurance-row' + insurance_row);
  
  insurance_row++;
}

function addDatePicker(rowid) {
  $('#' + rowid + ' .input-date').daterangepicker({
    autoApply: true,
    autoUpdateInput: false,
    singleDatePicker: true,
    timePicker: false,
    timePicker24Hour: false,
    locale: {
      format: 'YYYY-MM-DD',
      applyLabel: '确定',
      cancelLabel: '清除'
    }
  })
}
//--></script>
<?php echo $footer; ?>