<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        采购列表
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>采购合同号：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="采购合同号" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>产品名称编码：</label>
                  <input type="text" class="form-control" name="filter_bsku" placeholder="产品名称编码" value="<?php echo $filter_bsku; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>采购时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">采购列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <div class="table-responsive">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>合同编号</th>
              <th>合同日期</th>
              <th>采购种类</th>
              <th>采购数量</th>
              <th>采购时间</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($orders)) { ?>
            <?php foreach ($orders as $order) { ?>
            <tr data-id="<?php echo $order['purchase_id']; ?>" data-no="<?php echo $order['order_no']; ?>">
              <td><?php echo $order['order_no']; ?></td>
              <td><?php echo $order['complete_date']; ?></td>
              <td><div class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  <?php echo count($order['bsku_list']); ?>
                  <span class="caret"></span>
                </a>
                <ul class="dropdown-menu">
                  <?php $quantity = 0; ?>
                  <?php foreach ($order['bsku_list'] as $list) { ?>
                  <?php $quantity += (int)$list['order_quan']; ?>
                  <li><a href="javascript:void(0);"><?php echo $list['bsku'] . $list['spec_name']; ?>：<?php echo $list['order_quan']; ?></a></li>
                  <?php } ?>
                </ul>
              </div></td>
              <td><?php echo $quantity; ?></td>
              <td><?php echo $order['date_added']; ?></td>
              <td class="text-right">
                <button class="btn btn-primary" type="button" data-toggle="modal" data-target="#edit-modal">修改编号</button>
                <a class="btn btn-success" href="<?php echo $order['view']; ?>" target="_blank">查看</a>
                <a class="btn btn-danger" href="<?php echo $order['export']; ?>">下载</a>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="6" align="center"> 暂无备货数据 </td>
            <?php } ?>
          </tbody></table>
          </div>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 修改 -->
      <div class="modal modal-primary fade" id="edit-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $edit; ?>" method="post" enctype="multipart/form-data" id="form-edit">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">修改</h4>
              </div>
              <div class="modal-body">
                <p>确定修改此合同号吗？</p>
                <input type="text" name="order_no" value="" placeholder="输入采购合同号" id="input-product" class="form-control" />
                <input id="edit-id" name="purchase_id" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="edit-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_bsku = $('input[name=\'filter_bsku\']').val();
  
      if (filter_bsku) {
        url += '&filter_bsku=' + encodeURIComponent(filter_bsku);
      }
  
      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });

    $('#edit-modal').on('show.bs.modal', function(event) {
      $('#edit-id').val($(event.relatedTarget).parents('tr').data('id'))
      $('input[name="order_no"]').val($(event.relatedTarget).parents('tr').data('no'))
    })
    $('#edit-yes').on('click', () => {$('#form-edit').submit()})
  })()
</script>
<?php echo $footer; ?>