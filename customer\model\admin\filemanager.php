<?php
class ModelAdminFileManager extends Model {
    public function getFileList($data = array()) {
        $sql = "SELECT *, CASE file_type WHEN 'folder' THEN 1 ELSE 0 END AS is_folder FROM _union_file WHERE status >= '0'";

        if (!empty($data['filter_author'])) {
            $sql .= " AND union_id = '" . (int)$data['filter_author'] . "'";
        }

        if (!empty($data['filter_folder'])) {
            $sql .= " AND folder_id = '" . (int)$data['filter_folder'] . "'";
            if (!empty($data['folder_ids'])) {
                $sql .= " AND (";
                foreach ($data['folder_ids'] as $k => $v) {
                    if ($k != 0) {
                        $sql .= " OR";
                    }
                    $sql .= " SUBSTRING_INDEX(SUBSTRING_INDEX(parent_folder_ids, ',', 3), ',', -1) = '".$v."'";
                }
                $sql .= ")";
            }
        } elseif (!empty($data['filter_name'])) {
            $tags_id = $this->getLikeTagsName($data['filter_name']);
            if (!empty($tags_id)) {
                $sql .= " AND (file_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
                foreach ($tags_id as $tag) {
                    $sql .= " OR tags LIKE '%," . $this->db->escape($tag['file_tags_id']) . ",%'";
                }
                $sql .= ")";
            }else {
                $sql .= " AND file_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
            }
            if (!empty($data['folder_ids'])) {
                $sql .= " AND (";
                foreach ($data['folder_ids'] as $k => $v) {
                    if ($k != 0) {
                        $sql .= " OR";
                    }
                    $sql .= " SUBSTRING_INDEX(SUBSTRING_INDEX(parent_folder_ids, ',', 3), ',', -1) = '".$v."'";
                }
                $sql .= ")";
            }

        } else {
            $sql .= " AND folder_id = '0'";
            if (!empty($data['all_folder_ids'])) {
                $sql .= " AND FIND_IN_SET(file_id, '" . implode(',',$data['all_folder_ids']) . "')";
            }
        }

        $sql .= " ORDER BY is_folder DESC, file_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalFiles($data = array()) {
        $sql = "SELECT COUNT(*) AS total FROM _union_file WHERE status >= '0'";

        if (!empty($data['filter_author'])) {
            $sql .= " AND union_id = '" . (int)$data['filter_author'] . "'";
        }

        if (!empty($data['filter_folder'])) {
            $sql .= " AND folder_id = '" . (int)$data['filter_folder'] . "'";
            if (!empty($data['folder_ids'])) {
                $sql .= " AND (";
                foreach ($data['folder_ids'] as $k => $v) {
                    if ($k != 0) {
                        $sql .= " OR";
                    }
                    $sql .= " SUBSTRING_INDEX(SUBSTRING_INDEX(parent_folder_ids, ',', 3), ',', -1) = '".$v."'";
                }
                $sql .= ")";
            }
        } elseif (!empty($data['filter_name'])) {
            $tags_id = $this->getLikeTagsName($data['filter_name']);
            if (!empty($tags_id)) {
                $sql .= " AND (file_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
                foreach ($tags_id as $tag) {
                    $sql .= " OR tags LIKE '%," . $this->db->escape($tag['file_tags_id']) . ",%'";
                }
                $sql .= ")";
            }else {
                $sql .= " AND file_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
            }
            if (!empty($data['folder_ids'])) {
                $sql .= " AND (";
                foreach ($data['folder_ids'] as $k => $v) {
                    if ($k != 0) {
                        $sql .= " OR";
                    }
                    $sql .= " SUBSTRING_INDEX(SUBSTRING_INDEX(parent_folder_ids, ',', 3), ',', -1) = '".$v."'";
                }
                $sql .= ")";
            }

        } else {
            $sql .= " AND folder_id = '0'";
            if (!empty($data['all_folder_ids'])) {
                $sql .= " AND FIND_IN_SET(file_id, '" . implode(',',$data['all_folder_ids']) . "')";
            }
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function addFile($data) {
        $this->db->query("INSERT INTO _union_file SET union_id = '" . (int)$data['union_id'] . "', folder_id = '" . (int)$data['folder_id'] . "', file_name = '" . $this->db->escape($data['file_name']) . "', file_type = '" . $this->db->escape($data['file_type']) . "', bucket = '" . $this->db->escape($data['bucket']) . "', file_key = '" . $this->db->escape($data['file_key']) . "', file_hash = '" . $this->db->escape($data['file_hash']) . "', file_thumb = '" . $this->db->escape($data['file_thumb']) . "', file_size = '" . (int)$data['file_size'] . "', tags = '', status = '0', date_added = NOW()");

        $this->setParentFolderIds($this->db->getLastId());
    }

    public function getFile($file_id = 0) {
        $query = $this->db->query("SELECT *, CASE file_type WHEN 'folder' THEN 1 ELSE 0 END AS is_folder FROM _union_file WHERE file_id = '" . (int)$file_id . "'");

        return $query->row;
    }

    public function deleteFile($file_id, $union_id) {
        $this->db->query("UPDATE _union_file SET status = '-1' WHERE file_id = '" . (int)$file_id . "' AND union_id = '" . (int)$union_id . "'");
    }

    public function getParentFolder($folder) {
        if (!empty($folder)) {
            $query = $this->db->query("SELECT folder_id FROM _union_file WHERE status >= '0' AND file_id = '" . (int)$folder . "'");

            if ($query->row) {
                return $query->row['folder_id'];
            }
        }

        return '';
    }

    public function getFileExist($folder, $file) {
        $query = $this->db->query("SELECT file_id FROM _union_file WHERE status >= '0' AND folder_id = '" . (int)$folder . "' AND file_name = '" . $this->db->escape($file) . "'");

        if ($query->row) {
            return $query->row['file_id'];
        } else {
            return false;
        }
    }

    public function setFileTags($file_id,$tags) {
        $this->db->query("UPDATE _union_file SET tags = '".$tags."' WHERE file_id = '" . (int)$file_id . "'");
    }

    public function getDefaultTags() {
        $query = $this->db->query("SELECT * FROM _union_file_tags ORDER BY date_added DESC");

        return $query->rows;
    }

    public function addDefaultTags($name) {
        $this->db->query("INSERT INTO _union_file_tags SET name = '" . $name . "', date_added = NOW()");

        $file_tags_id = $this->db->getLastId();

        return $file_tags_id;
    }

    public function getINTagsName($tags) {
        $query = $this->db->query("SELECT * FROM _union_file_tags WHERE file_tags_id in ('" . implode("','", array_filter(explode(',',$tags))) . "')");

        $file_tags = [];
        if (!empty($query->rows)) {
            foreach ($query->rows as $tags) {
                $file_tags[$tags['file_tags_id']] = $tags['name'];
            }
        }
        return $file_tags;
    }

    public function getLikeTagsName($Like_name) {
        $query = $this->db->query("SELECT file_tags_id FROM _union_file_tags WHERE name LIKE '%" . $this->db->escape($Like_name)."%'");

        return $query->rows;
    }

    public function getAllParentFolderName($parent_folder_ids) {
        $res = [];
        if (!empty($parent_folder_ids)) {
            foreach (explode(',',$parent_folder_ids) as $v) {
                $query = $this->db->query("SELECT file_id,file_name FROM _union_file WHERE file_type = 'folder' AND status >= '0' AND file_id = '".$v."'");
                if (!empty($query->row)) {
                    $res[] = $query->row;
                }
            }
        }
//        $query = $this->db->query("SELECT file_id,file_name FROM _union_file WHERE file_type = 'folder' AND status >= '0' AND FIND_IN_SET(file_id,'" . $parent_folder_ids . "')");

        return $res;
    }

    public function setParentFolderIds($file_id) {
        $all_parent = $this->getAllParentFolder($file_id);
        array_shift($all_parent);
        if (count($all_parent) == 1) {
            $file_power_query = $this->db->query("SELECT * FROM _union_file_power WHERE FIND_IN_SET($all_parent[0],folder_ids)");
            foreach ((array)$file_power_query->rows as $v) {
                $this->db->query("UPDATE _union_file_power SET folder_ids = '" . $this->db->escape($v['folder_ids'].','.$file_id) . "' WHERE file_power_id = '".$v['file_power_id']."'");
            }
        }
        if (count($all_parent) == 0) {
            $parent_folder_ids = "0,".$file_id;
        } else {
            $parent_folder_ids = "0,".implode(',',array_reverse($all_parent)).",".$file_id;
        }
        $this->db->query("UPDATE _union_file SET parent_folder_ids='".$this->db->escape($parent_folder_ids)."' WHERE file_id = '".$file_id."'");
    }

    public function getAllParentFolder($folder_id,$parent_ids = []) {
        $parent_ids[] = $folder_id;
        $query = $this->db->query("SELECT * FROM _union_file WHERE file_id = '".$folder_id."'");
        if ($query->row['folder_id'] != 0) {
            $aub_parent_ids = $this->getAllParentFolder($query->row['folder_id'],$parent_ids);
            $parent_ids = array_unique(array_merge($parent_ids,$aub_parent_ids));
        }
        return $parent_ids;
    }
}