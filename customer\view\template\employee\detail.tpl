<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        员工信息
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title">查看详情</h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <div class="nav-tabs-custom">
            <ul class="nav nav-tabs">
              <li><a href="#tab-base" data-toggle="tab">基础信息</a></li>
              <li><a href="#tab-card" data-toggle="tab">身份信息</a></li>
              <li><a href="#tab-job" data-toggle="tab">任职情况</a></li>
              <li><a href="#tab-edu" data-toggle="tab">学历信息</a></li>
              <li><a href="#tab-contract" data-toggle="tab">劳动合同</a></li>
              <li><a href="#tab-insurance" data-toggle="tab">保险缴纳</a></li>
              <li><a href="#tab-track" data-toggle="tab">轨迹记录</a></li>
            </ul>
            <div class="tab-content">
              <div class="tab-pane" id="tab-base">
                <table class="table table-bordered table-hover"><tbody>
                  <tr>
                    <td width="180">姓名：</td>
                    <td><?php echo $employee_info['fullname']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">手机号：</td>
                    <td><?php echo $employee_info['telephone']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">常住地址：</td>
                    <td><?php echo $employee_info['address']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">紧急联系人：</td>
                    <td><?php echo $employee_info['contact_name']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">紧急联系人关系：</td>
                    <td><?php echo $employee_info['contact_relation']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">紧急联系人手机号：</td>
                    <td><?php echo $employee_info['contact_telephone']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">入职来源：</td>
                    <td><?php echo $employee_info['channel_from']; ?></td>
                  </tr>
                </tbody></table>
              </div>
              <div class="tab-pane" id="tab-card">
                <table class="table table-bordered table-hover"><tbody>
                  <tr>
                    <td width="180">性别：</td>
                    <td><?php echo $employee_info['sex']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">出生日期：</td>
                    <td><?php echo $employee_info['birth_date']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">民族：</td>
                    <td><?php echo $employee_info['nation']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">婚姻状况：</td>
                    <td><?php echo $employee_info['marriage']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">政治面貌：</td>
                    <td><?php echo $employee_info['politics']; ?></td>
                  </tr>
                  <?php if (!empty($employee_info['card_img'])) { ?>
                  <tr>
                    <td width="180">身份证图片：</td>
                    <td><img width="360" src="<?php echo HTTP_IMAGE . $employee_info['card_img']; ?>" class="img-thumbnail" /></td>
                  </tr>
                  <?php } ?>
                  <tr>
                    <td width="180">身份证件号码：</td>
                    <td><?php echo $employee_info['card_no']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">身份证姓名：</td>
                    <td><?php echo $employee_info['card_name']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">身份证地址：</td>
                    <td><?php echo $employee_info['card_address']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">证件有效期：</td>
                    <td><?php echo $employee_info['card_expire']; ?></td>
                  </tr>
                </tbody></table>
              </div>
              <div class="tab-pane" id="tab-job">
                <table class="table table-bordered table-hover"><tbody>
                  <tr>
                    <td width="180">所属部门：</td>
                    <td><?php echo $employee_info['job_centre'] . '-' . $employee_info['job_department']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">职位：</td>
                    <td><?php echo $employee_info['job_post']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">办公地址：</td>
                    <td><?php echo $employee_info['job_office']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">工号：</td>
                    <td><?php echo $employee_info['job_number']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">入职时间：</td>
                    <td><?php echo $employee_info['job_work_date']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">预计转正时间：</td>
                    <td><?php echo $employee_info['job_estimate_date']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">实际转正时间：</td>
                    <td><?php echo $employee_info['job_regular_date']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">离职时间：</td>
                    <td><?php echo $employee_info['job_leave_date']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">任职状态：</td>
                    <td><?php echo $employee_info['job_status']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">保密协议：</td>
                    <td><?php echo $employee_info['job_confidentiality']; ?></td>
                  </tr>
                </tbody></table>
              </div>
              <div class="tab-pane" id="tab-edu">
                <table class="table table-bordered table-hover"><tbody>
                  <tr>
                    <td width="180">最高学历：</td>
                    <td><?php echo $employee_info['edu_degree']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">毕业院校：</td>
                    <td><?php echo $employee_info['edu_school']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">所学专业：</td>
                    <td><?php echo $employee_info['edu_speciality']; ?></td>
                  </tr>
                  <tr>
                    <td width="180">毕业时间：</td>
                    <td><?php echo $employee_info['edu_graduate_date']; ?></td>
                  </tr>
                </tbody></table>
              </div>
              <div class="tab-pane" id="tab-contract">
                <div class="table-responsive">
                  <table class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <td class="text-left">单位名称</td>
                        <td class="text-left">签订时间</td>
                        <td class="text-left">起始时间</td>
                        <td class="text-left">到期时间</td>
                      </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($contracts as $contract) { ?>
                      <tr>
                        <td class="text-left"><?php echo $contract['company']; ?></td>
                        <td class="text-left"><?php echo $contract['sign_date']; ?></td>
                        <td class="text-left"><?php echo $contract['start_date']; ?></td>
                        <td class="text-left"><?php echo $contract['end_date']; ?></td>
                      </tr>
                      <?php } ?>
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="tab-pane" id="tab-insurance">
                <div class="table-responsive">
                  <table class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <td class="text-left">保险类型</td>
                        <td class="text-left">缴纳单位</td>
                        <td class="text-left">增员时间</td>
                        <td class="text-left">减员时间</td>
                      </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($insurances as $insurance) { ?>
                      <tr>
                        <td class="text-left"><?php echo $insurance['insurance_name']; ?></td>
                        <td class="text-left"><?php echo $insurance['company']; ?></td>
                        <td class="text-left"><?php echo $insurance['add_date']; ?></td>
                        <td class="text-left"><?php echo $insurance['del_date']; ?></td>
                      </tr>
                      <?php } ?>
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="tab-pane" id="tab-track">
                <ul class="list-group">
                  <li class="list-group-item">
                    <h4 class="list-group-item-heading">入职 / <?php echo $employee_info['job_work_date']; ?></h4>
                  </li>
                  <?php foreach ($logs as $log) { ?>
                  <li class="list-group-item">
                    <h4 class="list-group-item-heading"><?php echo $log['log_name']; ?> / <?php echo date($date_format, strtotime($log['date_added'])); ?></h4>
                    <p class="list-group-item-text"><?php echo $log['content']; ?></p>
                  </li>
                  <?php } ?>
                  <?php if (!empty($employee_info['job_leave_date'])) { ?>
                  <li class="list-group-item">
                    <h4 class="list-group-item-heading">离职 / <?php echo $employee_info['job_leave_date']; ?></h4>
                  </li>
                  <?php } ?>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
$('.nav-tabs li:first a').tab('show');
</script>
<?php echo $footer; ?>