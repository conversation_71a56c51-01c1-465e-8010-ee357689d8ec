<?php
class ModelAdminGoods extends Model {
	public function getStocks($data = array()) {
		$sql = "SELECT spec_no, spec_name, order_num, modified, img_url, DATE(last_sales_time) AS last_sales, last_inout_time, (purchase_num + to_purchase_num) AS purchase_num, (avaliable_num + lock_num) AS stock_num, (order_num - avaliable_num - lock_num) AS out_num FROM wdt_stocks WHERE warehouse_id = '7' AND Status = '1' AND deleted = '0' AND flag_id != '510'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (in_array($this->user->real_name, array('优源工艺品有限公司', '豪丽工艺品', '万腾工艺品厂'))) {
			$providers = $this->getProviders();
			foreach ($providers as $provider) {
				if ($this->user->real_name == $provider['provider_name']) {
					$data['filter_provider'] = $provider['provider_no'];
				}
			}
		}

		if (!empty($data['filter_provider'])) {
			$sql .= " AND spec_no IN (SELECT DISTINCT spec_no FROM wdt_purchase_provider_goods_list WHERE provider_no = '" . $this->db->escape($data['filter_provider']) . "')";
		}

		if (!empty($data['filter_rule'])) {
            if ($data['filter_rule'] == 'outstock') {
                $sql .= " AND (avaliable_num + lock_num) < order_num AND modified >= DATE_SUB(NOW(), INTERVAL 90 DAY)";
            } elseif ($data['filter_rule'] == 'allstock') {
                $sql .= " AND modified >= DATE_SUB(NOW(), INTERVAL 360 DAY)";
            }
        }

		$sort_data = array(
			'spec_no',
			'order_num',
			'purchase_num',
			'stock_num',
			'out_num',
			'last_sales',
			'last_inout_time'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY rec_id";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int) $data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalStocks($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM wdt_stocks WHERE warehouse_id = '7' AND Status = '1' AND deleted = '0' AND flag_id != '510'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (in_array($this->user->real_name, array('优源工艺品有限公司', '豪丽工艺品', '万腾工艺品厂'))) {
			$providers = $this->getProviders();
			foreach ($providers as $provider) {
				if ($this->user->real_name == $provider['provider_name']) {
					$data['filter_provider'] = $provider['provider_no'];
				}
			}
		}

		if (!empty($data['filter_provider'])) {
			$sql .= " AND spec_no IN (SELECT DISTINCT spec_no FROM wdt_purchase_provider_goods_list WHERE provider_no = '" . $this->db->escape($data['filter_provider']) . "')";
		}

		if (!empty($data['filter_rule'])) {
            if ($data['filter_rule'] == 'outstock') {
                $sql .= " AND (avaliable_num + lock_num) < order_num AND modified >= DATE_SUB(NOW(), INTERVAL 90 DAY)";
            } elseif ($data['filter_rule'] == 'allstock') {
                $sql .= " AND modified >= DATE_SUB(NOW(), INTERVAL 360 DAY)";
            }
        }

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function getPurchaseProviders($bsku_list) {
		$providers = array();

		if (!empty($bsku_list)) {
			$query = $this->db->query("SELECT provider_name, spec_no FROM wdt_purchase_provider_goods_list WHERE spec_no IN ('" . implode("','", $bsku_list) . "')");

			foreach ($query->rows as $row) {
				$providers[$row['spec_no']][] = $row['provider_name'];
			}
		}

		return $providers;
	}

	public function addPurchaseArrival($data) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "stock_arrival SET user_id = '" . (int)$this->user->user_id . "', arrival_quan = '" . (int)$data['arrival_quan'] . "', bsku = '" . $this->db->escape($data['bsku']) . "', arrival_date = '" . $this->db->escape($data['arrival_date']) . "', date_added = NOW()");
	}

	public function getPurchaseArrivals($bsku_list) {
		$arrivals = array();

		if (!empty($bsku_list)) {
			$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "stock_arrival WHERE arrival_date >= CURDATE() AND bsku IN ('" . implode("','", $bsku_list) . "') ORDER BY arrival_id ASC");

			foreach ($query->rows as $row) {
				$arrivals[$row['bsku']] = array(
					'date'	=> $row['arrival_date'],
					'quan'	=> $row['arrival_quan']
				);
			}
		}

		return $arrivals;
	}

	public function addStockOutQuan($data) {
		$this->db->query("UPDATE " . DB_PREFIX . "stock_outquan SET status = '0' WHERE status = '1' AND bsku = '" . $this->db->escape($data['bsku']) . "'");

		$this->db->query("INSERT INTO " . DB_PREFIX . "stock_outquan SET user_id = '" . (int)$this->user->user_id . "', retail_quan = '" . (int)$data['retail_quan'] . "', bsku = '" . $this->db->escape($data['bsku']) . "', wholesale_quan = '" . $this->db->escape($data['wholesale_quan']) . "', status = '1', date_added = NOW()");
	}

	public function getStockOutQuan($bsku_list) {
		$quans = array();

		if (!empty($bsku_list)) {
			$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "stock_outquan WHERE status = '1' AND bsku IN ('" . implode("','", $bsku_list) . "') ORDER BY out_id ASC");

			foreach ($query->rows as $row) {
				$quans[$row['bsku']] = array(
					'retail'	=> $row['retail_quan'],
					'wholesale'	=> $row['wholesale_quan']
				);
			}
		}

		return $quans;
	}

    public function getProviders() {
        return array(
            ['provider_no' => '004', 'provider_name' => '优源工艺品有限公司'],
            ['provider_no' => '034', 'provider_name' => '豪丽工艺品'],
            ['provider_no' => '008', 'provider_name' => '悦迪工艺品有限责任公司'],
            ['provider_no' => '013', 'provider_name' => '万腾工艺品厂'],
            ['provider_no' => 'HCT', 'provider_name' => '火车头工艺品有限公司'],
            ['provider_no' => '017', 'provider_name' => '泉州市丰泽黎发工艺有限公司'],
            ['provider_no' => '025', 'provider_name' => '冠星'],
            ['provider_no' => '029', 'provider_name' => '华晟工艺品'],
            ['provider_no' => '007', 'provider_name' => '若缇工厂']
        );
    }

    public function getStockRules() {
        return array(
            'outstock' => '缺货产品',
            'allstock' => '所有产品'
        );
    }

    public function getGoods($data = array()) {
        $sql = "SELECT * FROM wdt_goods_list WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(goods_no, goods_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_provider'])) {
            $sql .= " AND goods_no IN (SELECT DISTINCT goods_no FROM wdt_purchase_provider_goods_list WHERE provider_no = '" . $this->db->escape($data['filter_provider']) . "')";
        }

        if (isset($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY goods_modified";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int) $data['limit'];
        }
        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getGoodsTotal($data = array()) {
        $sql = "SELECT count(*) as total FROM wdt_goods_list WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(goods_no, goods_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_provider'])) {
            $sql .= " AND goods_no IN (SELECT DISTINCT goods_no FROM wdt_purchase_provider_goods_list WHERE provider_no = '" . $this->db->escape($data['filter_provider']) . "')";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getOneGoods($goods_id) {
        $query = $this->db->query("SELECT * FROM wdt_goods_list WHERE goods_id='".$goods_id."'");

        return $query->row;
    }

    public function getSpecs($goods_id,$data = array()) {
        $sql = "SELECT wsl.*,gd.copyright_images FROM wdt_spec_list as wsl LEFT JOIN " . DB_PREFIX . "goods_detail as gd ON wsl.spec_id=gd.spec_id WHERE wsl.goods_id='".$goods_id."' ORDER BY wsl.spec_name ASC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int) $data['limit'];
        }
        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getSpecsTotal($goods_id) {
        $sql = "SELECT count(*) as total FROM wdt_spec_list WHERE goods_id='".$goods_id."'";

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function setGoodDetailCopyright($spec_id,$copyright_image,$del=0) {
        $goods_detail = $this->db->query("SELECT goods_detail_id,copyright_images FROM " . DB_PREFIX . "goods_detail WHERE spec_id='".$spec_id."'");

        if (!empty($goods_detail->row)) {
            if (!empty($goods_detail->row['copyright_images'])) {
                $copyright_images_arr = json_decode($goods_detail->row['copyright_images'],true);
                if ($del == 1) {
                    $key = array_search($copyright_image,$copyright_images_arr);
                    if ($key !== false) unset($copyright_images_arr[$key]);
                } else {
                    $copyright_images_arr[] = $copyright_image;
                }
                $this->db->query("UPDATE " . DB_PREFIX . "goods_detail SET copyright_images = '".json_encode($copyright_images_arr,320)."' WHERE spec_id='".$spec_id."'");
            } else {
                $this->db->query("UPDATE " . DB_PREFIX . "goods_detail SET copyright_images = '".json_encode([$copyright_image],320)."' WHERE spec_id='".$spec_id."'");
            }
        } else {
            $this->db->query("INSERT INTO " . DB_PREFIX . "goods_detail SET copyright_images = '".json_encode([$copyright_image],320)."',spec_id='".$spec_id."',date_added = NOW()");
        }
    }

    public function getGoodsDetail($goods_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "goods_detail WHERE goods_id='".$goods_id."'");

        return $query->row;
    }

    public function addPackage($data) {
    	$package = $this->db->query("SELECT package_id, ispack FROM " . DB_PREFIX . "goods_package WHERE provider_no = '" . $this->db->escape($data['provider_no']) . "' AND bsku = '" . $this->db->escape($data['bsku']) . "'")->row;

		if (!empty($package)) {
			$package_id = $package['package_id'];
			$ispack = ($package['ispack'] == '1') ? true : false;
		} else {
			$this->db->query("INSERT INTO " . DB_PREFIX . "goods_package SET provider_no = '" . $this->db->escape($data['provider_no']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', pending_quan = '0', boxstock_quan = '0', boxin_quan = '0', boxout_quan = '0', packstock_quan = '0', packin_quan = '0', packout_quan = '0', ispack = '1', date_added = NOW(), date_modified = NOW()");

			$package_id = $this->db->getLastId();
			$ispack = true;
		}

		$data['quantity'] = abs((int)$data['quantity']);

		$this->db->query("INSERT INTO " . DB_PREFIX . "goods_package_detail SET package_id = '" . (int)$package_id . "', bsku = '" . $this->db->escape($data['bsku']) . "', quantity = '" . (int)$data['quantity'] . "', action = '" . $this->db->escape($data['action']) . "', date_added = NOW()");

		if ($data['action'] == 'inbox') {
			$this->db->query("UPDATE " . DB_PREFIX . "goods_package SET boxstock_quan = boxstock_quan + '" . (int)$data['quantity'] . "', boxin_quan = boxin_quan + '" . (int)$data['quantity'] . "', date_modified = NOW() WHERE package_id = '" . (int)$package_id . "'");
		} elseif ($data['action'] == 'inpack') {
			$this->db->query("UPDATE " . DB_PREFIX . "goods_package SET packstock_quan = packstock_quan + '" . (int)$data['quantity'] . "', packin_quan = packin_quan + '" . (int)$data['quantity'] . "', date_modified = NOW() WHERE package_id = '" . (int)$package_id . "'");
		} elseif ($data['action'] == 'out') {
			if ($ispack) {
				$this->db->query("UPDATE " . DB_PREFIX . "goods_package SET pending_quan = pending_quan - '" . (int)$data['quantity'] . "', boxstock_quan = boxstock_quan - '" . (int)$data['quantity'] . "', boxout_quan = boxout_quan + '" . (int)$data['quantity'] . "', packstock_quan = packstock_quan - '" . (int)$data['quantity'] . "', packout_quan = packout_quan + '" . (int)$data['quantity'] . "', date_modified = NOW() WHERE package_id = '" . (int)$package_id . "'");
			} else {
				$this->db->query("UPDATE " . DB_PREFIX . "goods_package SET pending_quan = pending_quan - '" . (int)$data['quantity'] . "', boxstock_quan = boxstock_quan - '" . (int)$data['quantity'] . "', boxout_quan = boxout_quan + '" . (int)$data['quantity'] . "', date_modified = NOW() WHERE package_id = '" . (int)$package_id . "'");
			}
		}
	}

    public function getPackages($data = array()) {
		$sql = "SELECT package_id, provider_no, bsku, pending_quan, boxstock_quan, packstock_quan, ispack, date_modified, spec_no, spec_name, img_url, (pending_quan - boxstock_quan) AS outbox, (pending_quan - packstock_quan) AS outpack FROM (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl LEFT JOIN " . DB_PREFIX . "goods_package gp ON (gp.bsku = sl.spec_no) WHERE 1";

		if (!empty($data['filter_name'])) {
			$sql .= " AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_provider'])) {
			$sql .= " AND spec_no IN (SELECT DISTINCT spec_no FROM wdt_purchase_provider_goods_list WHERE provider_no = '" . $this->db->escape($data['filter_provider']) . "')  AND (provider_no = '" . $this->db->escape($data['filter_provider']) . "' OR provider_no IS NULL)";
		}

		if (!empty($data['filter_rule'])) {
            if ($data['filter_rule'] == 'outbox') {
                $sql .= " AND boxstock_quan < pending_quan";
            } elseif ($data['filter_rule'] == 'outpack') {
                $sql .= " AND ispack = '1' AND packstock_quan < pending_quan";
            } elseif ($data['filter_rule'] == 'outall') {
                $sql .= " AND (boxstock_quan < pending_quan OR (ispack = '1' AND packstock_quan < pending_quan))";
            }
        }

		$sort_data = array(
			'provider_no',
			'spec_no',
			'spec_name',
			'pending_quan',
			'boxstock_quan',
			'packstock_quan',
			'ispack',
			'outbox',
			'outpack',
			'date_modified'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY date_modified";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int) $data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalPackages($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl LEFT JOIN " . DB_PREFIX . "goods_package gp ON (gp.bsku = sl.spec_no) WHERE 1";

		if (!empty($data['filter_name'])) {
			$sql .= " AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_provider'])) {
			$sql .= " AND spec_no IN (SELECT DISTINCT spec_no FROM wdt_purchase_provider_goods_list WHERE provider_no = '" . $this->db->escape($data['filter_provider']) . "')  AND (provider_no = '" . $this->db->escape($data['filter_provider']) . "' OR provider_no IS NULL)";
		}

		if (!empty($data['filter_rule'])) {
            if ($data['filter_rule'] == 'outbox') {
                $sql .= " AND boxstock_quan < pending_quan";
            } elseif ($data['filter_rule'] == 'outpack') {
                $sql .= " AND ispack = '1' AND packstock_quan < pending_quan";
            } elseif ($data['filter_rule'] == 'outall') {
                $sql .= " AND (boxstock_quan < pending_quan OR (ispack = '1' AND packstock_quan < pending_quan))";
            }
        }

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

    public function getPackageDetails($package_id, $data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "goods_package_detail WHERE package_id = '" . (int)$package_id . "' ORDER BY package_detail_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalPackageDetails($package_id) {
        $query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "goods_package_detail WHERE package_id = '" . (int)$package_id . "'");

        return $query->row['total'];
    }

    public function getPackageRules() {
        return array(
            'outbox'	=> '彩盒不足',
            'outpack'	=> '保丽龙不足',
            'outall'	=> '彩盒或保丽龙不足',
            'allstock'	=> '所有包材'
        );
    }

    public function getSpecsCustom($spec_id) {
        $sql = "SELECT wsl.*,gc.custom_value FROM wdt_spec_list as wsl LEFT JOIN " . DB_PREFIX . "goods_custom as gc ON wsl.spec_id=gc.spec_id WHERE wsl.spec_id='".$spec_id."'";

        $query = $this->db->query($sql);

        return $query->row;
    }

    public function setSpecsCustom($data) {
        $goods_custom = $this->db->query("SELECT goods_custom_id FROM " . DB_PREFIX . "goods_custom WHERE spec_id='".$data['spec_id']."'");

        if (!empty($goods_custom->row)) {
            $this->db->query("UPDATE " . DB_PREFIX . "goods_custom SET custom_value = '".$data['custom_value']."' WHERE goods_custom_id='".$goods_custom->row['goods_custom_id']."'");
        } else {
            $this->db->query("INSERT INTO " . DB_PREFIX . "goods_custom SET custom_value = '".$data['custom_value']."',spec_id='".$data['spec_id']."',date_added = NOW()");
        }
    }

	public function getLabels() {
        $sql = "SELECT * FROM " . DB_PREFIX . "label WHERE pid = '0'";

        $query = $this->db->query($sql);
        $data = [];
        foreach ((array)$query->rows as $k => $v) {
            $data[$k]['label_id'] = $v['label_id'];
            $data[$k]['name'] = $v['name'];
            $data[$k]['tag'] = '';
            $sql = "SELECT * FROM " . DB_PREFIX . "label WHERE pid = '".$v['label_id']."' AND status = '1'";
            $query = $this->db->query($sql);
            if (!empty($query->rows)) {
                $data[$k]['tag'] = implode(',',array_column($query->rows,'name'));
            }
        }

        return $data;
    }

    public function getLabelsArray($status = 0) {
        $sql = "SELECT * FROM " . DB_PREFIX . "label WHERE pid = '0' AND status = '1'";

        $query = $this->db->query($sql);
        $data = [];
        foreach ((array)$query->rows as $k => $v) {
            $data[$k]['label_id'] = $v['label_id'];
            $data[$k]['name'] = $v['name'];
            $data[$k]['tag'] = '';
            $sql = "SELECT * FROM " . DB_PREFIX . "label WHERE pid = '".$v['label_id']."'";

            $query = $this->db->query($sql);
            if (!empty($query->rows)) {
                if (!empty($status)) {
                    $data[$k]['tag'] = $query->rows;
                } else {
                    $data[$k]['tag'] = array_column($query->rows,'name','label_id');
                }
            }
        }

        return $data;
    }

    public function getLabel($label_id,$status = 0) {
        $sql = "SELECT * FROM " . DB_PREFIX . "label WHERE label_id = '".(int)$label_id."'";

        $query = $this->db->query($sql);
        $data = [];
        if (!empty($query->row)) {
            $data['label_id'] = $query->row['label_id'];
            $data['name'] = $query->row['name'];
            $data['status'] = $query->row['status'];
            $data['tag'] = '';
            $sql = "SELECT * FROM " . DB_PREFIX . "label WHERE pid = '".(int)$query->row['label_id']."'";
            if (!empty($status)) {
                $sql .= " AND status = '1'";
            }
            $query = $this->db->query($sql);
            if (!empty($query->rows)) {
                $data['tag'] = $query->rows;
            }
        }
        return $data;
    }

    public function addLabel($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "label SET name = '".$this->db->escape($data['name'])."',status = '".(int)$data['status']."',pid='0'");
        $label_id = $this->db->getLastId();
        foreach ($data['tag'] as $v) {
            if (!empty($v)) {
                $status = !empty($v['status']) ? 1 : 0;
                $this->db->query("INSERT INTO " . DB_PREFIX . "label SET name = '".$this->db->escape($v['name'])."',status = '".(int)$status."',pid='".(int)$label_id."'");
            }
        }
    }

    public function editLabel($label_id,$data) {
        $this->db->query("UPDATE " . DB_PREFIX . "label SET name = '".$this->db->escape($data['name'])."',status = '".(int)$data['status']."' WHERE label_id = '".(int)$label_id."'");

        if (!empty($data['oldtag'])) {
            $sql = "SELECT * FROM " . DB_PREFIX . "label WHERE pid = '".(int)$label_id."'";
            $query = $this->db->query($sql);
            if (!empty($query->rows)) {
                foreach ($query->rows as $v) {
                    if (!empty($data['oldtag'][$v['label_id']])) {
                        $status = !empty($data['oldtag'][$v['label_id']]['status']) ? 1 : 0;
                        if ($v['status'] != $status) {
                            $this->db->query("UPDATE " . DB_PREFIX . "label SET status = '".(int)$status."' WHERE label_id = '".(int)$v['label_id']."'");
                        }
                    } else {
                        $parent_id = intval($label_id);
                        $child_value =$v['label_id'];
                        $tag_where = sprintf(
                            " AND label_bindings->>'$.\"%d\"' = '%s'",
                            $parent_id,
                            $child_value
                        );
                        $label_spec_query = $this->db->query("SELECT spec_id FROM " . DB_PREFIX . "label_spec WHERE 1".$tag_where);
                        if (empty($label_spec_query->rows)) {
                            $this->db->query("DELETE FROM " . DB_PREFIX . "label WHERE label_id = '".(int)$v['label_id']."'");
                        } else {
                            $this->db->query("UPDATE " . DB_PREFIX . "label SET status = '0' WHERE label_id = '".(int)$v['label_id']."'");
                        }
                    }
                }
            }
        }

        if (!empty($data['tag'])) {
            foreach ($data['tag'] as $v) {
                if (!empty($v)) {
                    $status = !empty($v['status']) ? 1 : 0;
                    $this->db->query("INSERT INTO " . DB_PREFIX . "label SET name = '".$this->db->escape($v['name'])."',status = '".(int)$status."',pid='".(int)$label_id."'");
                }
            }
        }
    }

    public function getSpec($spec_id) {
        $sql = "SELECT * FROM wdt_spec_list WHERE spec_id='".$spec_id."'";

        $query = $this->db->query($sql);

        return $query->row;
    }

    public function getSpecsTag($spec_id) {
        $sql = "SELECT wsl.*,ls.label_bindings FROM wdt_spec_list as wsl LEFT JOIN " . DB_PREFIX . "label_spec as ls ON wsl.spec_id=ls.spec_id WHERE wsl.spec_id='".$spec_id."'";

        $query = $this->db->query($sql);

        return $query->row;
    }

    public function setSpecsTag($data) {
        $this->db->query("REPLACE INTO " . DB_PREFIX . "label_spec SET spec_id = '".(int)$data['spec_id']."',spec_no = '".$this->db->escape($data['spec_no'])."',label_bindings = '".$this->db->escape($data['label_bindings'])."'");
    }

    public function getGabesStatistics($data) {
        $sql = "SELECT bsku,SUM(quantity) as sum_quantity,ls.label_bindings,wsl.* FROM " . DB_PREFIX . "store_sales AS ss LEFT JOIN (SELECT spec_id,spec_no,spec_name,img_url FROM wdt_spec_list WHERE deleted = 0 GROUP BY spec_no) AS wsl ON ss.bsku = wsl.spec_no LEFT JOIN " . DB_PREFIX . "label_spec AS ls ON ss.bsku = ls.spec_no WHERE bsku != '合计:'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(wsl.spec_name, ss.bsku) like '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(ss.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(ss.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $sql .= " GROUP BY ss.bsku ORDER BY sum_quantity DESC";

        if (!empty($data['filter_row'])) {
            $sql .= " LIMIT " . (int)$data['filter_row'];
        } else {
            $sql .= " LIMIT 20";
        }

        $query = $this->db->query($sql);

        $tag_where = "";
        foreach ($data['filter_least_labels'] as $v) {
            $parent_id = intval($v['name']);
            $child_value =$v['value'];
            $tag_where .= sprintf(
                " AND label_bindings->>'$.\"%d\"' = '%s'",
                $parent_id,
                $child_value
            );
        }

        $search_label = sprintf(" label_bindings->>'$.\"%d\"' as tag", $data['search_label']);
        $res = [];
        foreach ($query->rows as $sku_key => $sku) {
            $res[$sku_key]['spec_id'] = $sku['spec_id'];
            $res[$sku_key]['spec_name'] = $sku['spec_name'];
            $res[$sku_key]['img_url'] = $sku['img_url'];
            $res[$sku_key]['spec_no'] = $sku['bsku'];
            $res[$sku_key]['quantity'] = $sku['sum_quantity'];
            $res[$sku_key]['label_bindings'] = !empty($sku['label_bindings']) ? json_decode($sku['label_bindings'],true) : [];
            $labels = [];
            $label_bindings = [];
            $label = $this->getLabel($data['search_label'],1);

            $label_spec_query = $this->db->query("SELECT ".$search_label." FROM " . DB_PREFIX . "label_spec WHERE spec_id='".$sku['spec_id']."'".$tag_where);
            if (!empty($label_spec_query->rows)) {
                $label_spec = array_column($label_spec_query->rows,'tag','tag');
                $label_tag = array_column($label['tag'],'name','label_id');
                $labels = array_diff_key($label_tag,$label_spec);
            }

            $res[$sku_key]['labels'] = $labels;
        }
        return $res;
    }

    public function getInTransits($data = array()) {
        $sql = "SELECT t.* FROM (SELECT d.spec_no,s.spec_name,s.img_url,o.provider_no,o.provider_name,CAST(SUM(d.num) AS SIGNED) AS total_num,CAST(SUM(d.stockin_num) AS SIGNED) AS total_stockin_num,CAST(SUM(d.lack_num) AS SIGNED) AS total_lack_num,COUNT(DISTINCT o.purchase_id) AS order_count,GROUP_CONCAT(CONCAT('预计到货:', o.expect_arrive_time, ',合同:', IFNULL(o.remark, ''), ',未到货数:', CAST(d.lack_num AS SIGNED)) ORDER BY o.expect_arrive_time ASC SEPARATOR '<br>') AS combined_order_info,MAX(o.modified) AS latest_modified,CASE WHEN DATEDIFF(NOW(), MIN(o.expect_arrive_time)) > 0 THEN CAST(DATEDIFF(NOW(), MIN(o.expect_arrive_time)) AS CHAR) ELSE '' END AS max_days_delay FROM wdt_purchase_order_details d JOIN wdt_purchase_order o ON d.purchase_id = o.purchase_id LEFT JOIN wdt_spec_list s ON d.spec_no = s.spec_no AND s.deleted = 0 WHERE o.status IN (40, 50) AND d.lack_num > 0";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(d.spec_no,s.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_provider'])) {
            $sql .= " AND o.provider_no = '" . $this->db->escape($data['filter_provider']) . "'";
        }

        if (!empty($data['filter_rule'])) {
            if ($data['filter_rule'] == 'outInTransit') {
                $sql .= " AND o.expect_arrive_time < NOW()";
            }
        }

        $sort_data = array(
            'latest_modified',
            'total_lack_num',
            'total_stockin_num',
            'total_num',
            'total_loss_quantity',
        );

        $sql .= " GROUP BY d.spec_no, o.provider_no, o.provider_name, s.spec_name, s.img_url) t";
        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY t." . $data['sort'];
        } else {
            $sql .= " ORDER BY t.total_lack_num";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int) $data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalinTransits($data = array()) {
        $sql = "SELECT d.spec_no,s.spec_name,s.img_url,o.provider_no,o.provider_name,CAST(SUM(d.num) AS SIGNED) AS total_num,CAST(SUM(d.stockin_num) AS SIGNED) AS total_stockin_num,CAST(SUM(d.lack_num) AS SIGNED) AS total_lack_num,COUNT(DISTINCT o.purchase_id) AS order_count,GROUP_CONCAT(CONCAT('预计到货:', o.expect_arrive_time, ',合同:', IFNULL(o.remark, ''), ',未到货数:', CAST(d.lack_num AS SIGNED)) ORDER BY o.expect_arrive_time ASC SEPARATOR '<br>') AS combined_order_info,MAX(o.modified) AS latest_modified FROM wdt_purchase_order_details d JOIN wdt_purchase_order o ON d.purchase_id = o.purchase_id LEFT JOIN wdt_spec_list s ON d.spec_no = s.spec_no AND s.deleted = 0 WHERE o.status IN (40, 50) AND d.lack_num > 0";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(d.spec_no,s.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_provider'])) {
            $sql .= " AND o.provider_no = '" . $this->db->escape($data['filter_provider']) . "'";
        }

        if (!empty($data['filter_rule'])) {
            if ($data['filter_rule'] == 'outInTransit') {
                $sql .= " AND o.expect_arrive_time < NOW()";
            }
        }
        $sort_data = array(
            'latest_modified',
        );

        $sql .= " GROUP BY d.spec_no, o.provider_no, o.provider_name, s.spec_name, s.img_url";
        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY latest_modified";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        $query = $this->db->query($sql);

        return $query->num_rows;
    }

    public function getInTransitRules() {
        return array(
            'outInTransit' => '到货超期',
            'allInTransit' => '所有产品'
        );
    }

    public function getInTransitLoss($spec_no = '', $provider_name = '') {
        $sql = "SELECT images FROM " . DB_PREFIX . "loss WHERE bsku = '" . $this->db->escape($spec_no) . "' AND supplier = '" . $this->db->escape($provider_name) . "'";
        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalLossQuantity($spec_no = '', $provider_name = '') {
        $sql = "SELECT SUM(quantity) as total_loss_quantity FROM " . DB_PREFIX . "loss WHERE bsku = '" . $this->db->escape($spec_no) . "' AND supplier = '" . $this->db->escape($provider_name) . "'";
        $query = $this->db->query($sql);

        return $query->row['total_loss_quantity'];
    }
}
