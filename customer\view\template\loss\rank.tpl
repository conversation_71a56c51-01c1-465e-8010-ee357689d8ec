<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        破损率排行
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索供应商/商家编码/名称" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>责任原因：</label>
                  <select class="form-control" name="filter_reason">
                    <option value="*">全部原因</option>
                    <?php foreach($reasons as $reason) { ?>
                    <?php if ($reason['name'] == $filter_reason) { ?>
                    <option value="<?php echo $reason['name']; ?>" selected="selected"><?php echo $reason['name']; ?></option>
                    <?php } else{ ?>
                    <option value="<?php echo $reason['name']; ?>"><?php echo $reason['name']; ?></option>
                    <?php } ?>
                      <?php foreach($reason['subs'] as $sub) { ?>
                      <?php $subname = $reason['name'] . '-' . $sub; ?>
                      <?php if ($subname == $filter_reason) { ?>
                      <option value="<?php echo $subname; ?>" selected="selected"><?php echo $subname; ?></option>
                      <?php } else{ ?>
                      <option value="<?php echo $subname; ?>"><?php echo $subname; ?></option>
                      <?php } ?>
                      <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach ($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>时间区间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right" id="reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right" id="reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start-time" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end-time" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">          
          <div class="pull-right">
            <button type="button" id="button-filter" class="btn bg-purple"><i class="glyphicon glyphicon-search"></i> 筛选</button>
          </div>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">破损列表</p>
          <div class="box-tools">
          </div>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'bsku') { ?>
                  <a href="<?php echo $sort_bsku; ?>" class="<?php echo strtolower($order); ?>">商家编码</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_bsku; ?>">商家编码</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'spec_name') { ?>
                  <a href="<?php echo $sort_pname; ?>" class="<?php echo strtolower($order); ?>">破损产品</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_pname; ?>">破损产品</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'allquan') { ?>
                  <a href="<?php echo $sort_allquan; ?>" class="<?php echo strtolower($order); ?>">破损数量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_allquan; ?>">破损数量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'allcost') { ?>
                  <a href="<?php echo $sort_allcost; ?>" class="<?php echo strtolower($order); ?>">破损成本</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_allcost; ?>">破损成本</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'allship') { ?>
                  <a href="<?php echo $sort_allship; ?>" class="<?php echo strtolower($order); ?>">发货数量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_allship; ?>">发货数量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'ratio') { ?>
                  <a href="<?php echo $sort_ratio; ?>" class="<?php echo strtolower($order); ?>">破损率</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_ratio; ?>">破损率</a>
                <?php } ?>
              </th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($ranks)) { ?>
            <?php foreach ($ranks as $rank) { ?>
            <tr>
              <td><?php echo $rank['bsku']; ?></td>
              <td><?php echo $rank['pname']; ?></td>
              <td><?php echo $rank['allquan']; ?></td>
              <td><?php echo $rank['allcost']; ?></td>
              <td><?php echo $rank['allship']; ?></td>
              <td class="h4 no-margin">
                <span class="label label-primary"><?php echo $rank['ratio']; ?> %</span>
              </td>
              <td class="text-right">
                <a class="btn btn-info" href="<?php echo $rank['view']; ?>" title="" target="_blank">查看详情</a>
              </td>
            </tr>
            <?php } ?>
            <?php } else{ ?>
            <td colspan="7" align="center"> 暂无破损数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after, .table a.undesc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after, .table a.unasc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('#reservation').daterangepicker({
      autoUpdateInput: false,
      daterangepicker: true,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('#reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start-time').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end-time').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('#reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start-time').val('')
      $('#filter-end-time').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_reason = $('select[name=\'filter_reason\']').val();

      if (filter_reason != '*') {
        url += '&filter_reason=' + encodeURIComponent(filter_reason);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>