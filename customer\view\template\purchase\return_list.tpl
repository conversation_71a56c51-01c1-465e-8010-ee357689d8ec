<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        店铺退货
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="产品名称编码" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>退货店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>退货时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">退货列表</h3>
          <div class="box-tools">
            <button class="btn btn-warning" type="button" data-toggle="modal" data-target="#add-modal">新增退货</button>
          </div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">备货店铺</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">备货店铺</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'bsku') { ?>
                  <a href="<?php echo $sort_bsku; ?>" class="<?php echo strtolower($order); ?>">商品编码</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_bsku; ?>">商品编码</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'spec_name') { ?>
                  <a href="<?php echo $sort_spec; ?>" class="<?php echo strtolower($order); ?>">产品名称</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_spec; ?>">产品名称</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'quantity') { ?>
                  <a href="<?php echo $sort_plan; ?>" class="<?php echo strtolower($order); ?>">退货数量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_plan; ?>">退货数量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'return_id') { ?>
                  <a href="<?php echo $sort_added; ?>" class="<?php echo strtolower($order); ?>">退货时间</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_added; ?>">退货时间</a>
                <?php } ?>
              </th>
            </tr>
            <?php if (!empty($return)) { ?>
            <?php foreach ($return as $list) { ?>
            <tr>
              <td><?php echo $list['storename']; ?></td>
              <td><?php echo $list['bsku']; ?></td>
              <td><?php echo $list['spec_name']; ?></td>
              <td><?php echo $list['quantity']; ?></td>
              <td><?php echo $list['date_added']; ?></td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="5" align="center"> 暂无退货数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>      
      <!-- 新增 -->
      <div class="modal modal-warning fade" id="add-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $add; ?>" method="post" enctype="multipart/form-data" id="form-add" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">请填写退货信息</h4>
              </div>
              <div class="modal-body">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-store">退货店铺：</label>
                  <div class="col-sm-8">
                    <select name="store_id" id="input-store" class="form-control">
                      <option value="">请选择退货店铺</option>
                      <?php foreach ($stores as $store) { ?>
                      <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-product">退货产品：</label>
                  <div class="col-sm-8">
                    <input type="text" value="" placeholder="输入产品编码或名称查找" id="input-product" class="form-control" />
                    <input type="hidden" name="bsku" id="input-bsku" value="" />
                    <div class="text-danger">请选择退货产品</div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">退货数量：</label>
                  <div class="col-sm-8">
                    <input type="number" name="quantity" value="" placeholder="请输入退货数量" id="input-quan" class="form-control" />
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">取消</button>
                <button id="add-yes" type="button" class="btn btn-outline">提交保存</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }
  
      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });

    $('#add-modal').on('show.bs.modal', function(event) {
      $('#input-store').val('');
      $('#input-product').val('');
      $('#input-bsku').val('');
      $('#input-quan').val('');
      $('#add-modal .text-danger').html('请选择退货产品');
    })
    $('#add-yes').on('click', () => {$('#form-add').submit()})
  })()
</script>
<script type="text/javascript">
// Autocomplete */
(function($) {
  $.fn.autocomplete = function(option) {
    return this.each(function() {
      var $this = $(this);
      var $dropdown = $('<ul class="dropdown-menu" />');

      this.timer = null;
      this.items = {};

      $.extend(this, option);

      $this.attr('autocomplete', 'off');

      // Focus
      $this.on('focus', function() {
        this.request();
      });

      // Blur
      $this.on('blur', function() {
        setTimeout(function(object) {
          object.hide();
        }, 200, this);
      });

      // Keydown
      $this.on('keydown', function(event) {
        switch(event.keyCode) {
          case 27: // escape
            this.hide();
            break;
          default:
            this.request();
            break;
        }
      });

      // Click
      this.click = function(event) {
        event.preventDefault();

        var value = $(event.target).parent().attr('data-value');

        if (value && this.items[value]) {
          this.select(this.items[value]);
        }
      }

      // Show
      this.show = function() {
        var pos = $this.position();

        $dropdown.css({
          'min-width': '50%',
          top: pos.top + $this.outerHeight(),
          left: pos.left
        });

        $dropdown.show();
      }

      // Hide
      this.hide = function() {
        $dropdown.hide();
      }

      // Request
      this.request = function() {
        clearTimeout(this.timer);

        this.timer = setTimeout(function(object) {
          object.source($(object).val(), $.proxy(object.response, object));
        }, 200, this);
      }

      // Response
      this.response = function(json) {
        var html = '';
        var category = {};
        var name;
        var i = 0, j = 0;

        if (json.length) {
          for (i = 0; i < json.length; i++) {
            // update element items
            this.items[json[i]['value']] = json[i];

            if (!json[i]['category']) {
              // ungrouped items
              html += '<li data-value="' + json[i]['value'] + '"><a href="#">' + json[i]['value'] + json[i]['label'] + '</a></li>';
            } else {
              // grouped items
              name = json[i]['category'];
              if (!category[name]) {
                category[name] = [];
              }

              category[name].push(json[i]);
            }
          }

          for (name in category) {
            html += '<li class="dropdown-header">' + name + '</li>';

            for (j = 0; j < category[name].length; j++) {
              html += '<li data-value="' + category[name][j]['value'] + '"><a href="#">&nbsp;&nbsp;&nbsp;' + category[name][j]['label'] + '</a></li>';
            }
          }
        }

        if (html) {
          this.show();
        } else {
          this.hide();
        }

        $dropdown.html(html);
      }

      $dropdown.on('click', '> li > a', $.proxy(this.click, this));
      $this.after($dropdown);
    });
  }
})(window.jQuery);

$(document).ready(function () {
  $('#input-product').autocomplete({
    'source': function(request, response) {
      $.ajax({
        url: '<?php echo $autoStock; ?>&filter_name=' + encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response(json);
          // response($.map(json, function(item) {
          //   return item
          // }));
        }
      });
    },
    'select': function(item) {
      $('#input-product').val(item['value'] + item['label']);
      $('#input-bsku').val(item['value']);
      $('#add-modal .text-danger').html('选中产品：' + item['value'] + item['label']);
    }
  });
});
</script>
<?php echo $footer; ?>