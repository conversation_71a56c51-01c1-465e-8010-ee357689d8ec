<?php echo $header; ?>
<style type="text/css" media="screen">
    .sink{background-color: #fbfbfb;}
    li{padding: 5px 10px;}
</style>
<div id="content">
    <div class="container">
        <div class="row">
            <div class="col-sm-8 col-sm-offset-2">
                <div class="row" style="padding:25px 0;">
                    <div class="col-sm-3"></div>
                </div><br><br>
                <div class="row">
                    <div class="col-sm-6 col-sm-offset-3 sink nav-tabs-custom" style="padding:15px;">
                        <ul class="nav nav-tabs">
                            <li class="active"><h4 href="#tab-pwd" data-toggle="tab">密码登录</h4></li>
                            <li><h4 href="#tab-sms" data-toggle="tab">短信登录</h4></li>
                        </ul>
                        <?php if ($error_warning) { ?>
                        <div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> <?php echo $error_warning; ?>
                            <button type="button" class="close" data-dismiss="alert">&times;</button>
                        </div>
                        <?php } ?>
                        <form action="<?php echo $action; ?>" class="tab-content sink" method="post" enctype="multipart/form-data">
                            <div class="tab-pane active row" id="tab-pwd">
                                <div class="col-sm-12 sink" style="padding-top:20px;">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-addon" style="background-color:#e1dede;"><i class="fa fa-user" style="color:#fff;"></i></span>
                                            <input type="text" name="username" class="form-control" placeholder="请填写手机号：">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-addon" style="background-color:#e1dede;"><i class="fa fa-lock" style="color:#fff;"></i></span>
                                            <input type="password" name="password" class="form-control" placeholder="请输入密码：">
                                      </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane row" id="tab-sms">
                                <div class="col-sm-12 sink" style="padding-top:20px;">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-addon" style="background-color:#e1dede;"><i class="fa fa-user" style="color:#fff;"></i></span>
                                            <input type="text" name="telephone" class="form-control" placeholder="请填写手机号：">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-addon" style="background-color:#e1dede;"><i class="fa fa-lock" style="color:#fff;"></i></span>
                                            <input type="text" name="code" class="form-control" placeholder="请填写验证码：">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-primary">获取验证码</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row sink">
                                <div class="col-sm-8 col-sm-offset-2" style="padding-top:30px;padding-bottom:30px;">
                                    <button type="submit" class="btn btn-danger btn-block">登&nbsp;&nbsp;录</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="static/bower_components/jquery/dist/jquery.min.js"></script>
<script src="static/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
<script type="text/javascript">
    (function(){
        var rejectgetcode = false

        // 正则表达式用于匹配中国大陆手机号码（11位数字，以1开头）
        var isPhone = /^1[3-9]\d{9}$/;
        // 验证手机号函数
        function validatePhone(phone) {
            return isPhone.test(phone);
        }

        function cd(second){
            if(second > 0 && rejectgetcode){
                $('.btn-primary').text('重新获取('+ second +'s)')
                setTimeout(function(){cd(--second)}, 1000)
            }else{
                $('.btn-primary').text('获取验证码')
                rejectgetcode = false
            }
        }
        $('.btn-primary').on('click', function(event) {
            event.preventDefault()
            if (rejectgetcode) return
            if (validatePhone($('input[name="telephone"]').val())) {
                $.ajax({
                    url: '<?php echo $getcode; ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {telephone:$('input[name="telephone"]').val()},
                    success: function(json) {
                        if (json['success']) {
                            rejectgetcode = true
                            cd(60)
                        } else if (json['disable']) {
                            alert('用户不存在')
                        } else if (json['message']) {
                            alert(json['message'])
                        } else {
                            alert('发送失败')
                        }
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        // alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
                    }
                })
            } else {
                alert('请填写正确的手机号')
            }
        });
    })();
</script>
<?php echo $footer; ?>