<?php
class ControllerAdminTemplate extends Controller {
    public function header() {
        $data = array();

        return $this->load->view('common/header.tpl', $data);
    }

    public function top() {
        $data = array();

        $data['headimg'] = '';
        $data['customer_name'] = $this->user->real_name;
        $data['access'] = $this->user->getPermissions('access');
        $data['modify'] = $this->user->getPermissions('modify');

        $data['goods'] = $this->url->link('goods/list');
        $data['basicGoods'] = $this->url->link('admin/basic/getGoods', 'token=' . $this->session->data['token']);
        $data['basicAuths'] = $this->url->link('admin/basic/getAuths', 'token=' . $this->session->data['token']);
        $data['basicInfos'] = $this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token']);
        $data['basicFreight'] = $this->url->link('admin/basic/getFreights', 'token=' . $this->session->data['token']);

        $data['KpiItem'] = $this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token']);
        $data['KpiUser'] = $this->url->link('admin/kpi/userList', 'token=' . $this->session->data['token']);
        $data['KpiRate'] = $this->url->link('admin/kpi/rateList', 'token=' . $this->session->data['token']);
        $data['KpiScore'] = $this->url->link('admin/kpi/scoreList', 'token=' . $this->session->data['token']);
        $data['myKpiItem'] = $this->url->link('admin/kpi/myItem', 'token=' . $this->session->data['token']);

        $data['goodsLabes'] = $this->url->link('admin/goods/labes', 'token=' . $this->session->data['token']);
        $data['goodsLabesStatistics'] = $this->url->link('admin/goods/labesStatistics', 'token=' . $this->session->data['token']);
        $data['goodsArchives'] = $this->url->link('admin/goods/getArchives', 'token=' . $this->session->data['token']);
        $data['goodsStock'] = $this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token']);
        $data['goodInTransits'] = $this->url->link('admin/goods/getInTransits', 'token=' . $this->session->data['token']);
        $data['goodsPackage'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token']);
        $data['flowProduce'] = $this->url->link('admin/flow/getProduce', 'token=' . $this->session->data['token']);
        $data['researchAndDevelopment'] = $this->url->link('admin/development/getDevelopments', 'token=' . $this->session->data['token']);

        $data['lossList'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token']);
        $data['lossRank'] = $this->url->link('admin/loss/getRankList', 'token=' . $this->session->data['token']);

        $data['employeeList'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token']);
        $data['employeeRemind'] = $this->url->link('admin/employee/getRemind', 'token=' . $this->session->data['token']);

        $data['grossProfitRate'] = $this->url->link('admin/bill/grossProfitRate', 'token=' . $this->session->data['token']);
        $data['performance'] = $this->url->link('admin/bill/performance', 'token=' . $this->session->data['token']);
        $data['billList'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token']);
        $data['feeList'] = $this->url->link('admin/bill/getFeeList', 'token=' . $this->session->data['token']);
        $data['billCost'] = $this->url->link('admin/bill/getCost', 'token=' . $this->session->data['token']);
        $data['billShipCost'] = $this->url->link('admin/bill/getShipCost', 'token=' . $this->session->data['token']);
        $data['billReturnCost'] = $this->url->link('admin/bill/getReturnCost', 'token=' . $this->session->data['token']);
        $data['stockIn'] = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token']);
        $data['stockOut'] = $this->url->link('admin/remainder/getOutList', 'token=' . $this->session->data['token']);
        $data['stockCost'] = $this->url->link('admin/remainder/getCostList', 'token=' . $this->session->data['token']);
        $data['wmsInList'] = $this->url->link('admin/outwms/getInList', 'token=' . $this->session->data['token']);
        $data['wmsOutList'] = $this->url->link('admin/outwms/getOutList', 'token=' . $this->session->data['token']);
        $data['wmsCostList'] = $this->url->link('admin/outwms/getCostList', 'token=' . $this->session->data['token']);
        $data['alipayBillSortList'] = $this->url->link('admin/billcheck/alipayBillSortList', 'token=' . $this->session->data['token']);
        $data['alipayBillSortKeyList'] = $this->url->link('admin/billcheck/alipayBillSortKeyList', 'token=' . $this->session->data['token']);
        $data['alipayBillSortUpload'] = $this->url->link('admin/billcheck/alipayBillSortUploadList', 'token=' . $this->session->data['token']);
        $data['alipayBillSortExportList'] = $this->url->link('admin/billcheck/alipayBillSortExportList', 'token=' . $this->session->data['token']);
        $data['expressFeeTemplateList'] = $this->url->link('admin/billcheck/expressFeeTemplateList', 'token=' . $this->session->data['token']);
        $data['expressFeeExportList'] = $this->url->link('admin/billcheck/expressFeeExportList', 'token=' . $this->session->data['token']);
        $data['expressFeeStandardList'] = $this->url->link('admin/billcheck/expressFeeStandardList', 'token=' . $this->session->data['token']);
        $data['expressFeeSurchargesList'] = $this->url->link('admin/billcheck/expressFeeSurchargesList', 'token=' . $this->session->data['token']);
        $data['expressFeeUpload'] = $this->url->link('admin/billcheck/expressFeeUploadList', 'token=' . $this->session->data['token']);
        $data['expressFeeList'] = $this->url->link('admin/billcheck/expressFeeList', 'token=' . $this->session->data['token']);
        $data['goodsPriceList'] = $this->url->link('admin/billcheck/goodsPriceList', 'token=' . $this->session->data['token']);
        $data['goodsPriceTemplateList'] = $this->url->link('admin/billcheck/goodsPriceTemplateList', 'token=' . $this->session->data['token']);
        $data['goodsPriceUpload'] = $this->url->link('admin/billcheck/goodsPriceUploadList', 'token=' . $this->session->data['token']);
        $data['goodsPriceExportList'] = $this->url->link('admin/billcheck/goodsPriceExportList', 'token=' . $this->session->data['token']);
        $data['shopId'] = $this->url->link('admin/shop/getShopIds', 'token=' . $this->session->data['token']);

        $data['purchaseNew'] = $this->url->link('admin/purchase/newPlan', 'token=' . $this->session->data['token']);
        $data['purchaseProduct'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token']);
        $data['purchaseStore'] = $this->url->link('admin/purchase/storePlan', 'token=' . $this->session->data['token']);
        $data['purchasePlan'] = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token']);
        $data['transfers'] = $this->url->link('admin/purchase/getTransfers', 'token=' . $this->session->data['token']);
        $data['purchaseReturn'] = $this->url->link('admin/purchase/getReturn', 'token=' . $this->session->data['token']);
        $data['purchaseOrder'] = $this->url->link('admin/purchase/order', 'token=' . $this->session->data['token']);
        $data['purchaseList'] = $this->url->link('admin/purchase/getList', 'token=' . $this->session->data['token']);
        $data['purchaseStock'] = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token']);
        $data['uploadSales'] = $this->url->link('admin/purchase/uploadSales', 'token=' . $this->session->data['token']);
        $data['purchaseContract'] = $this->url->link('admin/purchase/contract', 'token=' . $this->session->data['token']);
        $data['purchasePushList'] = $this->url->link('admin/purchase/pushList', 'token=' . $this->session->data['token']);
        $data['purchaseAntifakeList'] = $this->url->link('admin/purchase/antifakeList', 'token=' . $this->session->data['token']);


        $data['sheet'] = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token']);
        $data['sheetColumn'] = $this->url->link('admin/sheet/sheetColumn', 'token=' . $this->session->data['token']);
        $data['sheetKpi'] = $this->url->link('admin/sheet/sheetKpi', 'token=' . $this->session->data['token']);
        $data['sheetGroupUser'] = $this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token']);
        $data['sheetFlowList'] = $this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token']);
        $data['sheetNodeList'] = $this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token']);
        $data['sheetNodeActList'] = $this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token']);

        $data['customer'] = $this->url->link('admin/customer/getList', 'token=' . $this->session->data['token']);
        $data['customerGroup'] = $this->url->link('admin/customer/getGroups', 'token=' . $this->session->data['token']);
        $data['customerUpload'] = $this->url->link('admin/customer/upload', 'token=' . $this->session->data['token']);
        $data['customerReport'] = $this->url->link('admin/customer/getReport', 'token=' . $this->session->data['token']);
        $data['customerSales'] = $this->url->link('admin/customer/offSalesList', 'token=' . $this->session->data['token']);

        $data['user'] = $this->url->link('admin/user/getList', 'token=' . $this->session->data['token']);
	    $data['userGroup'] = $this->url->link('admin/usergroup/getList', 'token=' . $this->session->data['token']);
        $data['errorLog'] = $this->url->link('admin/common/errorLog', 'token=' . $this->session->data['token']);
        $data['importSales'] = $this->url->link('admin/import/uploadSales', 'token=' . $this->session->data['token']);
        $data['importShipment'] = $this->url->link('admin/import/uploadShipment', 'token=' . $this->session->data['token']);
        $data['password'] = $this->url->link('admin/common/password', 'token=' . $this->session->data['token']);
        $data['logout'] = $this->url->link('admin/common/logout', 'token=' . $this->session->data['token']);
        $data['department'] = $this->url->link('admin/department/getList', 'token=' . $this->session->data['token']);
        $data['role'] = $this->url->link('admin/role/getList', 'token=' . $this->session->data['token']);
        $data['fileManagerPower'] = $this->url->link('admin/filemanagerpower/index', 'token=' . $this->session->data['token']);

        $query = $this->db->query("SELECT union_login_id,login_token FROM _union_login WHERE union_id = '" . (int)$this->session->data['union_id'] . "' AND login_url LIKE 'https://teamapi.roogo.cn%' AND date_added >= DATE_SUB(NOW(), INTERVAL 1 DAY)");

        if ($query->num_rows == 0) {
            $login_token = token(32);
            $session_token = token(32);
            $this->db->query("INSERT INTO _union_login SET union_id = '" . (int)$this->session->data['union_id'] . "', login_ip = '" . $this->db->escape($this->request->server['REMOTE_ADDR']) . "', login_token = '" . $this->db->escape($login_token) . "', session_token = '" . $this->db->escape($session_token) . "', login_url = 'https://teamapi.roogo.cn', date_added = NOW(), date_modified = NOW()");
        } else {
            $login_token = $query->row['login_token'];
        }
        $data['tasksUrlToken'] = $login_token;
        $data['tasksUrlTime'] = time();
        return $this->load->view('common/content_top.tpl', $data);
    }

    public function bottom() {
        $data = array();

        $data['fileManager'] = $this->url->link('admin/fileManager', 'token=' . $this->session->data['token']);

        return $this->load->view('common/content_bottom.tpl', $data);
    }

    public function footer() {
        $data = array();

        return $this->load->view('common/footer.tpl', $data);
    }
}
