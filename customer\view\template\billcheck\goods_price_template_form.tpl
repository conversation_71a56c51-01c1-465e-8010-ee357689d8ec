<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">工厂：</label>
              <div class="col-sm-8">
                <input type="text" name="name" value="<?php if(!empty($goodsPriceTemplate['name'])){ ?><?php echo $goodsPriceTemplate['name']; ?><?php } ?>" placeholder="请输入快递公司" id="input-name" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">编码：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[bsku]" value="<?php if(!empty($goodsPriceTemplate['setting']['bsku'])){ ?><?php echo $goodsPriceTemplate['setting']['bsku']; ?><?php } ?>" placeholder="请输入编码取值列" id="input-bsku" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">数量：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[quantity]" value="<?php if(!empty($goodsPriceTemplate['setting']['quantity'])){ ?><?php echo $goodsPriceTemplate['setting']['quantity']; ?><?php } ?>" placeholder="请输入数量取值列" id="input-quantity" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">单价：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[price]" value="<?php if(!empty($goodsPriceTemplate['setting']['price'])){ ?><?php echo $goodsPriceTemplate['setting']['price']; ?><?php } ?>" placeholder="请输入单价取值列" id="input-price" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">总金额：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[total_prices]" value="<?php if(!empty($goodsPriceTemplate['setting']['total_prices'])){ ?><?php echo $goodsPriceTemplate['setting']['total_prices']; ?><?php } ?>" placeholder="请输入总金额取值列" id="input-total_prices" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">开始删除行数：</label>
              <div class="col-sm-8">
                <input type="number" name="setting[s_line]" value="<?php if(!empty($goodsPriceTemplate['setting']['s_line'])){ ?><?php echo $goodsPriceTemplate['setting']['s_line']; ?><?php }else{ ?>1<?php } ?>" placeholder="请输入开始行数" id="input-s_line" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">结尾删除行数：</label>
              <div class="col-sm-8">
                <input type="number" name="setting[e_line]" value="<?php if(!empty($goodsPriceTemplate['setting']['e_line'])){ ?><?php echo $goodsPriceTemplate['setting']['e_line']; ?><?php }else{ ?>0<?php } ?>" placeholder="请输入结尾删除行数" id="input-e_day" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script type="text/javascript">
  function toVaild() {
    // if ($('.glyphicon-info-sign').length) {
    //   confirm('数据不存在')
    //   return false;
    // }
    var name = document.getElementById("input-name").value;
    if (name == "") {
      confirm('请输入快递公司')
      return false;
    }
    return true;
  }


</script>
<?php echo $footer; ?>