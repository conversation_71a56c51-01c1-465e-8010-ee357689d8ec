<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        在途库存
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索编码/名称" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>供应商：</label>
                  <select class="form-control" name="filter_provider">
                    <option value="*">全部供应商</option>
                    <?php foreach ($providers as $provider) { ?>
                    <?php if ($provider['provider_no'] == $filter_provider) { ?>
                    <option value="<?php echo $provider['provider_no']; ?>" selected="selected"><?php echo $provider['provider_name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $provider['provider_no']; ?>"><?php echo $provider['provider_name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>筛选产品：</label>
                  <select class="form-control" name="filter_rule">
                    <?php foreach($rules as $rule_code => $rule_name) { ?>
                    <?php if ($rule_code == $filter_rule) { ?>
                    <option value="<?php echo $rule_code; ?>" selected="selected"><?php echo $rule_name; ?></option>
                    <?php } else{ ?>
                    <option value="<?php echo $rule_code; ?>"><?php echo $rule_name; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <a class="btn btn-danger" href="<?php echo $export; ?>"><i class="glyphicon glyphicon-download-alt"></i> 下载数据</a>
          
          <div class="pull-right">
            <button type="button" id="button-filter" class="btn bg-purple"><i class="glyphicon glyphicon-search"></i> 筛选</button>
          </div>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">在途库存列表</p>
          <div class="box-tools"></div>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th style="max-width: 200px;">图片</th>
              <th>产品名称/编码</th>
              <th>供应商</th>
              <!--
              <th>
                <?php if ($sort == 'order_num') { ?>
                  <a href="<?php echo $sort_num; ?>" class="<?php echo strtolower($order); ?>">总采购数</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_num; ?>">总采购数</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'purchase_num') { ?>
                  <a href="<?php echo $sort_stockin_num; ?>" class="<?php echo strtolower($order); ?>">已到货数量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_stockin_num; ?>">已到货数量</a>
                <?php } ?>
              </th>
              -->
              <th>
                <?php if ($sort == 'out_num') { ?>
                  <a href="<?php echo $sort_lack_num; ?>" class="<?php echo strtolower($order); ?>">未到货数量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_lack_num; ?>">未到货数量</a>
                <?php } ?>
              </th>
              <th>订单数</th>
              <th>最大超期天数</th>
              <th>订单详情</th>
              <!--
              <th>
                <?php if ($sort == 'last_sales') { ?>
                <a href="<?php echo $sort_loss_quantity; ?>" class="<?php echo strtolower($order); ?>">破损数量</a>
                <?php } else { ?>
                <a href="<?php echo $sort_loss_quantity; ?>">破损数量</a>
                <?php } ?>
              </th>
              -->
              <th>破损数量</th>
              <th>操作</th>
            </tr>
            <?php if (!empty($in_transits)) { ?>
            <?php foreach ($in_transits as $in_transit) { ?>
            <tr data-id="<?php echo $in_transit['spec_no']; ?>">
              <td><img width="100" src="<?php echo $in_transit['img_url']; ?>" class="img-thumbnail"></td>
              <td>
                <?php echo $in_transit['spec_name']; ?><br>
                <?php echo $in_transit['spec_no']; ?>
              </td>
              <td><?php echo $in_transit['provider_name']; ?></td>
              <!--
              <td><?php echo $in_transit['total_num']; ?></td>
              <td><?php echo $in_transit['total_stockin_num']; ?></td>
              -->
              <td><?php echo $in_transit['total_lack_num']; ?></td>
              <td><?php echo $in_transit['order_count']; ?></td>
              <td><?php echo $in_transit['max_days_delay']; ?></td>
              <td><?php echo $in_transit['combined_order_info']; ?></td>
              <td class="h4 no-margin">
                <?php if ($in_transit['total_loss_quantity'] > 0) { ?>
                <a href="<?php echo $loss_url; ?>&spec_no=<?php echo $in_transit['spec_no']; ?>&provider_name=<?php echo $in_transit['provider_name']; ?>" target="_blank"><span class="label label-primary"><?php echo $in_transit['total_loss_quantity']; ?></span></a>
                <?php } ?>
              </td>
              <td class="text-right">
                <a class="btn btn-success" href="<?php echo $stock_in_url; ?>&spec_no=<?php echo $in_transit['spec_no']; ?>&provider_name=<?php echo $in_transit['provider_no']; ?>" title="" target="_blank">入库详情</a>
              </td>
            </tr>
            <?php } ?>
            <?php } else{ ?>
            <td colspan="11" align="center"> 暂无库存数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期显示
    $('#input-date-start').daterangepicker({
      autoApply: true,
      autoUpdateInput: false,
      singleDatePicker: true,
      timePicker: false,
      locale: {
        format: 'YYYY-MM-DD',
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    $('#input-date-start').on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('YYYY-MM-DD'))
    })
    $('#input-date-start').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_provider = $('select[name=\'filter_provider\']').val();

      if (filter_provider != '*') {
        url += '&filter_provider=' + encodeURIComponent(filter_provider);
      }

      var filter_rule = $('select[name=\'filter_rule\']').val();

      if (filter_rule != '*') {
        url += '&filter_rule=' + encodeURIComponent(filter_rule);
      }

      location.href = url;
    });

    $('#info-modal').on('show.bs.modal', function(event) {
      $('#info-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#info-yes').on('click', () => {
      if ($('#input-retail').val() || $('#input-wholesale').val()) {
        $('#form-info').submit()
      } else {
        $('#info-modal').modal('toggle')
      }
    })

    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
  })()
</script>
<?php echo $footer; ?>