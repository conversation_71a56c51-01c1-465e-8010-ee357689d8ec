<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        导入采购入库
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form enctype="multipart/form-data" id="form-upload" class="form-horizontal">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-push_date">仓库验收日期：</label>
              <div class="col-sm-8">
                <input type="text" name="push_date" id="input-push_date" class="form-control pull-right reservation" placeholder="选择仓库验收日期" value="<?php echo $date; ?>">
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="select-calculate">选择仓库：</label>
              <div class="col-sm-8">
                <select name="warehouse_no" id="select-warehouse" class="form-control">
                  <?php foreach($warehouses as $warehouse){ ?>
                  <option value="<?php echo $warehouse['warehouse_no']; ?>"><?php echo $warehouse['name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="select-calculate">选择供应商：</label>
              <div class="col-sm-8">
                <select name="provider_no" id="select-provider" class="form-control">
                  <option value="0">请选择供应商</option>
                  <?php foreach($providers as $provider){ ?>
                  <option value="<?php echo $provider['provider_no']; ?>" <?php if(!empty($purchase_contract['provider_no']) && ($purchase_contract['provider_no'] == $provider['provider_no'])){ ?>selected="selected"<?php } ?>><?php echo $provider['provider_name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-file">选择表格：</label>
              <div class="col-sm-4">
                <input type="file" name="file" id="input-file" />
                <p class="help-block">上传表格结构必须和模板一致。</p>
              </div>
            </div>            
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="button-upload" type="button">导入表格</button>
                <a href="<?php echo $template; ?>" class="btn btn-default">下载模板</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      singleDatePicker:true,
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('YYYY-MM-DD'));
    })
  })()
</script>
<script type="text/javascript">
$('button[id=\'button-upload\']').on('click', function() {
    if ($('#form-upload input[name=\'file\']').val() != '') {
      $.ajax({
        url: '<?php echo $action; ?>',
        type: 'post',
        dataType: 'json',
        data: new FormData($('#form-upload')[0]),
        cache: false,
        contentType: false,
        processData: false,
        success: function(json) {
          $('.alert-danger, .alert-success').remove();

          if (json['error']) {
            $('.container-fluid').prepend('<div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
          }

          if (json['success']) {
            $('.container-fluid').prepend('<div class="alert alert-success"><i class="glyphicon glyphicon-info-sign"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');

            $('#form-upload input[name=\'file\']').val('');
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }
});
</script>
<?php echo $footer; ?>