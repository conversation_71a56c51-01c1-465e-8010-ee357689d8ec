<?php
class ControllerAdminUsergroup extends Controller {
    private $error = array();

    public function add() {
        $this->load->model('admin/usergroup');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_usergroup->addUserGroup($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/usergroup/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function edit() {
        $this->load->model('admin/usergroup');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_usergroup->editUserGroup($this->request->get['user_group_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/usergroup/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function delete() {
        $this->load->model('admin/usergroup');

        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $user_group_id) {
                $this->model_admin_usergroup->deleteUserGroup($user_group_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/usergroup/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getList();
    }

    public function getList() {
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/usergroup/add', 'token=' . $this->session->data['token'] . $url);
        $data['setpermission'] = $this->url->link('admin/usergroup/setPermission', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/usergroup/delete', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['users'] = array();

        $filter_data = array(
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );

        $this->load->model('admin/usergroup');
        $results = $this->model_admin_usergroup->getUserGroups($filter_data);

        foreach ($results as $result) {
            $data['usergroups'][] = array(
                'user_group_id'    => $result['user_group_id'],
                'name'             => $result['name'],
                'permission'       => json_decode($result['permission'],true),
                'route'            => $result['route'],
                'edit'             => $this->url->link('admin/usergroup/edit', 'token=' . $this->session->data['token'] . '&user_group_id=' . $result['user_group_id'] . $url)
            );
        }

        $total = $this->model_admin_usergroup->getTotalUserGroups();

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/usergroup/getList', 'token=' . $this->session->data['token'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $permissions = $this->model_admin_usergroup->getPermissions();
        $data['permissions'] = [];
        if (!empty($permissions['value'])) {
            $permissions = json_decode($permissions['value'],true);
            foreach ((array)$permissions as $v) {
                $data['permissions'][$v['key']] = $v['value'];
            }
        }

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('usergroup/list.tpl', $data));
    }

    public function setPermission() {
        $data['text_form'] =  $this->language->get('text_edit');

        $this->load->model('admin/usergroup');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && !empty($this->request->post['permissions'])) {
            $this->model_admin_usergroup->setPermissions(array_values($this->request->post['permissions']));
        }

        $permissions = $this->model_admin_usergroup->getPermissions();
        $data['num'] = 0;
        $data['permissions'] = [];
        if (!empty($permissions['value'])) {
            $data['permissions'] = json_decode($permissions['value'],true);
            $data['num'] = count($data['permissions']);
        }
        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['action'] = $this->url->link('admin/usergroup/setPermission', 'token=' . $this->session->data['token'] . $url);
        $data['cancel'] = $this->url->link('admin/usergroup/getList', 'token=' . $this->session->data['token'] . $url);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('usergroup/permission.tpl', $data));
    }

    protected function getForm() {
        $data['text_form'] = !isset($this->request->get['user_group_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['user_group_id'])) {
            $data['action'] = $this->url->link('admin/usergroup/add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/usergroup/edit', 'token=' . $this->session->data['token'] . '&user_group_id=' . $this->request->get['user_group_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/usergroup/getList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['user_group_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $user_group_info = $this->model_admin_usergroup->getUserGroup($this->request->get['user_group_id']);
        }

        $permissions = $this->model_admin_usergroup->getPermissions();
        $data['permissions'] = [];
        if (!empty($permissions['value'])) {
            $permissions = json_decode($permissions['value'],true);
            foreach ((array)$permissions as $v) {
                $data['permissions'][$v['key']] = $v['value'];
            }
        }

        if (isset($this->request->post['name'])) {
            $data['name'] = $this->request->post['name'];
        } elseif (!empty($user_group_info)) {
            $data['name'] = $user_group_info['name'];
        } else {
            $data['name'] = '';
        }

        $data['access_json'] = '{}';
        $data['modify_json'] = '{}';
        if (isset($this->request->post['permission'])) {
            if (!empty($this->request->post['permission']['access'])) {
                $data['access_json'] = json_encode($this->request->post['permission']['access']);
            }
            if (!empty($this->request->post['permission']['modify'])) {
                $data['modify_json'] = json_encode($this->request->post['permission']['modify']);
            }
        } elseif (!empty($user_group_info)) {
            $permission = json_decode($user_group_info['permission'],true);
            $data['access_json'] = !empty($permission['access']) ? json_encode($permission['access']) : '{}';
            $data['modify_json'] = !empty($permission['modify']) ? json_encode($permission['modify']) : '{}';
        }

        if (isset($this->request->post['route'])) {
            $data['route'] = $this->request->post['route'];
        } elseif (!empty($user_group_info)) {
            $data['route'] = $user_group_info['route'];
        } else {
            $data['route'] = '';
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('usergroup/form.tpl', $data));
    }

    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'admin/usergroup')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['name']) < 1) || (utf8_strlen($this->request->post['name']) > 32)) {
            $this->error['warning'] = $this->language->get('error_input_empty');
        }

        if (empty($this->request->post['permission']['access']) || empty($this->request->post['permission']['modify'])) {
            $this->error['warning'] = $this->language->get('error_input_empty');
        }

        if ((utf8_strlen($this->request->post['route']) < 1) || (utf8_strlen($this->request->post['route']) > 32)) {
            $this->error['warning'] = $this->language->get('error_input_empty');
        }
        return !$this->error;
    }

    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'admin/usergroup')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        return !$this->error;
    }
}