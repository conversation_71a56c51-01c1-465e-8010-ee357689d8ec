<?php
class ControllerAdminR<PERSON>inder extends Controller {
	private $error = array();
    
    public function getInList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'stockin_date';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $data['stockins'] = array();

        $filter_data = array(
            'filter_name'       => $filter_name,
            'filter_store'		=> $filter_store,
            'filter_date_start'	=> $filter_date_start,
            'filter_date_end'	=> $filter_date_end,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/remainder');
        $results = $this->model_admin_remainder->getInList($filter_data);

        foreach ($results as $result) {
            $data['stockins'][] = array(
                'store'  => $stores[$result['store_id']] ?? '',
                'img'    => $result['img_url'],
                'bsku'   => $result['bsku'],
                'name'   => $result['spec_name'],
                'date'   => $result['stockin_date'],
                'cost'   => $result['stockin_cost'],
                'quan'   => $result['stockin_quan'],
                'sale'   => $result['sale_quan'],
                'remain' => $result['remain_quan']
            );
        }

        $total = $this->model_admin_remainder->getInTotal($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_store'] = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
        $data['sort_bsku'] = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token'] . '&sort=bsku' . $url);
        $data['sort_date'] = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token'] . '&sort=stockin_date' . $url);
        $data['sort_cost'] = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token'] . '&sort=stockin_cost' . $url);
        $data['sort_quan'] = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token'] . '&sort=stockin_quan' . $url);
        $data['sort_sale'] = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token'] . '&sort=sale_quan' . $url);
        $data['sort_remain'] = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token'] . '&sort=remain_quan' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/remainder/getInList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('remainder/in_list.tpl', $data));
    }

    public function getOutList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'stockout_month';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $data['stockouts'] = array();

        $filter_data = array(
            'filter_name'       => $filter_name,
            'filter_store'      => $filter_store,
            'filter_date_start' => $filter_date_start,
            'filter_date_end'   => $filter_date_end,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit')
        );

        $this->load->model('admin/remainder');
        $data['months'] = $this->model_admin_remainder->getMonths();

        $results = $this->model_admin_remainder->getOutList($filter_data);

        foreach ($results as $result) {
            $data['stockouts'][] = array(
                'out_store' => $stores[$result['store_id']] ?? '',
                'out_month' => $result['stockout_month'],
                'out_count' => $result['stockout_count'],
                'out_quan'  => $result['stockout_quan'],
                'status'    => $result['status'],
                'detail'    => $this->url->link('admin/remainder/getOutDetail', 'token=' . $this->session->data['token'] . '&store_id=' . $result['store_id'] . '&stockout_month=' . $result['stockout_month'])
            );
        }

        $total = $this->model_admin_remainder->getOutTotal($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_store'] = $this->url->link('admin/remainder/getOutList', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
        $data['sort_month'] = $this->url->link('admin/remainder/getOutList', 'token=' . $this->session->data['token'] . '&sort=stockout_month' . $url);
        $data['sort_count'] = $this->url->link('admin/remainder/getOutList', 'token=' . $this->session->data['token'] . '&sort=stockout_count' . $url);
        $data['sort_quan'] = $this->url->link('admin/remainder/getOutList', 'token=' . $this->session->data['token'] . '&sort=stockout_quan' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/remainder/getOutList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/remainder/getOutList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('remainder/out_list.tpl', $data));
    }

    public function getOutDetail(){
        if (isset($this->request->get['store_id'])) {
            $store_id = $this->request->get['store_id'];
        } else {
            $store_id = '';
        }

        if (isset($this->request->get['stockout_month'])) {
            $stockout_month = $this->request->get['stockout_month'];
        } else {
            $stockout_month = '';
        }

        $url = '';

        if (isset($this->request->get['store_id'])) {
            $url .= '&store_id=' . $this->request->get['store_id'];
        }

        if (isset($this->request->get['stockout_month'])) {
            $url .= '&stockout_month=' . $this->request->get['stockout_month'];
        }

        $data['handle'] = $this->url->link('admin/remainder/handle', 'token=' . $this->session->data['token'] . $url);
        $data['list'] = $this->url->link('admin/remainder/getOutList', 'token=' . $this->session->data['token']);

        $this->load->model('admin/remainder');
        $data['products'] = $this->model_admin_remainder->getOutDetail($store_id, $stockout_month);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('remainder/out_detail.tpl', $data));
    }

    public function getCostList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_type'])) {
            $filter_type = $this->request->get['filter_type'];
        } else {
            $filter_type = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'month';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $data['costlist'] = array();

        $filter_data = array(
            'filter_name'       => $filter_name,
            'filter_store'      => $filter_store,
            'filter_type'       => $filter_type,
            'filter_date_start' => $filter_date_start,
            'filter_date_end'   => $filter_date_end,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit')
        );

        $this->load->model('admin/remainder');
        $data['months'] = $this->model_admin_remainder->getMonths();
        $data['types'] = $this->model_admin_remainder->getCostType();

        $results = $this->model_admin_remainder->getCostList($filter_data);

        foreach ($results as $result) {
            $data['costlist'][] = array(
                'cost_store' => $stores[$result['store_id']] ?? '',
                'cost_month' => $result['month'],
                'cost_typeid'=> $result['cost_type'],
                'cost_type'  => $data['types'][$result['cost_type']] ?? '',
                'cost_count' => $result['cost_count'],
                'cost_quan'  => $result['cost_quan'],
                'cost_total' => $result['cost_total'],
                'detail'     => $this->url->link('admin/remainder/getCostDetail', 'token=' . $this->session->data['token'] . '&store_id=' . $result['store_id'] . '&month=' . $result['month'] . '&type=' . $result['cost_type'])
            );
        }

        $total = $this->model_admin_remainder->getCostTotal($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_type'])) {
            $url .= '&filter_type=' . $this->request->get['filter_type'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_store'] = $this->url->link('admin/remainder/getCostList', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
        $data['sort_month'] = $this->url->link('admin/remainder/getCostList', 'token=' . $this->session->data['token'] . '&sort=month' . $url);
        $data['sort_type'] = $this->url->link('admin/remainder/getCostList', 'token=' . $this->session->data['token'] . '&sort=cost_type' . $url);
        $data['sort_count'] = $this->url->link('admin/remainder/getCostList', 'token=' . $this->session->data['token'] . '&sort=cost_count' . $url);
        $data['sort_quan'] = $this->url->link('admin/remainder/getCostList', 'token=' . $this->session->data['token'] . '&sort=cost_quan' . $url);
        $data['sort_total'] = $this->url->link('admin/remainder/getCostList', 'token=' . $this->session->data['token'] . '&sort=cost_total' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_type'])) {
            $url .= '&filter_type=' . $this->request->get['filter_type'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/remainder/getCostList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_type'] = $filter_type;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/remainder/getCostList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('remainder/cost_list.tpl', $data));
    }

    public function getCostDetail(){
        if (isset($this->request->get['store_id'])) {
            $store_id = $this->request->get['store_id'];
        } else {
            $store_id = '';
        }

        if (isset($this->request->get['month'])) {
            $month = $this->request->get['month'];
        } else {
            $month = '';
        }

        if (isset($this->request->get['type'])) {
            $type = $this->request->get['type'];
        } else {
            $type = '';
        }

        $this->load->model('admin/remainder');        
        $data['types'] = $this->model_admin_remainder->getCostType();
        $data['products'] = $this->model_admin_remainder->getCostDetail($store_id, $month, $type);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('remainder/cost_detail.tpl', $data));
    }

    public function handle() {
        $json = array();

        $isHandle = $this->cache->get(CACHE_PREFIX . 'handle.outcost');

        if (!$isHandle) {
            $this->cache->set(CACHE_PREFIX . 'handle.outcost', time());
            
            if (isset($this->request->get['store_id'])) {
                $store_id = $this->request->get['store_id'];
            } else {
                $store_id = '';
            }

            if (isset($this->request->get['stockout_month'])) {
                $stockout_month = $this->request->get['stockout_month'];
            } else {
                $stockout_month = '';
            }

            $this->load->model('admin/remainder');
            $results = $this->model_admin_remainder->getHandleOuts($store_id, $stockout_month);

            foreach ($results as $result) {
                if (isset($this->request->post['quantity'][$result['stockout_id']])) {
                    $result['stockout_quan'] = $this->request->post['quantity'][$result['stockout_id']];
                    $this->model_admin_remainder->updateOutDetail($result);
                }
            }

            if (count($results) == 0) {
                $this->model_admin_remainder->addOverTimeCost($store_id, $stockout_month);

                $this->session->data['success'] = $this->language->get('text_edit_success');
                $json['success'] = $this->language->get('text_edit_success');
            } else {
                $json['next'] = true;
            }

            $this->cache->delete(CACHE_PREFIX . 'handle.outcost');
        }

        $this->response->setOutJson($json);
    }
}
