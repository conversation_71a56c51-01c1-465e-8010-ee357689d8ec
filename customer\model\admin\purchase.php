<?php
class ModelAdminPurchase extends Model {
    public function addPlan($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_plan SET purchase_id = '0', store_id = '" . (int)$data['store_id'] . "', user_id = '" . (int)$this->user->user_id . "', bsku = '" . $this->db->escape($data['bsku']) . "', plan_quan = '" . abs((int)$data['plan_quan']) . "', state = '0', date_added = NOW(), date_modified = NOW()");

        $this->addStoreStock($data['store_id'], $data['bsku']);
    }

    public function editPlan($plan_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "purchase_plan SET bsku = '" . $this->db->escape($data['bsku']) . "', plan_quan = '" . abs((int)$data['plan_quan']) . "', date_modified = NOW() WHERE plan_id = '" . (int)$plan_id . "'");
    }

    public function deletePlan($plan_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "purchase_plan WHERE plan_id = '" . (int)$plan_id . "' AND state = '0'");
    }

    public function deleteInvalidPlan() {
        $this->db->query("DELETE FROM " . DB_PREFIX . "purchase_plan WHERE plan_quan = '0' AND state = '0'");
    }

    public function getStorePlan($store_id, $bsku) {
        $query = $this->db->query("SELECT plan_id, bsku, plan_quan FROM " . DB_PREFIX . "purchase_plan WHERE store_id = '" . (int)$store_id . "' AND bsku = '" . $this->db->escape($bsku) . "' AND state = '0'");

        return $query->row;
    }

    public function getStorePlans($store_id = 0) {
        $sql = "SELECT plan_id, store_id, bsku, plan_quan FROM " . DB_PREFIX . "purchase_plan WHERE state = '0'";

        if (!empty($store_id)) {
            $sql .= " AND store_id = '" . (int)$store_id . "'";
        }

        $sql .= " ORDER BY bsku ASC";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function addStoreStock($store_id, $bsku) {
        $query = $this->db->query("SELECT stock_id FROM " . DB_PREFIX . "store_stock WHERE store_id = '" . (int)$store_id . "' AND bsku = '" . $this->db->escape($bsku) . "'");

        if (!$query->row) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "store_stock SET store_id = '" . (int)$store_id . "', bsku = '" . $this->db->escape($bsku) . "', stock_quan = '0', order_quan = '0', sale_quan = '0', 1dsales = '0', 3dsales = '0', 7dsales = '0', 15dsales = '0', 30dsales = '0', 60dsales = '0', 90dsales = '0', stop = '0', date_added = NOW(), date_modified = NOW()");
        }
    }

    public function getStoreStock($data) {
        $sql = "SELECT store_id, bsku, stock_quan, order_quan, sale_quan, 1dsales, 3dsales, 7dsales, 15dsales, 30dsales, 60dsales, 90dsales, stop, spec_name, img_url, prop2 FROM " . DB_PREFIX . "store_stock ss LEFT JOIN (SELECT spec_no, spec_name, img_url, prop2 FROM wdt_spec_list WHERE deleted = '0') sl ON (ss.bsku = sl.spec_no) WHERE 1";

        // if ($this->user->store_ids) {
        //     $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        // }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(bsku, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($data['filter_store']) . "')";
        }

        if (!empty($data['filter_bsku_list'])) {
            $sql .= " AND bsku IN ('" . implode("','", $data['filter_bsku_list']) . "')";
        }

        if (!empty($data['filter_bsku_notlist'])) {
            $sql .= " AND bsku NOT IN ('" . implode("','", $data['filter_bsku_notlist']) . "')";
        }

        if (!empty($data['filter_rule'])) {
            if ($data['filter_rule'] == 'less25day') {
                $sql .= " AND stock_quan < (3dsales + 7dsales + 15dsales)";
            } elseif ($data['filter_rule'] == 'notplan') {
                // $sql .= " AND (plan_quan IS NULL OR plan_quan = '0')";
                $sql .= " AND CONCAT(store_id, bsku) NOT IN (SELECT CONCAT(store_id, bsku) FROM " . DB_PREFIX . "purchase_plan WHERE state = '0')";
            } elseif ($data['filter_rule'] == 'inplan') {
                // $sql .= " AND (plan_quan IS NOT NULL AND plan_quan > '0')";
                $sql .= " AND CONCAT(store_id, bsku) IN (SELECT CONCAT(store_id, bsku) FROM " . DB_PREFIX . "purchase_plan WHERE state = '0')";
            } elseif ($data['filter_rule'] == 'oversold') {
                $sql .= " AND stock_quan < 0";
            }
        }

        $sort_data = array(
            'stock_id',
            'store_id',
            'bsku',
            'stock_quan',
            'sale_quan',
            'order_quan',
            '1dsales',
            '3dsales',
            '7dsales',
            '15dsales',
            '30dsales',
            '60dsales',
            '90dsales',
            'date_added'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY stock_id";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getStoreCost($store_id = 0) {
        $member_sql =   "SELECT SUM(ss.stock_quan*sl.member_price) as cost FROM " . DB_PREFIX . "store_stock ss LEFT JOIN (SELECT spec_no, member_price FROM wdt_spec_list WHERE deleted = '0') sl ON ss.bsku = sl.spec_no WHERE ss.store_id= '" . (int)$store_id ."'";
        $member_query = $this->db->query($member_sql);

        $retail_sql =   "SELECT SUM(ss.stock_quan*sl.retail_price) as cost FROM " . DB_PREFIX . "store_stock ss LEFT JOIN (SELECT spec_no, retail_price FROM wdt_spec_list WHERE deleted = '0') sl ON ss.bsku = sl.spec_no WHERE ss.store_id= '" . (int)$store_id ."'";
        $retail_query = $this->db->query($retail_sql);

        return ['member_cost'=>$member_query->row['cost'],'retail_cost'=>$retail_query->row['cost']];
    }

    public function getTotalStoreStocks($data) {
        $sql = "SELECT COUNT(stock_id) AS total FROM " . DB_PREFIX . "store_stock ss LEFT JOIN (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl ON (ss.bsku = sl.spec_no) WHERE 1";

        // if ($this->user->store_ids) {
        //     $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        // }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(bsku, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($data['filter_store']) . "')";
        }

        if (!empty($data['filter_bsku_list'])) {
            $sql .= " AND bsku IN ('" . implode("','", $data['filter_bsku_list']) . "')";
        }

        if (!empty($data['filter_bsku_notlist'])) {
            $sql .= " AND bsku NOT IN ('" . implode("','", $data['filter_bsku_notlist']) . "')";
        }

        if (!empty($data['filter_rule'])) {
            if ($data['filter_rule'] == 'less25day') {
                $sql .= " AND stock_quan < (3dsales + 7dsales + 15dsales)";
            } elseif ($data['filter_rule'] == 'notplan') {
                // $sql .= " AND (plan_quan IS NULL OR plan_quan = '0')";
                $sql .= " AND CONCAT(store_id, bsku) NOT IN (SELECT CONCAT(store_id, bsku) FROM " . DB_PREFIX . "purchase_plan WHERE state = '0')";
            } elseif ($data['filter_rule'] == 'inplan') {
                // $sql .= " AND (plan_quan IS NOT NULL AND plan_quan > '0')";
                $sql .= " AND CONCAT(store_id, bsku) IN (SELECT CONCAT(store_id, bsku) FROM " . DB_PREFIX . "purchase_plan WHERE state = '0')";
            } elseif ($data['filter_rule'] == 'oversold') {
                $sql .= " AND stock_quan < 0";
            }
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getStocks($data) {
        $sql = "SELECT * FROM (SELECT bsku, SUM(stock_quan) AS stock_quan, SUM(order_quan) AS order_quan, SUM(sale_quan) AS sale_quan FROM " . DB_PREFIX . "store_stock GROUP BY bsku) ss LEFT JOIN (SELECT spec_no, spec_name, order_num, purchase_num, to_purchase_num, avaliable_num, lock_num, img_url FROM wdt_stocks WHERE warehouse_id = '7' AND Status = '1' AND deleted = '0') st ON (ss.bsku = st.spec_no) WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(bsku, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        $sort_data = array(
            'bsku',
            'stock_quan',
            'sale_quan',
            'order_quan',
            'order_num',
            'purchase_num',
            'avaliable_num'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY stock_id";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalStocks($data) {
        $sql = "SELECT COUNT(DISTINCT bsku) AS total FROM " . DB_PREFIX . "store_stock ss LEFT JOIN (SELECT spec_no, spec_name FROM wdt_stocks WHERE warehouse_id = '7' AND Status = '1' AND deleted = '0') st ON (ss.bsku = st.spec_no) WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(bsku, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getStoreRules() {
        return array(
            'less25day' => '预计少于25天销量',
            'notplan'   => '无备货产品',
            'inplan'    => '已备货产品',
            'oversold'  => '库存超卖'
        );
    }

    public function getStoreClass() {
        return array(
            '家居饰品-小件精品',
            '喜事用品',
            '宠物园艺类目',
            '车载类目',
            'IP馆',
            '文旅纪念品',
            '办公文化',
            '家装主材'
        );
    }

    public function getWdtStocks($data) {
        $sql = "SELECT spec_no, spec_name, order_num, purchase_num, to_purchase_num, avaliable_num, lock_num, img_url FROM wdt_stocks WHERE warehouse_id = '7' AND Status = '1' AND deleted = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(goods_no, spec_no, goods_name, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['spec_in_list'])) {
            $sql .= " AND spec_no IN ('" . implode("','", $data['spec_in_list']) . "')";
        }

        if (!empty($data['spec_not_list'])) {
            $sql .= " AND spec_no NOT IN ('" . implode("','", $data['spec_not_list']) . "')";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getWdtGoods($data) {
        $sql = "SELECT goods_id, goods_no, goods_name FROM wdt_goods_list WHERE deleted = '0'";

        if (!empty($data['filter_new'])) {
            $sql .= " AND flag_id = '546'";
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getWdtSpecs($data) {
        $sql = "SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_goods'])) {
            $sql .= " AND goods_id = '" . (int)$data['filter_goods'] . "'";
        }

        if (!empty($data['spec_in_list'])) {
            $sql .= " AND spec_no IN ('" . implode("','", $data['spec_in_list']) . "')";
        }

        if (!empty($data['spec_not_list'])) {
            $sql .= " AND spec_no NOT IN ('" . implode("','", $data['spec_not_list']) . "')";
        }

        $sql .= " ORDER BY spec_id ASC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getPlans($data) {
        $sql = "SELECT pp.plan_id, pp.store_id, pp.bsku, sl.spec_name, pp.plan_quan, pp.date_added, p.order_no, p.complete_date FROM " . DB_PREFIX . "purchase_plan pp LEFT JOIN " . DB_PREFIX . "purchase p ON (pp.purchase_id = p.purchase_id) LEFT JOIN (SELECT spec_no, spec_name FROM wdt_spec_list WHERE deleted = '0') sl ON (pp.bsku = sl.spec_no) WHERE pp.state = '1'";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(pp.store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(pp.bsku, p.order_no, sl.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND pp.store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(pp.date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(pp.date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sort_data = array(
            'plan_id',
            'store_id',
            'bsku',
            'spec_name',
            'plan_quan',
            'order_no',
            'complete_date',
            'date_added'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY plan_id";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalPlans($data) {
        $sql = "SELECT COUNT(pp.plan_id) AS total FROM " . DB_PREFIX . "purchase_plan pp LEFT JOIN " . DB_PREFIX . "purchase p ON (pp.purchase_id = p.purchase_id) LEFT JOIN (SELECT spec_no, spec_name FROM wdt_spec_list WHERE deleted = '0') sl ON (pp.bsku = sl.spec_no) WHERE pp.state = '1'";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(pp.store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(pp.bsku, p.order_no, sl.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND pp.store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(pp.date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(pp.date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function addReturn($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "store_return SET store_id = '" . (int)$data['store_id'] . "', user_id = '" . (int)$this->user->user_id . "', bsku = '" . $this->db->escape($data['bsku']) . "', quantity = '" . abs((int)$data['quantity']) . "', state = '1', date_added = NOW()");

        $this->db->query("UPDATE " . DB_PREFIX . "store_stock SET stock_quan = stock_quan + '" . abs((int)$data['quantity']) . "', date_modified = NOW() WHERE store_id = '" . (int)$data['store_id'] . "' AND bsku = '" . $this->db->escape($data['bsku']) . "'");
    }

    public function getReturn($data) {
        $sql = "SELECT sr.return_id, sr.store_id, sr.bsku, sl.spec_name, sr.quantity, sr.date_added FROM " . DB_PREFIX . "store_return sr LEFT JOIN (SELECT spec_no, spec_name FROM wdt_spec_list WHERE deleted = '0') sl ON (sr.bsku = sl.spec_no) WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(sr.store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(sr.bsku, sl.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND sr.store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(sr.date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(sr.date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sort_data = array(
            'return_id',
            'store_id',
            'bsku',
            'spec_name',
            'quantity',
            'date_added'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY return_id";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalReturn($data) {
        $sql = "SELECT COUNT(sr.return_id) AS total FROM " . DB_PREFIX . "store_return sr LEFT JOIN (SELECT spec_no, spec_name FROM wdt_spec_list WHERE deleted = '0') sl ON (sr.bsku = sl.spec_no) WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(sr.store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(sr.bsku, sl.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND sr.store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(sr.date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(sr.date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function addNewOrder($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "purchase SET user_id = '" . (int)$this->user->user_id . "', order_no = '" . $this->db->escape($data['order_no']) . "', complete_date = '" . $this->db->escape($data['complete_date']) . "', date_added = NOW()");

        $purchase_id = $this->db->getLastId();

        if (isset($data['order_quan'])) {
            foreach ($data['order_quan'] as $bsku => $quan) {
                if (!empty($quan)) {
                    $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_detail SET purchase_id = '" . (int)$purchase_id . "', bsku = '" . $this->db->escape($bsku) . "', order_quan = '" . abs((int)$quan) . "', sale_quan = '0'");
                }
            }
        }

        if (isset($data['plan_quan'])) {
            foreach ($data['plan_quan'] as $bsku => $stores) {
                foreach ($stores as $store_id => $quan) {
                    if (!empty($quan)) {
                        $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_plan SET purchase_id = '" . (int)$purchase_id . "', store_id = '" . (int)$store_id . "', user_id = '" . (int)$this->user->user_id . "', bsku = '" . $this->db->escape($bsku) . "', plan_quan = '" . abs((int)$quan) . "', state = '1', date_added = NOW(), date_modified = NOW()");

                        $this->addStoreStock($store_id, $bsku);

                        $this->db->query("UPDATE " . DB_PREFIX . "store_stock SET stock_quan = stock_quan + '" . abs((int)$quan) . "', order_quan = order_quan + '" . abs((int)$quan) . "', date_modified = NOW() WHERE store_id = '" . (int)$store_id . "' AND bsku = '" . $this->db->escape($bsku) . "'");
                    }
                }
            }
        }
    }

    public function addOrder($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "purchase SET user_id = '" . (int)$this->user->user_id . "', order_no = '" . $this->db->escape($data['order_no']) . "', complete_date = '" . $this->db->escape($data['complete_date']) . "', date_added = NOW()");

        $purchase_id = $this->db->getLastId();

        if (isset($data['selected'])) {
            $plans = array();
            foreach ($data['selected'] as $bsku => $plan_ids) {
                if (!empty($data['quantity'][$bsku])) {
                    $plans[] = $plan_ids;

                    $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_detail SET purchase_id = '" . (int)$purchase_id . "', bsku = '" . $this->db->escape($bsku) . "', order_quan = '" . abs((int)$data['quantity'][$bsku]) . "', sale_quan = '0'");
                }
            }

            if (!empty($plans)) {
                $query = $this->db->query("SELECT store_id, bsku, plan_quan FROM " . DB_PREFIX . "purchase_plan WHERE state = '0' AND plan_id IN (" . $this->db->escape(implode(',', $plans)) . ")");

                foreach ($query->rows as $row) {
                    $this->db->query("UPDATE " . DB_PREFIX . "store_stock SET stock_quan = stock_quan + '" . abs((int)$row['plan_quan']) . "', order_quan = order_quan + '" . abs((int)$row['plan_quan']) . "', date_modified = NOW() WHERE store_id = '" . (int)$row['store_id'] . "' AND bsku = '" . $this->db->escape($row['bsku']) . "'");
                }

                $this->db->query("UPDATE " . DB_PREFIX . "purchase_plan SET purchase_id = '" . (int)$purchase_id . "', state = '1', date_modified = NOW() WHERE state = '0' AND  plan_id IN (" . $this->db->escape(implode(',', $plans)) . ")");
            }
        }
    }

    public function subtractOrder($purchase_id) {
        $query = $this->db->query("SELECT store_id, bsku, plan_quan FROM " . DB_PREFIX . "purchase_plan WHERE purchase_id = '" . (int)$purchase_id . "'");

        foreach ($query->rows as $row) {
            echo("UPDATE " . DB_PREFIX . "store_stock SET stock_quan = stock_quan - '" . abs((int)$row['plan_quan']) . "', order_quan = order_quan - '" . abs((int)$row['plan_quan']) . "', date_modified = NOW() WHERE store_id = '" . (int)$row['store_id'] . "' AND bsku = '" . $this->db->escape($row['bsku']) . "';<br>");
        }
    }

    public function editOrderNo($purchase_id, $order_no) {
        $this->db->query("UPDATE " . DB_PREFIX . "purchase SET order_no = '" . $this->db->escape($order_no) . "' WHERE purchase_id = '" . (int)$purchase_id . "'");
    }

    public function getOrders($data = array()) {
        $sql = "SELECT purchase_id, order_no, complete_date, date_added FROM " . DB_PREFIX . "purchase WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND order_no LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_bsku'])) {
            $sql .= " AND purchase_id IN (SELECT purchase_id FROM " . DB_PREFIX . "purchase_detail WHERE bsku IN (SELECT spec_no FROM wdt_spec_list WHERE deleted = '0' AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_bsku']) . "%')) ";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " ORDER BY purchase_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalOrders($data = array()) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "purchase WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND order_no LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_bsku'])) {
            $sql .= " AND purchase_id IN (SELECT purchase_id FROM " . DB_PREFIX . "purchase_detail WHERE bsku IN (SELECT spec_no FROM wdt_spec_list WHERE deleted = '0' AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_bsku']) . "%')) ";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getOrder($purchase_id) {
        $query = $this->db->query("SELECT purchase_id, order_no, complete_date FROM " . DB_PREFIX . "purchase WHERE purchase_id = '" . (int)$purchase_id . "'");

        return $query->row;
    }

    public function getOrderDetail($purchase_id) {
        $query = $this->db->query("SELECT bsku, spec_name, img_url, order_quan FROM " . DB_PREFIX . "purchase_detail pd LEFT JOIN (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl ON (pd.bsku = sl.spec_no) WHERE purchase_id = '" . (int)$purchase_id . "' ORDER BY bsku ASC");

        return $query->rows;
    }

    public function getOrderPlans($purchase_id) {
        $query = $this->db->query("SELECT store_id, bsku, plan_quan FROM " . DB_PREFIX . "purchase_plan WHERE purchase_id = '" . (int)$purchase_id . "'");

        return $query->rows;
    }

    public function addStoreSales($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "store_sales SET store_id = '" . (int)$data['store_id'] . "', store_name = '" . $this->db->escape($data['store_name']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', gsku = '" . $this->db->escape($data['gsku']) . "', sale_date = '" . $this->db->escape($data['sale_date']) . "', quantity = '" . (int)$data['quantity'] . "', total = '" . (float)$data['total'] . "', state = '0', date_added = NOW()");
    }

    public function getSalesExist($sale_date) {
        $query = $this->db->query("SELECT sales_id FROM " . DB_PREFIX . "store_sales WHERE sale_date = '" . $this->db->escape($sale_date) . "' LIMIT 1");

        return $query->num_rows;
    }

    public function addTransfer($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "store_transfer SET from_store_id = '" . (int)$data['from_store_id'] . "', from_user_id = '" . (int)$this->user->user_id . "', to_store_id = '" . (int)$data['to_store_id'] . "', mode = '" . $this->db->escape($data['mode']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', transfer_quan = '" . abs((int)$data['transfer_quan']) . "', to_user_id = '0', state = '0', date_added = NOW(), date_modified = NOW()");
    }

    public function editTransfer($transfer_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "store_transfer SET from_store_id = '" . (int)$data['from_store_id'] . "', to_store_id = '" . (int)$data['to_store_id'] . "', mode = '" . $this->db->escape($data['mode']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', transfer_quan = '" . abs((int)$data['transfer_quan']) . "', date_modified = NOW() WHERE transfer_id = '" . (int)$transfer_id . "' AND from_user_id = '" . (int)$this->user->user_id . "' AND state = '0'");
    }

    public function deleteTransfer($transfer_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "store_transfer WHERE transfer_id = '" . (int)$transfer_id . "' AND from_user_id = '" . (int)$this->user->user_id . "' AND state = '0'");
    }

    public function actionTransfer($transfer_id, $state) {
        if ($this->user->store_ids) {
            $this->db->query("UPDATE " . DB_PREFIX . "store_transfer SET state = '" . (int)$state . "', to_user_id = '" . (int)$this->user->user_id . "' WHERE transfer_id = '" . (int)$transfer_id . "' AND state = '0' AND FIND_IN_SET(to_store_id, '" . $this->db->escape($this->user->store_ids) . "')");

            if ($this->db->countAffected() && ((int)$state == 1)) {
                $transfer_info = $this->getTransfer($transfer_id);

                if ($transfer_info['mode'] == 'out') {
                    $from_store_id = $transfer_info['to_store_id'];
                    $to_store_id = $transfer_info['from_store_id'];
                } else {
                    $from_store_id = $transfer_info['from_store_id'];
                    $to_store_id = $transfer_info['to_store_id'];
                }

                $this->db->query("UPDATE " . DB_PREFIX . "store_stock SET stock_quan = stock_quan + '" . (int)$transfer_info['transfer_quan'] . "', date_modified = NOW() WHERE store_id = '" . (int)$from_store_id . "' AND bsku = '" . $this->db->escape($transfer_info['bsku']) . "'");

                $this->db->query("UPDATE " . DB_PREFIX . "store_stock SET stock_quan = stock_quan - '" . (int)$transfer_info['transfer_quan'] . "', date_modified = NOW() WHERE store_id = '" . (int)$to_store_id . "' AND bsku = '" . $this->db->escape($transfer_info['bsku']) . "'");
            }
        }
    }

    public function getTransferStock($data = array()) {
        $sql = "SELECT store_id, bsku, stock_quan FROM " . DB_PREFIX . "store_stock WHERE stock_quan > 0";

        if (!empty($data['filter_bsku_list'])) {
            $sql .= " AND bsku IN ('" . implode("','", $data['filter_bsku_list']) . "')";
        }

        $sql .= " ORDER BY stock_quan DESC";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTransfer($transfer_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "store_transfer WHERE transfer_id = '" . (int)$transfer_id . "'");

        return $query->row;
    }

    public function getTransfers($data) {
        $sql = "SELECT st.*, sl.spec_name FROM " . DB_PREFIX . "store_transfer st LEFT JOIN (SELECT spec_no, spec_name FROM wdt_spec_list WHERE deleted = '0') sl ON (st.bsku = sl.spec_no) WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND (FIND_IN_SET(from_store_id, '" . $this->db->escape($this->user->store_ids) . "') OR FIND_IN_SET(to_store_id, '" . $this->db->escape($this->user->store_ids) . "'))";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(st.bsku, sl.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_mode'])) {
            $sql .= " AND st.mode = '" . $this->db->escape($data['filter_mode']) . "'";
        }

        if (isset($data['filter_state']) && $data['filter_state'] !== '') {
            $sql .= " AND st.state = '" . (int)$data['filter_state'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(st.date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(st.date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " ORDER BY transfer_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalTransfers($data) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "store_transfer st LEFT JOIN (SELECT spec_no, spec_name FROM wdt_spec_list WHERE deleted = '0') sl ON (st.bsku = sl.spec_no) WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND (FIND_IN_SET(from_store_id, '" . $this->db->escape($this->user->store_ids) . "') OR FIND_IN_SET(to_store_id, '" . $this->db->escape($this->user->store_ids) . "'))";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(st.bsku, sl.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_mode'])) {
            $sql .= " AND st.mode = '" . $this->db->escape($data['filter_mode']) . "'";
        }

        if (isset($data['filter_state']) && $data['filter_state'] !== '') {
            $sql .= " AND st.state = '" . (int)$data['filter_state'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(st.date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(st.date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getWdtSpecsTwo($data) {
        $sql = "SELECT spec_no, spec_name, img_url, member_price, prop2, prop5, weight, goods_id FROM wdt_spec_list WHERE deleted = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        $sql .= " ORDER BY spec_id ASC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);
        if (!empty($query->rows)) {
            foreach ($query->rows as $key=>$val) {
                $provider_last = $this->db->query("SELECT last_price FROM wdt_purchase_provider_goods_list WHERE spec_no = '".$val['spec_no']."' AND provider_no = '".$data['provider_no']."'");
                if (!empty($provider_last->row['last_price'])) {
                    $query->rows[$key]['last_price'] = $provider_last->row['last_price'];
                } else {
                    $last = $this->db->query("SELECT * FROM wdt_purchase_provider_goods_list WHERE spec_no = '".$val['spec_no']."' ORDER BY rec_id DESC LIMIT 0,1");
                    if (!empty($last->row['last_price'])) {
                        $query->rows[$key]['last_price'] = $last->row['last_price'];
                    }
                }
                $goods_no = $this->getGoodsNo($val['spec_no']);
                $getPackaging = $this->getPurchaseContractContent($goods_no['goods_no'],1);
                if (!empty($getPackaging['value'])) {
                    $query->rows[$key]['packaging'] = $getPackaging['value'];
                }
                $getAccessories = $this->getPurchaseContractContent($goods_no['goods_no'],3);
                if (!empty($getAccessories['value'])) {
                    $query->rows[$key]['accessories'] = $getAccessories['value'];
                }
                $getAttention = $this->getPurchaseContractContent($goods_no['goods_no'],4);
                if (!empty($getAttention['value'])) {
                    $query->rows[$key]['attention'] = $getAttention['value'];
                }
                $content_weight = $this->getPurchaseContractContent($goods_no['goods_no'],5);
                if (!empty($content_weight['value'])) {
                    $query->rows[$key]['content_weight'] = $content_weight['value'];
                }
                $content_img_url = $this->getPurchaseContractContent($val['spec_no'],6);
                if (!empty($content_img_url['value'])) {
                    $query->rows[$key]['img_url'] = $content_img_url['value'];
                }

                $provider_goods_list = $this->db->query("SELECT provider_name,provider_no FROM wdt_purchase_provider_goods_list WHERE spec_no = '".$val['spec_no']."' ORDER BY rec_id DESC");
                $query->rows[$key]['provider_goods_list'] = $provider_goods_list->rows;
            }
        }
        return $query->rows;
    }

    public function getPurchaseContract($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "purchase_contract WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND contract_number LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND sign_date >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND sign_date <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        if (!empty($data['sort'])) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (!empty($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getPurchaseContractTotal($data = array()) {
        $sql = "SELECT count(*) as total FROM " . DB_PREFIX . "purchase_contract WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND contract_number LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND sign_date >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND sign_date <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function addPurchaseContract($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_contract SET contract_number = '" . $this->db->escape($data['contract_number']). "', warehouse_date = '" . $this->db->escape($data['warehouse_date']) . "', sign_date = '" . $this->db->escape($data['sign_date']) . "', delivery_date = '" . $this->db->escape($data['delivery_date']) . "', provider_no = '" . $this->db->escape($data['provider_no']) . "', specs = '" . $this->db->escape(json_encode(array_values($data['specs']),320)) . "', manner_of_packing = '" . $this->db->escape(json_encode(array_values($data['manner_of_packing']),320)) . "', carton_mark = '" . $this->db->escape($data['carton_mark']) . "', date_added = NOW()");

        $purchase_contract_id = $this->db->getLastId();

        $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_contract_package SET purchase_contract_id = '" . (int)$purchase_contract_id . "', updated_provider = '', updated_specs = '', updated = '0', date_modified = NOW()");
    }

    public function editPurchaseContract($data,$purchase_contract_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "purchase_contract SET contract_number = '" . $this->db->escape($data['contract_number']). "', warehouse_date = '" . $this->db->escape($data['warehouse_date']) . "', sign_date = '" . $this->db->escape($data['sign_date']) . "', delivery_date = '" . $this->db->escape($data['delivery_date']) . "', provider_no = '" . $this->db->escape($data['provider_no']) . "', specs = '" . $this->db->escape(json_encode(array_values($data['specs']),320)) . "', manner_of_packing = '" . $this->db->escape(json_encode(array_values($data['manner_of_packing']),320)) . "', carton_mark = '" . $this->db->escape($data['carton_mark']) . "', date_modified = NOW() WHERE purchase_contract_id='".$this->db->escape($purchase_contract_id)."'");

        $this->db->query("UPDATE " . DB_PREFIX . "purchase_contract_package SET updated = '0', date_modified = NOW() WHERE purchase_contract_id = '" . (int)$purchase_contract_id . "'");
    }

    public function getOnePurchaseContract($purchase_contract_id) {
        $sql = "SELECT pc.*,wpp.provider_name,wpp.contact,wpp.mobile,wpp.address FROM " . DB_PREFIX . "purchase_contract as pc LEFT JOIN wdt_purchase_provider as wpp ON pc.provider_no=wpp.provider_no WHERE pc.purchase_contract_id='".$this->db->escape($purchase_contract_id)."'";

        $query = $this->db->query($sql);

        return $query->row;
    }

    public function getSpecDetail($spec_no) {
        $sql = "SELECT spec_no, spec_name, img_url, prop2 FROM wdt_spec_list WHERE spec_no = '".$this->db->escape($spec_no)."'";

        $query = $this->db->query($sql);

        return $query->row;
    }

    public function getPurchaseProviders() {
        $sql = "SELECT provider_no,provider_name FROM wdt_purchase_provider WHERE is_disabled is null AND deleted is null ";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getPurchaseContractContent($no,$type) {
        $sql = "SELECT value FROM " . DB_PREFIX . "purchase_contract_content WHERE type = '".$type."' AND no = '".$no."'";

        $query = $this->db->query($sql);

        return $query->row;
    }

    public function setPurchaseContractContent($no,$type,$value) {
        $sql = "SELECT value FROM " . DB_PREFIX . "purchase_contract_content WHERE type = '".$type."' AND no = '".$no."'";

        $query = $this->db->query($sql);
        if (!empty($query->row)) {
            $this->db->query("UPDATE " . DB_PREFIX . "purchase_contract_content SET value = '" . $this->db->escape($value) . "' WHERE  type = '".$this->db->escape($type)."' AND no = '".$this->db->escape($no)."'");
        } else {
            $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_contract_content SET value = '" . $this->db->escape($value) . "',type = '" . $this->db->escape($type) . "',no = '" . $this->db->escape($no) . "'");
        }

        return $query->row;
    }

    public function getGoodsNo($spec_no) {
        $query = $this->db->query("SELECT wgl.goods_no FROM wdt_spec_list as wsl LEFT JOIN wdt_goods_list as wgl ON wsl.goods_id=wgl.goods_id WHERE wsl.spec_no='".$spec_no."'");

        return $query->row;
    }

    public function getGoodsNoTwo($goods_id) {
        $query = $this->db->query("SELECT goods_no FROM wdt_goods_list  WHERE goods_id='".$goods_id."'");

        return $query->row;
    }

    //关联字段
    public function getLetter() {
        $letter = [
            0 => 'A',
            1 => 'B',
            2 => 'C',
            3 => 'D',
            4 => 'E',
            5 => 'F',
            6 => 'G',
            7 => 'H',
            8 => 'I',
            9 => 'J',
            10 => 'K',
            11 => 'L',
            12 => 'M',
            13 => 'N',
            14 => 'O',
            15 => 'P',
            16 => 'Q',
            17 => 'R',
            18 => 'S',
            19 => 'T',
            20 => 'U',
            21 => 'V',
            22 => 'W',
            23 => 'X',
            24 => 'Y',
            25 => 'Z',
        ];
        return $letter;
    }

    public function getPackagingList() {
        $packaging_list = [
            1 => '双层气泡袋+牛皮纸内盒+彩标+产品底部贴防伪标(确保产品运输途中不会晃动损坏)',
            2 => 'OPP袋+12kg保丽龙全保+牛皮纸盒+彩标+产品底部贴防伪标(确保产品运输途中不会晃动损坏)',
            3 => '双层气泡袋+新版通用瓦楞彩盒+不干胶贴在盒子顶部+产品底部贴防伪标(确保产品运输途中不会晃动损坏)',
            4 => 'OPP袋+12kg保丽龙全保+新版通用瓦楞彩盒+不干胶贴在盒子顶部+底部产品贴防伪标(确保产品运输途中不会晃动损坏)',
            5 => '双层气泡袋+唐小圆公版彩盒+不干胶贴在盒子顶部+产品底部贴防伪标(确保产品运输途中不会晃动损坏)',
            6 => 'OPP袋+12kg保丽龙全保+唐小圆公版彩盒+不干胶贴在盒子顶部+产品底部贴防伪标(确保产品运输途中不会晃动损坏)',
            7 => '双层气泡袋+如果通用版彩盒+不干胶贴在盒子顶部+产品底部贴防伪标(确保产品运输途中不会晃动损坏)',
            8 => 'OPP袋+12kg保丽龙全保+如果通用版彩盒+不干胶贴在盒子顶部+底部产品贴防伪标(确保产品运输途中不会晃动损坏)',
        ];
        return $packaging_list;
    }

    public function getPackagingLikeList() {
        $packaging_list = [
            1 => '双层气泡袋+牛皮纸内盒,双层汽泡袋+牛皮纸内盒',
            2 => 'OPP袋+12kg保丽龙全保+牛皮纸盒',
            3 => '双层气泡袋+新版通用瓦楞彩盒,双层汽泡袋+新版通用瓦楞彩盒',
            4 => 'OPP袋+12kg保丽龙全保+新版通用瓦楞彩盒',
            5 => '双层气泡袋+唐小圆公版彩盒',
            6 => 'OPP袋+12kg保丽龙全保+唐小圆公版彩盒',
            7 => '双层气泡袋+如果通用版彩盒',
            8 => 'OPP袋+12kg保丽龙全保+如果通用版彩盒+不干胶贴在盒子顶部',
        ];
        return $packaging_list;
    }


    public function getPackagingListImg() {
        $packaging_list = [
            1 => [
                'interior_manner_of_packing_1_1.jpg',
                'interior_manner_of_packing_1_2.jpg'
            ],
            2 => [
                'interior_manner_of_packing_2_1.jpg',
                'interior_manner_of_packing_2_2.jpg',
                'interior_manner_of_packing_2_3.jpg'
            ],
            3 => [
                'interior_manner_of_packing_1_1.jpg',
                'interior_manner_of_packing_caihe.png'
            ],
            4 => [
                'interior_manner_of_packing_2_1.jpg',
//                'interior_manner_of_packing_2_2.jpg',
                'interior_manner_of_packing_caihe.png'
            ],
            5 => [
                'interior_manner_of_packing_1_1.jpg',
                'interior_manner_of_packing_3_1.png'
            ],
            6 => [
                'interior_manner_of_packing_2_1.jpg',
                'interior_manner_of_packing_3_1.png'
            ],
            7 => [
                'interior_manner_of_packing_1_1.jpg',
                'interior_manner_of_packing_caihe.png'
            ],
            8 => [
                'interior_manner_of_packing_2_1.jpg',
//                'interior_manner_of_packing_2_2.jpg',
                'interior_manner_of_packing_caihe.png'
            ],
        ];
        return $packaging_list;
    }

    public function getAccessoriesList() {
        $packaging_list = [
            1 => '底部配胶塞',
            2 => '加绒点、胶塞',
            3 => '加2片3m胶',
            4 => '加1片3m胶',
            5 => '加绒点'
        ];
        return $packaging_list;
    }

    public function getAttentionList() {
        $packaging_list = [
            1 => 'OPP袋+珍珠棉+彩盒(要确保产品运输途中不会晃动损坏)',
            2 => '花好月圆套装包装方式',
            3 => '一般定制款才会需要注意事项',
        ];
        return $packaging_list;
    }

	public function editSpecImgUrl($no,$value) {
        $this->db->query("UPDATE wdt_spec_list SET img_url = '" . $this->db->escape($value). "' WHERE spec_no='".$this->db->escape($no)."'");
	}

    public function getCrowdProduct($contract_ids) {
        $products = [];

        if (!empty($contract_ids)) {
            $query = $this->db->query("SELECT purchase_contract_id, specs FROM " . DB_PREFIX . "purchase_contract WHERE crowd IS NULL AND purchase_contract_id IN (".$this->db->escape($contract_ids).")");

            foreach ($query->rows as $row) {
                foreach ((array)json_decode($row['specs'],true) as $spec) {
                    $option = $this->db->query("SELECT product_option_id, product_id FROM cf_product_option WHERE option_sku = '" . $this->db->escape(trim($spec['spec_no'])) . "'")->row;

                    if (!empty($option)) {
                        if (isset($products[$option['product_id']][$option['product_option_id']])) {
                            $products[$option['product_id']][$option['product_option_id']]['quan'] += (int)$spec['num'];
                        } else {
                            $products[$option['product_id']][$option['product_option_id']] = array(
                                'bsku'  => $spec['spec_no'],
                                'name'  => $spec['spec_name'],
                                'img'   => $spec['img_url'],
                                'price' => $spec['price'],
                                'quan'  => (int)$spec['num']
                            );
                        }
                    }
                }
            }
        }

        return $products;
    }

    public function updateCrowdProduct($contract_ids, $orders) {
        $names = [];

        foreach ($orders as $product_id => $product) {
            $product_info = $this->db->query("SELECT * FROM cf_product WHERE product_id = '" . (int)$product_id . "'")->row;

            if (!empty($product_info)) {
                $names[] = $product_info['product_name'];
                $times = (int)$product_info['orders'] + 1;
                $order_total = 0;

                foreach ($product as $option_id => $option) {
                    $this->db->query("INSERT INTO cf_product_order SET product_option_id = '" . (int)$option_id . "', order_times = '" . (int)$times . "', order_price = '" . (float)$option['price'] . "', order_quantity = '" . (int)$option['quan'] . "', order_sold = '0', date_added = NOW()");

                    $order_total += moneyformat($option['price']) * integerformat($option['quan']);
                }

                $this->db->query("UPDATE cf_product SET orders = '" . (int)$times . "', price = '" . moneyformat($order_total / $product_info['quantity']) . "', status = '1', date_start = NOW(), date_end = DATE_ADD(NOW(), INTERVAL 2 DAY) WHERE product_id = '" . (int)$product_id . "'");
            
                $this->db->query("UPDATE cf_user_product SET date_modified = DATE_ADD(NOW(), INTERVAL 2 DAY) WHERE product_id = '" . (int)$product_id . "' AND orders = '" . (int)$product_info['orders'] . "'");
            }
        }

        if (!empty($contract_ids)) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_contract_crowd SET contract_ids = '".$this->db->escape($contract_ids)."', orders = '" . $this->db->escape(json_encode($orders)) . "', date_added = NOW()");

            $this->db->query("UPDATE " . DB_PREFIX . "purchase_contract SET crowd = '1' WHERE crowd IS NULL AND purchase_contract_id IN (".$this->db->escape($contract_ids).")");
        }

        if (!empty($names)) {
            $ding = new DingTalk();
            $res = $ding->sendRobotMsg('https://oapi.dingtalk.com/robot/send?access_token=0b6809413876b7e7fe1b57384a988b1c1b551046899c403386625852a9925e22', 'SEC2c8c9a9a59a34d291f22d1e8037a86d47585cc45cf535f80e0c80c7ebb6509d5', ['msgtype' => 'text', 'text' => ['content' => implode(', ', $names) . ' 翻单'], 'at' => ['isAtAll' => true]]);
        }
    }

    public function getPushWarehouseList() {
        $list = [
            ['warehouse_no'=>'HM','name'=>'洪梅仓']
        ];
        return $list;
    }

    public function addPush($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_push SET user_id = '" . (int)$this->user->user_id . "', push_date = '" . $this->db->escape($data['push_date']) . "', warehouse_no = '" . $this->db->escape($data['warehouse_no']) . "', provider_no = '" . $this->db->escape($data['provider_no']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', spec_name = '" . $this->db->escape($data['spec_name']) . "', PSC = '" . $this->db->escape($data['PSC']) . "', carton_numbers = '" . $this->db->escape($data['carton_numbers']) . "', mantissa = '" . $this->db->escape($data['mantissa']) . "', quantity = '" . (int)$data['quantity'] . "', remark = '" . $this->db->escape($data['remark']) . "', status = '" . (int)$data['status'] . "', push_data = '" . $this->db->escape($data['push_data']) . "', date_added = '" . $this->db->escape($data['date_added']) . "', date_modified = '" . $this->db->escape($data['date_modified']) . "'");

        $push_id = $this->db->getLastId();

        return $push_id;
    }

    public function editPush($push_id,$data) {
        $this->db->query("UPDATE " . DB_PREFIX . "purchase_push SET push_date = '" . $this->db->escape($data['push_date']) . "', warehouse_no = '" . $this->db->escape($data['warehouse_no']) . "', provider_no = '" . $this->db->escape($data['provider_no']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', spec_name = '" . $this->db->escape($data['spec_name']) . "', PSC = '" . $this->db->escape($data['PSC']) . "', carton_numbers = '" . $this->db->escape($data['carton_numbers']) . "', mantissa = '" . $this->db->escape($data['mantissa']) . "', quantity = '" . (int)$data['quantity'] . "', remark = '" . $this->db->escape($data['remark']) . "', date_modified = NOW() WHERE push_id = '" . (int)$push_id . "' AND status <> '1'");
    }

    public function getPushs($data) {
        $sql = "SELECT * FROM " . DB_PREFIX . "purchase_push pp LEFT JOIN (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl ON (pp.bsku = sl.spec_no) WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(pp.bsku, sl.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_state']) && $data['filter_state'] !== '-1') {
            $sql .= " AND pp.status = '" . (int)$data['filter_state'] . "'";
        }

        if (isset($data['filter_warehouse']) && $data['filter_warehouse'] !== '') {
            $sql .= " AND pp.warehouse_no = '" . $data['filter_warehouse'] . "'";
        }

        if (isset($data['filter_provider']) && $data['filter_provider'] !== '') {
            $sql .= " AND pp.provider_no = '" . $data['filter_provider'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND pp.push_date >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND pp.push_date <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " ORDER BY push_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalPushs($data) {
        $sql = "SELECT COUNT(pp.push_id) AS total FROM " . DB_PREFIX . "purchase_push pp LEFT JOIN (SELECT spec_no, spec_name FROM wdt_spec_list WHERE deleted = '0') sl ON (pp.bsku = sl.spec_no) WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(pp.bsku, sl.spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_state']) && $data['filter_state'] !== '-1') {
            $sql .= " AND pp.status = '" . (int)$data['filter_state'] . "'";
        }

        if (isset($data['filter_warehouse']) && $data['filter_warehouse'] !== '') {
            $sql .= " AND pp.warehouse_no = '" . $data['filter_warehouse'] . "'";
        }

        if (isset($data['filter_provider']) && $data['filter_provider'] !== '') {
            $sql .= " AND pp.provider_no = '" . $data['filter_provider'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND pp.push_date >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND pp.push_date <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getCron() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "cron WHERE incident = 'purchase_push'");
        return $query->row;
    }

    public function setCron() {
        $this->db->query("UPDATE " . DB_PREFIX . "cron SET status = '1' WHERE incident = 'purchase_push' AND status = '0'");
    }

    public function getPushInsufficients() {
        $data = [];
        $purchase_push_groups =  $this->db->query("SELECT warehouse_no,provider_no,bsku,sum(quantity) as quantity FROM " . DB_PREFIX . "purchase_push WHERE status = '2' OR status = '3' GROUP BY warehouse_no,provider_no,bsku");
        foreach ((array)$purchase_push_groups->rows as $purchase_push_group) {
            $purchase_order = $this->db->query("SELECT pod.spec_no,SUM(pod.num),SUM(pod.stockin_num),SUM(pod.arrive_num),SUM(COALESCE(ppd.lack_num, pod.lack_num)) AS wrk FROM wdt_purchase_order_details pod JOIN wdt_purchase_order po ON pod.purchase_id = po.purchase_id LEFT JOIN cr_purchase_push_detail ppd ON pod.rec_id = ppd.rec_id WHERE pod.spec_no = '" .$purchase_push_group['bsku']. "' AND po.warehouse_no = '" .$purchase_push_group['warehouse_no']. "' AND po.provider_no = '" .$purchase_push_group['provider_no']. "' AND (po.status = 40 OR po.status = 50) GROUP BY pod.spec_no");
            $wrk = 0;
            if ($purchase_order->row) {
                $wrk = $purchase_order->row['wrk'];
            }
            if ($wrk < $purchase_push_group['quantity']) {
                $data[] = [
                    'bsku' => $purchase_push_group['bsku'],
                    'warehouse_no' => $purchase_push_group['warehouse_no'],
                    'provider_no' => $purchase_push_group['provider_no'],
                    'quantity' => $purchase_push_group['quantity'],
                    'wrk' => $wrk,
                    'num' => $purchase_push_group['quantity'] - $wrk,
                ];
            }
        }

        return $data;
    }

    public function deletePush($push_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "purchase_push WHERE push_id = '" . (int)$push_id . "'");
    }

    public function getPush($push_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "purchase_push WHERE push_id = '" . (int)$push_id . "' AND status = '0'");
        return $query->row;
    }

    public function getAntifakes($data) {
        $sql = "SELECT *,CONCAT(prefix, LPAD(start_num, digit - LENGTH(prefix), '0'), '-', prefix, LPAD(end_num, digit - LENGTH(prefix), '0')) AS antifake_range FROM " . DB_PREFIX . "purchase_antifake WHERE 1";

        if (!empty($data['filter_name'])) {
            $prefix = preg_replace("/[^a-zA-Z]/", "", $data['filter_name']);
            $digit = strlen($data['filter_name']);
            $num = preg_replace('/\D/', '', $data['filter_name']);
            $sql .= " AND prefix = '" . $this->db->escape($prefix) . "'";
            $sql .= " AND digit = '" . (int)$digit . "'";
            $sql .= " AND start_num <= '" . (int)$num . "' AND end_num >= '" . (int)$num . "'";
        }

        if (isset($data['filter_provider']) && $data['filter_provider'] !== '') {
            $sql .= " AND provider_no = '" . $data['filter_provider'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        if (!empty($data['filter_prefix'])) {
            $sql .= " AND prefix = '" . $this->db->escape($data['filter_prefix']) . "'";
        }

        if (!empty($data['filter_digit'])) {
            $sql .= " AND digit = '" . (int)$data['filter_digit'] . "'";
        }

        if (!empty($data['filter_antifake_id'])) {
            $sql .= " AND antifake_id <> '" . (int)$data['filter_antifake_id'] . "'";
        }

        if (!empty($data['filter_start_num']) && !empty($data['filter_end_num'])) {
            $sql .= " AND ((start_num <= '" . (int)$data['filter_start_num'] . "' AND end_num >= '" . (int)$data['filter_start_num'] . "') OR (start_num <= '" . (int)$data['filter_end_num'] . "' AND end_num >= '" . (int)$data['filter_end_num'] . "')  OR (start_num >= '" . (int)$data['filter_start_num'] . "' AND end_num <= '" . (int)$data['filter_end_num'] . "'))";
        }


        $sql .= " ORDER BY antifake_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalAntifakes($data) {
        $sql = "SELECT COUNT(antifake_id) AS total FROM " . DB_PREFIX . "purchase_antifake WHERE 1";

        if (!empty($data['filter_name'])) {
            $prefix = preg_replace("/[^a-zA-Z]/", "", $data['filter_name']);
            $digit = strlen($data['filter_name']);
            $num = preg_replace('/\D/', '', $data['filter_name']);
            $sql .= " AND prefix = '" . $this->db->escape($prefix) . "'";
            $sql .= " AND digit = '" . (int)$digit . "'";
            $sql .= " AND start_num <= '" . (int)$num . "' AND end_num >= '" . (int)$num . "'";
        }

        if (isset($data['filter_provider']) && $data['filter_provider'] !== '') {
            $sql .= " AND provider_no = '" . $data['filter_provider'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        if (!empty($data['filter_prefix'])) {
            $sql .= " AND prefix = '" . $this->db->escape($data['filter_prefix']) . "'";
        }

        if (!empty($data['filter_digit'])) {
            $sql .= " AND digit = '" . (int)$data['filter_digit'] . "'";
        }

        if (!empty($data['filter_antifake_id'])) {
            $sql .= " AND antifake_id <> '" . (int)$data['filter_antifake_id'] . "'";
        }

        if (!empty($data['filter_start_num']) && !empty($data['filter_end_num'])) {
            $sql .= " AND ((start_num <= '" . (int)$data['filter_start_num'] . "' AND end_num >= '" . (int)$data['filter_start_num'] . "') OR (start_num <= '" . (int)$data['filter_end_num'] . "' AND end_num >= '" . (int)$data['filter_end_num'] . "')  OR (start_num >= '" . (int)$data['filter_start_num'] . "' AND end_num <= '" . (int)$data['filter_end_num'] . "'))";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getAntifake($antifake_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "purchase_antifake WHERE antifake_id = '" . (int)$antifake_id . "'");
        return $query->row;
    }

    public function addAntifake($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "purchase_antifake SET provider_no = '" . $this->db->escape($data['provider_no']) . "', prefix = '" . $this->db->escape($data['prefix']) . "', digit = '" . (int)$data['digit'] . "', start_num = '" . (int)$data['start_num'] . "', end_num = '" . (int)$data['end_num'] . "', date_added = NOW()");
    }

    public function editAntifake($antifake_id,$data) {
        $this->db->query("UPDATE " . DB_PREFIX . "purchase_antifake SET provider_no = '" . $this->db->escape($data['provider_no']) . "', prefix = '" . $this->db->escape($data['prefix']) . "', digit = '" . (int)$data['digit'] . "', start_num = '" . (int)$data['start_num'] . "', end_num = '" . (int)$data['end_num'] . "' WHERE antifake_id = '" . (int)$antifake_id . "'");
    }

    public function deleteAntifake($antifake_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "purchase_antifake WHERE antifake_id = '" . (int)$antifake_id . "'");
    }

    public function getAntifakeAllocation($data) {
        $sql = "SELECT provider_no,SUM(end_num - start_num + 1) AS allocation FROM " . DB_PREFIX . "purchase_antifake WHERE 1";

        if (!empty($data['filter_name'])) {
            $prefix = preg_replace("/[^a-zA-Z]/", "", $data['filter_name']);
            $digit = strlen($data['filter_name']);
            $num = preg_replace('/\D/', '', $data['filter_name']);
            $sql .= " AND prefix = '" . $this->db->escape($prefix) . "'";
            $sql .= " AND digit = '" . (int)$digit . "'";
            $sql .= " AND start_num <= '" . (int)$num . "' AND end_num >= '" . (int)$num . "'";
        }

        if (isset($data['filter_provider']) && $data['filter_provider'] !== '') {
            $sql .= " AND provider_no = '" . $data['filter_provider'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " GROUP BY provider_no";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalAntifakeAllocation($data) {
        $sql = "SELECT provider_no FROM " . DB_PREFIX . "purchase_antifake WHERE 1";

        if (!empty($data['filter_name'])) {
            $prefix = preg_replace("/[^a-zA-Z]/", "", $data['filter_name']);
            $digit = strlen($data['filter_name']);
            $num = preg_replace('/\D/', '', $data['filter_name']);
            $sql .= " AND prefix = '" . $this->db->escape($prefix) . "'";
            $sql .= " AND digit = '" . (int)$digit . "'";
            $sql .= " AND start_num <= '" . (int)$num . "' AND end_num >= '" . (int)$num . "'";
        }

        if (isset($data['filter_provider']) && $data['filter_provider'] !== '') {
            $sql .= " AND provider_no = '" . $data['filter_provider'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " GROUP BY provider_no";

        $query = $this->db->query($sql);

        return $query->num_rows;
    }
}