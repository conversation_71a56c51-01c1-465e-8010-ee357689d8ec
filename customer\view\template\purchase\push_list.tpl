<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        采购入库
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="产品名称编码" value="<?php echo $filter_name; ?>">
                </div>
              </div>

              <div class="col-md-4">
                <div class="form-group">
                  <label>仓库验收时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>

              <div class="col-md-4">
                <div class="form-group">
                  <label>仓库：</label>
                  <select class="form-control" name="filter_warehouse">
                    <option value="*">全部仓库</option>
                    <?php foreach($warehouses as $warehouse_key => $warehouse) { ?>
                    <?php if ($warehouse_key == $filter_warehouse) { ?>
                    <option value="<?php echo $warehouse_key; ?>" selected="selected"><?php echo $warehouse; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $warehouse_key; ?>"><?php echo $warehouse; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>

            <div class="col-md-4">
              <div class="form-group">
                <label>供应商：</label>
                <select class="form-control" name="filter_provider">
                  <option value="*">全部供应商</option>
                  <?php foreach($providers as $provider_key => $provider) { ?>
                  <?php if ($provider_key == $filter_provider) { ?>
                  <option value="<?php echo $provider_key; ?>" selected="selected"><?php echo $provider; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $provider_key; ?>"><?php echo $provider; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="col-md-4">
              <div class="form-group">
                <label>状态：</label>
                <select class="form-control" name="filter_state">
                  <option <?php if($filter_state==-1){ ?>selected="selected"<?php } ?> value="*">全部状态</option>
                  <option <?php if($filter_state==0){ ?>selected="selected"<?php } ?> value="0">未处理</option>
                  <option <?php if($filter_state==1){ ?>selected="selected"<?php } ?> value="1">已处理</option>
                  <option <?php if($filter_state==2){ ?>selected="selected"<?php } ?> value="2">采购数量不足</option>
                  <option <?php if($filter_state==3){ ?>selected="selected"<?php } ?> value="3">无采购数量</option>
                  <!--<option <?php if($filter_state==4){ ?>selected="selected"<?php } ?> value="4">已核对</option>-->
                </select>
              </div>
            </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <?php if (!empty($execute_detail['status'] == 0)) { ?>
          <a class="btn btn-success" href="<?php echo $import; ?>"><i class="glyphicon glyphicon-upload"></i> 导入数据</a>
          <a class="btn btn-info but-change" href="javascript:execute();"><i class="glyphicon glyphicon-repeat"></i> 执行</a>
          <?php } else { ?>
          <a class="btn btn-success" href="javascript:;"><i class="glyphicon glyphicon-upload"></i> 导入数据</a>
          <a class="btn btn-danger" href="javascript:;"><i class="glyphicon glyphicon-repeat"></i> 执行中</a>
          <?php } ?>
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>

      <?php if (!empty($push_insufficients)) { ?>
      <div class="box box-danger">
        <div class="box-header with-border">
          <h3 class="box-title">采购数量不足产品</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
            <table class="table text-middle table-bordered table-hover table-striped">
              <tbody><tr>
                <th>编码</th>
                <th>仓库</th>
                <th>供应商</th>
                <th>本次数量</th>
                <th>采购未入库数量</th>
                <th>应补数量</th>
              </tr>
              <?php foreach ($push_insufficients as $push_insufficient) { ?>
              <tr>
                <td><?php echo $push_insufficient['bsku']; ?></td>
                <td> <?php if (!empty($warehouses[$push_insufficient['warehouse_no']])) { ?><?php echo $warehouses[$push_insufficient['warehouse_no']]; ?><?php } ?></td>
                <td> <?php if (!empty($providers[$push_insufficient['provider_no']])) { ?><?php echo $providers[$push_insufficient['provider_no']]; ?><?php } ?></td>
                <td><?php echo $push_insufficient['quantity']; ?></td>
                <td><?php echo $push_insufficient['wrk']; ?></td>
                <td><?php echo $push_insufficient['num']; ?></td>
              </tr>
              <?php } ?>
              </tbody></table>
          </form>
        </div>
      </div>
      <?php } ?>

      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">采购入库</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>图片</th>
              <th>产品名称/编码</th>
              <th>仓库验收日期</th>
              <th>仓库</th>
              <th>供应商</th>
              <th>入库数量</th>
              <th>状态</th>
              <th>装箱数</th>
              <th>箱数</th>
              <th>尾数</th>
              <th>详情(采购单号--入库单号--入库数量)</th>
              <th>备注</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($pushs)) { ?>
            <?php foreach ($pushs as $push) { ?>
            <tr data-id="<?php echo $push['push_id']; ?>">
              <td><img width="100" src="<?php echo $push['img_url']; ?>" class="img-thumbnail"></td>
              <td><?php echo $push['spec_name']; ?><br><?php echo $push['bsku']; ?></td>
              <td><?php echo $push['push_date']; ?></td>
              <td> <?php if (!empty($warehouses[$push['warehouse_no']])) { ?><?php echo $warehouses[$push['warehouse_no']]; ?><?php } ?></td>
              <td> <?php if (!empty($providers[$push['provider_no']])) { ?><?php echo $providers[$push['provider_no']]; ?><?php } ?></td>
              <td><?php echo $push['quantity']; ?></td>
              <td><?php if($push['status']==0){ ?>未处理<?php }else if($push['status']==1){ ?>已处理<?php }else if($push['status']==2){ ?>采购数量不足<?php }else if($push['status']==3){ ?>无采购数量<?php }else if($push['status']==4){ ?>已核对<?php } ?></td>
              <td><?php echo $push['PSC']; ?></td>
              <td><?php echo $push['carton_numbers']; ?></td>
              <td><?php echo $push['mantissa']; ?></td>
              <td>
                <?php if (!empty($push['push_data'])) { ?>
                <?php foreach ($push['push_data'] as $push_data) { ?>
                <?php echo $push_data['purchase_no']; ?>--<?php echo $push_data['outer_no']; ?>--<?php echo $push_data['stockin_num']; ?>
                <br>
                <?php } ?>
                <?php } ?>
              </td>
              <td><?php echo $push['remark']; ?></td>
              <td class="text-right">
                <?php if($push['status']!=1){ ?>
                <a class="btn btn-success" href="<?php echo $editPush; ?>&push_id=<?php echo $push['push_id']; ?>" title="">修改</a>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#user-del-modal">删除</button>
                <?php } ?>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="13" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 删除 -->
      <div class="modal modal-danger fade" id="user-del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delPush; ?>" method="post" enctype="multipart/form-data" id="form-user">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除吗？</p>
                <input id="user-del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="user-del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_warehouse = $('select[name=\'filter_warehouse\']').val();

      if (filter_warehouse != '*') {
        url += '&filter_warehouse=' + encodeURIComponent(filter_warehouse);
      }

      var filter_provider = $('select[name=\'filter_provider\']').val();

      if (filter_provider != '*') {
        url += '&filter_provider=' + encodeURIComponent(filter_provider);
      }

      var filter_state = $('select[name=\'filter_state\']').val();

      if (filter_state != '*') {
        url += '&filter_state=' + encodeURIComponent(filter_state);
      }

      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });

    $('#user-del-modal').on('show.bs.modal', function(event) {
      $('#user-del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#user-del-yes').on('click', () => {$('#form-user').submit()})
  })()
</script>

<script>
  function execute() {
    $.ajax({
      url: '<?php echo $execute; ?>',
      type: 'post',
      dataType: 'json',
      success: function(json) {
        if (json['status'] == 1) {
          $(".but-change").attr('href','javascript:;')
          $(".but-change").text('执行中')
          $(".but-change").removeClass('btn-success')
          $(".but-change").addClass('btn-danger')
        }
      },
      error: function(xhr, ajaxOptions, thrownError) {
        confirm("失败，请刷新页面重试")
      }
    });
  }
</script>
<?php echo $footer; ?>