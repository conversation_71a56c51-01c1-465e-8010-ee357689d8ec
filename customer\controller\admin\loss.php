<?php
class Controller<PERSON>d<PERSON><PERSON>oss extends Controller {
    private $error = array();

    public function add() {
        $this->load->model('admin/loss');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_loss->addLoss($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_reason'])) {
                $url .= '&filter_reason=' . urlencode(html_entity_decode($this->request->get['filter_reason'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_solution'])) {
                $url .= '&filter_solution=' . urlencode(html_entity_decode($this->request->get['filter_solution'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_user'])) {
                $url .= '&filter_user=' . $this->request->get['filter_user'];
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_reshipno'])) {
                $url .= '&filter_reshipno=' . $this->request->get['filter_reshipno'];
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function edit() {
        $this->load->model('admin/loss');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_loss->editLoss($this->request->get['loss_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_reason'])) {
                $url .= '&filter_reason=' . urlencode(html_entity_decode($this->request->get['filter_reason'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_solution'])) {
                $url .= '&filter_solution=' . urlencode(html_entity_decode($this->request->get['filter_solution'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_user'])) {
                $url .= '&filter_user=' . $this->request->get['filter_user'];
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_reshipno'])) {
                $url .= '&filter_reshipno=' . $this->request->get['filter_reshipno'];
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function delete() {
        $this->load->model('admin/loss');

        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $loss_id) {
                $this->model_admin_loss->deleteLoss($loss_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_reason'])) {
                $url .= '&filter_reason=' . urlencode(html_entity_decode($this->request->get['filter_reason'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_solution'])) {
                $url .= '&filter_solution=' . urlencode(html_entity_decode($this->request->get['filter_solution'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_user'])) {
                $url .= '&filter_user=' . $this->request->get['filter_user'];
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_reshipno'])) {
                $url .= '&filter_reshipno=' . $this->request->get['filter_reshipno'];
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getList();
    }

    public function export() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_reason'])) {
            $filter_reason = $this->request->get['filter_reason'];
        } else {
            $filter_reason = '';
        }

        if (isset($this->request->get['filter_solution'])) {
            $filter_solution = $this->request->get['filter_solution'];
        } else {
            $filter_solution = '';
        }

        if (isset($this->request->get['filter_user'])) {
            $filter_user = $this->request->get['filter_user'];
        } else {
            $filter_user = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_reshipno'])) {
            $filter_reshipno = $this->request->get['filter_reshipno'];
        } else {
            $filter_reshipno = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'date_added';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_reason'		=> $filter_reason,
            'filter_solution'	=> $filter_solution,
            'filter_user'		=> $filter_user,
            'filter_store'		=> $filter_store,
            'filter_reshipno'	=> $filter_reshipno,
            'filter_date_start'	=> $filter_date_start,
            'filter_date_end'	=> $filter_date_end,
            'sort'			=> $sort,
            'order'			=> $order
        );

        $this->load->model('admin/setting');
        $results = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($results as $result) {
            $stores[$result['store_id']] = $result['name'];
        }

        $export_head = array('记录时间', '破损产品', '编码', '破损数量', '产品总成本', '供应商', '破损图片', '责任原因', '处理方案', '所属店铺', '客户ID', '所在地', '订单编号', '订单金额', '订单图片', '责任人', '退款金额', '补偿金额', '快递公司', '快递单号', '快递运费', '补发快递', '补发单号', '补发运费','备注');
        $export_data = array();
        $export_data[] = $export_head;

        $this->load->model('admin/loss');
        $results = $this->model_admin_loss->getLosses($filter_data);

        foreach ($results as $result) {
            $export_data[] = array(
                $result['date_added'],
                $result['spec_name'],
                $result['bsku'],
                $result['quantity'],
                $result['allcost'],
                $result['supplier'],
                $this->model_admin_loss->getCellLink($result['images']),
                $result['responsible'] . '-' . $result['reason'],
                $result['solution'],
                $stores[$result['store_id']] ?? '',
                $result['buyer_id'],
                $result['buyer_location'],
                $result['order_no'],
                $result['order_fee'],
                $this->model_admin_loss->getCellLink($result['order_img']),
                $result['handler'],
                $result['refund_fee'],
                $result['loss_fee'],
                $result['ship_name'],
                $result['ship_no'],
                $result['ship_fee'],
                $result['reship_name'],
                $result['reship_no'],
                $result['reship_fee'],
                $result['remark']
            );
        }

        if (!empty($export_data)) {
            $this->load->model('admin/excel');
            $this->model_admin_excel->export('破损表' . date('Y-m-d'), $export_data, array(3 => 'numbric', 4 => 'numbric', 6 => 'link', 13 => 'numbric', 14 => 'link', 16 => 'numbric', 17 => 'numbric'), '.xlsx');
        }

        $this->getList();
    }

    public function getList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_reason'])) {
            $filter_reason = $this->request->get['filter_reason'];
        } else {
            $filter_reason = '';
        }

        if (isset($this->request->get['filter_solution'])) {
            $filter_solution = $this->request->get['filter_solution'];
        } else {
            $filter_solution = '';
        }

        if (isset($this->request->get['filter_user'])) {
            $filter_user = $this->request->get['filter_user'];
        } else {
            $filter_user = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_reshipno'])) {
            $filter_reshipno = $this->request->get['filter_reshipno'];
        } else {
            $filter_reshipno = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'date_added';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_reason'])) {
            $url .= '&filter_reason=' . urlencode(html_entity_decode($this->request->get['filter_reason'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_solution'])) {
            $url .= '&filter_solution=' . urlencode(html_entity_decode($this->request->get['filter_solution'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_user'])) {
            $url .= '&filter_user=' . $this->request->get['filter_user'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_reshipno'])) {
            $url .= '&filter_reshipno=' . $this->request->get['filter_reshipno'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/loss/add', 'token=' . $this->session->data['token'] . $url);
        $data['export'] = $this->url->link('admin/loss/export', 'token=' . $this->session->data['token'] . $url);
        $data['import'] = $this->url->link('admin/loss/getListImport', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/loss/delete', 'token=' . $this->session->data['token'] . $url);

        $data['losses'] = array();

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_reason'		=> $filter_reason,
            'filter_solution'	=> $filter_solution,
            'filter_user'		=> $filter_user,
            'filter_store'		=> $filter_store,
            'filter_reshipno'	=> $filter_reshipno,
            'filter_date_start'	=> $filter_date_start,
            'filter_date_end'	=> $filter_date_end,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $this->load->model('admin/loss');
        $data['reasons'] = $this->model_admin_loss->getReasons();
        $data['solutions'] = $this->model_admin_loss->getSolutions();
        $data['users'] = $this->model_admin_loss->getUsers();

        $results = $this->model_admin_loss->getLosses($filter_data);

        foreach ($results as $result) {
            $data['losses'][] = array(
                'loss_id'	=> $result['loss_id'],
                'responsible' => $result['responsible'],
                'reason'	=> $result['reason'],
                'bsku'		=> $result['bsku'],
                'pname'		=> $result['spec_name'],
                'quantity'	=> $result['quantity'],
                'username'	=> $data['users'][$result['user_id']] ?? '',
                'supplier'	=> $result['supplier'],
                'solution'	=> $result['solution'],
                'allcost'	=> $result['allcost'],
                'images'	=> !empty($result['images']) ? explode(',', $result['images']) : [],
                'location'	=> $result['buyer_location'],
                'date_added'=> $result['date_added'],
                'remark'    => $result['remark'],
                'edit'  => $this->url->link('admin/loss/edit', 'token=' . $this->session->data['token'] . '&loss_id=' . $result['loss_id'] . $url),
                'view'	=> $this->url->link('admin/loss/getDetail', 'token=' . $this->session->data['token'] . '&loss_id=' . $result['loss_id'] . $url)
            );
        }

        $total = $this->model_admin_loss->getTotalLosses($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_reason'])) {
            $url .= '&filter_reason=' . urlencode(html_entity_decode($this->request->get['filter_reason'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_solution'])) {
            $url .= '&filter_solution=' . urlencode(html_entity_decode($this->request->get['filter_solution'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_user'])) {
            $url .= '&filter_user=' . $this->request->get['filter_user'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_reshipno'])) {
            $url .= '&filter_reshipno=' . $this->request->get['filter_reshipno'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_respon'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . '&sort=responsible' . $url);
        $data['sort_bsku'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . '&sort=bsku' . $url);
        $data['sort_supplier'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . '&sort=supplier' . $url);
        $data['sort_user'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . '&sort=user_id' . $url);
        $data['sort_location'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . '&sort=buyer_location' . $url);
        $data['sort_solution'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . '&sort=solution' . $url);
        $data['sort_cost'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . '&sort=allcost' . $url);
        $data['sort_date'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . '&sort=date_added' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_reason'])) {
            $url .= '&filter_reason=' . urlencode(html_entity_decode($this->request->get['filter_reason'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_solution'])) {
            $url .= '&filter_solution=' . urlencode(html_entity_decode($this->request->get['filter_solution'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_user'])) {
            $url .= '&filter_user=' . $this->request->get['filter_user'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_reshipno'])) {
            $url .= '&filter_reshipno=' . $this->request->get['filter_reshipno'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_reason'] = $filter_reason;
        $data['filter_solution'] = $filter_solution;
        $data['filter_user'] = $filter_user;
        $data['filter_store'] = $filter_store;
        $data['filter_reshipno'] = $filter_reshipno;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('loss/list.tpl', $data));
    }

    public function getDetail() {
        if (isset($this->request->get['loss_id'])) {
            $loss_id = $this->request->get['loss_id'];
        } else {
            $loss_id = 0;
        }

        $this->load->model('admin/loss');
        $data['loss_info'] = $this->model_admin_loss->getLoss($loss_id);

        if (empty($data['loss_info'])) {
            $this->response->redirect($this->url->link('admin/loss/getList', 'token=' . $this->session->data['token']));
        }

        $this->load->model('admin/setting');
        $data['store'] = $this->model_admin_setting->getStore($data['loss_info']['store_id']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('loss/detail.tpl', $data));
    }

    public function getRankList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_reason'])) {
            $filter_reason = $this->request->get['filter_reason'];
        } else {
            $filter_reason = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = date('Y-m-01');
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = date('Y-m-d');
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'ratio';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (!empty($filter_store)) {
            $url .= '&filter_store=' . $filter_store;
        }

        if (!empty($filter_date_start)) {
            $url .= '&filter_date_start=' . $filter_date_start;
        }

        if (!empty($filter_date_end)) {
            $url .= '&filter_date_end=' . $filter_date_end;
        }

        $data['ranks'] = array();

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_reason'		=> $filter_reason,
            'filter_store'		=> $filter_store,
            'filter_date_start'	=> $filter_date_start,
            'filter_date_end'	=> $filter_date_end,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $this->load->model('admin/loss');
        $data['reasons'] = $this->model_admin_loss->getReasons();

        $results = $this->model_admin_loss->getRanks($filter_data);

        foreach ($results as $result) {
            $data['ranks'][] = array(
                'bsku'		=> $result['bsku'],
                'pname'		=> $result['spec_name'],
                'allquan'	=> $result['allquan'],
                'allcost'	=> $result['allcost'],
                'allship'	=> $result['allship'],
                'ratio'		=> moneyformat($result['ratio'] * 100),
                'view'		=> $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . '&filter_name=' . $result['bsku'] . $url)
            );
        }

        $total = $this->model_admin_loss->getTotalRanks($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_reason'])) {
            $url .= '&filter_reason=' . urlencode(html_entity_decode($this->request->get['filter_reason'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_bsku'] = $this->url->link('admin/loss/getRankList', 'token=' . $this->session->data['token'] . '&sort=bsku' . $url);
        $data['sort_pname'] = $this->url->link('admin/loss/getRankList', 'token=' . $this->session->data['token'] . '&sort=spec_name' . $url);
        $data['sort_allquan'] = $this->url->link('admin/loss/getRankList', 'token=' . $this->session->data['token'] . '&sort=allquan' . $url);
        $data['sort_allcost'] = $this->url->link('admin/loss/getRankList', 'token=' . $this->session->data['token'] . '&sort=allcost' . $url);
        $data['sort_allship'] = $this->url->link('admin/loss/getRankList', 'token=' . $this->session->data['token'] . '&sort=allship' . $url);
        $data['sort_ratio'] = $this->url->link('admin/loss/getRankList', 'token=' . $this->session->data['token'] . '&sort=ratio' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_reason'])) {
            $url .= '&filter_reason=' . urlencode(html_entity_decode($this->request->get['filter_reason'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/loss/getRankList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_reason'] = $filter_reason;
        $data['filter_store'] = $filter_store;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/loss/getRankList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('loss/rank.tpl', $data));
    }

    protected function getForm() {
        $data['text_form'] = !isset($this->request->get['loss_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_reason'])) {
            $url .= '&filter_reason=' . urlencode(html_entity_decode($this->request->get['filter_reason'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_solution'])) {
            $url .= '&filter_solution=' . urlencode(html_entity_decode($this->request->get['filter_solution'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_user'])) {
            $url .= '&filter_user=' . $this->request->get['filter_user'];
        }

        if (isset($this->request->get['filter_user'])) {
            $url .= '&filter_user=' . $this->request->get['filter_user'];
        }

        if (isset($this->request->get['filter_reshipno'])) {
            $url .= '&filter_reshipno=' . $this->request->get['filter_reshipno'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['loss_id'])) {
            $data['action'] = $this->url->link('admin/loss/add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/loss/edit', 'token=' . $this->session->data['token'] . '&loss_id=' . $this->request->get['loss_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/loss/getList', 'token=' . $this->session->data['token'] . $url);
        $data['autoProvider'] = $this->url->link('admin/loss/autoProvider', '&token=' . $this->session->data['token']);
        $data['getToken'] = $this->url->link('admin/common/getUploadToken', 'token=' . $this->session->data['token']);

        if (isset($this->request->get['loss_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $loss_info = $this->model_admin_loss->getLoss($this->request->get['loss_id']);
        }

        $data['reasons'] = $this->model_admin_loss->getReasons();
        $data['locations'] = $this->model_admin_loss->getLocations();
        $data['solutions'] = $this->model_admin_loss->getSolutions();
        $data['shipnames'] = $this->model_admin_loss->getShipnames();

        $fields = array('store_id', 'responsible', 'reason', 'order_no', 'buyer_id', 'buyer_location', 'bsku', 'spec_name', 'quantity', 'cost_fee', 'supplier', 'handler', 'order_fee', 'solution', 'ship_name', 'ship_no', 'ship_fee', 'reship_name', 'reship_no', 'reship_fee', 'refund_fee', 'loss_fee','remark');

        foreach ($fields as $field) {
            if (isset($this->request->post[$field])) {
                $data[$field] = $this->request->post[$field];
            } elseif (!empty($loss_info)) {
                $data[$field] = $loss_info[$field];
            } else {
                $data[$field] = '';
            }
        }

        $data['belong'] = $data['responsible'] . '-' . $data['reason'];

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        if (isset($this->request->post['order_img'])) {
            $data['order_img'] = $this->request->post['order_img'];
        } elseif (!empty($loss_info)) {
            $data['order_img'] = explode(',', $loss_info['order_img']);
        } else {
            $data['order_img'] = array();
        }

        if (isset($this->request->post['images'])) {
            $data['images'] = $this->request->post['images'];
        } elseif (!empty($loss_info['images'])) {
            $data['images'] = explode(',', $loss_info['images']);
        } else {
            $data['images'] = array();
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('loss/form.tpl', $data));
    }

    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'admin/loss')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (!empty($this->request->post['belong'])) {
            $belong = explode('-', $this->request->post['belong']);
            $this->request->post['responsible'] = $belong[0] ?? '';
            $this->request->post['reason'] = $belong[1] ?? '';
        } else {
            $this->error['warning'] = '责任原因不能为空！';
            return false;
        }

        if (empty($this->request->post['bsku']) || empty($this->request->post['quantity'])) {
            $this->error['warning'] = '破损产品或数量不能为空！';
            return false;
        }

        if (empty($this->request->post['supplier'])) {
            $this->error['warning'] = '供应商不能为空！';
            return false;
        }

//        if (empty($this->request->post['images'])) {
//            $this->error['warning'] = '破损图片不能为空！';
//            return false;
//        }

        if (empty($this->request->post['store_id'])) {
            $this->error['warning'] = '所属店铺不能为空！';
            return false;
        }

//        if (empty($this->request->post['buyer_location'])) {
//            $this->error['warning'] = '所在地不能为空！';
//            return false;
//        }

        if (empty($this->request->post['solution'])) {
            $this->error['warning'] = '处理方案不能为空！';
            return false;
        }

        return !$this->error;
    }

    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'admin/loss')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }

    public function autoProvider() {
        $json = array();

        if (isset($this->request->get['filter_name'])) {
            if (isset($this->request->get['filter_name'])) {
                $filter_name = $this->request->get['filter_name'];
            } else {
                $filter_name = '';
            }

            if (isset($this->request->get['limit'])) {
                $limit = $this->request->get['limit'];
            } else {
                $limit = 50;
            }

            $bsku_list = array();

            $filter_data = array(
                'filter_name'   => $filter_name,
                'start'         => 0,
                'limit'         => $limit
            );

            $this->load->model('admin/loss');
            $results = $this->model_admin_loss->getWdtProviders($filter_data);

            foreach ($results as $result) {
                $bsku_list[$result['spec_no']]['value'] = $result['spec_no'];
                $bsku_list[$result['spec_no']]['label'] = strip_tags(html_entity_decode($result['spec_name'], ENT_QUOTES, 'UTF-8'));
                $bsku_list[$result['spec_no']]['providers'][] = array(
                    'supplier'	=> $result['provider_name'],
                    'price'		=> $result['price']
                );
            }

            foreach ($bsku_list as $list) {
                $json[] = $list;
            }
        }

        $this->response->setOutJson($json);
    }


    public function getListImport() {
        $data['action'] = $this->url->link('admin/loss/import', 'token=' . $this->session->data['token']);
        $data['template'] = $this->url->link('admin/loss/template', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('loss/import.tpl', $data));
    }

    public function import() {
        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            if (isset($_FILES['file']) && $_FILES['file']['error'] == 0) {
                $this->load->helper('office/autoload');

                $excelPath = $_FILES['file']['tmp_name']; // 获取上传文件的临时路径

                try {
                    //识别文件类型
                    $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($excelPath);
                    //创建读取器
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
                    $reader->setReadDataOnly(true); // 只读数据，不读样式
                    //加载文件
                    $spreadsheet = $reader->load($excelPath);
                    //获取第一个工作表
                    $objWorksheet = $spreadsheet->getSheet(0);
                    //获取所有数据
                    $data = $objWorksheet->toArray();
                    //获取第二个工作表
                    $objWorksheet = $spreadsheet->getSheet(1);
                    //获取店铺数据
                    $store_list = $objWorksheet->toArray();
                } catch (\PhpOffice\PhpSpreadsheet\Reader\Exception $e) {
                    die('读取文件出错: '.$e->getMessage());
                } catch (Exception $e) {
                    die('发生错误: '.$e->getMessage());
                }

                $store = [];
                foreach ($store_list as $v) {
                    if (!empty($v[0]) && !empty($v[1])) {
                        $store[$v[1]] = $v[0];
                    }
                }

                array_shift($data);

                $this->load->model('admin/loss');
                $datetime = date('Y-m-d H:i:s',time());
                $add_num = 0;
                foreach ($data as $v) {
                    if (!empty($v[0]) && !empty($v[1]) && !empty($v[2]) && !empty($v[3]) && !empty($v[4]) && !empty($v[5])) {
                        $last_providers = $this->model_admin_loss->getLastWdtProviders($v[0]);
                        $store_id = array_search($v[3],$store);
                        if (!empty($last_providers) && $store_id) {
                            $add_data = [
                                'user_id' => (int)$this->user->user_id,
                                'store_id' => $store_id,
                                'responsible' => '工厂责任',
                                'reason' => '破损',
                                'order_no' => $v[5],
                                'buyer_id' => $v[4],
                                'buyer_location' => '',
                                'bsku' => $v[0],
                                'spec_name' => $last_providers['spec_name'],
                                'quantity' => $v[1],
                                'cost_fee' => $last_providers['last_price'],
                                'supplier' => $last_providers['provider_name'],
                                'handler' => '',
//                                'order_img' => '',
                                'order_fee' => '',
//                                'images' => '',
                                'solution' => $v[2],
                                'ship_name' => '',
                                'ship_no' => '',
                                'ship_fee' => '',
                                'reship_name' => '',
                                'reship_no' => '',
                                'reship_fee' => 0.00,
                                'refund_fee' => 0.00,
                                'loss_fee' => 0.00,
                                'status' => 0,
                                'date_added' => $datetime,
                                'date_modified' => $datetime,
                                'remark' => !empty($v[6]) ? $v[6] : '',
                            ];
                            $loss_id = $this->model_admin_loss->addLoss($add_data);
                            if (!empty($loss_id)) $add_num += 1;
                        }
                    }
                }
                $json['success'] = '成功导入'.$add_num.'条数据';
            }
        }
        $this->response->setOutJson($json);
    }

    /**
     * 计算图片所在的单元格坐标
     */
    private function getImageCellCoordinate($drawing, $worksheet) {
        // 获取图片的左上角坐标
        $topLeftColumn = $drawing->getCoordinates();
        $topLeftRow = $drawing->getOffsetY() / 20; // 近似计算行号

        // 转换为单元格坐标 (如 A1)
        $columnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($topLeftColumn);
        $rowIndex = ceil($topLeftRow);

        return \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($columnIndex) . $rowIndex;
    }

    public function template() {
        $file = DIR_DOWNLOAD . '导入质量反馈模板.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }
}
