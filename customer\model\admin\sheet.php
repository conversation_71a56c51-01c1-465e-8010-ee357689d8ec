<?php

class ModelAdminSheet extends Model
{

    function getCurrentTimestampInMilliseconds() {
        // 获取当前的 Unix 时间戳和微秒
        $microtime = microtime(true);
        // 将其乘以1000转化为毫秒
        $milliseconds = (int) ($microtime * 1000);
        return $milliseconds;
    }


    public function getSheetById($sheetId)
    {
        $query = $this->db->query("SELECT * FROM rg_sheet WHERE isDelete = 0 and  cate = '" . (int)$sheetId . "'");

        return $query->rows;
    }

    public function getOneSheet($data = array())
    {
        $sql = "select sheet_id,name from rg_sheet where cate = 0 and  isDelete = 0";


        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getFlowList()
    {
        $sql = "select flow_id,flow_name from rg_flow where is_delete = 0  order by flow_sort desc";

        $query = $this->db->query($sql);

        return $query->rows;
    }
    public function getSheets($data = array(), $type = '-1')
    {
        $sql = "SELECT sheet_id, name, cate, type,createTime FROM rg_sheet WHERE isDelete = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_status']) && $data['filter_status'] != '*') {
            $sql .= " AND  cate = '" . $this->db->escape(0) . "'";
        }


        if (!empty($data['filter_one_sheet']) && empty($data['filter_second_sheet'])) {
            $sql .= " AND  cate = '" . $this->db->escape($data['filter_one_sheet']) . "'";
        }

        if (!empty($data['filter_second_sheet'])) {
            $sql .= " AND  cate  = '" . $this->db->escape($data['filter_second_sheet']) . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND createTime >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND createTime <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }

        if ($type != '-1') {
            $sql .= " AND type  = $type ";
        }

        $sort_data = array(
            'sheet_id',
            'name',
            'createTime'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY createTime";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }



        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getSheetKpi($data = array())
    {
        $months = str_replace("-", "", $data['filter_months']);

        $field = "a.id,a.union_id,a.months,a.score,a.real_name,b.extra_info";
        $sql = "SELECT $field FROM rg_union_score as a left join _union_extra_info as b on a.union_id = b.union_id and extra_key = 'performance'  where a.months = $months" ;

        if (!empty($data['filter_name'])) {
            $sql .= " AND  a.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }
        

        if (isset($data['sort']) && $data['sort']) {
            $sort = $data['sort'] == 'extra_info' ?  'b.extra_info' : 'a.'.$data['sort'];
            $sql .= " ORDER BY " . $sort;
        } else {
            $sql .= " ORDER BY a.score";
        }



        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }



        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        // echo $sql;die;
        $query = $this->db->query($sql);
        return $query->rows;
    }
    public function getSheetKpiDetail($data = array())
    {
        $sql = "SELECT id,union_id,remark,deadline,real_name,create_time,is_compensation FROM rg_score_deduction_log where 1=1 ";

        if (!empty($data['filter_union_id'])) {
            $sql .= " AND  union_id = '" . $this->db->escape($data['filter_union_id']) . "'";
        }

        if (!empty($data['filter_months']) && $data['filter_months'] != '*') {
            $sql .= " AND  months = '" . $this->db->escape($data['filter_months']) . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND deadline >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND deadline <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }


      
        $sort_data = array(
            'deadline',
            'real_name',
            'remark'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY id";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }


    public function getSheetKpiDetailTotal($data = array())
    {
        $sql = "SELECT count(id) as count FROM rg_score_deduction_log where 1=1 ";


        if (!empty($data['filter_union_id'])) {
            $sql .= " AND  union_id = '" . $this->db->escape($data['filter_union_id']) . "'";
        }

        if (!empty($data['filter_months']) && $data['filter_months'] != '*') {
            $sql .= " AND  months = '" . $this->db->escape($data['filter_months']) . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND deadline >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND deadline <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }



      
    
        $query = $this->db->query($sql);

        return $query->row['count'];
    }

    public function getSheetKpiTotal($data = array())
    {
        $sql = "SELECT count(union_id) as count FROM rg_union_score where 1=1 ";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_months']) && $data['filter_months'] != '*') {
            $months = str_replace("-", "", $data['filter_months']);
            $sql .= " AND  months = '" . $this->db->escape($months) . "'";
        }

      
    
        $query = $this->db->query($sql);

        return $query->row['count'];
    }

    public function getColumns($data = array(), $sheet_id = 0)
    {
        $sql = "SELECT * FROM rg_column WHERE sheet_id = $sheet_id AND isDelete = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }


        if (!empty($data['filter_date_start'])) {
            $sql .= " AND createTime >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND createTime <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }


        $sort_data = array(
            'name',
            'sort',
            'type',
            'createTime'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY sort";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }


        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalSheets($data, $type = '-1')
    {
        $sql = "SELECT COUNT(*) AS total FROM  rg_sheet WHERE isDelete = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_one_sheet']) && empty($data['filter_second_sheet'])) {
            $sql .= " AND  cate = '" . $this->db->escape($data['filter_one_sheet']) . "'";
        }

        if (!empty($data['filter_second_sheet'])) {
            $sql .= " AND  cate  = '" . $this->db->escape($data['filter_second_sheet']) . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND createTime >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND createTime <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }

        if ($type != '-1') {
            $sql .= " AND type  = $type ";
        }


        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getTotalColumns($data, $sheet_id = 0)
    {
        $sql = "SELECT COUNT(*) AS total FROM  rg_column WHERE sheet_id = $sheet_id AND  isDelete = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }


        if (!empty($data['filter_date_start'])) {
            $sql .= " AND createTime >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND createTime <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }


        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function validateName(array $data, int $sheet_id = 0)
    {
        $name = $data['name'];
        $filter_one_sheet = $data['filter_one_sheet'] ?? 0;
        $filter_second_sheet = $data['filter_second_sheet'] ?? 0;

        if (empty($data['filter_one_sheet'])) {
            $sql = " select * from rg_sheet where  cate = 0 and isDelete = 0  and `name` =  '$name'"; //顶级
        }

        if ($sheet_id) {
            $info = $this->db->query(" select * from rg_sheet where  sheet_id = $sheet_id");
            $info = $info->row;
            if ($data['filter_one_sheet'] == $sheet_id) $sql = " select * from rg_sheet where  cate = 0 and isDelete = 0  and `name` =  '$name'"; //顶级
            if ($data['filter_second_sheet'] == $sheet_id) $sql = " select * from rg_sheet where  cate = $filter_one_sheet and isDelete = 0  and `name` =  '$name'"; //二级
            if ($data['filter_one_sheet'] != $sheet_id && $data['filter_second_sheet'] != $sheet_id) $sql = " select * from rg_sheet where  cate = $filter_second_sheet and isDelete = 0  and `name` =  '$name'";
        } else {
            if (empty($data['filter_one_sheet'])) {
                $sql = " select * from rg_sheet where  cate = 0 and isDelete = 0  and `name` =  '$name'"; //顶级
            } else {
                if (empty($data['filter_second_sheet'])) {
                    $sql = " select * from rg_sheet where  cate = $filter_one_sheet and isDelete = 0  and `name` =  '$name'"; //二级
                } else {
                    $sql = " select * from rg_sheet where  cate = $filter_second_sheet and isDelete = 0  and `name` =  '$name'"; //三级
                }
            }
        }

        $query = $this->db->query($sql);
        if ($query->row) {
            if (isset($info)) {
                if ($info['name'] == $name) return 200;
            }
            return 100;
        }
        return 200;
    }

    public function validateColumnName(array $data, int $sheet_id = 0, int $column_id = 0)
    {

        $name = $data['name'];
        $type = $data['type'];

        if ($column_id) {
            $info = $this->db->query(" select * from rg_column where  column_id = $column_id");
            $info = $info->row;
        }

        $sql = " select * from rg_column where  sheet_id = $sheet_id and isDelete = 0  and type = '$type' and `name` =  '$name'"; //顶级
        $query = $this->db->query($sql);
        if ($query->row) {
            if (isset($info)) {
                if ($info['name'] == $name) return 200;
            }
            return 100;
        }
        return 200;
    }


    public function addSheet(array $data)
    {
        $name = $data['name'];
        $filter_one_sheet = $data['filter_one_sheet'] ?? 0;
        $filter_second_sheet = $data['filter_second_sheet'] ?? 0;
        $flow_id = $data['flow_id'] ?? 0;


        if (empty($data['filter_one_sheet'])) {
            $sheet_id = 0;
            $type = 0;
        } else {
            if (empty($data['filter_second_sheet'])) {
                $sheet_id = $filter_one_sheet;
                $type = 1;
            } else {
                $sheet_id = $filter_second_sheet;
                $type = 2;
            }
        }

        switch ($type) {
            //最顶级，没有break，继续执行下面的
            case 0:
                $sheet_data = [
                    'name' => $name,
                    'cate' => 0,
                    'type' => 0,
                    'createTime' => time(),
                    'flow_id' => $flow_id,
                    'asName'=>$flow_id ? '流程':'待办事项'
                ];
                $this->db->query("INSERT INTO " . " rg_sheet " . " SET  name =  '" . $this->db->escape($sheet_data['name']) . "', cate = '" . $this->db->escape($sheet_data['cate']) . "', type = '" . $this->db->escape($sheet_data['type']) . "', flow_id = '" . $this->db->escape($sheet_data['flow_id']) . "', createTime = '" . $this->db->escape($sheet_data['createTime']) . "', asName = '" . $this->db->escape($sheet_data['asName']) . "'");

                $sheet_id = $this->db->getLastId();

                $name = '数据表1';
            //数据表，没有break，继续执行下面的
            case 1:
                $sheet_data = [
                    'name' => $name,
                    'cate' => $sheet_id,
                    'type' => 1,
                    'createTime' => time()

                ];
                $this->db->query("INSERT INTO " . " rg_sheet " . " SET  name =  '" . $this->db->escape($sheet_data['name']) . "', cate = '" . $this->db->escape($sheet_data['cate']) . "', type = '" . $this->db->escape($sheet_data['type']) . "', createTime = '" . $this->db->escape($sheet_data['createTime']) . "'");

                $sheet_id = $this->db->getLastId();

                $column = ['string' => ['标题','描述'], "member" => ['处理人员','发起人'], "date" => '截止日期', "file" => '附件', "radio" => ['完成进度'],'dingshi'=>'重复时间','dingshidate'=>'停止时间','isdingshi'=>'重复待办','number'=>['紧急程度']];

                $asName = ['string' => ['标题','描述'], "member" => ['执行人员','发起人'], "date" => '日期', "file" => '附件', "radio" => ['完成进度'],'dingshi'=>'定时任务','dingshidate'=>'定时任务截止时间','isdingshi'=>'是否开启定时任务','number'=>['星级']];

                //流程-字段
                if($flow_id){
                    $column = ['flow'=>'流程','flowNode'=>'当前环节','string' => ['标题','描述'], "member" => ['处理人员','发起人'], "date" => '截止日期', "file" => '附件', "radio" => ['完成进度'],'number'=>['紧急程度']];

                    $asName = ['flow'=>'流程','flowNode'=>'当前步骤','string' => ['标题','描述'], "member" => ['执行人员','发起人'], "date" => '日期', "file" => '附件', "radio" => ['完成进度'],'number'=>['星级']];
                }
                
                

                $num = 0;   
                $columnIds = $columnName = [];
                foreach ($column as $k => $v) {
                    $num += 10000;
                    if(is_array($v)){
                        foreach ($v as $k_k => $v_v) {
                            $num += 10000;
                            $temp = [
                                'name' => $v_v,
                                'type' => $k,
                                'createTime' => time(),
                                'sheet_id' => $sheet_id,
                                'sort' => $num,
                                'asName'=>$asName[$k][$k_k]
                            ];
                            $columnName[] = $k;

                            $this->db->query("INSERT INTO " . " rg_column " . " SET  name =  '" . $this->db->escape($temp['name']) . "', type = '" . $this->db->escape($temp['type']) . "', sheet_id = '" . $this->db->escape($temp['sheet_id']) . "' , sort = '" . $this->db->escape($temp['sort']) . "', createTime = '" . $this->db->escape($temp['createTime']) . "' , asName = '" . $this->db->escape($temp['asName']) . "'");
                            $columnIds[] = $this->db->getLastId();
                            //字段完成度  默认添加两个选项
                            if($v_v == '完成进度'){
                                $radio_check_column_id = $this->db->getLastId();
    
                                $this->db->query("INSERT INTO " . " rg_radio_check " . " SET  sheet_id =  '" . $this->db->escape($sheet_id) . "', title = '" . $this->db->escape('进行中') . "', type = '" . $this->db->escape(0) . "', createTime = '" . $this->db->escape($this->db->escape(time())) . "', column_id = '" . $radio_check_column_id . "'");
                                $radio_check_id = $this->db->getLastId();

                                $this->db->query("INSERT INTO " . " rg_radio_check " . " SET  sheet_id =  '" . $this->db->escape($sheet_id) . "', title = '" . $this->db->escape('已完成') . "', type = '" . $this->db->escape(0) . "', createTime = '" . $this->db->escape($this->db->escape(time())) . "', column_id = '" . $radio_check_column_id . "'");

                                $this->db->query("INSERT INTO " . " rg_radio_check " . " SET  sheet_id =  '" . $this->db->escape($sheet_id) . "', title = '" . $this->db->escape('完结') . "', type = '" . $this->db->escape(0) . "', createTime = '" . $this->db->escape($this->db->escape(time())) . "', column_id = '" . $radio_check_column_id . "'");
                                
                            }
                            unset($temp);
                        }

                    }else{
                        $temp = [
                            'name' => $v,
                            'type' => $k,
                            'createTime' => time(),
                            'sheet_id' => $sheet_id,
                            'sort' => $num,
                            'content' => $radio_check_id ?? '',
                            'asName'=>$asName[$k]
                        ];
                        $columnName[] = $k;
                        $this->db->query("INSERT INTO " . " rg_column " . " SET  name =  '" . $this->db->escape($temp['name']) . "', type = '" . $this->db->escape($temp['type']) . "', sheet_id = '" . $this->db->escape($temp['sheet_id']) . "' , sort = '" . $this->db->escape($temp['sort']) . "', createTime = '" . $this->db->escape($temp['createTime']) . "' , asName = '" . $this->db->escape($temp['asName']) . "'");
                        $columnIds[] = $this->db->getLastId();
                        unset($temp);
                    }
                }



                $name = '待办事项';
            //数据视图
            case 2:
                $sheet_data = [
                    'name' => $name,
                    'cate' => $sheet_id,
                    'type' => 2,
                    'createTime' => time(),
                    'userId' => '-2',
                    'asName' => '所有待办'
                ];
                $this->db->query("INSERT INTO " . " rg_sheet " . " SET  name =  '" . $this->db->escape($sheet_data['name']) . "', cate = '" . $this->db->escape($sheet_data['cate']) . "', userId = '" . $this->db->escape($sheet_data['userId']) .  "', asName = '" . $this->db->escape($sheet_data['asName']) . "', type = '" . $this->db->escape($sheet_data['type']) ."', createTime = '" . $this->db->escape($sheet_data['createTime']) . "'");


                $sheet_data = [
                    'name' => '待审事项',
                    'cate' => $sheet_id,
                    'type' => 2,
                    'createTime' => time(),
                    'userId' => '-1',
                    'asName' => '待审事项'
                ];
                $this->db->query("INSERT INTO " . " rg_sheet " . " SET  name =  '" . $this->db->escape($sheet_data['name']) . "', cate = '" . $this->db->escape($sheet_data['cate']) . "', type = '" . $this->db->escape($sheet_data['type']) ."', userId = '" . $this->db->escape($sheet_data['userId']) ."', asName = '" . $this->db->escape($sheet_data['asName']) . "', createTime = '" . $this->db->escape($sheet_data['createTime']) . "'");
                $sheet_id_view = $this->db->getLastId();

                $union_id = $this->db->query("select * from _union where status = 1");
                $union_id = $union_id->rows;
                $union_id = array_column($union_id, 'union_id');

                foreach($union_id as $v){
                    $this->db->query("INSERT INTO rg_sheet_query SET selectCom = '" . $this->db->escape(" and bt0.extContent = '已完成'   ") . "', 
                    sheet_id = '" . $this->db->escape($sheet_id_view) . "', 
                    selectComJson = '" . $this->db->escape('[{"column_id":' . $radio_check_column_id . ',"rule":"=","value":"' . '已完成' . '"}]') . "', 
                    field = '" . $this->db->escape(' ,bt0.extContent AS btname0') . "', 
                    `from` = '" . $this->db->escape(' LEFT JOIN ( SELECT row_id, column_id, content,extContent FROM `rg_record` WHERE sheet_id = ' . $sheet_id . ' AND column_id = ' . $radio_check_column_id . ' ) AS bt0 ON r.row_id = bt0.row_id  ') . "', 
                    union_id = '" . $this->db->escape($v) . "'");
                }


               break;
        }
    }

    public function addColumn($data, $sheet_id)
    {
        $this->db->query("INSERT INTO " . " rg_column " . " SET  name =  '" . $this->db->escape($data['name']) . "', type = '" . $this->db->escape($data['type']) . "', sort = '" . $this->db->escape($data['sort']) . "', sheet_id = '" . $this->db->escape($sheet_id) . "', createTime = '" . $this->db->escape(time()) . "'");

        $column_id = $this->db->getLastId();

        if ($data['type'] == 'radio' || $data['type'] == 'check') {
            $type = $data['type'] == 'radio' ? 0 : 1;
            foreach ($data['radio_check'] as $v) {
                $this->db->query("INSERT INTO " . " rg_radio_check " . " SET  sheet_id =  '" . $this->db->escape($sheet_id) . "', title = '" . $this->db->escape($v) . "', type = '" . $this->db->escape($type) . "', createTime = '" . $this->db->escape($this->db->escape(time())) . "', column_id = '" . $column_id . "'");
            }
        }
        $rows = $this->db->query("select * from rg_row where sheet_id = $sheet_id and isDelete= 0 ");
        $rows = $rows->rows;
        $rowsId = array_column($rows, 'row_id');
        for ($i = 0; $i < count($rows); $i++) {
            $RgRecord = [
                'sheet_id' => $sheet_id,
                'column_id' => $column_id,
                'row_id' => $rowsId[$i],
                'content' => '',
                'type' => $data['type'],
                'createTime' => time()
            ];
            $this->db->query("INSERT INTO " . " rg_record " . " SET  sheet_id =  '" . $this->db->escape($RgRecord['sheet_id']) . "', column_id = '" . $this->db->escape($RgRecord['column_id']) . "', row_id = '" . $this->db->escape($RgRecord['row_id']) . "', content = '', extContent='', type = '" . $this->db->escape($RgRecord['type']) . "', createTime = '" . $this->db->escape($RgRecord['createTime']) . "'");
        }
    }

    public function editColumn($column_id, array $data, $sheet_id = 0)
    {
        $name = $data['name'];
        $type = $data['type'];
        $this->db->query(" update rg_column set `name` =  '$name',type = '$type',sort = " . $this->db->escape($data['sort']) . " where column_id = $column_id");
        if ($data['type'] == 'radio' || $data['type'] == 'check') {
            $type = $data['type'] == 'radio' ? 0 : 1;
            foreach ($data['radio_check'] as $v) {
                $this->db->query("INSERT INTO " . " rg_radio_check " . " SET  sheet_id =  '" . $this->db->escape($sheet_id) . "', title = '" . $this->db->escape($v) . "', type = '" . $this->db->escape($type) . "', createTime = '" . $this->db->escape($this->db->escape(time())) . "', column_id = '" . $column_id . "'");
            }
        }
    }


    public function editsheet($sheet_id, array $sheet_data)
    {
        $name = $sheet_data['name'];
        $this->db->query(" update rg_sheet set `name` =  '$name' where sheet_id = $sheet_id");
    }

    public function getSheetInfo(int $sheet_id)
    {
        $info = $this->db->query(" select * from rg_sheet where sheet_id = $sheet_id ");
        $info = $info->row;
        return $info;
    }

    public function getOneSheetIdByCate(array $sheet_info)
    {
        $info = $this->db->query(" select * from rg_sheet where sheet_id =  " . $sheet_info['cate']);
        $info = $info->row;
        if ($info['cate'] == 0) return $info;
        if ($info['cate'] != 0) {
            $info = $this->db->query(" select * from rg_sheet where sheet_id =  " . $info['cate']);
            $info = $info->row;
            return $info;
        }
    }



    public function getSecondSheetByCate(array $sheet_info)
    {
        $info = $this->db->query(" select * from rg_sheet where sheet_id =  " . $sheet_info['cate']);
        $info = $info->row;
        return $info;
    }

    public function getSheetInfoById( int $sheet_id)
    {
        $info = $this->db->query(" select * from rg_sheet where sheet_id =  $sheet_id");
        $info = $info->row;
        return $info;
    }

    public function validateDel(array $sheet_ids)
    {
        $sheet_id = $sheet_ids[0];
        $info = $this->db->query(" select * from rg_sheet where sheet_id =  $sheet_id ");
        $info = $info->row;
        if ($info['cate'] != 0) {
            $info = $this->db->query(" select count(*) as count from rg_sheet where isDelete = 0 and   cate =  " . $info['cate']);
            $info = $info->row;
            if ($info['count'] == 1) return 100;  //二级和三级 至少要保留一个。
        }
        return 200;
    }

    public function validateColumnDel(int $sheet_id,array $column_ids)
    {
        $info = $this->db->query(" select count(*) as count from rg_column where sheet_id =  $sheet_id and isDelete = 0 ");
        $info = $info->row;
        $column_ids = implode(',', $column_ids);
        $infoName = $this->db->query(" select asName from rg_column where sheet_id =  $sheet_id and isDelete = 0 and column_id in($column_ids) ");
        $noDeleteName = array('发起人','执行人员','标题','完成进度');
        if(in_array($infoName->rows[0]['asName'],$noDeleteName)){
            return 300;
        }

  
        if ($info['count'] == 1) return 100;
        return 200;

    }

    public function deletesheet(int $sheet_id)
    {
        $dataInfo = $this->db->query(" select * from rg_sheet where sheet_id =  $sheet_id ");
        $dataInfo = $dataInfo->row;
        $this->db->query(" update rg_sheet set isDelete = 1 where  sheet_id =  " . $dataInfo['sheet_id']);

        $typeOneSheetId = [];
        if($dataInfo['type'] == 1) $typeOneSheetId[] = $dataInfo['sheet_id'];

        if ($dataInfo['cate'] == 0) {
            $cateData = $this->db->query(" select * from rg_sheet where cate =  $sheet_id and isDelete = 0  ");
            $cateData = $cateData->rows;

            if ($cateData) {
                $typeOneSheetId = array_column($cateData, 'sheet_id');
                $sheetIds = implode(',', $typeOneSheetId);
                $this->db->query(" update rg_sheet set isDelete = 1 where  sheet_id  in($sheetIds)");

                $typeData = $this->db->query(" select * from rg_sheet where cate in($sheetIds) and isDelete = 0 ");
                $typeData = $typeData->rows;

                if ($typeData) {
                    $sheetIds = implode(',', array_column($typeData, 'sheet_id'));
                    $this->db->query(" update rg_sheet set isDelete = 1 where  sheet_id  in($sheetIds)");
                }
            }
        } else {
            $sheetData = $this->db->query(" select * from rg_sheet where cate =  $sheet_id and isDelete = 0  ");
            $sheetData = $sheetData->rows;

            if ($sheetData) {
                $sheetIds = implode(',', array_column($sheetData, 'sheet_id'));
                $this->db->query(" update rg_sheet set isDelete = 1 where  sheet_id  in($sheetIds)");
            }

        }

        //删字段
        if($typeOneSheetId){
            $sheetIds = implode(',',$typeOneSheetId);
            $this->db->query(" update rg_column set isDelete = 1 where  sheet_id  in($sheetIds)");

            //删行
            $this->db->query(" update rg_row set isDelete = 1 where  sheet_id  in($sheetIds)");
    
            //删单元格
            $this->db->query(" update rg_record set isDelete = 1 where  sheet_id  in($sheetIds)");
        }
       

    }

    public function deleteColumn(int $column_id)
    {
        $query = $this->db->query(" update rg_column set isDelete = 1 where column_id = $column_id");
        // return $query->row;
    }

    public function getColumnInfo($column_id)
    {
        $query = $this->db->query("select * from rg_column where column_id = $column_id");
        return $query->row;
    }


    public function getRadioCheck($column_id)
    {
        $query = $this->db->query("select * from rg_radio_check where column_id = $column_id");
        return $query->rows;
    }

    function getMonthTimestamps($month) {
        // 获取当前年份
        $year = date('Y');
        
        // 验证月份参数是否合法（1 到 12）
        if ($month < 1 || $month > 12) {
            return "月份不合法，请输入1到12之间的数字。";
        }
    
        // 获取该月第一天的时间戳
        $firstDayTimestamp = mktime(0, 0, 0, $month, 1, $year);
    
        // 获取该月最后一天的时间戳
        $lastDayTimestamp = mktime(0, 0, 0, $month + 1, 0, $year);
        
        return [
            'first_day' => $firstDayTimestamp,
            'last_day' => $lastDayTimestamp,
        ];
    }

    public function getUser($data = array()){
        $sql = "SELECT * FROM _union  where status = 1      ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    public function getUserAdmin($data = array()){
        $sql = "SELECT * FROM rg_special_user  where is_delete = 0      ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    public function getUnionGroup($id = 0){
        $sql = "SELECT * FROM rg_union_group  where is_delete = 0  and id = $id ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    

    public function addEditUserAdmin($data = array(),$user_id = 0){
        $sql =  " update rg_special_user set is_delete = 1 ";
        $this->db->query($sql);


        $sj = time();
        foreach($data['union_id'] as $union_id){
            $sql = "INSERT INTO rg_special_user (union_id,create_time,user_id) VALUES ('$union_id', '$sj','$user_id')";
            $this->db->query($sql);
        }

      return true;
    }

    public function addEditGroupUser($data = array()){
        $id = $data['id'];
        $users = $this->getUser();
        $users = array_column($users,'real_name','union_id');
        $real_name = $union_id  = array();

        foreach($data['union_id'] as $v){
            $real_name[] = $users[$v];
            $union_id[] = intval($v);
        }

        $name = $data['name'];

        $real_name = $real_name ? implode(',',$real_name) : '';
        $union_id   = json_encode($union_id);
        $sj = time();

        if($id){
            $sql =  " update rg_union_group set name = '$name',user_name='$real_name',union_id='$union_id',update_time=$sj where id = $id ";

        }else{
            $sql = "INSERT INTO rg_union_group (`name`,`union_id`,`user_name`,`create_time`) VALUES ('$name', '$union_id','$real_name', $sj)";
            
        }
        $this->db->query($sql);

      return true;
    }



    public function getSheetGroupUser($data = array())
    {
        $sql = "SELECT id,union_id,user_name,create_time,`name` FROM rg_union_group where is_delete = 0  ";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  (user_name  like '%" . $this->db->escape($data['filter_name']) . "%' or `name`  like '%" . $this->db->escape($data['filter_name']) . "%')";
        }

        $sort_data = array(
            'user_name',
            'create_time'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY id";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        $query = $this->db->query($sql);

        return $query->rows;
    }


    public function getSheetGroupUserTotal($data = array())
    {
        $sql = "SELECT count(*) as count FROM rg_union_group where is_delete = 0  ";

        if (!empty($data['user_name'])) {
            $sql .= " AND  user_name = 'like %" . $this->db->escape($data['user_name']) . "%'";
        }
    
        $query = $this->db->query($sql);

        return $query->row['count'];
    }

    public function deleteGroupUser(int $id)
    {
        $this->db->query(" update rg_union_group set `is_delete` =  1 where id = $id " );
    }



    public function getKpiAlgo($data = array()){
        $months = $data['filter_months'];
        $months = str_replace('-', '', $months);

        $field = "a.union_id,a.months,a.score,a.real_name,b.extra_info";
        $sql = "SELECT $field FROM rg_union_score as a left join _union_extra_info as b on a.union_id = b.union_id and extra_key = 'performance'  where a.months = $months";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  a.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }


        if (isset($data['sort']) && $data['sort']) {
            $sort = $data['sort'] == 'extra_info' ?  'b.extra_info' : 'a.'.$data['sort'];
            $sql .= " ORDER BY " . $sort;
        } else {
            $sql .= " ORDER BY a.score";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }
        $query = $this->db->query($sql);

        return $query->rows;


    }

    public function updateCompensationStatus($id) {
        try {
            $info =  $this->db->query("select * from rg_score_deduction_log where id = " . (int)$id);
            $info = $info->row;

            $remark = $info['remark'].'[扣分已失效]';
            $this->db->query("UPDATE rg_score_deduction_log SET is_compensation = 1 ,remark = '$remark' WHERE id = " . (int)$id);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    public function findCompensationStatus($id) {
        $info =  $this->db->query("select * from rg_score_deduction_log where id = " . (int)$id);
        $info = $info->row;
        return $info;
    }

    public function updateCompensationScore($month,$union_id) {
        try {
            $unionScore = $this->db->query("SELECT * FROM rg_union_score WHERE months = $month AND union_id = $union_id");
            $unionScore = $unionScore->row;
            $score = $unionScore['score'] + 1;
            $this->db->query("UPDATE rg_union_score SET score =  $score  WHERE months = $month AND union_id = $union_id");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }



}
