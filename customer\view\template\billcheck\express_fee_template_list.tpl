<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        快递模板管理
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">快递公司列表</h3>
          <div class="box-tools">
            <a class="btn btn-primary" href="<?php echo $add; ?>">添加</a>
          </div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <a href="javascript:;">快递公司</a>
              </th>
              <th>
                <a href="javascript:;">模板配置</a>
              </th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($templates)) { ?>
              <?php foreach ($templates as $template) { ?>
                <tr data-id="<?php echo $template['express_fee_template_id']; ?>">
                  <td><?php echo $template['name']; ?></td>
                  <?php $setting = json_decode($template['setting'],true); ?>
                  <td>
                    快递单号：<?php echo $setting['courier_number']; ?><br>
                    省份：<?php echo $setting['province']; ?><br>
                    市/区：<?php echo $setting['city']; ?><br>
                    发货日期：<?php echo $setting['delivery_date']; ?><br>
                    长度：<?php echo $setting['long']; ?><br>
                    宽度：<?php echo $setting['wide']; ?><br>
                    高度：<?php echo $setting['high']; ?><br>
                    重量：<?php echo $setting['weight']; ?><br>
                    快递费：<?php echo $setting['price']; ?><br>
                    加价：<?php echo $setting['additional_charge']; ?><br>
                    开始删除行数：<?php echo $setting['s_line']; ?><br>
                    结尾删除行数：<?php echo $setting['e_line']; ?>
                  </td>
                  <td class="text-right">
                    <a class="btn btn-success" href="<?php echo $edit_url; ?><?php echo $template['express_fee_template_id']; ?>" title="" target="_blank">编辑</a>
                    <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">删除</button>
                  </td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
      </div>

      <!-- 删除 -->
      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-del">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除此快递公司吗？该操作会删除对应的快递报价，此操作不可恢复！</p>
                <input id="del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
  })()
</script>
<?php echo $footer; ?>