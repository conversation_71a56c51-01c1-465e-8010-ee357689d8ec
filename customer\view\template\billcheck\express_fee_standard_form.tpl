<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">快递公司：</label>
              <div class="col-sm-8">
                <select  class="form-control select-province"  name="name">
                  <?php foreach ($all_template as $template) { ?>
                  <option <?php if(!empty($expressFeeStandard['name']) && $expressFeeStandard['name']==$template['express_fee_template_id']){ ?>selected<?php } ?> value="<?php echo $template['express_fee_template_id']; ?>"><?php echo $template['name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-quan">材积重量：</label>
              <div class="col-sm-8">
                <div class="radio">
                  <label><input type="radio" name="type" onchange="volumeRatio($(this))" value="2" <?php if(empty($expressFeeStandard['type']) || (!empty($expressFeeStandard['type']) && $expressFeeStandard['type'] == 2)){ ?>checked<?php } ?>>否</label>
                  <label style="margin-left: 10px"><input type="radio" name="type" onchange="volumeRatio($(this))" value="1" <?php if(!empty($expressFeeStandard['type']) && $expressFeeStandard['type'] == 1){ ?>checked<?php } ?>>是</label>
                </div>
              </div>
            </div>

            <div class="form-group volume-ratio" <?php if((!empty($expressFeeStandard['type']) && $expressFeeStandard['type'] == 2) || empty($expressFeeStandard['type'])){ ?>style="display: none"<?php } ?>>
              <label class="col-sm-2 control-label" for="input-name">材积比：</label>
              <div class="col-sm-8">
                <input type="text" name="volume_ratio" value="<?php if(!empty($expressFeeStandard['volume_ratio'])){ ?><?php echo $expressFeeStandard['volume_ratio']; ?><?php } ?>" placeholder="请输入材积比" id="input-volume_ratio" class="form-control" />
              </div>
            </div>
            <?php if(!empty($expressFeeStandard['weight'])){ ?>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-quan">重量区间（KG）：</label>
                <div class="col-sm-8 keyword">
                  <?php foreach($expressFeeStandard['weight'] as $weight_key => $weight){ ?>
                  <div>
                    <input type="text" name="weight[<?php echo $weight_key; ?>][]" value="<?php echo $weight[0]; ?>" placeholder="请输入初始重量"  class="form-control" style="width: 30%;float: left;<?php if($weight_key != 0){ ?>margin-top: 5px;<?php } ?>" />
                    <input type="text" name="weight[<?php echo $weight_key; ?>][]" value="<?php echo $weight[1]; ?>" placeholder="请输入结束重量"  class="form-control" style="margin-left:10px;width: 30%;float: left;<?php if($weight_key != 0){ ?>margin-top: 5px;<?php } ?>" />
                    <?php if($weight_key == 0){ ?>
                      <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                        <a class="btn btn-primary" href="javascript:;" onclick="addweight($(this))">添加区间</a>
                      </div>
                    <?php } else { ?>
                      <div class="box-tools" style="width: 15%;float: left;margin-left: 8px;margin-top: 5px;">
                        <a class="btn btn-danger" href="javascript:;" onclick="delweight($(this))">删除</a>
                      </div>
                    <?php } ?>
                  </div>
                  <?php } ?>
                </div>
              </div>
            <?php } else { ?>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-quan">重量区间（KG）：</label>
                <div class="col-sm-8 keyword">
                  <div>
                    <input type="text" name="weight[0][]" value="" placeholder="请输入初始重量"  class="form-control" style="width: 30%;float: left" />
                    <input type="text" name="weight[0][]" value="" placeholder="请输入结束重量"  class="form-control" style="margin-left:10px;width: 30%;float: left" />
                    <div class="box-tools" style="width: 15%;float: left;margin-left: 8px">
                      <a class="btn btn-primary" href="javascript:;" onclick="addweight($(this))">添加区间</a>
                    </div>
                  </div>
                </div>
              </div>
            <?php } ?>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">起步重量（KG）：</label>
              <div class="col-sm-8">
                <input type="text" name="starting_weight" value="<?php if(!empty($expressFeeStandard['starting_weight'])){ ?><?php echo $expressFeeStandard['starting_weight']; ?><?php } ?>" placeholder="请输入起步重" id="input-starting_weight" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">首重（KG）：</label>
              <div class="col-sm-8">
                <input type="text" name="first_weight" value="<?php if(!empty($expressFeeStandard['first_weight'])){ ?><?php echo $expressFeeStandard['first_weight']; ?><?php }else{ ?>1<?php } ?>" placeholder="请输入首重" id="input-first_weight" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">续重（KG）：</label>
              <div class="col-sm-8">
                <input type="text" name="continue_weight" value="<?php if(!empty($expressFeeStandard['continue_weight'])){ ?><?php echo $expressFeeStandard['continue_weight']; ?><?php }else{ ?>1<?php } ?>" placeholder="请输入续重" id="input-continue_weight" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">开始日期：</label>
              <div class="col-sm-8">
                <input type="number" name="s_day" value="<?php if(!empty($expressFeeStandard['s_day'])){ ?><?php echo $expressFeeStandard['s_day']; ?><?php }else{ ?><?php } ?>" placeholder="请输入开始日期" id="input-s_day" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">结束日期：</label>
              <div class="col-sm-8">
                <input type="number" max="31" name="e_day" value="<?php if(!empty($expressFeeStandard['e_day'])){ ?><?php echo $expressFeeStandard['e_day']; ?><?php }else{ ?><?php } ?>" placeholder="请输入结束日期" id="input-e_day" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">权重：</label>
              <div class="col-sm-8">
                <input type="number"  name="sort" value="<?php if(!empty($expressFeeStandard['sort'])){ ?><?php echo $expressFeeStandard['sort']; ?><?php }else{ ?><?php } ?>" placeholder="请输入权重" id="input-sort" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script type="text/javascript">
  var num = parseInt('<?php echo $num; ?>');
  function toVaild() {
    // if ($('.glyphicon-info-sign').length) {
    //   confirm('数据不存在')
    //   return false;
    // }
    var name = document.getElementById("input-name").value;
    if (name == "") {
      confirm('请输入快递公司')
      return false;
    }
    return true;
  }

  function volumeRatio(obj) {
    if (obj.val() == 1) {
      $(".volume-ratio").show()
    } else {
      $(".volume-ratio").hide()
    }
  }

  function addweight(obj) {
    num += 1;
    var addHtml =
            '<div>' +
            '<input type="text" name="weight['+num+'][]" value="" placeholder="请输入初始重量"  class="form-control" style="width: 30%;float: left;margin-top: 5px;" />'+
            '<input type="text" name="weight['+num+'][]" value="" placeholder="请输入结束重量"  class="form-control" style="margin-left:10px;width: 30%;float: left;margin-top: 5px;" />'+
            '<div class="box-tools" style="width: 15%;float: left;margin-left: 8px;margin-top: 5px">' +
            '<a class="btn btn-danger" href="javascript:;" onclick="delweight($(this),'+num+')">删除</a>' +
            '</div>' +
            '</div>';
    $(obj).parent().parent().parent().append(addHtml);
  }

  function delweight(obj) {
    $(obj).parent().parent().remove();
  }

</script>
<?php echo $footer; ?>