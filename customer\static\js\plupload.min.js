/**
 * Plupload - multi-runtime File Uploader
 * v3.1.0
 *
 * Copyright 2017, Ephox
 * Released under AGPLv3 License.
 *
 * License: http://www.plupload.com/license
 * Contributing: http://www.plupload.com/contributing
 *
 * Date: 2017-03-07
 */
!function(e,t){var i=function(){var e={};return t.apply(e,arguments),e.plupload};"function"==typeof define&&define.amd?define("plupload",[],i):"object"==typeof module&&module.exports?module.exports=i():e.plupload=i()}(this||window,function(){!function(e,t){"use strict";function i(e,t){for(var i,n=[],r=0;r<e.length;++r){if(i=a[e[r]]||o(e[r]),!i)throw"module definition dependecy not found: "+e[r];n.push(i)}t.apply(null,n)}function n(e,n,r){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(n===t)throw"invalid module definition, dependencies must be specified";if(r===t)throw"invalid module definition, definition function must be specified";i(n,function(){a[e]=r.apply(null,arguments)})}function r(e){return!!a[e]}function o(t){for(var i=e,n=t.split(/[.\/]/),r=0;r<n.length;++r){if(!i[n[r]])return;i=i[n[r]]}return i}function s(i){for(var n=0;n<i.length;n++){for(var r=e,o=i[n],s=o.split(/[.\/]/),u=0;u<s.length-1;++u)r[s[u]]===t&&(r[s[u]]={}),r=r[s[u]];r[s[s.length-1]]=a[o]}}var a={};n("moxie/core/utils/Basic",[],function(){function e(e){var t;return e===t?"undefined":null===e?"null":e.nodeType?"node":{}.toString.call(e).match(/\s([a-z|A-Z]+)/)[1].toLowerCase()}function t(){return a(!1,!1,arguments)}function i(){return a(!0,!1,arguments)}function n(){return a(!1,!0,arguments)}function r(){return a(!0,!0,arguments)}function o(t){switch(e(t)){case"array":return a(!1,!0,[[],t]);case"object":return a(!1,!0,[{},t]);default:return t}}function s(i){switch(e(i)){case"array":return Array.prototype.slice.call(i);case"object":return t({},i)}return i}function a(t,i,n){var r,o=n[0];return c(n,function(n,u){u>0&&c(n,function(n,u){var c=-1!==p(e(n),["array","object"]);return n===r||t&&o[u]===r?!0:(c&&i&&(n=s(n)),e(o[u])===e(n)&&c?a(t,i,[o[u],n]):o[u]=n,void 0)})}),o}function u(e,t){function i(){this.constructor=e}for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n]);return i.prototype=t.prototype,e.prototype=new i,e.super=t.prototype,e}function c(e,t){var i,n,r,o;if(e){try{i=e.length}catch(s){i=o}if(i===o||"number"!=typeof i){for(n in e)if(e.hasOwnProperty(n)&&t(e[n],n)===!1)return}else for(r=0;i>r;r++)if(t(e[r],r)===!1)return}}function l(t){var i;if(!t||"object"!==e(t))return!0;for(i in t)return!1;return!0}function d(t,i){function n(r){"function"===e(t[r])&&t[r](function(e){++r<o&&!e?n(r):i(e)})}var r=0,o=t.length;"function"!==e(i)&&(i=function(){}),t&&t.length||i(),n(r)}function h(e,t){var i=0,n=e.length,r=new Array(n);c(e,function(e,o){e(function(e){if(e)return t(e);var s=[].slice.call(arguments);s.shift(),r[o]=s,i++,i===n&&(r.unshift(null),t.apply(this,r))})})}function p(e,t){if(t){if(Array.prototype.indexOf)return Array.prototype.indexOf.call(t,e);for(var i=0,n=t.length;n>i;i++)if(t[i]===e)return i}return-1}function m(t,i){var n=[];"array"!==e(t)&&(t=[t]),"array"!==e(i)&&(i=[i]);for(var r in t)-1===p(t[r],i)&&n.push(t[r]);return n.length?n:!1}function f(e,t){var i=[];return c(e,function(e){-1!==p(e,t)&&i.push(e)}),i.length?i:null}function g(e){var t,i=[];for(t=0;t<e.length;t++)i[t]=e[t];return i}function x(e){return e?String.prototype.trim?String.prototype.trim.call(e):e.toString().replace(/^\s*/,"").replace(/\s*$/,""):e}function v(e){if("string"!=typeof e)return e;var t,i={t:1099511627776,g:1073741824,m:1048576,k:1024};return e=/^([0-9\.]+)([tmgk]?)$/.exec(e.toLowerCase().replace(/[^0-9\.tmkg]/g,"")),t=e[2],e=+e[1],i.hasOwnProperty(t)&&(e*=i[t]),Math.floor(e)}function y(e){var t=[].slice.call(arguments,1);return e.replace(/%([a-z])/g,function(e,i){var n=t.shift();switch(i){case"s":return n+"";case"d":return parseInt(n,10);case"f":return parseFloat(n);case"c":return"";default:return n}})}function _(e,t){var i=this;setTimeout(function(){e.call(i)},t||1)}var E=function(){var e=0;return function(t){var i,n=(new Date).getTime().toString(32);for(i=0;5>i;i++)n+=Math.floor(65535*Math.random()).toString(32);return(t||"o_")+n+(e++).toString(32)}}();return{guid:E,typeOf:e,extend:t,extendIf:i,extendImmutable:n,extendImmutableIf:r,clone:o,inherit:u,each:c,isEmptyObj:l,inSeries:d,inParallel:h,inArray:p,arrayDiff:m,arrayIntersect:f,toArray:g,trim:x,sprintf:y,parseSizeStr:v,delay:_}}),n("moxie/core/I18n",["moxie/core/utils/Basic"],function(e){var t={};return{addI18n:function(i){return e.extend(t,i)},translate:function(e){return t[e]||e},_:function(e){return this.translate(e)},sprintf:function(t){var i=[].slice.call(arguments,1);return t.replace(/%[a-z]/g,function(){var t=i.shift();return"undefined"!==e.typeOf(t)?t:""})}}}),n("moxie/core/utils/Env",["moxie/core/utils/Basic"],function(e){function t(e,t,i){var n=0,r=0,o=0,s={dev:-6,alpha:-5,a:-5,beta:-4,b:-4,RC:-3,rc:-3,"#":-2,p:1,pl:1},a=function(e){return e=(""+e).replace(/[_\-+]/g,"."),e=e.replace(/([^.\d]+)/g,".$1.").replace(/\.{2,}/g,"."),e.length?e.split("."):[-8]},u=function(e){return e?isNaN(e)?s[e]||-7:parseInt(e,10):0};for(e=a(e),t=a(t),r=Math.max(e.length,t.length),n=0;r>n;n++)if(e[n]!=t[n]){if(e[n]=u(e[n]),t[n]=u(t[n]),e[n]<t[n]){o=-1;break}if(e[n]>t[n]){o=1;break}}if(!i)return o;switch(i){case">":case"gt":return o>0;case">=":case"ge":return o>=0;case"<=":case"le":return 0>=o;case"==":case"=":case"eq":return 0===o;case"<>":case"!=":case"ne":return 0!==o;case"":case"<":case"lt":return 0>o;default:return null}}var i=function(e){var t="",i="?",n="function",r="undefined",o="object",s="name",a="version",u={has:function(e,t){return-1!==t.toLowerCase().indexOf(e.toLowerCase())},lowerize:function(e){return e.toLowerCase()}},c={rgx:function(){for(var t,i,s,a,u,c,l,d=0,h=arguments;d<h.length;d+=2){var p=h[d],m=h[d+1];if(typeof t===r){t={};for(a in m)u=m[a],typeof u===o?t[u[0]]=e:t[u]=e}for(i=s=0;i<p.length;i++)if(c=p[i].exec(this.getUA())){for(a=0;a<m.length;a++)l=c[++s],u=m[a],typeof u===o&&u.length>0?2==u.length?t[u[0]]=typeof u[1]==n?u[1].call(this,l):u[1]:3==u.length?t[u[0]]=typeof u[1]!==n||u[1].exec&&u[1].test?l?l.replace(u[1],u[2]):e:l?u[1].call(this,l,u[2]):e:4==u.length&&(t[u[0]]=l?u[3].call(this,l.replace(u[1],u[2])):e):t[u]=l?l:e;break}if(c)break}return t},str:function(t,n){for(var r in n)if(typeof n[r]===o&&n[r].length>0){for(var s=0;s<n[r].length;s++)if(u.has(n[r][s],t))return r===i?e:r}else if(u.has(n[r],t))return r===i?e:r;return t}},l={browser:{oldsafari:{major:{1:["/8","/1","/3"],2:"/4","?":"/"},version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}}},device:{sprint:{model:{"Evo Shift 4G":"7373KT"},vendor:{HTC:"APA",Sprint:"Sprint"}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2000:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",RT:"ARM"}}}},d={browser:[[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]+).+version\/([\w\.-]+)/i,/(opera).+version\/([\w\.]+)/i,/(opera)[\/\s]+([\w\.]+)/i],[s,a],[/\s(opr)\/([\w\.]+)/i],[[s,"Opera"],a],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]+)*/i,/(avant\s|iemobile|slim|baidu)(?:browser)?[\/\s]?([\w\.]*)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(rekonq)\/([\w\.]+)*/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi)\/([\w\.-]+)/i],[s,a],[/(trident).+rv[:\s]([\w\.]+).+like\sgecko/i],[[s,"IE"],a],[/(edge)\/((\d+)?[\w\.]+)/i],[s,a],[/(yabrowser)\/([\w\.]+)/i],[[s,"Yandex"],a],[/(comodo_dragon)\/([\w\.]+)/i],[[s,/_/g," "],a],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i,/(uc\s?browser|qqbrowser)[\/\s]?([\w\.]+)/i],[s,a],[/(dolfin)\/([\w\.]+)/i],[[s,"Dolphin"],a],[/((?:android.+)crmo|crios)\/([\w\.]+)/i],[[s,"Chrome"],a],[/XiaoMi\/MiuiBrowser\/([\w\.]+)/i],[a,[s,"MIUI Browser"]],[/android.+version\/([\w\.]+)\s+(?:mobile\s?safari|safari)/i],[a,[s,"Android Browser"]],[/FBAV\/([\w\.]+);/i],[a,[s,"Facebook"]],[/version\/([\w\.]+).+?mobile\/\w+\s(safari)/i],[a,[s,"Mobile Safari"]],[/version\/([\w\.]+).+?(mobile\s?safari|safari)/i],[a,s],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[s,[a,c.str,l.browser.oldsafari.version]],[/(konqueror)\/([\w\.]+)/i,/(webkit|khtml)\/([\w\.]+)/i],[s,a],[/(navigator|netscape)\/([\w\.-]+)/i],[[s,"Netscape"],a],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix)\/([\w\.-]+)/i,/(mozilla)\/([\w\.]+).+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]+)*/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[s,a]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[a,[s,"EdgeHTML"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[s,a],[/rv\:([\w\.]+).*(gecko)/i],[a,s]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[s,a],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*|windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[s,[a,c.str,l.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[s,"Windows"],[a,c.str,l.os.windows.version]],[/\((bb)(10);/i],[[s,"BlackBerry"],a],[/(blackberry)\w*\/?([\w\.]+)*/i,/(tizen)[\/\s]([\w\.]+)/i,/(android|webos|palm\os|qnx|bada|rim\stablet\sos|meego|contiki)[\/\s-]?([\w\.]+)*/i,/linux;.+(sailfish);/i],[s,a],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]+)*/i],[[s,"Symbian"],a],[/\((series40);/i],[s],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[s,"Firefox OS"],a],[/(nintendo|playstation)\s([wids3portablevu]+)/i,/(mint)[\/\s\(]?(\w+)*/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\/\s-]?([\w\.-]+)*/i,/(hurd|linux)\s?([\w\.]+)*/i,/(gnu)\s?([\w\.]+)*/i],[s,a],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[s,"Chromium OS"],a],[/(sunos)\s?([\w\.]+\d)*/i],[[s,"Solaris"],a],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]+)*/i],[s,a],[/(ip[honead]+)(?:.*os\s*([\w]+)*\slike\smac|;\sopera)/i],[[s,"iOS"],[a,/_/g,"."]],[/(mac\sos\sx)\s?([\w\s\.]+\w)*/i,/(macintosh|mac(?=_powerpc)\s)/i],[[s,"Mac OS"],[a,/_/g,"."]],[/((?:open)?solaris)[\/\s-]?([\w\.]+)*/i,/(haiku)\s(\w+)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.]*)*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms)/i,/(unix)\s?([\w\.]+)*/i],[s,a]]},h=function(e){var i=e||(window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:t);this.getBrowser=function(){return c.rgx.apply(this,d.browser)},this.getEngine=function(){return c.rgx.apply(this,d.engine)},this.getOS=function(){return c.rgx.apply(this,d.os)},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS()}},this.getUA=function(){return i},this.setUA=function(e){return i=e,this},this.setUA(i)};return h}(),n=function(){var t={define_property:function(){return!1}(),create_canvas:function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))}(),return_response_type:function(t){try{if(-1!==e.inArray(t,["","text","document"]))return!0;if(window.XMLHttpRequest){var i=new XMLHttpRequest;if(i.open("get","/"),"responseType"in i)return i.responseType=t,i.responseType!==t?!1:!0}}catch(n){}return!1},use_data_uri:function(){var e=new Image;return e.onload=function(){t.use_data_uri=1===e.width&&1===e.height},setTimeout(function(){e.src="data:image/gif;base64,R0lGODlhAQABAIAAAP8AAAAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw=="},1),!1}(),use_data_uri_over32kb:function(){return t.use_data_uri&&("IE"!==o.browser||o.version>=9)},use_data_uri_of:function(e){return t.use_data_uri&&33e3>e||t.use_data_uri_over32kb()},use_fileinput:function(){if(navigator.userAgent.match(/(Android (1.0|1.1|1.5|1.6|2.0|2.1))|(Windows Phone (OS 7|8.0))|(XBLWP)|(ZuneWP)|(w(eb)?OSBrowser)|(webOS)|(Kindle\/(1.0|2.0|2.5|3.0))/))return!1;var e=document.createElement("input");return e.setAttribute("type","file"),!e.disabled}};return function(i){var n=[].slice.call(arguments);return n.shift(),"function"===e.typeOf(t[i])?t[i].apply(this,n):!!t[i]}}(),r=(new i).getResult(),o={can:n,uaParser:i,browser:r.browser.name,version:r.browser.version,os:r.os.name,osVersion:r.os.version,verComp:t,swf_url:"../flash/Moxie.swf",xap_url:"../silverlight/Moxie.xap",global_event_dispatcher:"moxie.core.EventTarget.instance.dispatchEvent"};return o.OS=o.os,o}),n("moxie/core/utils/Dom",["moxie/core/utils/Env"],function(e){var t=function(e){return"string"!=typeof e?e:document.getElementById(e)},i=function(e,t){if(!e.className)return!1;var i=new RegExp("(^|\\s+)"+t+"(\\s+|$)");return i.test(e.className)},n=function(e,t){i(e,t)||(e.className=e.className?e.className.replace(/\s+$/,"")+" "+t:t)},r=function(e,t){if(e.className){var i=new RegExp("(^|\\s+)"+t+"(\\s+|$)");e.className=e.className.replace(i,function(e,t,i){return" "===t&&" "===i?" ":""})}},o=function(e,t){return e.currentStyle?e.currentStyle[t]:window.getComputedStyle?window.getComputedStyle(e,null)[t]:void 0},s=function(t,i){function n(e){var t,i,n=0,r=0;return e&&(i=e.getBoundingClientRect(),t="CSS1Compat"===c.compatMode?c.documentElement:c.body,n=i.left+t.scrollLeft,r=i.top+t.scrollTop),{x:n,y:r}}var r,o,s,a=0,u=0,c=document;if(t=t,i=i||c.body,t&&t.getBoundingClientRect&&"IE"===e.browser&&(!c.documentMode||c.documentMode<8))return o=n(t),s=n(i),{x:o.x-s.x,y:o.y-s.y};for(r=t;r&&r!=i&&r.nodeType;)a+=r.offsetLeft||0,u+=r.offsetTop||0,r=r.offsetParent;for(r=t.parentNode;r&&r!=i&&r.nodeType;)a-=r.scrollLeft||0,u-=r.scrollTop||0,r=r.parentNode;return{x:a,y:u}},a=function(e){return{w:e.offsetWidth||e.clientWidth,h:e.offsetHeight||e.clientHeight}};return{get:t,hasClass:i,addClass:n,removeClass:r,getStyle:o,getPos:s,getSize:a}}),n("moxie/core/utils/Events",["moxie/core/utils/Basic"],function(e){function t(){this.returnValue=!1}function i(){this.cancelBubble=!0}var n={},r="moxie_"+e.guid(),o=function(o,s,a,u){var c,l;s=s.toLowerCase(),o.addEventListener?(c=a,o.addEventListener(s,c,!1)):o.attachEvent&&(c=function(){var e=window.event;e.target||(e.target=e.srcElement),e.preventDefault=t,e.stopPropagation=i,a(e)},o.attachEvent("on"+s,c)),o[r]||(o[r]=e.guid()),n.hasOwnProperty(o[r])||(n[o[r]]={}),l=n[o[r]],l.hasOwnProperty(s)||(l[s]=[]),l[s].push({func:c,orig:a,key:u})},s=function(t,i,o){var s,a;if(i=i.toLowerCase(),t[r]&&n[t[r]]&&n[t[r]][i]){s=n[t[r]][i];for(var u=s.length-1;u>=0&&(s[u].orig!==o&&s[u].key!==o||(t.removeEventListener?t.removeEventListener(i,s[u].func,!1):t.detachEvent&&t.detachEvent("on"+i,s[u].func),s[u].orig=null,s[u].func=null,s.splice(u,1),o===a));u--);if(s.length||delete n[t[r]][i],e.isEmptyObj(n[t[r]])){delete n[t[r]];try{delete t[r]}catch(c){t[r]=a}}}},a=function(t,i){t&&t[r]&&e.each(n[t[r]],function(e,n){s(t,n,i)})};return{addEvent:o,removeEvent:s,removeAllEvents:a}}),n("moxie/core/utils/Url",["moxie/core/utils/Basic"],function(e){var t=function(i,n){var r,o=["source","scheme","authority","userInfo","user","pass","host","port","relative","path","directory","file","query","fragment"],s=o.length,a={http:80,https:443},u={},c=/^(?:([^:\/?#]+):)?(?:\/\/()(?:(?:()(?:([^:@\/]*):?([^:@\/]*))?@)?(\[[\da-fA-F:]+\]|[^:\/?#]*)(?::(\d*))?))?()(?:(()(?:(?:[^?#\/]*\/)*)()(?:[^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,l=c.exec(i||""),d=/^\/\/\w/.test(i);switch(e.typeOf(n)){case"undefined":n=t(document.location.href,!1);break;case"string":n=t(n,!1)}for(;s--;)l[s]&&(u[o[s]]=l[s]);if(r=!d&&!u.scheme,(d||r)&&(u.scheme=n.scheme),r){u.host=n.host,u.port=n.port;var h="";/^[^\/]/.test(u.path)&&(h=n.path,h=/\/[^\/]*\.[^\/]*$/.test(h)?h.replace(/\/[^\/]+$/,"/"):h.replace(/\/?$/,"/")),u.path=h+(u.path||"")}return u.port||(u.port=a[u.scheme]||80),u.port=parseInt(u.port,10),u.path||(u.path="/"),delete u.source,u},i=function(e){var i={http:80,https:443},n="object"==typeof e?e:t(e);return n.scheme+"://"+n.host+(n.port!==i[n.scheme]?":"+n.port:"")+n.path+(n.query?n.query:"")},n=function(e){function i(e){return[e.scheme,e.host,e.port].join("/")}return"string"==typeof e&&(e=t(e)),i(t())===i(e)};return{parseUrl:t,resolveUrl:i,hasSameOrigin:n}}),n("moxie/core/Exceptions",["moxie/core/utils/Basic"],function(e){function t(e,t){var i;for(i in e)if(e[i]===t)return i;return null}return{RuntimeError:function(){function i(e,i){this.code=e,this.name=t(n,e),this.message=this.name+(i||": RuntimeError "+this.code)}var n={NOT_INIT_ERR:1,EXCEPTION_ERR:3,NOT_SUPPORTED_ERR:9,JS_ERR:4};return e.extend(i,n),i.prototype=Error.prototype,i}(),OperationNotAllowedException:function(){function t(e){this.code=e,this.name="OperationNotAllowedException"}return e.extend(t,{NOT_ALLOWED_ERR:1}),t.prototype=Error.prototype,t}(),ImageError:function(){function i(e){this.code=e,this.name=t(n,e),this.message=this.name+": ImageError "+this.code}var n={WRONG_FORMAT:1,MAX_RESOLUTION_ERR:2,INVALID_META_ERR:3};return e.extend(i,n),i.prototype=Error.prototype,i}(),FileException:function(){function i(e){this.code=e,this.name=t(n,e),this.message=this.name+": FileException "+this.code}var n={NOT_FOUND_ERR:1,SECURITY_ERR:2,ABORT_ERR:3,NOT_READABLE_ERR:4,ENCODING_ERR:5,NO_MODIFICATION_ALLOWED_ERR:6,INVALID_STATE_ERR:7,SYNTAX_ERR:8};return e.extend(i,n),i.prototype=Error.prototype,i}(),DOMException:function(){function i(e){this.code=e,this.name=t(n,e),this.message=this.name+": DOMException "+this.code}var n={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25};return e.extend(i,n),i.prototype=Error.prototype,i}(),EventException:function(){function t(e){this.code=e,this.name="EventException"}return e.extend(t,{UNSPECIFIED_EVENT_TYPE_ERR:0}),t.prototype=Error.prototype,t}()}}),n("moxie/core/EventTarget",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic"],function(e,t,i){function n(){this.uid=i.guid()}var r={};return i.extend(n.prototype,{init:function(){this.uid||(this.uid=i.guid("uid_"))},addEventListener:function(e,t,n,o){var s,a=this;return this.hasOwnProperty("uid")||(this.uid=i.guid("uid_")),e=i.trim(e),/\s/.test(e)?(i.each(e.split(/\s+/),function(e){a.addEventListener(e,t,n,o)}),void 0):(e=e.toLowerCase(),n=parseInt(n,10)||0,s=r[this.uid]&&r[this.uid][e]||[],s.push({fn:t,priority:n,scope:o||this}),r[this.uid]||(r[this.uid]={}),r[this.uid][e]=s,void 0)},hasEventListener:function(e){var t;return e?(e=e.toLowerCase(),t=r[this.uid]&&r[this.uid][e]):t=r[this.uid],t?t:!1},removeEventListener:function(e,t){var n,o,s=this;if(e=e.toLowerCase(),/\s/.test(e))return i.each(e.split(/\s+/),function(e){s.removeEventListener(e,t)}),void 0;if(n=r[this.uid]&&r[this.uid][e]){if(t){for(o=n.length-1;o>=0;o--)if(n[o].fn===t){n.splice(o,1);break}}else n=[];n.length||(delete r[this.uid][e],i.isEmptyObj(r[this.uid])&&delete r[this.uid])}},removeAllEventListeners:function(){r[this.uid]&&delete r[this.uid]},dispatchEvent:function(e){var n,o,s,a,u,c={},l=!0;if("string"!==i.typeOf(e)){if(a=e,"string"!==i.typeOf(a.type))throw new t.EventException(t.EventException.UNSPECIFIED_EVENT_TYPE_ERR);e=a.type,a.total!==u&&a.loaded!==u&&(c.total=a.total,c.loaded=a.loaded),c.async=a.async||!1}if(-1!==e.indexOf("::")?function(t){n=t[0],e=t[1]}(e.split("::")):n=this.uid,e=e.toLowerCase(),o=r[n]&&r[n][e]){o.sort(function(e,t){return t.priority-e.priority}),s=[].slice.call(arguments),s.shift(),c.type=e,s.unshift(c);var d=[];i.each(o,function(e){s[0].target=e.scope,c.async?d.push(function(t){setTimeout(function(){t(e.fn.apply(e.scope,s)===!1)},1)}):d.push(function(t){t(e.fn.apply(e.scope,s)===!1)})}),d.length&&i.inSeries(d,function(e){l=!e})}return l},bindOnce:function(e,t,i,n){var r=this;r.bind.call(this,e,function o(){return r.unbind(e,o),t.apply(this,arguments)},i,n)},bind:function(){this.addEventListener.apply(this,arguments)},unbind:function(){this.removeEventListener.apply(this,arguments)},unbindAll:function(){this.removeAllEventListeners.apply(this,arguments)},trigger:function(){return this.dispatchEvent.apply(this,arguments)},handleEventProps:function(e){var t=this;this.bind(e.join(" "),function(e){var t="on"+e.type.toLowerCase();"function"===i.typeOf(this[t])&&this[t].apply(this,arguments)}),i.each(e,function(e){e="on"+e.toLowerCase(e),"undefined"===i.typeOf(t[e])&&(t[e]=null)})}}),n.instance=new n,n}),n("moxie/runtime/Runtime",["moxie/core/utils/Env","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/EventTarget"],function(e,t,i,n){function r(e,n,o,a,u){var c,l=this,d=t.guid(n+"_"),h=u||"browser";e=e||{},s[d]=this,o=t.extend({access_binary:!1,access_image_binary:!1,display_media:!1,do_cors:!1,drag_and_drop:!1,filter_by_extension:!0,resize_image:!1,report_upload_progress:!1,return_response_headers:!1,return_response_type:!1,return_status_code:!0,send_custom_headers:!1,select_file:!1,select_folder:!1,select_multiple:!0,send_binary_string:!1,send_browser_cookies:!0,send_multipart:!0,slice_blob:!1,stream_upload:!1,summon_file_dialog:!1,upload_filesize:!0,use_http_method:!0},o),e.preferred_caps&&(h=r.getMode(a,e.preferred_caps,h)),c=function(){var e={};return{exec:function(t,i,n,r){return c[i]&&(e[t]||(e[t]={context:this,instance:new c[i]}),e[t].instance[n])?e[t].instance[n].apply(this,r):void 0},removeInstance:function(t){delete e[t]},removeAllInstances:function(){var i=this;t.each(e,function(e,n){"function"===t.typeOf(e.instance.destroy)&&e.instance.destroy.call(e.context),i.removeInstance(n)})}}}(),t.extend(this,{initialized:!1,uid:d,type:n,mode:r.getMode(a,e.required_caps,h),shimid:d+"_container",clients:0,options:e,can:function(e,i){var n=arguments[2]||o;if("string"===t.typeOf(e)&&"undefined"===t.typeOf(i)&&(e=r.parseCaps(e)),"object"===t.typeOf(e)){for(var s in e)if(!this.can(s,e[s],n))return!1;return!0}return"function"===t.typeOf(n[e])?n[e].call(this,i):i===n[e]},getShimContainer:function(){var e,n=i.get(this.shimid);return n||(e=i.get(this.options.container)||document.body,n=document.createElement("div"),n.id=this.shimid,n.className="moxie-shim moxie-shim-"+this.type,t.extend(n.style,{position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.appendChild(n),e=null),n},getShim:function(){return c},shimExec:function(e,t){var i=[].slice.call(arguments,2);return l.getShim().exec.call(this,this.uid,e,t,i)},exec:function(e,t){var i=[].slice.call(arguments,2);return l[e]&&l[e][t]?l[e][t].apply(this,i):l.shimExec.apply(this,arguments)},destroy:function(){if(l){var e=i.get(this.shimid);e&&e.parentNode.removeChild(e),c&&c.removeAllInstances(),this.unbindAll(),delete s[this.uid],this.uid=null,d=l=c=e=null}}}),this.mode&&e.required_caps&&!this.can(e.required_caps)&&(this.mode=!1)}var o={},s={};return r.order="html5,flash,silverlight,html4",r.getRuntime=function(e){return s[e]?s[e]:!1},r.addConstructor=function(e,t){t.prototype=n.instance,o[e]=t},r.getConstructor=function(e){return o[e]||null},r.getInfo=function(e){var t=r.getRuntime(e);return t?{uid:t.uid,type:t.type,mode:t.mode,can:function(){return t.can.apply(t,arguments)}}:null},r.parseCaps=function(e){var i={};return"string"!==t.typeOf(e)?e||{}:(t.each(e.split(","),function(e){i[e]=!0}),i)},r.can=function(e,t){var i,n,o=r.getConstructor(e);return o?(i=new o({required_caps:t}),n=i.mode,i.destroy(),!!n):!1},r.thatCan=function(e,t){var i=(t||r.order).split(/\s*,\s*/);for(var n in i)if(r.can(i[n],e))return i[n];return null},r.getMode=function(e,i,n){var r=null;if("undefined"===t.typeOf(n)&&(n="browser"),i&&!t.isEmptyObj(e)){if(t.each(i,function(i,n){if(e.hasOwnProperty(n)){var o=e[n](i);if("string"==typeof o&&(o=[o]),r){if(!(r=t.arrayIntersect(r,o)))return r=!1}else r=o}}),r)return-1!==t.inArray(n,r)?n:r[0];if(r===!1)return!1}return n},r.capTrue=function(){return!0},r.capFalse=function(){return!1},r.capTest=function(e){return function(){return!!e}},r}),n("moxie/core/utils/Mime",["moxie/core/utils/Basic","moxie/core/I18n"],function(e,t){var i="application/msword,doc dot,application/pdf,pdf,application/pgp-signature,pgp,application/postscript,ps ai eps,application/rtf,rtf,application/vnd.ms-excel,xls xlb,application/vnd.ms-powerpoint,ppt pps pot,application/zip,zip,application/x-shockwave-flash,swf swfl,application/vnd.openxmlformats-officedocument.wordprocessingml.document,docx,application/vnd.openxmlformats-officedocument.wordprocessingml.template,dotx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,xlsx,application/vnd.openxmlformats-officedocument.presentationml.presentation,pptx,application/vnd.openxmlformats-officedocument.presentationml.template,potx,application/vnd.openxmlformats-officedocument.presentationml.slideshow,ppsx,application/x-javascript,js,application/json,json,audio/mpeg,mp3 mpga mpega mp2,audio/x-wav,wav,audio/x-m4a,m4a,audio/ogg,oga ogg,audio/aiff,aiff aif,audio/flac,flac,audio/aac,aac,audio/ac3,ac3,audio/x-ms-wma,wma,image/bmp,bmp,image/gif,gif,image/jpeg,jpg jpeg jpe,image/photoshop,psd,image/png,png,image/svg+xml,svg svgz,image/tiff,tiff tif,text/plain,asc txt text diff log,text/html,htm html xhtml,text/css,css,text/csv,csv,text/rtf,rtf,video/mpeg,mpeg mpg mpe m2v,video/quicktime,qt mov,video/mp4,mp4,video/x-m4v,m4v,video/x-flv,flv,video/x-ms-wmv,wmv,video/avi,avi,video/webm,webm,video/3gpp,3gpp 3gp,video/3gpp2,3g2,video/vnd.rn-realvideo,rv,video/ogg,ogv,video/x-matroska,mkv,application/vnd.oasis.opendocument.formula-template,otf,application/octet-stream,exe",n={mimes:{},extensions:{},addMimeType:function(e){var t,i,n,r=e.split(/,/);for(t=0;t<r.length;t+=2){for(n=r[t+1].split(/ /),i=0;i<n.length;i++)this.mimes[n[i]]=r[t];this.extensions[r[t]]=n}},extList2mimes:function(t,i){var n,r,o,s,a=this,u=[];for(r=0;r<t.length;r++)for(n=t[r].extensions.toLowerCase().split(/\s*,\s*/),o=0;o<n.length;o++){if("*"===n[o])return[];if(s=a.mimes[n[o]],i&&/^\w+$/.test(n[o]))u.push("."+n[o]);else if(s&&-1===e.inArray(s,u))u.push(s);else if(!s)return[]}return u},mimes2exts:function(t){var i=this,n=[];return e.each(t,function(t){if(t=t.toLowerCase(),"*"===t)return n=[],!1;var r=t.match(/^(\w+)\/(\*|\w+)$/);r&&("*"===r[2]?e.each(i.extensions,function(e,t){new RegExp("^"+r[1]+"/").test(t)&&[].push.apply(n,i.extensions[t])}):i.extensions[t]&&[].push.apply(n,i.extensions[t]))}),n},mimes2extList:function(i){var n=[],r=[];return"string"===e.typeOf(i)&&(i=e.trim(i).split(/\s*,\s*/)),r=this.mimes2exts(i),n.push({title:t.translate("Files"),extensions:r.length?r.join(","):"*"}),n},getFileExtension:function(e){var t=e&&e.match(/\.([^.]+)$/);return t?t[1].toLowerCase():""},getFileMime:function(e){return this.mimes[this.getFileExtension(e)]||""}};return n.addMimeType(i),n}),n("moxie/runtime/RuntimeClient",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/Runtime"],function(e,t,i,n){return function(){var e;i.extend(this,{connectRuntime:function(r){function o(i){var s,u;return i.length?(s=i.shift().toLowerCase(),(u=n.getConstructor(s))?(e=new u(r),e.bind("Init",function(){e.initialized=!0,setTimeout(function(){e.clients++,a.ruid=e.uid,a.trigger("RuntimeInit",e)},1)}),e.bind("Error",function(){e.destroy(),o(i)}),e.bind("Exception",function(e,i){var n=i.name+"(#"+i.code+")"+(i.message?", from: "+i.message:"");a.trigger("RuntimeError",new t.RuntimeError(t.RuntimeError.EXCEPTION_ERR,n))}),e.mode?(e.init(),void 0):(e.trigger("Error"),void 0)):(o(i),void 0)):(a.trigger("RuntimeError",new t.RuntimeError(t.RuntimeError.NOT_INIT_ERR)),e=null,void 0)}var s,a=this;if("string"===i.typeOf(r)?s=r:"string"===i.typeOf(r.ruid)&&(s=r.ruid),s){if(e=n.getRuntime(s))return a.ruid=s,e.clients++,e;throw new t.RuntimeError(t.RuntimeError.NOT_INIT_ERR)}o((r.runtime_order||n.order).split(/\s*,\s*/))},disconnectRuntime:function(){e&&--e.clients<=0&&e.destroy(),e=null},getRuntime:function(){return e&&e.uid?e:e=null},exec:function(){return e?e.exec.apply(this,arguments):null},can:function(t){return e?e.can(t):!1}})}}),n("moxie/file/FileInput",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Mime","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/I18n","moxie/runtime/Runtime","moxie/runtime/RuntimeClient"],function(e,t,i,n,r,o,s,a,u){function c(t){var o,c,d;if(-1!==e.inArray(e.typeOf(t),["string","node"])&&(t={browse_button:t}),c=n.get(t.browse_button),!c)throw new r.DOMException(r.DOMException.NOT_FOUND_ERR);d={accept:[{title:s.translate("All Files"),extensions:"*"}],multiple:!1,required_caps:!1,container:c.parentNode||document.body},t=e.extend({},d,t),"string"==typeof t.required_caps&&(t.required_caps=a.parseCaps(t.required_caps)),"string"==typeof t.accept&&(t.accept=i.mimes2extList(t.accept)),o=n.get(t.container),o||(o=document.body),"static"===n.getStyle(o,"position")&&(o.style.position="relative"),o=c=null,u.call(this),e.extend(this,{uid:e.guid("uid_"),ruid:null,shimid:null,files:null,init:function(){var i=this;i.bind("RuntimeInit",function(r,o){i.ruid=o.uid,i.shimid=o.shimid,i.bind("Ready",function(){i.trigger("Refresh")},999),i.bind("Refresh",function(){var i,r,s,a,u;s=n.get(t.browse_button),a=n.get(o.shimid),s&&(i=n.getPos(s,n.get(t.container)),r=n.getSize(s),u=parseInt(n.getStyle(s,"z-index"),10)||0,a&&e.extend(a.style,{top:i.y+"px",left:i.x+"px",width:r.w+"px",height:r.h+"px",zIndex:u+1})),a=s=null}),o.exec.call(i,"FileInput","init",t)}),i.connectRuntime(e.extend({},t,{required_caps:{select_file:!0}}))},getOption:function(e){return t[e]},setOption:function(e,n){if(t.hasOwnProperty(e)){var o=t[e];switch(e){case"accept":"string"==typeof n&&(n=i.mimes2extList(n));break;case"container":case"required_caps":throw new r.FileException(r.FileException.NO_MODIFICATION_ALLOWED_ERR)}t[e]=n,this.exec("FileInput","setOption",e,n),this.trigger("OptionChanged",e,n,o)}},disable:function(t){var i=this.getRuntime();i&&this.exec("FileInput","disable","undefined"===e.typeOf(t)?!0:t)},refresh:function(){this.trigger("Refresh")},destroy:function(){var t=this.getRuntime();t&&(t.exec.call(this,"FileInput","destroy"),this.disconnectRuntime()),"array"===e.typeOf(this.files)&&e.each(this.files,function(e){e.destroy()}),this.files=null,this.unbindAll()}}),this.handleEventProps(l)}var l=["ready","change","cancel","mouseenter","mouseleave","mousedown","mouseup"];return c.prototype=o.instance,c}),n("moxie/core/utils/Encode",[],function(){var e=function(e){return unescape(encodeURIComponent(e))},t=function(e){return decodeURIComponent(escape(e))},i=function(e,i){if("function"==typeof window.atob)return i?t(window.atob(e)):window.atob(e);var n,r,o,s,a,u,c,l,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",h=0,p=0,m="",f=[];if(!e)return e;e+="";do s=d.indexOf(e.charAt(h++)),a=d.indexOf(e.charAt(h++)),u=d.indexOf(e.charAt(h++)),c=d.indexOf(e.charAt(h++)),l=s<<18|a<<12|u<<6|c,n=255&l>>16,r=255&l>>8,o=255&l,f[p++]=64==u?String.fromCharCode(n):64==c?String.fromCharCode(n,r):String.fromCharCode(n,r,o);while(h<e.length);return m=f.join(""),i?t(m):m},n=function(t,i){if(i&&(t=e(t)),"function"==typeof window.btoa)return window.btoa(t);var n,r,o,s,a,u,c,l,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",h=0,p=0,m="",f=[];if(!t)return t;do n=t.charCodeAt(h++),r=t.charCodeAt(h++),o=t.charCodeAt(h++),l=n<<16|r<<8|o,s=63&l>>18,a=63&l>>12,u=63&l>>6,c=63&l,f[p++]=d.charAt(s)+d.charAt(a)+d.charAt(u)+d.charAt(c);while(h<t.length);m=f.join("");var g=t.length%3;return(g?m.slice(0,g-3):m)+"===".slice(g||3)};return{utf8_encode:e,utf8_decode:t,atob:i,btoa:n}}),n("moxie/file/Blob",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient"],function(e,t,i){function n(o,s){function a(t,i,o){var s,a=r[this.uid];return"string"===e.typeOf(a)&&a.length?(s=new n(null,{type:o,size:i-t}),s.detach(a.substr(t,s.size)),s):null}i.call(this),o&&this.connectRuntime(o),s?"string"===e.typeOf(s)&&(s={data:s}):s={},e.extend(this,{uid:s.uid||e.guid("uid_"),ruid:o,size:s.size||0,type:s.type||"",slice:function(e,t,i){return this.isDetached()?a.apply(this,arguments):this.getRuntime().exec.call(this,"Blob","slice",this.getSource(),e,t,i)
},getSource:function(){return r[this.uid]?r[this.uid]:null},detach:function(e){if(this.ruid&&(this.getRuntime().exec.call(this,"Blob","destroy"),this.disconnectRuntime(),this.ruid=null),e=e||"","data:"==e.substr(0,5)){var i=e.indexOf(";base64,");this.type=e.substring(5,i),e=t.atob(e.substring(i+8))}this.size=e.length,r[this.uid]=e},isDetached:function(){return!this.ruid&&"string"===e.typeOf(r[this.uid])},destroy:function(){this.detach(),delete r[this.uid]}}),s.data?this.detach(s.data):r[this.uid]=s}var r={};return n}),n("moxie/file/FileReader",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/core/Exceptions","moxie/core/EventTarget","moxie/file/Blob","moxie/runtime/RuntimeClient"],function(e,t,i,n,r,o){function s(){function n(e,n){if(this.trigger("loadstart"),this.readyState===s.LOADING)return this.trigger("error",new i.DOMException(i.DOMException.INVALID_STATE_ERR)),this.trigger("loadend"),void 0;if(!(n instanceof r))return this.trigger("error",new i.DOMException(i.DOMException.NOT_FOUND_ERR)),this.trigger("loadend"),void 0;if(this.result=null,this.readyState=s.LOADING,n.isDetached()){var o=n.getSource();switch(e){case"readAsText":case"readAsBinaryString":this.result=o;break;case"readAsDataURL":this.result="data:"+n.type+";base64,"+t.btoa(o)}this.readyState=s.DONE,this.trigger("load"),this.trigger("loadend")}else this.connectRuntime(n.ruid),this.exec("FileReader","read",e,n)}o.call(this),e.extend(this,{uid:e.guid("uid_"),readyState:s.EMPTY,result:null,error:null,readAsBinaryString:function(e){n.call(this,"readAsBinaryString",e)},readAsDataURL:function(e){n.call(this,"readAsDataURL",e)},readAsText:function(e){n.call(this,"readAsText",e)},abort:function(){this.result=null,-1===e.inArray(this.readyState,[s.EMPTY,s.DONE])&&(this.readyState===s.LOADING&&(this.readyState=s.DONE),this.exec("FileReader","abort"),this.trigger("abort"),this.trigger("loadend"))},destroy:function(){this.abort(),this.exec("FileReader","destroy"),this.disconnectRuntime(),this.unbindAll()}}),this.handleEventProps(a),this.bind("Error",function(e,t){this.readyState=s.DONE,this.error=t},999),this.bind("Load",function(){this.readyState=s.DONE},999)}var a=["loadstart","progress","load","abort","error","loadend"];return s.EMPTY=0,s.LOADING=1,s.DONE=2,s.prototype=n.instance,s}),n("plupload",["moxie/core/I18n","moxie/core/utils/Env","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Url","moxie/core/EventTarget","moxie/runtime/Runtime","moxie/file/FileInput","moxie/file/FileReader"],function(e,t,i,n,r,o,s,a,u,c){return t.global_event_dispatcher="plupload.EventTarget.instance.dispatchEvent",{VERSION:"3.1.0",STOPPED:1,STARTED:2,QUEUED:1,UPLOADING:2,FAILED:4,DONE:5,GENERIC_ERROR:-100,HTTP_ERROR:-200,IO_ERROR:-300,SECURITY_ERROR:-400,INIT_ERROR:-500,FILE_SIZE_ERROR:-600,FILE_EXTENSION_ERROR:-601,FILE_DUPLICATE_ERROR:-602,IMAGE_FORMAT_ERROR:-700,MEMORY_ERROR:-701,IMAGE_DIMENSIONS_ERROR:-702,OPTION_ERROR:-800,ua:t,typeOf:i.typeOf,inherit:i.inherit,extend:i.extend,extendImmutable:i.extendImmutable,extendIf:i.extendIf,inSeries:i.inSeries,inParallel:i.inParallel,guid:i.guid,getAll:function(e){var t,r=[];"array"!==i.typeOf(e)&&(e=[e]);for(var o=e.length;o--;)t=n.get(e[o]),t&&r.push(t);return r.length?r:null},get:n.get,each:i.each,getPos:n.getPos,getSize:n.getSize,xmlEncode:function(e){var t={"<":"lt",">":"gt","&":"amp",'"':"quot","'":"#39"},i=/[<>&\"\']/g;return e?(""+e).replace(i,function(e){return t[e]?"&"+t[e]+";":e}):e},toArray:i.toArray,inArray:i.inArray,addI18n:e.addI18n,translate:e.translate,sprintf:i.sprintf,isEmptyObj:i.isEmptyObj,hasClass:n.hasClass,addClass:n.addClass,removeClass:n.removeClass,getStyle:n.getStyle,addEvent:r.addEvent,removeEvent:r.removeEvent,removeAllEvents:r.removeAllEvents,cleanName:function(e){var t,i;for(i=[/[\300-\306]/g,"A",/[\340-\346]/g,"a",/\307/g,"C",/\347/g,"c",/[\310-\313]/g,"E",/[\350-\353]/g,"e",/[\314-\317]/g,"I",/[\354-\357]/g,"i",/\321/g,"N",/\361/g,"n",/[\322-\330]/g,"O",/[\362-\370]/g,"o",/[\331-\334]/g,"U",/[\371-\374]/g,"u"],t=0;t<i.length;t+=2)e=e.replace(i[t],i[t+1]);return e=e.replace(/\s+/g,"_"),e=e.replace(/[^a-z0-9_\-\.]+/gi,"")},buildUrl:function(e,t){var n="";return i.each(t,function(e,t){n+=(n?"&":"")+encodeURIComponent(t)+"="+encodeURIComponent(e)}),n&&(e+=(e.indexOf("?")>0?"&":"?")+n),e},formatSize:function(t){function i(e,t){return Math.round(e*Math.pow(10,t))/Math.pow(10,t)}if(t=parseInt(t,10),isNaN(t))return e.translate("N/A");var n=Math.pow(1024,4);return t>n?i(t/n,1)+" "+e.translate("tb"):t>(n/=1024)?i(t/n,1)+" "+e.translate("gb"):t>(n/=1024)?i(t/n,1)+" "+e.translate("mb"):t>1024?Math.round(t/1024)+" "+e.translate("kb"):t+" "+e.translate("b")},resolveUrl:o.resolveUrl,parseSize:i.parseSizeStr,delay:i.delay,EventTarget:s,Runtime:a,FileInput:u,FileReader:c}}),n("plupload/core/Collection",["moxie/core/utils/Basic"],function(e){var t=function(){function i(){var e;for(e in r);return r[e]}var n,r={},o=0;e.extend(this,{count:function(){return o},hasKey:function(e){return r.hasOwnProperty(e)},get:function(e){return r[e]},first:function(){for(var e in r)return r[e]},last:function(){return n},toObject:function(){return r},add:function(t,i){var s=this;return"object"!=typeof t||i?r.hasOwnProperty(t)?s.update.apply(s,arguments):(r[t]=n=i,o++,void 0):e.each(t,function(e,t){s.add(t,e)})},remove:function(e){if(this.hasKey(e)){var t=r[e];delete r[e],o--,n===t&&(n=i())}},extract:function(e){var t=this.get(e);return this.remove(e),t},shift:function(){var e,t,i=this;for(t in r)return e=r[t],i.remove(t),e},update:function(e,t){r[e]=t},each:function(t){e.each(r,t)},combineWith:function(){var i=new t;return i.add(r),e.each(arguments,function(e){e instanceof t&&i.add(e.toObject())}),i},clear:function(){r={},n=null,o=0}})};return t}),n("moxie/file/File",["moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/file/Blob"],function(e,t,i){function n(n,r){r||(r={}),i.apply(this,arguments),this.type||(this.type=t.getFileMime(r.name));var o;if(r.name)o=r.name.replace(/\\/g,"/"),o=o.substr(o.lastIndexOf("/")+1);else if(this.type){var s=this.type.split("/")[0];o=e.guid((""!==s?s:"file")+"_"),t.extensions[this.type]&&(o+="."+t.extensions[this.type][0])}e.extend(this,{name:o||e.guid("file_"),relativePath:"",lastModifiedDate:r.lastModifiedDate||(new Date).toLocaleString()})}return n.prototype=i.prototype,n}),n("moxie/file/FileDrop",["moxie/core/I18n","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/core/utils/Env","moxie/file/File","moxie/runtime/RuntimeClient","moxie/core/EventTarget","moxie/core/utils/Mime"],function(e,t,i,n,r,o,s,a,u){function c(i){var r,o=this;"string"==typeof i&&(i={drop_zone:i}),r={accept:[{title:e.translate("All Files"),extensions:"*"}],required_caps:{drag_and_drop:!0}},i="object"==typeof i?n.extend({},r,i):r,i.container=t.get(i.drop_zone)||document.body,"static"===t.getStyle(i.container,"position")&&(i.container.style.position="relative"),"string"==typeof i.accept&&(i.accept=u.mimes2extList(i.accept)),s.call(o),n.extend(o,{uid:n.guid("uid_"),ruid:null,files:null,init:function(){o.bind("RuntimeInit",function(e,t){o.ruid=t.uid,t.exec.call(o,"FileDrop","init",i),o.dispatchEvent("ready")}),o.connectRuntime(i)},destroy:function(){var e=this.getRuntime();e&&(e.exec.call(this,"FileDrop","destroy"),this.disconnectRuntime()),this.files=null,this.unbindAll()}}),this.handleEventProps(l)}var l=["ready","dragenter","dragleave","drop","error"];return c.prototype=a.instance,c}),n("plupload/core/ArrCollection",["moxie/core/utils/Basic"],function(e){var t=function(){var t=[];e.extend(this,{count:function(){return t.length},hasKey:function(e){return this.getIdx(e)>-1},get:function(e){var i=this.getIdx(e);return i>-1?t[i]:null},getIdx:function(e){for(var i=0,n=t.length;n>i;i++)if(t[i].uid===e)return i;return-1},getByIdx:function(e){return t[e]},first:function(){return t[0]},last:function(){return t[t.length-1]},add:function(e){e=arguments[1]||e;var i=this.getIdx(e.uid);return i>-1?(t[i]=e,i):(t.push(e),t.length-1)},remove:function(e){return!!this.extract(e)},splice:function(i,n){return i="undefinded"===e.typeOf(i)?0:Math.max(i,0),n="undefinded"!==e.typeOf(n)&&i+n<t.length?n:t.length-i,t.splice(i,n)},extract:function(e){var i=this.getIdx(e);return i>-1?t.splice(i,1):null},shift:function(){return t.shift()},update:function(e,i){var n=this.getIdx(e);return n>-1?(t[n]=i,!0):!1},each:function(i){e.each(t,i)},combineWith:function(){return Array.prototype.concat.apply(this.toArray(),arguments)},sort:function(e){t.sort(e||function(e,t){return e.priority-t.priority})},clear:function(){t=[]},toObject:function(){for(var e={},i=0,n=t.length;n>i;i++)e[t[i].uid]=t[i];return e},toArray:function(){return Array.prototype.slice.call(t)}})};return t}),n("plupload/core/Optionable",["moxie/core/utils/Basic","moxie/core/EventTarget"],function(e,t){return function(t){function i(){t.apply(this,arguments),this._options={}}return e.inherit(i,t),e.extend(i.prototype,{setOption:function(t,i,n){var r,o=this;return"object"==typeof t?(n=i,e.each(t,function(e,t){o.setOption(t,e,n)}),void 0):((!n||o._options.hasOwnProperty(t))&&(r=e.clone(o._options[t]),"object"===e.typeOf(i)&&"object"===e.typeOf(o._options[t])?e.extend(o._options[t],i):o._options[t]=i,o.trigger("OptionChanged",t,i,r)),void 0)},getOption:function(t){if(!t)return this._options;var i=this._options[t];return e.inArray(e.typeOf(i),["array","object"])>-1?e.extendImmutable({},i):i},setOptions:function(e,t){"object"==typeof e&&this.setOption(e,t)},getOptions:function(){return this.getOption()}}),i}(t)}),n("plupload/core/Queueable",["moxie/core/utils/Env","moxie/core/utils/Basic","plupload/core/Optionable"],function(e,t,i){return function(e){function i(){e.apply(this,arguments),this.uid=t.guid(),this.state=i.IDLE,this.processed=0,this.total=0,this.percent=0,this.retries=0,this.priority=0,this.startedTimestamp=0,this.processedTimestamp=0}return i.IDLE=1,i.PROCESSING=2,i.PAUSED=6,i.RESUMED=7,i.DONE=5,i.FAILED=4,i.DESTROYED=8,t.inherit(i,e),t.extend(i.prototype,{start:function(){var e=this.state;return this.state===i.PROCESSING?!1:(this.startedTimestamp||(this.startedTimestamp=+new Date),this.state===i.IDLE?(this.state=i.PROCESSING,this.trigger("statechanged",this.state,e),this.pause(),t.delay.call(this,function(){this.trigger("beforestart")&&this.resume()}),!1):(this.state=i.PROCESSING,this.trigger("statechanged",this.state,e),this.trigger("started"),!0))},pause:function(){var e=this.state;return this.state!==i.PROCESSING?!1:(this.processed=this.percent=0,this.loaded=this.processed,this.state=i.PAUSED,this.trigger("statechanged",this.state,e),this.trigger("paused"),!0)},resume:function(){var e=this.state;return this.state!==i.PAUSED&&this.state!==i.RESUMED?!1:(this.state=i.RESUMED,this.trigger("statechanged",this.state,e),this.trigger("resumed"),!0)},stop:function(){var e=this.state;return this.state===i.IDLE?!1:(this.processed=this.percent=0,this.loaded=this.processed,this.startedTimestamp=0,this.state=i.IDLE,this.trigger("statechanged",this.state,e),this.trigger("stopped"),!0)},done:function(e){var t=this.state;return this.state===i.DONE?!1:(this.processed=this.total,this.loaded=this.processed,this.percent=100,this.processedTimestamp=+new Date,this.state=i.DONE,this.trigger("statechanged",this.state,t),this.trigger("done",e),this.trigger("processed"),!0)},failed:function(e){var t=this.state;return this.state===i.FAILED?!1:(this.processed=this.percent=0,this.loaded=this.processed,this.processedTimestamp=+new Date,this.state=i.FAILED,this.trigger("statechanged",this.state,t),this.trigger("failed",e),this.trigger("processed"),!0)},progress:function(e,t){t&&(this.total=t),this.processed=Math.min(e,this.total),this.loaded=this.processed,this.percent=Math.ceil(100*(this.processed/this.total)),this.trigger({type:"progress",loaded:this.processed,total:this.total})},destroy:function(){var e=this.state;return this.state===i.DESTROYED?!1:(this.state=i.DESTROYED,this.trigger("statechanged",this.state,e),this.trigger("destroy"),this.unbindAll(),!0)}}),i}(i)}),n("plupload/core/Stats",[],function(){return function(){var e=this;e.size=0,e.total=0,e.loaded=0,e.uploaded=0,e.done=0,e.failed=0,e.queued=0,e.paused=0,e.processing=0,e.paused=0,e.percent=0,e.bytesPerSec=0,e.processedPerSec=0,e.reset=function(){e.size=e.total=e.loaded=e.processed=e.uploaded=e.done=e.failed=e.queued=e.processing=e.paused=e.percent=e.bytesPerSec=e.processedPerSec=0}}}),n("plupload/core/Queue",["moxie/core/utils/Basic","plupload/core/ArrCollection","plupload/core/Queueable","plupload/core/Stats"],function(e,t,i,n){return function(r){function o(i){r.apply(this,arguments),this._queue=new t,this.stats=new n,this._options=e.extend({},this._options,{max_slots:1,max_retries:0,auto_start:!1,finish_active:!1},i)}function s(){var e;return this.forEachItem(function(t){return t.state===i.IDLE||t.state===i.RESUMED?(e=t,!1):void 0}),e?e:null}function a(){var e;return this.state!==i.PROCESSING&&this.state!==i.PAUSED?!1:this.stats.processing<this.getOption("max_slots")&&(e=s.call(this))?(e.setOptions(this.getOptions()),e.start()):!1}return e.inherit(o,r),e.extend(o.prototype,{count:function(){return this._queue.count()},start:function(){var e=this.state;return this.state===i.PROCESSING?!1:(this.startedTimestamp||(this.startedTimestamp=+new Date),this.state=i.PROCESSING,this.trigger("statechanged",this.state,e),this.trigger("started"),a.call(this))},pause:function(){return o.super.pause.call(this)?(this.forEachItem(function(e){e.pause()}),void 0):!1},stop:function(){return!o.super.stop.call(this)||this.getOption("finish_active")?!1:(this.isActive()&&this.forEachItem(function(e){e.stop()}),void 0)},forEachItem:function(e){this._queue.each(e)},getItem:function(e){return this._queue.get(e)},addItem:function(t){var n=this;t.bind("Started",function(){n.calcStats()&&e.delay.call(n,a)}),t.bind("Resumed",function(){n.start()}),t.bind("Paused",function(){n.calcStats()&&e.delay.call(n,function(){a.call(n)||n.stats.processing||n.pause()})}),t.bind("Processed Stopped",function(){n.calcStats()&&e.delay.call(n,function(){a.call(n)||this.stats.processing||this.stats.paused||n.stop()})}),t.bind("Progress",function(){n.calcStats()&&n.trigger("Progress",n.stats.processed,n.stats.total,n.stats)}),t.bind("Failed",function(){n.getOption("max_retries")&&this.retries<n.getOption("max_retries")&&(this.stop(),this.retries++)}),this._queue.add(t.uid,t),this.calcStats(),t.trigger("Queued"),(n.getOption("auto_start")||n.state===i.PAUSED)&&e.delay.call(this,this.start)},extractItem:function(e){var t=this._queue.get(e);return t&&(this.stopItem(t.uid),this._queue.remove(e),this.calcStats()),t},removeItem:function(e){var t=this.extractItem(e);return t?(t.destroy(),!0):!1},stopItem:function(e){var t=this._queue.get(e);return t?t.stop():!1},pauseItem:function(e){var t=this._queue.get(e);return t?t.pause():!1},resumeItem:function(t){var i=this._queue.get(t);return i?(e.delay.call(this,function(){this.start()}),i.resume()):!1},splice:function(e,t){return this._queue.splice(e,t)},isActive:function(){return this.stats&&(this.stats.processing||this.stats.paused)},countSpareSlots:function(){return Math.max(this.getOption("max_slots")-this.stats.processing,0)},toArray:function(){return this._queue.toArray()},clear:function(){var e=this;return e.state!==i.IDLE?(e.bindOnce("Stopped",function(){e.clear()}),e.stop()):(e._queue.clear(),e.stats.reset(),void 0)},calcStats:function(){var e=this,t=e.stats,n=0,r=0;return t?(t.reset(),e.forEachItem(function(o){switch(o.state){case i.DONE:t.done++,t.uploaded=t.done;break;case i.FAILED:t.failed++;break;case i.PROCESSING:t.processing++;break;case i.PAUSED:t.paused++;break;default:t.queued++}n+=o.processed,(!o.processedTimestamp||o.processedTimestamp>e.startedTimestamp)&&(r+=n),t.processedPerSec=Math.ceil(r/((+new Date-e.startedTimestamp||1)/1e3)),t.processed=n,t.total+=o.total,t.total&&(t.percent=Math.ceil(100*(t.processed/t.total)))}),e.percent=t.percent,t.loaded=t.processed,t.size=t.total,t.bytesPerSec=t.processedPerSec,!0):!1},destroy:function(){var t=this;return t.state===i.DESTROYED?!1:t.state!==i.IDLE?(t.bindOnce("Stopped",function(){e.delay.call(t,t.destroy)}),t.stop()):(t.clear(),o.super.destroy.call(this),t._queue=t.stats=null,!0)}}),o}(i)}),n("plupload/QueueUpload",["moxie/core/utils/Basic","plupload/core/Queue"],function(e,t){return function(i){function n(e){t.call(this,{max_slots:1,max_retries:0,auto_start:!1,finish_active:!1,url:!1,chunk_size:0,multipart:!0,http_method:"POST",params:{},headers:!1,file_data_name:"file",send_file_name:!0,stop_on_fail:!0}),this.setOption=function(e,t){"object"!=typeof e&&"max_upload_slots"==e&&(e="max_slots"),n.prototype.setOption.call(this,e,t,!0)},this.setOptions(e)}return e.inherit(n,i),n}(t)}),n("plupload/QueueResize",["moxie/core/utils/Basic","plupload/core/Queue"],function(e,t){return function(i){function n(e){t.call(this,{max_slots:1,max_retries:0,auto_start:!1,finish_active:!1,resize:{}}),this.setOption=function(e,t){"object"!=typeof e&&"max_resize_slots"==e&&(e="max_slots"),n.prototype.setOption.call(this,e,t,!0)},this.setOptions(e)}return e.inherit(n,i),n}(t)}),n("moxie/runtime/RuntimeTarget",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(e,t,i){function n(){this.uid=e.guid("uid_"),t.call(this),this.destroy=function(){this.disconnectRuntime(),this.unbindAll()}}return n.prototype=i.instance,n}),n("moxie/file/FileReaderSync",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/utils/Encode"],function(e,t,i){return function(){function n(e,t){if(!t.isDetached()){var n=this.connectRuntime(t.ruid).exec.call(this,"FileReaderSync","read",e,t);return this.disconnectRuntime(),n}var r=t.getSource();switch(e){case"readAsBinaryString":return r;case"readAsDataURL":return"data:"+t.type+";base64,"+i.btoa(r);case"readAsText":for(var o="",s=0,a=r.length;a>s;s++)o+=String.fromCharCode(r[s]);return o}}t.call(this),e.extend(this,{uid:e.guid("uid_"),readAsBinaryString:function(e){return n.call(this,"readAsBinaryString",e)},readAsDataURL:function(e){return n.call(this,"readAsDataURL",e)},readAsText:function(e){return n.call(this,"readAsText",e)}})}}),n("moxie/xhr/FormData",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/file/Blob"],function(e,t,i){function n(){var e,n=[];t.extend(this,{append:function(r,o){var s=this,a=t.typeOf(o);o instanceof i?e={name:r,value:o}:"array"===a?(r+="[]",t.each(o,function(e){s.append(r,e)})):"object"===a?t.each(o,function(e,t){s.append(r+"["+t+"]",e)}):"null"===a||"undefined"===a||"number"===a&&isNaN(o)?s.append(r,"false"):n.push({name:r,value:o.toString()})},hasBlob:function(){return!!this.getBlob()},getBlob:function(){return e&&e.value||null},getBlobName:function(){return e&&e.name||null},each:function(i){t.each(n,function(e){i(e.value,e.name)}),e&&i(e.value,e.name)},destroy:function(){e=null,n=[]}})}return n}),n("moxie/xhr/XMLHttpRequest",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/utils/Encode","moxie/core/utils/Url","moxie/runtime/Runtime","moxie/runtime/RuntimeTarget","moxie/file/Blob","moxie/file/FileReaderSync","moxie/xhr/FormData","moxie/core/utils/Env","moxie/core/utils/Mime"],function(e,t,i,n,r,o,s,a,u,c,l,d){function h(){this.uid=e.guid("uid_")}function p(){function i(e,t){return I.hasOwnProperty(e)?1===arguments.length?l.can("define_property")?I[e]:O[e]:(l.can("define_property")?I[e]=t:O[e]=t,void 0):void 0}function u(t){function n(){b&&(b.destroy(),b=null),a.dispatchEvent("loadend"),a=null}function r(r){b.bind("LoadStart",function(e){i("readyState",p.LOADING),a.dispatchEvent("readystatechange"),a.dispatchEvent(e),L&&a.upload.dispatchEvent(e)}),b.bind("Progress",function(e){i("readyState")!==p.LOADING&&(i("readyState",p.LOADING),a.dispatchEvent("readystatechange")),a.dispatchEvent(e)}),b.bind("UploadProgress",function(e){L&&a.upload.dispatchEvent({type:"progress",lengthComputable:!1,total:e.total,loaded:e.loaded})}),b.bind("Load",function(t){i("readyState",p.DONE),i("status",Number(r.exec.call(b,"XMLHttpRequest","getStatus")||0)),i("statusText",m[i("status")]||""),i("response",r.exec.call(b,"XMLHttpRequest","getResponse",i("responseType"))),~e.inArray(i("responseType"),["text",""])?i("responseText",i("response")):"document"===i("responseType")&&i("responseXML",i("response")),k=r.exec.call(b,"XMLHttpRequest","getAllResponseHeaders"),a.dispatchEvent("readystatechange"),i("status")>0?(L&&a.upload.dispatchEvent(t),a.dispatchEvent(t)):(M=!0,a.dispatchEvent("error")),n()}),b.bind("Abort",function(e){a.dispatchEvent(e),n()}),b.bind("Error",function(e){M=!0,i("readyState",p.DONE),a.dispatchEvent("readystatechange"),F=!0,a.dispatchEvent(e),n()}),r.exec.call(b,"XMLHttpRequest","send",{url:x,method:v,async:S,user:y,password:_,headers:A,mimeType:D,encoding:T,responseType:a.responseType,withCredentials:a.withCredentials,options:U},t)}var a=this;E=(new Date).getTime(),b=new s,"string"==typeof U.required_caps&&(U.required_caps=o.parseCaps(U.required_caps)),U.required_caps=e.extend({},U.required_caps,{return_response_type:a.responseType}),t instanceof c&&(U.required_caps.send_multipart=!0),e.isEmptyObj(A)||(U.required_caps.send_custom_headers=!0),P||(U.required_caps.do_cors=!0),U.ruid?r(b.connectRuntime(U)):(b.bind("RuntimeInit",function(e,t){r(t)}),b.bind("RuntimeError",function(e,t){a.dispatchEvent("RuntimeError",t)}),b.connectRuntime(U))}function g(){i("responseText",""),i("responseXML",null),i("response",null),i("status",0),i("statusText",""),E=w=null}var x,v,y,_,E,w,b,R,O=this,I={timeout:0,readyState:p.UNSENT,withCredentials:!1,status:0,statusText:"",responseType:"",responseXML:null,responseText:null,response:null},S=!0,A={},T=null,D=null,C=!1,N=!1,L=!1,F=!1,M=!1,P=!1,B=null,z=null,U={},k="";e.extend(this,I,{uid:e.guid("uid_"),upload:new h,open:function(o,s,a,u,c){var l;if(!o||!s)throw new t.DOMException(t.DOMException.SYNTAX_ERR);if(/[\u0100-\uffff]/.test(o)||n.utf8_encode(o)!==o)throw new t.DOMException(t.DOMException.SYNTAX_ERR);if(~e.inArray(o.toUpperCase(),["CONNECT","DELETE","GET","HEAD","OPTIONS","POST","PUT","TRACE","TRACK"])&&(v=o.toUpperCase()),~e.inArray(v,["CONNECT","TRACE","TRACK"]))throw new t.DOMException(t.DOMException.SECURITY_ERR);if(s=n.utf8_encode(s),l=r.parseUrl(s),P=r.hasSameOrigin(l),x=r.resolveUrl(s),(u||c)&&!P)throw new t.DOMException(t.DOMException.INVALID_ACCESS_ERR);if(y=u||l.user,_=c||l.pass,S=a||!0,S===!1&&(i("timeout")||i("withCredentials")||""!==i("responseType")))throw new t.DOMException(t.DOMException.INVALID_ACCESS_ERR);C=!S,N=!1,A={},g.call(this),i("readyState",p.OPENED),this.dispatchEvent("readystatechange")},setRequestHeader:function(r,o){var s=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","content-transfer-encoding","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","user-agent","via"];if(i("readyState")!==p.OPENED||N)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(/[\u0100-\uffff]/.test(r)||n.utf8_encode(r)!==r)throw new t.DOMException(t.DOMException.SYNTAX_ERR);return r=e.trim(r).toLowerCase(),~e.inArray(r,s)||/^(proxy\-|sec\-)/.test(r)?!1:(A[r]?A[r]+=", "+o:A[r]=o,!0)},hasRequestHeader:function(e){return e&&A[e.toLowerCase()]||!1},getAllResponseHeaders:function(){return k||""},getResponseHeader:function(t){return t=t.toLowerCase(),M||~e.inArray(t,["set-cookie","set-cookie2"])?null:k&&""!==k&&(R||(R={},e.each(k.split(/\r\n/),function(t){var i=t.split(/:\s+/);2===i.length&&(i[0]=e.trim(i[0]),R[i[0].toLowerCase()]={header:i[0],value:e.trim(i[1])})})),R.hasOwnProperty(t))?R[t].header+": "+R[t].value:null},overrideMimeType:function(n){var r,o;if(~e.inArray(i("readyState"),[p.LOADING,p.DONE]))throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(n=e.trim(n.toLowerCase()),/;/.test(n)&&(r=n.match(/^([^;]+)(?:;\scharset\=)?(.*)$/))&&(n=r[1],r[2]&&(o=r[2])),!d.mimes[n])throw new t.DOMException(t.DOMException.SYNTAX_ERR);B=n,z=o},send:function(i,r){if(U="string"===e.typeOf(r)?{ruid:r}:r?r:{},this.readyState!==p.OPENED||N)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(i instanceof a)U.ruid=i.ruid,D=i.type||"application/octet-stream";else if(i instanceof c){if(i.hasBlob()){var o=i.getBlob();U.ruid=o.ruid,D=o.type||"application/octet-stream"}}else"string"==typeof i&&(T="UTF-8",D="text/plain;charset=UTF-8",i=n.utf8_encode(i));this.withCredentials||(this.withCredentials=U.required_caps&&U.required_caps.send_browser_cookies&&!P),L=!C&&this.upload.hasEventListener(),M=!1,F=!i,C||(N=!0),u.call(this,i)},abort:function(){if(M=!0,C=!1,~e.inArray(i("readyState"),[p.UNSENT,p.OPENED,p.DONE]))i("readyState",p.UNSENT);else{if(i("readyState",p.DONE),N=!1,!b)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);b.getRuntime().exec.call(b,"XMLHttpRequest","abort",F),F=!0}},destroy:function(){b&&("function"===e.typeOf(b.destroy)&&b.destroy(),b=null),this.unbindAll(),this.upload&&(this.upload.unbindAll(),this.upload=null)}}),this.handleEventProps(f.concat(["readystatechange"])),this.upload.handleEventProps(f)}var m={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"Reserved",307:"Temporary Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Requested Range Not Satisfiable",417:"Expectation Failed",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",426:"Upgrade Required",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",510:"Not Extended"};h.prototype=i.instance;var f=["loadstart","progress","abort","error","load","timeout","loadend"];return p.UNSENT=0,p.OPENED=1,p.HEADERS_RECEIVED=2,p.LOADING=3,p.DONE=4,p.prototype=i.instance,p}),n("plupload/ChunkUploader",["moxie/core/utils/Basic","plupload/core/Collection","plupload/core/Queueable","moxie/xhr/XMLHttpRequest","moxie/xhr/FormData"],function(e,t,i,n,r){function o(t){function s(t,i){var n="";return e.each(i,function(e,t){n+=(n?"&":"")+encodeURIComponent(t)+"="+encodeURIComponent(e)}),n&&(t+=(t.indexOf("?")>0?"&":"?")+n),t}var a;i.call(this),this._options={file_data_name:"file",headers:!1,http_method:"POST",multipart:!0,params:{},send_file_name:!0,url:!1},e.extend(this,{start:function(){var u,c,l=this,d=l._options;if(this.state===i.PROCESSING)return!1;a=new n,a.upload&&(a.upload.onprogress=function(e){l.progress(e.loaded,e.total)}),a.onload=function(){var e={response:this.responseText,status:this.status,responseHeaders:this.getAllResponseHeaders()};return this.status>=400?l.failed(e):(l.done(e),void 0)},a.onerror=function(){l.failed()},a.onloadend=function(){setTimeout(function(){a&&(a.destroy(),a=null)},1)};try{u=d.multipart?d.url:s(d.url,d.params),a.open(d.http_method,u,!0),e.isEmptyObj(d.headers)||e.each(d.headers,function(e,t){a.setRequestHeader(t,e)}),d.multipart?(c=new r,e.isEmptyObj(d.params)||e.each(d.params,function(e,t){c.append(t,e)}),c.append(d.file_data_name,t),a.send(c)):((e.isEmptyObj(d.headers)||!a.hasRequestHeader("content-type"))&&a.setRequestHeader("content-type","application/octet-stream"),a.send(t)),o.prototype.start.call(this)}catch(h){l.failed()}},stop:function(){a&&(a.abort(),a.destroy(),a=null),o.prototype.stop.call(this)},setOption:function(e,t){o.prototype.setOption.call(this,e,t,!0)},setOptions:function(e){o.prototype.setOption.call(this,e,!0)},destroy:function(){this.stop(),o.prototype.destroy.call(this)}})}return e.inherit(o,i),o}),n("plupload/FileUploader",["moxie/core/utils/Basic","plupload/core/Collection","plupload/core/Queueable","plupload/ChunkUploader"],function(e,t,i,n){function r(o,s){function a(){var e=0;return c.each(function(t){t.state===i.DONE&&(e+=t.end-t.start)}),e}function u(){for(var e=0;l>e&&c.hasKey(e);)e++;return e}var c=new t,l=1;i.call(this),this._options={chunk_size:0,params:{},send_file_name:!0,stop_on_fail:!0},e.extend(this,{name:o.name,start:function(){var e,t=this,a=this.state;return this.state===i.PROCESSING?!1:this.state!==i.IDLE||r.prototype.start.call(t)?(t._options.send_file_name&&(t._options.params.name=t.target_name||t.name),t._options.chunk_size?(l=Math.ceil(o.size/t._options.chunk_size),t.uploadChunk(!1,!0)):(e=new n(o),e.bind("progress",function(e){t.progress(e.loaded,e.total)}),e.bind("done",function(e,i){t.done(i)}),e.bind("failed",function(e,i){t.failed(i)}),e.setOptions(t._options),s.addItem(e)),this.state=i.PROCESSING,this.trigger("statechanged",this.state,a),this.trigger("started"),!0):!1},uploadChunk:function(t,r){var d,h,p=this,m=this.getOption("chunk_size"),f={};return f.seq=parseInt(t,10)||u(),f.start=f.seq*m,f.end=Math.min(f.start+m,o.size),f.total=o.size,f.start<0||f.start>=o.size?!1:(h=e.extendImmutable({},this.getOptions(),{params:{chunk:f.seq,chunks:l}}),d=new n(o.slice(f.start,f.end,o.type)),d.bind("progress",function(e){p.progress(a()+e.loaded,o.size)}),d.bind("failed",function(t,n){c.add(f.seq,e.extend({state:i.FAILED},f)),p.trigger("chunkuploadfailed",e.extendImmutable({},f,n)),h.stop_on_fail&&p.failed(n)}),d.bind("done",function(t,n){c.add(f.seq,e.extend({state:i.DONE},f)),p.trigger("chunkuploaded",e.extendImmutable({},f,n)),a()>=o.size?(p.progress(o.size,o.size),p.done(n)):r&&e.delay(function(){p.uploadChunk(u(),r)})}),d.bind("processed",function(){this.destroy()}),d.setOptions(h),c.add(f.seq,e.extend({state:i.PROCESSING},f)),s.addItem(d),r&&s.countSpareSlots()&&p.uploadChunk(u(),r),!0)},destroy:function(){r.prototype.destroy.call(this),c.clear()}})}return e.inherit(r,i),r}),n("moxie/runtime/Transporter",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(e,t,i,n){function r(){function n(){l=d=0,c=this.result=null}function o(t,i){var n=this;u=i,n.bind("TransportingProgress",function(t){d=t.loaded,l>d&&-1===e.inArray(n.state,[r.IDLE,r.DONE])&&s.call(n)},999),n.bind("TransportingComplete",function(){d=l,n.state=r.DONE,c=null,n.result=u.exec.call(n,"Transporter","getAsBlob",t||"")},999),n.state=r.BUSY,n.trigger("TransportingStarted"),s.call(n)}function s(){var e,i=this,n=l-d;h>n&&(h=n),e=t.btoa(c.substr(d,h)),u.exec.call(i,"Transporter","receive",e,l)}var a,u,c,l,d,h;i.call(this),e.extend(this,{uid:e.guid("uid_"),state:r.IDLE,result:null,transport:function(t,i,r){var s=this;if(r=e.extend({chunk_size:204798},r),(a=r.chunk_size%3)&&(r.chunk_size+=3-a),h=r.chunk_size,n.call(this),c=t,l=t.length,"string"===e.typeOf(r)||r.ruid)o.call(s,i,this.connectRuntime(r));else{var u=function(e,t){s.unbind("RuntimeInit",u),o.call(s,i,t)};this.bind("RuntimeInit",u),this.connectRuntime(r)}},abort:function(){var e=this;e.state=r.IDLE,u&&(u.exec.call(e,"Transporter","clear"),e.trigger("TransportingAborted")),n.call(e)},destroy:function(){this.unbindAll(),u=null,this.disconnectRuntime(),n.call(this)}})}return r.IDLE=0,r.BUSY=1,r.DONE=2,r.prototype=n.instance,r}),n("moxie/image/Image",["moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/file/FileReaderSync","moxie/xhr/XMLHttpRequest","moxie/runtime/Runtime","moxie/runtime/RuntimeClient","moxie/runtime/Transporter","moxie/core/utils/Env","moxie/core/EventTarget","moxie/file/Blob","moxie/file/File","moxie/core/utils/Encode"],function(e,t,i,n,r,o,s,a,u,c,l,d,h){function p(){function n(e){try{return e||(e=this.exec("Image","getInfo")),this.size=e.size,this.width=e.width,this.height=e.height,this.type=e.type,this.meta=e.meta,""===this.name&&(this.name=e.name),!0
}catch(t){return this.trigger("error",t.code),!1}}function c(t){var n=e.typeOf(t);try{if(t instanceof p){if(!t.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);f.apply(this,arguments)}else if(t instanceof l){if(!~e.inArray(t.type,["image/jpeg","image/png"]))throw new i.ImageError(i.ImageError.WRONG_FORMAT);g.apply(this,arguments)}else if(-1!==e.inArray(n,["blob","file"]))c.call(this,new d(null,t),arguments[1]);else if("string"===n)"data:"===t.substr(0,5)?c.call(this,new l(null,{data:t}),arguments[1]):x.apply(this,arguments);else{if("node"!==n||"img"!==t.nodeName.toLowerCase())throw new i.DOMException(i.DOMException.TYPE_MISMATCH_ERR);c.call(this,t.src,arguments[1])}}catch(r){this.trigger("error",r.code)}}function f(t,i){var n=this.connectRuntime(t.ruid);this.ruid=n.uid,n.exec.call(this,"Image","loadFromImage",t,"undefined"===e.typeOf(i)?!0:i)}function g(t,i){function n(e){r.ruid=e.uid,e.exec.call(r,"Image","loadFromBlob",t)}var r=this;r.name=t.name||"",t.isDetached()?(this.bind("RuntimeInit",function(e,t){n(t)}),i&&"string"==typeof i.required_caps&&(i.required_caps=o.parseCaps(i.required_caps)),this.connectRuntime(e.extend({required_caps:{access_image_binary:!0,resize_image:!0}},i))):n(this.connectRuntime(t.ruid))}function x(e,t){var i,n=this;i=new r,i.open("get",e),i.responseType="blob",i.onprogress=function(e){n.trigger(e)},i.onload=function(){g.call(n,i.response,!0)},i.onerror=function(e){n.trigger(e)},i.onloadend=function(){i.destroy()},i.bind("RuntimeError",function(e,t){n.trigger("RuntimeError",t)}),i.send(null,t)}s.call(this),e.extend(this,{uid:e.guid("uid_"),ruid:null,name:"",size:0,width:0,height:0,type:"",meta:{},clone:function(){this.load.apply(this,arguments)},load:function(){c.apply(this,arguments)},resize:function(t){var n,r,o=this,s={x:0,y:0,width:o.width,height:o.height},a=e.extendIf({width:o.width,height:o.height,type:o.type||"image/jpeg",quality:90,crop:!1,fit:!0,preserveHeaders:!0,resample:"default",multipass:!0},t);try{if(!o.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);if(o.width>p.MAX_RESIZE_WIDTH||o.height>p.MAX_RESIZE_HEIGHT)throw new i.ImageError(i.ImageError.MAX_RESOLUTION_ERR);if(n=o.meta&&o.meta.tiff&&o.meta.tiff.Orientation||1,-1!==e.inArray(n,[5,6,7,8])){var u=a.width;a.width=a.height,a.height=u}if(a.crop){switch(r=Math.max(a.width/o.width,a.height/o.height),t.fit?(s.width=Math.min(Math.ceil(a.width/r),o.width),s.height=Math.min(Math.ceil(a.height/r),o.height),r=a.width/s.width):(s.width=Math.min(a.width,o.width),s.height=Math.min(a.height,o.height),r=1),"boolean"==typeof a.crop&&(a.crop="cc"),a.crop.toLowerCase().replace(/_/,"-")){case"rb":case"right-bottom":s.x=o.width-s.width,s.y=o.height-s.height;break;case"cb":case"center-bottom":s.x=Math.floor((o.width-s.width)/2),s.y=o.height-s.height;break;case"lb":case"left-bottom":s.x=0,s.y=o.height-s.height;break;case"lt":case"left-top":s.x=0,s.y=0;break;case"ct":case"center-top":s.x=Math.floor((o.width-s.width)/2),s.y=0;break;case"rt":case"right-top":s.x=o.width-s.width,s.y=0;break;case"rc":case"right-center":case"right-middle":s.x=o.width-s.width,s.y=Math.floor((o.height-s.height)/2);break;case"lc":case"left-center":case"left-middle":s.x=0,s.y=Math.floor((o.height-s.height)/2);break;case"cc":case"center-center":case"center-middle":default:s.x=Math.floor((o.width-s.width)/2),s.y=Math.floor((o.height-s.height)/2)}s.x=Math.max(s.x,0),s.y=Math.max(s.y,0)}else r=Math.min(a.width/o.width,a.height/o.height);this.exec("Image","resize",s,r,a)}catch(c){o.trigger("error",c.code)}},downsize:function(t){var i,n={width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90,crop:!1,preserveHeaders:!0,resample:"default"};i="object"==typeof t?e.extend(n,t):e.extend(n,{width:arguments[0],height:arguments[1],crop:arguments[2],preserveHeaders:arguments[3]}),this.resize(i)},crop:function(e,t,i){this.downsize(e,t,!0,i)},getAsCanvas:function(){if(!u.can("create_canvas"))throw new i.RuntimeError(i.RuntimeError.NOT_SUPPORTED_ERR);return this.exec("Image","getAsCanvas")},getAsBlob:function(e,t){if(!this.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);return this.exec("Image","getAsBlob",e||"image/jpeg",t||90)},getAsDataURL:function(e,t){if(!this.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);return this.exec("Image","getAsDataURL",e||"image/jpeg",t||90)},getAsBinaryString:function(e,t){var i=this.getAsDataURL(e,t);return h.atob(i.substring(i.indexOf("base64,")+7))},embed:function(n,r){function o(t,r){var o=this;if(u.can("create_canvas")){var l=o.getAsCanvas();if(l)return n.appendChild(l),l=null,o.destroy(),c.trigger("embedded"),void 0}var d=o.getAsDataURL(t,r);if(!d)throw new i.ImageError(i.ImageError.WRONG_FORMAT);if(u.can("use_data_uri_of",d.length))n.innerHTML='<img src="'+d+'" width="'+o.width+'" height="'+o.height+'" />',o.destroy(),c.trigger("embedded");else{var p=new a;p.bind("TransportingComplete",function(){s=c.connectRuntime(this.result.ruid),c.bind("Embedded",function(){e.extend(s.getShimContainer().style,{top:"0px",left:"0px",width:o.width+"px",height:o.height+"px"}),s=null},999),s.exec.call(c,"ImageView","display",this.result.uid,width,height),o.destroy()}),p.transport(h.atob(d.substring(d.indexOf("base64,")+7)),t,{required_caps:{display_media:!0},runtime_order:"flash,silverlight",container:n})}}var s,c=this,l=e.extend({width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90},r);try{if(!(n=t.get(n)))throw new i.DOMException(i.DOMException.INVALID_NODE_TYPE_ERR);if(!this.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);this.width>p.MAX_RESIZE_WIDTH||this.height>p.MAX_RESIZE_HEIGHT;var d=new p;return d.bind("Resize",function(){o.call(this,l.type,l.quality)}),d.bind("Load",function(){this.downsize(l)}),this.meta.thumb&&this.meta.thumb.width>=l.width&&this.meta.thumb.height>=l.height?d.load(this.meta.thumb.data):d.clone(this,!1),d}catch(m){this.trigger("error",m.code)}},destroy:function(){this.ruid&&(this.getRuntime().exec.call(this,"Image","destroy"),this.disconnectRuntime()),this.meta&&this.meta.thumb&&this.meta.thumb.data.destroy(),this.unbindAll()}}),this.handleEventProps(m),this.bind("Load Resize",function(){return n.call(this)},999)}var m=["progress","load","error","resize","embedded"];return p.MAX_RESIZE_WIDTH=8192,p.MAX_RESIZE_HEIGHT=8192,p.prototype=c.instance,p}),n("plupload/ImageResizer",["plupload","plupload/core/Queueable","moxie/image/Image"],function(e,t,i){function n(e){t.call(this),this._options={width:0,height:0,type:"image/jpeg",quality:90,crop:!1,fit:!0,preserveHeaders:!0,resample:"default",multipass:!0},this.setOption=function(e){("object"==typeof e||this._options.hasOwnProperty(e))&&n.prototype.setOption.apply(this,arguments)},this.start=function(t){var n,r=this;t&&this.setOptions(t.resize),n=new i,n.bind("load",function(){this.resize(r.getOptions())}),n.bind("resize",function(){r.done(this.getAsBlob(r.getOption("type"),r.getOption("quality"))),this.destroy()}),n.bind("error",function(){r.failed(),this.destroy()}),n.load(e)}}return e.inherit(n,t),e.Image=i,n}),n("plupload/File",["plupload","plupload/core/Queueable","plupload/FileUploader","plupload/ImageResizer"],function(e,t,i,n){function r(a,u,c){t.call(this),e.extend(this,{id:this.uid,name:a.name,target_name:null,type:a.type,size:a.size,origSize:a.size,start:function(){var i=this.state;return this.state===t.PROCESSING?!1:(this.state=t.PROCESSING,this.trigger("statechanged",this.state,i),this.trigger("started"),!e.isEmptyObj(this._options.resize)&&o(this.type)&&s(a,"send_binary_string")?this.resizeAndUpload():this.upload(),!0)},getSource:function(){return a},getNative:function(){return this.getFile().getSource()},resizeAndUpload:function(){var e=this,t=new n(a);t.bind("progress",function(t){e.progress(t.loaded,t.total)}),t.bind("done",function(t,i){i=i,e.upload()}),t.bind("failed",function(){e.upload()}),c.addItem(t)},upload:function(){var e=this,t=new i(a,u);t.bind("beforestart",function(){return e.trigger("beforeupload")}),t.bind("paused",function(){e.pause()}),t.bind("resumed",function(){this.start()}),t.bind("started",function(){e.trigger("startupload")}),t.bind("progress",function(t){e.progress(t.loaded,t.total)}),t.bind("done",function(t,i){e.done(i)}),t.bind("failed",function(t,i){e.failed(i)}),t.setOptions(e.getOptions()),t.start()},destroy:function(){r.prototype.destroy.call(this),a=null}})}function o(t){return e.inArray(t,["image/jpeg","image/png"])>-1}function s(t,i){if(t.ruid){var n=e.Runtime.getInfo(t.ruid);if(n)return n.can(i)}return!1}return e.inherit(r,t),r}),n("plupload/Uploader",["plupload","plupload/core/Collection","moxie/core/utils/Mime","moxie/file/Blob","moxie/file/File","moxie/file/FileInput","moxie/file/FileDrop","moxie/runtime/Runtime","plupload/core/Queue","plupload/QueueUpload","plupload/QueueResize","plupload/File"],function(e,t,i,n,r,o,s,a,u,c,l,d){function h(t){function i(){var e=R[0]||O[0];return e?e.getRuntime().uid:!1}function p(){this.bind("FilesAdded FilesRemoved",function(e){e.trigger("QueueChanged"),e.refresh()},this,999),this.bind("BeforeUpload",x),this.bind("Stopped",function(e){e.trigger("UploadComplete")}),this.bind("Error",_),this.bind("Destroy",E)}function g(t){var i=this,n=0,r=[],u={runtime_order:i.getOption("runtimes"),required_caps:i.getOption("required_features"),preferred_caps:i.getOption("preferred_caps"),swf_url:i.getOption("flash_swf_url"),xap_url:i.getOption("silverlight_xap_url")};e.each(i.getOption("runtimes").split(/\s*,\s*/),function(e){i.getOption(e)&&(u[e]=i.getOption(e))}),i.getOption("browse_button")&&e.each(i.getOption("browse_button"),function(t){r.push(function(r){var s=new o(e.extend({},u,{accept:i.getOption("filters").mime_types,name:i.getOption("file_data_name"),multiple:i.getOption("multi_selection"),container:i.getOption("container"),browse_button:t}));s.onready=function(){var t=a.getInfo(this.ruid);e.extend(i.features,{chunks:t.can("slice_blob"),multipart:t.can("send_multipart"),multi_selection:t.can("select_multiple")}),n++,R.push(this),r()},s.onchange=function(){i.addFile(this.files)},s.bind("mouseenter mouseleave mousedown mouseup",function(n){S||(i.getOption("browse_button_hover")&&("mouseenter"===n.type?e.addClass(t,i.getOption("browse_button_hover")):"mouseleave"===n.type&&e.removeClass(t,i.getOption("browse_button_hover"))),i.getOption("browse_button_active")&&("mousedown"===n.type?e.addClass(t,i.getOption("browse_button_active")):"mouseup"===n.type&&e.removeClass(t,i.getOption("browse_button_active"))))}),s.bind("mousedown",function(){i.trigger("Browse")}),s.bind("error runtimeerror",function(){s=null,r()}),s.init()})}),i.getOption("drop_element")&&e.each(i.getOption("drop_element"),function(t){r.push(function(r){var o=new s(e.extend({},u,{drop_zone:t}));o.onready=function(){var t=a.getInfo(this.ruid);e.extend(i.features,{chunks:t.can("slice_blob"),multipart:t.can("send_multipart"),dragdrop:t.can("drag_and_drop")}),n++,O.push(this),r()},o.ondrop=function(){i.addFile(this.files)},o.bind("error runtimeerror",function(){o=null,r()}),o.init()})}),e.inParallel(r,function(){"function"==typeof t&&t(n)})}function x(e,t){if(e.getOption("unique_names")){var i=t.name.match(/\.([^.]+)$/),n="part";i&&(n=i[1]),t.target_name=t.id+"."+n}}function _(t,i){i.code===e.INIT_ERROR?t.destroy():i.code===e.HTTP_ERROR&&t.state==e.STARTED&&t.trigger("CancelUpload")}function E(t){t.forEachItem(function(e){e.destroy()}),R.length&&(e.each(R,function(e){e.destroy()}),R=[]),O.length&&(e.each(O,function(e){e.destroy()}),O=[]),I=!1,w&&w.destroy(),b&&b.destroy(),A=w=b=null}var w,b,R=[],O=[],I=!1,S=!1,A=m(e.extend({backward_compatibility:!0,chunk_size:0,file_data_name:"file",filters:{mime_types:"*",prevent_duplicates:!1,max_file_size:0},flash_swf_url:"js/Moxie.swf",http_method:"POST",max_resize_slots:1,max_retries:0,max_upload_slots:1,multipart:!0,multipart_params:{},multi_selection:!0,params:{},resize:!1,runtimes:a.order,send_chunk_number:!0,send_file_name:!0,silverlight_xap_url:"js/Moxie.xap",required_features:!1,preferred_caps:!1},t));u.call(this),e.extend(this,{_options:A,id:this.uid,state:e.STOPPED,features:{},settings:A,runtime:null,files:[],total:this.stats,init:function(){var t,n,r=this;return t=r.getOption("preinit"),"function"==typeof t?t(r):e.each(t,function(e,t){r.bind(t,e)}),p.call(r),e.each(["container","browse_button","drop_element"],function(t){return null===r.getOption(t)?(n={code:e.INIT_ERROR,message:e.sprintf(e.translate("%s specified, but cannot be found."),t)},!1):void 0}),n?r.trigger("Error",n):r.getOption("browse_button")||r.getOption("drop_element")?(g.call(r,function(t){var n,o=r.getOption("init"),s=e.extendImmutable({},r.getOption(),{auto_start:!0});"function"==typeof o?o(r):e.each(o,function(e,t){r.bind(t,e)}),t?(I=!0,n=a.getInfo(i()),w=new c(s),b=new l(s),r.trigger("Init",{ruid:n.uid,runtime:r.runtime=n.type}),r.trigger("PostInit")):r.trigger("Error",{code:e.INIT_ERROR,message:e.translate("Init error.")})}),void 0):r.trigger("Error",{code:e.INIT_ERROR,message:e.translate("You must specify either browse_button or drop_element.")})},setOption:function(t,i){return I&&e.inArray(t,["container","browse_button","drop_element","runtimes","multi_selection","flash_swf_url","silverlight_xap_url"])>-1?this.trigger("Error",{code:e.OPTION_ERROR,message:e.sprintf(e.translate("%s option cannot be changed.")),option:t}):("object"!=typeof t&&(i=f(t,i,this._options),w&&w.setOption(t,i),b&&b.setOption(t,i)),h.prototype.setOption.call(this,t,i),void 0)},refresh:function(){R.length&&e.each(R,function(e){e.trigger("Refresh")}),O.length&&e.each(O,function(e){e.trigger("Refresh")}),this.trigger("Refresh")},stop:function(){h.prototype.stop.call(this)&&this.state!=e.STOPPED&&this.trigger("CancelUpload")},disableBrowse:function(){S=arguments[0]!==v?arguments[0]:!0,R.length&&e.each(R,function(e){e.disable(S)}),this.trigger("DisableBrowse",S)},getFile:function(e){return this.getItem(e)},addFile:function(t,o){function s(t){t.bind("beforeupload",function(e){return l.trigger("BeforeUpload",e.target)}),t.bind("startupload",function(){l.trigger("UploadFile",this)}),t.bind("progress",function(){l.trigger("UploadProgress",this)}),t.bind("done",function(e,t){l.trigger("FileUploaded",this,t)}),t.bind("failed",function(t,i){l.trigger("Error",e.extend({code:e.HTTP_ERROR,message:e.translate("HTTP Error."),file:this},i))})}function a(t,i){var n=[];e.each(l.getOption("filters"),function(e,i){y[i]&&n.push(function(n){y[i].call(l,e,t,function(e){n(!e)})})}),e.inParallel(n,i)}function u(t){var i=e.typeOf(t);if(t instanceof r){if(!t.ruid&&!t.isDetached()){if(!c)return!1;t.ruid=c,t.connectRuntime(c)}h.push(function(i){a(t,function(n){var r;n||(r=new d(t,w,b),o&&(r.name=o),s(r),l.addItem(r),p.push(r),l.trigger("FileFiltered",r)),e.delay(i)})})}else t instanceof n?(u(t.getSource()),t.destroy()):-1!==e.inArray(i,["file","blob"])?u(new r(null,t)):"node"===i&&"filelist"===e.typeOf(t.files)?e.each(t.files,u):"array"===i&&(o=null,e.each(t,u))}var c,l=this,h=[],p=[];c=i(),u(t),h.length&&e.inParallel(h,function(){p.length&&l.trigger("FilesAdded",p)})},removeFile:function(e){var t=this.extractItem("string"==typeof e?e:e.uid);t&&(this.trigger("FilesRemoved",[t]),t.destroy())},splice:function(){var t=0,i=e.STARTED==this.state,n=u.prototype.splice.apply(this,arguments);if(n.length){for(this.trigger("FilesRemoved",n),i&&this.stop(),t=0;t<n.length;t++)n[t].destroy();i&&this.start()}},dispatchEvent:function(e){var t,i;if(e=e.toLowerCase(),t=this.hasEventListener(e)){t.sort(function(e,t){return t.priority-e.priority}),i=[].slice.call(arguments),i.shift(),i.unshift(this);for(var n=0;n<t.length;n++)if(t[n].fn.apply(t[n].scope,i)===!1)return!1}return!0},bind:function(t,i,n,r){e.Uploader.prototype.bind.call(this,t,i,r,n)}}),A.backward_compatibility&&(this.bind("FilesAdded FilesRemoved",function(e){e.files=e.toArray()},this,999),this.bind("OptionChanged",function(t,i,n){t.settings[i]="object"==typeof n?e.extend({},n):n},this,999))}function p(t){function i(e,t,i){var n={chunks:"slice_blob",jpgresize:"send_binary_string",pngresize:"send_binary_string",progress:"report_upload_progress",multi_selection:"select_multiple",dragdrop:"drag_and_drop",drop_element:"drag_and_drop",headers:"send_custom_headers",urlstream_upload:"send_binary_string",canSendBinary:"send_binary",triggerDialog:"summon_file_dialog"};n[e]?r[n[e]]=t:i||(r[e]=t)}var n=t.required_features,r={};return"string"==typeof n?e.each(n.split(/\s*,\s*/),function(e){i(e,!0)}):"object"==typeof n?e.each(n,function(e,t){i(t,e)}):n===!0&&(t.chunk_size&&t.chunk_size>0&&(r.slice_blob=!0),e.isEmptyObj(t.resize)&&t.multipart!==!1||(r.send_binary_string=!0),t.http_method&&(r.use_http_method=t.http_method),e.each(t,function(e,t){i(t,!!e,!0)})),r}function m(t){return e.each(t,function(e,i){t[i]=f(i,e,t)}),t}function f(t,n,r){switch(t){case"chunk_size":(n=e.parseSize(n))&&(r.send_file_name=!0);break;case"headers":var o={};return"object"==typeof n&&e.each(n,function(e,t){o[t.toLowerCase()]=e}),o;case"http_method":return"PUT"===n.toUpperCase()?"PUT":"POST";case"filters":return"array"===e.typeOf(n)&&(n={mime_types:n}),n.mime_types&&("string"===e.typeOf(n.mime_types)&&(n.mime_types=i.mimes2extList(n.mime_types)),r.re_ext_filter=function(t){var i=[];return e.each(t,function(t){e.each(t.extensions.split(/,/),function(e){/^\s*\*\s*$/.test(e)?i.push("\\.*"):i.push("\\."+e.replace(new RegExp("["+"/^$.*+?|()[]{}\\".replace(/./g,"\\$&")+"]","g"),"\\$&"))})}),new RegExp("("+i.join("|")+")$","i")}(n.mime_types)),n;case"max_file_size":r&&!r.filters&&(r.filters={}),r.filters.max_file_size=n;break;case"multipart":n||(r.send_file_name=!0);break;case"multipart_params":r.params=r.multipart_params=n;break;case"resize":return n?e.extend({preserve_headers:!0,crop:!1},n):!1;case"prevent_duplicates":r&&!r.filters&&(r.filters={}),r.filters.prevent_duplicates=!!n;break;case"unique_names":n&&(r.send_file_name=!0);break;case"required_features":return p(e.extend({},r));case"preferred_caps":return p(e.extend({},r,{required_features:!0}));case"container":case"browse_button":case"drop_element":return"container"===t?e.get(n):e.getAll(n)}return n}function g(e,t){y[e]=t}function x(e,t){var i,n;return i=new h(e),n=a.thatCan(i.getOption("required_features"),t||e.runtimes),i.destroy(),n}var v,y={};return g("mime_types",function(t,i,n){t.length&&!this.getOption("re_ext_filter").test(i.name)?(this.trigger("Error",{code:e.FILE_EXTENSION_ERROR,message:e.translate("File extension error."),file:i}),n(!1)):n(!0)}),g("max_file_size",function(t,i,n){var r;t=e.parseSize(t),i.size!==r&&t&&i.size>t?(this.trigger("Error",{code:e.FILE_SIZE_ERROR,message:e.translate("File size error."),file:i}),n(!1)):n(!0)}),g("prevent_duplicates",function(t,i,n){var r=this;t&&this.forEachItem(function(t){return i.name===t.name&&i.size===t.size?(r.trigger("Error",{code:e.FILE_DUPLICATE_ERROR,message:e.translate("Duplicate file error."),file:i}),n(!1),void 0):void 0}),n(!0)}),h.addFileFilter=g,e.inherit(h,u),e.addFileFilter=g,e.predictRuntime=x,h}),n("moxie/runtime/html5/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(e,t,i,n){function o(t){var o=this,u=i.capTest,c=i.capTrue,l=e.extend({access_binary:u(window.FileReader||window.File&&window.File.getAsDataURL),access_image_binary:function(){return o.can("access_binary")&&!!a.Image},display_media:u((n.can("create_canvas")||n.can("use_data_uri_over32kb"))&&r("moxie/image/Image")),do_cors:u(window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest),drag_and_drop:u(function(){var e=document.createElement("div");return("draggable"in e||"ondragstart"in e&&"ondrop"in e)&&("IE"!==n.browser||n.verComp(n.version,9,">"))}()),filter_by_extension:u(function(){return!("Chrome"===n.browser&&n.verComp(n.version,28,"<")||"IE"===n.browser&&n.verComp(n.version,10,"<")||"Safari"===n.browser&&n.verComp(n.version,7,"<")||"Firefox"===n.browser&&n.verComp(n.version,37,"<"))}()),return_response_headers:c,return_response_type:function(e){return"json"===e&&window.JSON?!0:n.can("return_response_type",e)},return_status_code:c,report_upload_progress:u(window.XMLHttpRequest&&(new XMLHttpRequest).upload),resize_image:function(){return o.can("access_binary")&&n.can("create_canvas")},select_file:function(){return n.can("use_fileinput")&&window.File},select_folder:function(){return o.can("select_file")&&("Chrome"===n.browser&&n.verComp(n.version,21,">=")||"Firefox"===n.browser&&n.verComp(n.version,42,">="))},select_multiple:function(){return!(!o.can("select_file")||"Safari"===n.browser&&"Windows"===n.os||"iOS"===n.os&&n.verComp(n.osVersion,"7.0.0",">")&&n.verComp(n.osVersion,"8.0.0","<"))},send_binary_string:u(window.XMLHttpRequest&&((new XMLHttpRequest).sendAsBinary||window.Uint8Array&&window.ArrayBuffer)),send_custom_headers:u(window.XMLHttpRequest),send_multipart:function(){return!!(window.XMLHttpRequest&&(new XMLHttpRequest).upload&&window.FormData)||o.can("send_binary_string")},slice_blob:u(window.File&&(File.prototype.mozSlice||File.prototype.webkitSlice||File.prototype.slice)),stream_upload:function(){return o.can("slice_blob")&&o.can("send_multipart")},summon_file_dialog:function(){return o.can("select_file")&&("Firefox"===n.browser&&n.verComp(n.version,4,">=")||"Opera"===n.browser&&n.verComp(n.version,12,">=")||"IE"===n.browser&&n.verComp(n.version,10,">=")||!!~e.inArray(n.browser,["Chrome","Safari","Edge"]))},upload_filesize:c,use_http_method:c},arguments[2]);i.call(this,t,arguments[1]||s,l),e.extend(this,{init:function(){this.trigger("Init")},destroy:function(e){return function(){e.call(o),e=o=null}}(this.destroy)}),e.extend(this.getShim(),a)}var s="html5",a={};return i.addConstructor(s,o),a}),n("moxie/runtime/html5/file/Blob",["moxie/runtime/html5/Runtime","moxie/file/Blob"],function(e,t){function i(){function e(e,t,i){var n;if(!window.File.prototype.slice)return(n=window.File.prototype.webkitSlice||window.File.prototype.mozSlice)?n.call(e,t,i):null;try{return e.slice(),e.slice(t,i)}catch(r){return e.slice(t,i-t)}}this.slice=function(){return new t(this.getRuntime().uid,e.apply(this,arguments))}}return e.Blob=i}),n("moxie/runtime/html5/file/FileInput",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,t,i,n,r,o,s){function a(){var e,a;i.extend(this,{init:function(u){var c,l,d,h,p,m,f=this,g=f.getRuntime();e=u,d=o.extList2mimes(e.accept,g.can("filter_by_extension")),l=g.getShimContainer(),l.innerHTML='<input id="'+g.uid+'" type="file" style="font-size:999px;opacity:0;"'+(e.multiple&&g.can("select_multiple")?"multiple":"")+(e.directory&&g.can("select_folder")?"webkitdirectory directory":"")+(d?' accept="'+d.join(",")+'"':"")+" />",c=n.get(g.uid),i.extend(c.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),h=n.get(e.browse_button),a=n.getStyle(h,"z-index")||"auto",g.can("summon_file_dialog")&&("static"===n.getStyle(h,"position")&&(h.style.position="relative"),r.addEvent(h,"click",function(e){var t=n.get(g.uid);t&&!t.disabled&&t.click(),e.preventDefault()},f.uid),f.bind("Refresh",function(){p=parseInt(a,10)||1,n.get(e.browse_button).style.zIndex=p,this.getRuntime().getShimContainer().style.zIndex=p-1})),m=g.can("summon_file_dialog")?h:l,r.addEvent(m,"mouseover",function(){f.trigger("mouseenter")},f.uid),r.addEvent(m,"mouseout",function(){f.trigger("mouseleave")},f.uid),r.addEvent(m,"mousedown",function(){f.trigger("mousedown")},f.uid),r.addEvent(n.get(e.container),"mouseup",function(){f.trigger("mouseup")},f.uid),c.onchange=function x(){if(f.files=[],i.each(this.files,function(i){var n="";return e.directory&&"."==i.name?!0:(i.webkitRelativePath&&(n="/"+i.webkitRelativePath.replace(/^\//,"")),i=new t(g.uid,i),i.relativePath=n,f.files.push(i),void 0)}),"IE"!==s.browser&&"IEMobile"!==s.browser)this.value="";else{var n=this.cloneNode(!0);this.parentNode.replaceChild(n,this),n.onchange=x}f.files.length&&f.trigger("change")},f.trigger({type:"ready",async:!0}),l=null},setOption:function(e,t){var i=this.getRuntime(),r=n.get(i.uid);switch(e){case"accept":if(t){var s=t.mimes||o.extList2mimes(t,i.can("filter_by_extension"));r.setAttribute("accept",s.join(","))}else r.removeAttribute("accept");break;case"directory":t&&i.can("select_folder")?(r.setAttribute("directory",""),r.setAttribute("webkitdirectory","")):(r.removeAttribute("directory"),r.removeAttribute("webkitdirectory"));break;case"multiple":t&&i.can("select_multiple")?r.setAttribute("multiple",""):r.removeAttribute("multiple")}},disable:function(e){var t,i=this.getRuntime();(t=n.get(i.uid))&&(t.disabled=!!e)},destroy:function(){var t=this.getRuntime(),i=t.getShim(),o=t.getShimContainer(),s=e&&n.get(e.container),u=e&&n.get(e.browse_button);s&&r.removeAllEvents(s,this.uid),u&&(r.removeAllEvents(u,this.uid),u.style.zIndex=a),o&&(r.removeAllEvents(o,this.uid),o.innerHTML=""),i.removeInstance(this.uid),e=o=s=u=i=null}})}return e.FileInput=a}),n("moxie/runtime/html5/file/FileDrop",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime"],function(e,t,i,n,r,o){function s(){function e(e){if(!e.dataTransfer||!e.dataTransfer.types)return!1;var t=i.toArray(e.dataTransfer.types||[]);return-1!==i.inArray("Files",t)||-1!==i.inArray("public.file-url",t)||-1!==i.inArray("application/x-moz-file",t)}function s(e,i){if(u(e)){var n=new t(m,e);n.relativePath=i||"",f.push(n)}}function a(e){for(var t=[],n=0;n<e.length;n++)[].push.apply(t,e[n].extensions.split(/\s*,\s*/));return-1===i.inArray("*",t)?t:[]}function u(e){if(!g.length)return!0;var t=o.getFileExtension(e.name);return!t||-1!==i.inArray(t,g)}function c(e,t){var n=[];i.each(e,function(e){var t=e.webkitGetAsEntry();t&&(t.isFile?s(e.getAsFile(),t.fullPath):n.push(t))}),n.length?l(n,t):t()}function l(e,t){var n=[];i.each(e,function(e){n.push(function(t){d(e,t)})}),i.inSeries(n,function(){t()})}function d(e,t){e.isFile?e.file(function(i){s(i,e.fullPath),t()},function(){t()}):e.isDirectory?h(e,t):t()}function h(e,t){function i(e){r.readEntries(function(t){t.length?([].push.apply(n,t),i(e)):e()},e)}var n=[],r=e.createReader();i(function(){l(n,t)})}var p,m,f=[],g=[];i.extend(this,{init:function(t){var n,o=this;p=t,m=o.ruid,g=a(p.accept),n=p.container,r.addEvent(n,"dragover",function(t){e(t)&&(t.preventDefault(),t.dataTransfer.dropEffect="copy")},o.uid),r.addEvent(n,"drop",function(t){e(t)&&(t.preventDefault(),f=[],t.dataTransfer.items&&t.dataTransfer.items[0].webkitGetAsEntry?c(t.dataTransfer.items,function(){o.files=f,o.trigger("drop")}):(i.each(t.dataTransfer.files,function(e){s(e)}),o.files=f,o.trigger("drop")))},o.uid),r.addEvent(n,"dragenter",function(){o.trigger("dragenter")},o.uid),r.addEvent(n,"dragleave",function(){o.trigger("dragleave")},o.uid)},destroy:function(){r.removeAllEvents(p&&n.get(p.container),this.uid),m=f=g=p=null}})}return e.FileDrop=s}),n("moxie/runtime/html5/file/FileReader",["moxie/runtime/html5/Runtime","moxie/core/utils/Encode","moxie/core/utils/Basic"],function(e,t,i){function n(){function e(e){return t.atob(e.substring(e.indexOf("base64,")+7))}var n,r=!1;i.extend(this,{read:function(t,o){var s=this;s.result="",n=new window.FileReader,n.addEventListener("progress",function(e){s.trigger(e)}),n.addEventListener("load",function(t){s.result=r?e(n.result):n.result,s.trigger(t)}),n.addEventListener("error",function(e){s.trigger(e,n.error)}),n.addEventListener("loadend",function(e){n=null,s.trigger(e)}),"function"===i.typeOf(n[t])?(r=!1,n[t](o.getSource())):"readAsBinaryString"===t&&(r=!0,n.readAsDataURL(o.getSource()))},abort:function(){n&&n.abort()},destroy:function(){n=null}})}return e.FileReader=n}),n("moxie/runtime/html5/xhr/XMLHttpRequest",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/core/utils/Url","moxie/file/File","moxie/file/Blob","moxie/xhr/FormData","moxie/core/Exceptions","moxie/core/utils/Env"],function(e,t,i,n,r,o,s,a,u){function c(){function e(e,t){var i,n,r=this;i=t.getBlob().getSource(),n=new window.FileReader,n.onload=function(){t.append(t.getBlobName(),new o(null,{type:i.type,data:n.result})),m.send.call(r,e,t)},n.readAsBinaryString(i)}function c(){return!window.XMLHttpRequest||"IE"===u.browser&&u.verComp(u.version,8,"<")?function(){for(var e=["Msxml2.XMLHTTP.6.0","Microsoft.XMLHTTP"],t=0;t<e.length;t++)try{return new ActiveXObject(e[t])}catch(i){}}():new window.XMLHttpRequest}function l(e){var t=e.responseXML,i=e.responseText;return"IE"===u.browser&&i&&t&&!t.documentElement&&/[^\/]+\/[^\+]+\+xml/.test(e.getResponseHeader("Content-Type"))&&(t=new window.ActiveXObject("Microsoft.XMLDOM"),t.async=!1,t.validateOnParse=!1,t.loadXML(i)),t&&("IE"===u.browser&&0!==t.parseError||!t.documentElement||"parsererror"===t.documentElement.tagName)?null:t}function d(e){var t="----moxieboundary"+(new Date).getTime(),i="--",n="\r\n",r="",s=this.getRuntime();if(!s.can("send_binary_string"))throw new a.RuntimeError(a.RuntimeError.NOT_SUPPORTED_ERR);return h.setRequestHeader("Content-Type","multipart/form-data; boundary="+t),e.each(function(e,s){r+=e instanceof o?i+t+n+'Content-Disposition: form-data; name="'+s+'"; filename="'+unescape(encodeURIComponent(e.name||"blob"))+'"'+n+"Content-Type: "+(e.type||"application/octet-stream")+n+n+e.getSource()+n:i+t+n+'Content-Disposition: form-data; name="'+s+'"'+n+n+unescape(encodeURIComponent(e))+n}),r+=i+t+i+n}var h,p,m=this;t.extend(this,{send:function(i,r){var a=this,l="Mozilla"===u.browser&&u.verComp(u.version,4,">=")&&u.verComp(u.version,7,"<"),m="Android Browser"===u.browser,f=!1;if(p=i.url.replace(/^.+?\/([\w\-\.]+)$/,"$1").toLowerCase(),h=c(),h.open(i.method,i.url,i.async,i.user,i.password),r instanceof o)r.isDetached()&&(f=!0),r=r.getSource();else if(r instanceof s){if(r.hasBlob())if(r.getBlob().isDetached())r=d.call(a,r),f=!0;else if((l||m)&&"blob"===t.typeOf(r.getBlob().getSource())&&window.FileReader)return e.call(a,i,r),void 0;if(r instanceof s){var g=new window.FormData;r.each(function(e,t){e instanceof o?g.append(t,e.getSource()):g.append(t,e)}),r=g}}h.upload?(i.withCredentials&&(h.withCredentials=!0),h.addEventListener("load",function(e){a.trigger(e)}),h.addEventListener("error",function(e){a.trigger(e)}),h.addEventListener("progress",function(e){a.trigger(e)}),h.upload.addEventListener("progress",function(e){a.trigger({type:"UploadProgress",loaded:e.loaded,total:e.total})})):h.onreadystatechange=function(){switch(h.readyState){case 1:break;case 2:break;case 3:var e,t;try{n.hasSameOrigin(i.url)&&(e=h.getResponseHeader("Content-Length")||0),h.responseText&&(t=h.responseText.length)}catch(r){e=t=0}a.trigger({type:"progress",lengthComputable:!!e,total:parseInt(e,10),loaded:t});break;case 4:h.onreadystatechange=function(){},0===h.status?a.trigger("error"):a.trigger("load")}},t.isEmptyObj(i.headers)||t.each(i.headers,function(e,t){h.setRequestHeader(t,e)}),""!==i.responseType&&"responseType"in h&&(h.responseType="json"!==i.responseType||u.can("return_response_type","json")?i.responseType:"text"),f?h.sendAsBinary?h.sendAsBinary(r):function(){for(var e=new Uint8Array(r.length),t=0;t<r.length;t++)e[t]=255&r.charCodeAt(t);h.send(e.buffer)}():h.send(r),a.trigger("loadstart")},getStatus:function(){try{if(h)return h.status}catch(e){}return 0},getResponse:function(e){var t=this.getRuntime();try{switch(e){case"blob":var n=new r(t.uid,h.response),o=h.getResponseHeader("Content-Disposition");if(o){var s=o.match(/filename=([\'\"'])([^\1]+)\1/);s&&(p=s[2])}return n.name=p,n.type||(n.type=i.getFileMime(p)),n;case"json":return u.can("return_response_type","json")?h.response:200===h.status&&window.JSON?JSON.parse(h.responseText):null;case"document":return l(h);default:return""!==h.responseText?h.responseText:null}}catch(a){return null}},getAllResponseHeaders:function(){try{return h.getAllResponseHeaders()}catch(e){}return""},abort:function(){h&&h.abort()},destroy:function(){m=p=null
}})}return e.XMLHttpRequest=c}),n("moxie/runtime/html5/utils/BinaryReader",["moxie/core/utils/Basic"],function(e){function t(e){e instanceof ArrayBuffer?i.apply(this,arguments):n.apply(this,arguments)}function i(t){var i=new DataView(t);e.extend(this,{readByteAt:function(e){return i.getUint8(e)},writeByteAt:function(e,t){i.setUint8(e,t)},SEGMENT:function(e,n,r){switch(arguments.length){case 2:return t.slice(e,e+n);case 1:return t.slice(e);case 3:if(null===r&&(r=new ArrayBuffer),r instanceof ArrayBuffer){var o=new Uint8Array(this.length()-n+r.byteLength);e>0&&o.set(new Uint8Array(t.slice(0,e)),0),o.set(new Uint8Array(r),e),o.set(new Uint8Array(t.slice(e+n)),e+r.byteLength),this.clear(),t=o.buffer,i=new DataView(t);break}default:return t}},length:function(){return t?t.byteLength:0},clear:function(){i=t=null}})}function n(t){function i(e,i,n){n=3===arguments.length?n:t.length-i-1,t=t.substr(0,i)+e+t.substr(n+i)}e.extend(this,{readByteAt:function(e){return t.charCodeAt(e)},writeByteAt:function(e,t){i(String.fromCharCode(t),e,1)},SEGMENT:function(e,n,r){switch(arguments.length){case 1:return t.substr(e);case 2:return t.substr(e,n);case 3:i(null!==r?r:"",e,n);break;default:return t}},length:function(){return t?t.length:0},clear:function(){t=null}})}return e.extend(t.prototype,{littleEndian:!1,read:function(e,t){var i,n,r;if(e+t>this.length())throw new Error("You are trying to read outside the source boundaries.");for(n=this.littleEndian?0:-8*(t-1),r=0,i=0;t>r;r++)i|=this.readByteAt(e+r)<<Math.abs(n+8*r);return i},write:function(e,t,i){var n,r;if(e>this.length())throw new Error("You are trying to write outside the source boundaries.");for(n=this.littleEndian?0:-8*(i-1),r=0;i>r;r++)this.writeByteAt(e+r,255&t>>Math.abs(n+8*r))},BYTE:function(e){return this.read(e,1)},SHORT:function(e){return this.read(e,2)},LONG:function(e){return this.read(e,4)},SLONG:function(e){var t=this.read(e,4);return t>2147483647?t-4294967296:t},CHAR:function(e){return String.fromCharCode(this.read(e,1))},STRING:function(e,t){return this.asArray("CHAR",e,t).join("")},asArray:function(e,t,i){for(var n=[],r=0;i>r;r++)n[r]=this[e](t+r);return n}}),t}),n("moxie/runtime/html5/image/JPEGHeaders",["moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(e,t){return function i(n){var r,o,s,a=[],u=0;if(r=new e(n),65496!==r.SHORT(0))throw r.clear(),new t.ImageError(t.ImageError.WRONG_FORMAT);for(o=2;o<=r.length();)if(s=r.SHORT(o),s>=65488&&65495>=s)o+=2;else{if(65498===s||65497===s)break;u=r.SHORT(o+2)+2,s>=65505&&65519>=s&&a.push({hex:s,name:"APP"+(15&s),start:o,length:u,segment:r.SEGMENT(o,u)}),o+=u}return r.clear(),{headers:a,restore:function(t){var i,n,r;for(r=new e(t),o=65504==r.SHORT(2)?4+r.SHORT(4):2,n=0,i=a.length;i>n;n++)r.SEGMENT(o,0,a[n].segment),o+=a[n].length;return t=r.SEGMENT(),r.clear(),t},strip:function(t){var n,r,o,s;for(o=new i(t),r=o.headers,o.purge(),n=new e(t),s=r.length;s--;)n.SEGMENT(r[s].start,r[s].length,"");return t=n.SEGMENT(),n.clear(),t},get:function(e){for(var t=[],i=0,n=a.length;n>i;i++)a[i].name===e.toUpperCase()&&t.push(a[i].segment);return t},set:function(e,t){var i,n,r,o=[];for("string"==typeof t?o.push(t):o=t,i=n=0,r=a.length;r>i&&(a[i].name===e.toUpperCase()&&(a[i].segment=o[n],a[i].length=o[n].length,n++),!(n>=o.length));i++);},purge:function(){this.headers=a=[]}}}}),n("moxie/runtime/html5/image/ExifParser",["moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(e,i,n){function r(o){function s(i,r){var o,s,a,u,c,h,p,m,f=this,g=[],x={},v={1:"BYTE",7:"UNDEFINED",2:"ASCII",3:"SHORT",4:"LONG",5:"RATIONAL",9:"SLONG",10:"SRATIONAL"},y={BYTE:1,UNDEFINED:1,ASCII:1,SHORT:2,LONG:4,RATIONAL:8,SLONG:4,SRATIONAL:8};for(o=f.SHORT(i),s=0;o>s;s++)if(g=[],p=i+2+12*s,a=r[f.SHORT(p)],a!==t){if(u=v[f.SHORT(p+=2)],c=f.LONG(p+=2),h=y[u],!h)throw new n.ImageError(n.ImageError.INVALID_META_ERR);if(p+=4,h*c>4&&(p=f.LONG(p)+d.tiffHeader),p+h*c>=this.length())throw new n.ImageError(n.ImageError.INVALID_META_ERR);"ASCII"!==u?(g=f.asArray(u,p,c),m=1==c?g[0]:g,x[a]=l.hasOwnProperty(a)&&"object"!=typeof m?l[a][m]:m):x[a]=e.trim(f.STRING(p,c).replace(/\0$/,""))}return x}function a(e,t,i){var n,r,o,s=0;if("string"==typeof t){var a=c[e.toLowerCase()];for(var u in a)if(a[u]===t){t=u;break}}n=d[e.toLowerCase()+"IFD"],r=this.SHORT(n);for(var l=0;r>l;l++)if(o=n+12*l+2,this.SHORT(o)==t){s=o+8;break}if(!s)return!1;try{this.write(s,i,4)}catch(h){return!1}return!0}var u,c,l,d,h,p;if(i.call(this,o),c={tiff:{274:"Orientation",270:"ImageDescription",271:"Make",272:"Model",305:"Software",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer"},exif:{36864:"ExifVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",36867:"DateTimeOriginal",33434:"ExposureTime",33437:"FNumber",34855:"ISOSpeedRatings",37377:"ShutterSpeedValue",37378:"ApertureValue",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37386:"FocalLength",41986:"ExposureMode",41987:"WhiteBalance",41990:"SceneCaptureType",41988:"DigitalZoomRatio",41992:"Contrast",41993:"Saturation",41994:"Sharpness"},gps:{0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude"},thumb:{513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength"}},l={ColorSpace:{1:"sRGB",0:"Uncalibrated"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{1:"Daylight",2:"Fliorescent",3:"Tungsten",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 -5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},ExposureMode:{0:"Auto exposure",1:"Manual exposure",2:"Auto bracket"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},GPSLatitudeRef:{N:"North latitude",S:"South latitude"},GPSLongitudeRef:{E:"East longitude",W:"West longitude"}},d={tiffHeader:10},h=d.tiffHeader,u={clear:this.clear},e.extend(this,{read:function(){try{return r.prototype.read.apply(this,arguments)}catch(e){throw new n.ImageError(n.ImageError.INVALID_META_ERR)}},write:function(){try{return r.prototype.write.apply(this,arguments)}catch(e){throw new n.ImageError(n.ImageError.INVALID_META_ERR)}},UNDEFINED:function(){return this.BYTE.apply(this,arguments)},RATIONAL:function(e){return this.LONG(e)/this.LONG(e+4)},SRATIONAL:function(e){return this.SLONG(e)/this.SLONG(e+4)},ASCII:function(e){return this.CHAR(e)},TIFF:function(){return p||null},EXIF:function(){var t=null;if(d.exifIFD){try{t=s.call(this,d.exifIFD,c.exif)}catch(i){return null}if(t.ExifVersion&&"array"===e.typeOf(t.ExifVersion)){for(var n=0,r="";n<t.ExifVersion.length;n++)r+=String.fromCharCode(t.ExifVersion[n]);t.ExifVersion=r}}return t},GPS:function(){var t=null;if(d.gpsIFD){try{t=s.call(this,d.gpsIFD,c.gps)}catch(i){return null}t.GPSVersionID&&"array"===e.typeOf(t.GPSVersionID)&&(t.GPSVersionID=t.GPSVersionID.join("."))}return t},thumb:function(){if(d.IFD1)try{var e=s.call(this,d.IFD1,c.thumb);if("JPEGInterchangeFormat"in e)return this.SEGMENT(d.tiffHeader+e.JPEGInterchangeFormat,e.JPEGInterchangeFormatLength)}catch(t){}return null},setExif:function(e,t){return"PixelXDimension"!==e&&"PixelYDimension"!==e?!1:a.call(this,"exif",e,t)},clear:function(){u.clear(),o=c=l=p=d=u=null}}),65505!==this.SHORT(0)||"EXIF\0"!==this.STRING(4,5).toUpperCase())throw new n.ImageError(n.ImageError.INVALID_META_ERR);if(this.littleEndian=18761==this.SHORT(h),42!==this.SHORT(h+=2))throw new n.ImageError(n.ImageError.INVALID_META_ERR);d.IFD0=d.tiffHeader+this.LONG(h+=2),p=s.call(this,d.IFD0,c.tiff),"ExifIFDPointer"in p&&(d.exifIFD=d.tiffHeader+p.ExifIFDPointer,delete p.ExifIFDPointer),"GPSInfoIFDPointer"in p&&(d.gpsIFD=d.tiffHeader+p.GPSInfoIFDPointer,delete p.GPSInfoIFDPointer),e.isEmptyObj(p)&&(p=null);var m=this.LONG(d.IFD0+12*this.SHORT(d.IFD0)+2);m&&(d.IFD1=d.tiffHeader+m)}return r.prototype=i.prototype,r}),n("moxie/runtime/html5/image/JPEG",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEGHeaders","moxie/runtime/html5/utils/BinaryReader","moxie/runtime/html5/image/ExifParser"],function(e,t,i,n,r){function o(o){function s(e){var t,i,n=0;for(e||(e=c);n<=e.length();){if(t=e.SHORT(n+=2),t>=65472&&65475>=t)return n+=5,{height:e.SHORT(n),width:e.SHORT(n+=2)};i=e.SHORT(n+=2),n+=i-2}return null}function a(){var e,t,i=d.thumb();return i&&(e=new n(i),t=s(e),e.clear(),t)?(t.data=i,t):null}function u(){d&&l&&c&&(d.clear(),l.purge(),c.clear(),h=l=d=c=null)}var c,l,d,h;if(c=new n(o),65496!==c.SHORT(0))throw new t.ImageError(t.ImageError.WRONG_FORMAT);l=new i(o);try{d=new r(l.get("app1")[0])}catch(p){}h=s.call(this),e.extend(this,{type:"image/jpeg",size:c.length(),width:h&&h.width||0,height:h&&h.height||0,setExif:function(t,i){return d?("object"===e.typeOf(t)?e.each(t,function(e,t){d.setExif(t,e)}):d.setExif(t,i),l.set("app1",d.SEGMENT()),void 0):!1},writeHeaders:function(){return arguments.length?l.restore(arguments[0]):l.restore(o)},stripHeaders:function(e){return l.strip(e)},purge:function(){u.call(this)}}),d&&(this.meta={tiff:d.TIFF(),exif:d.EXIF(),gps:d.GPS(),thumb:a()})}return o}),n("moxie/runtime/html5/image/PNG",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader"],function(e,t,i){function n(n){function r(){var e,t;return e=s.call(this,8),"IHDR"==e.type?(t=e.start,{width:a.LONG(t),height:a.LONG(t+=4)}):null}function o(){a&&(a.clear(),n=l=u=c=a=null)}function s(e){var t,i,n,r;return t=a.LONG(e),i=a.STRING(e+=4,4),n=e+=4,r=a.LONG(e+t),{length:t,type:i,start:n,CRC:r}}var a,u,c,l;a=new i(n),function(){var t=0,i=0,n=[35152,20039,3338,6666];for(i=0;i<n.length;i++,t+=2)if(n[i]!=a.SHORT(t))throw new e.ImageError(e.ImageError.WRONG_FORMAT)}(),l=r.call(this),t.extend(this,{type:"image/png",size:a.length(),width:l.width,height:l.height,purge:function(){o.call(this)}}),o.call(this)}return n}),n("moxie/runtime/html5/image/ImageInfo",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEG","moxie/runtime/html5/image/PNG"],function(e,t,i,n){return function(r){var o,s=[i,n];o=function(){for(var e=0;e<s.length;e++)try{return new s[e](r)}catch(i){}throw new t.ImageError(t.ImageError.WRONG_FORMAT)}(),e.extend(this,{type:"",size:0,width:0,height:0,setExif:function(){},writeHeaders:function(e){return e},stripHeaders:function(e){return e},purge:function(){r=null}}),e.extend(this,o),this.purge=function(){o.purge(),o=null}}}),n("moxie/runtime/html5/image/ResizerCanvas",[],function(){function e(i,n){var r=i.width,o=Math.floor(r*n),s=!1;(.5>n||n>2)&&(n=.5>n?.5:2,s=!0);var a=t(i,n);return s?e(a,o/a.width):a}function t(e,t){var i=e.width,n=e.height,r=Math.floor(i*t),o=Math.floor(n*t),s=document.createElement("canvas");return s.width=r,s.height=o,s.getContext("2d").drawImage(e,0,0,i,n,0,0,r,o),e=null,s}return{scale:e}}),n("moxie/runtime/html5/image/Image",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/utils/Encode","moxie/file/Blob","moxie/file/File","moxie/runtime/html5/image/ImageInfo","moxie/runtime/html5/image/ResizerCanvas","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,t,i,n,r,o,s,a,u){function c(){function e(){if(!v&&!g)throw new i.ImageError(i.DOMException.INVALID_STATE_ERR);return v||g}function c(){var t=e();return"canvas"==t.nodeName.toLowerCase()?t:(v=document.createElement("canvas"),v.width=t.width,v.height=t.height,v.getContext("2d").drawImage(t,0,0),v)}function l(e){return n.atob(e.substring(e.indexOf("base64,")+7))}function d(e,t){return"data:"+(t||"")+";base64,"+n.btoa(e)}function h(e){var t=this;g=new Image,g.onerror=function(){f.call(this),t.trigger("error",i.ImageError.WRONG_FORMAT)},g.onload=function(){t.trigger("load")},g.src="data:"==e.substr(0,5)?e:d(e,_.type)}function p(e,t){var n,r=this;return window.FileReader?(n=new FileReader,n.onload=function(){t.call(r,this.result)},n.onerror=function(){r.trigger("error",i.ImageError.WRONG_FORMAT)},n.readAsDataURL(e),void 0):t.call(this,e.getAsDataURL())}function m(e,i){var n=Math.PI/180,r=document.createElement("canvas"),o=r.getContext("2d"),s=e.width,a=e.height;switch(t.inArray(i,[5,6,7,8])>-1?(r.width=a,r.height=s):(r.width=s,r.height=a),i){case 2:o.translate(s,0),o.scale(-1,1);break;case 3:o.translate(s,a),o.rotate(180*n);break;case 4:o.translate(0,a),o.scale(1,-1);break;case 5:o.rotate(90*n),o.scale(1,-1);break;case 6:o.rotate(90*n),o.translate(0,-a);break;case 7:o.rotate(90*n),o.translate(s,-a),o.scale(-1,1);break;case 8:o.rotate(-90*n),o.translate(-s,0)}return o.drawImage(e,0,0,s,a),r}function f(){x&&(x.purge(),x=null),y=g=v=_=null,w=!1}var g,x,v,y,_,E=this,w=!1,b=!0;t.extend(this,{loadFromBlob:function(e){var t=this.getRuntime(),n=arguments.length>1?arguments[1]:!0;if(!t.can("access_binary"))throw new i.RuntimeError(i.RuntimeError.NOT_SUPPORTED_ERR);return _=e,e.isDetached()?(y=e.getSource(),h.call(this,y),void 0):(p.call(this,e.getSource(),function(e){n&&(y=l(e)),h.call(this,e)}),void 0)},loadFromImage:function(e,t){this.meta=e.meta,_=new o(null,{name:e.name,size:e.size,type:e.type}),h.call(this,t?y=e.getAsBinaryString():e.getAsDataURL())},getInfo:function(){var t,i=this.getRuntime();return!x&&y&&i.can("access_image_binary")&&(x=new s(y)),t={width:e().width||0,height:e().height||0,type:_.type||u.getFileMime(_.name),size:y&&y.length||_.size||0,name:_.name||"",meta:null},b&&(t.meta=x&&x.meta||this.meta||{},!t.meta||!t.meta.thumb||t.meta.thumb.data instanceof r||(t.meta.thumb.data=new r(null,{type:"image/jpeg",data:t.meta.thumb.data}))),t},resize:function(t,i,n){var r=document.createElement("canvas");if(r.width=t.width,r.height=t.height,r.getContext("2d").drawImage(e(),t.x,t.y,t.width,t.height,0,0,r.width,r.height),v=a.scale(r,i),b=n.preserveHeaders,!b){var o=this.meta&&this.meta.tiff&&this.meta.tiff.Orientation||1;v=m(v,o)}this.width=v.width,this.height=v.height,w=!0,this.trigger("Resize")},getAsCanvas:function(){return v||(v=c()),v.id=this.uid+"_canvas",v},getAsBlob:function(e,t){return e!==this.type?(w=!0,new o(null,{name:_.name||"",type:e,data:E.getAsDataURL(e,t)})):new o(null,{name:_.name||"",type:e,data:E.getAsBinaryString(e,t)})},getAsDataURL:function(e){var t=arguments[1]||90;if(!w)return g.src;if(c(),"image/jpeg"!==e)return v.toDataURL("image/png");try{return v.toDataURL("image/jpeg",t/100)}catch(i){return v.toDataURL("image/jpeg")}},getAsBinaryString:function(e,t){if(!w)return y||(y=l(E.getAsDataURL(e,t))),y;if("image/jpeg"!==e)y=l(E.getAsDataURL(e,t));else{var i;t||(t=90),c();try{i=v.toDataURL("image/jpeg",t/100)}catch(n){i=v.toDataURL("image/jpeg")}y=l(i),x&&(y=x.stripHeaders(y),b&&(x.meta&&x.meta.exif&&x.setExif({PixelXDimension:this.width,PixelYDimension:this.height}),y=x.writeHeaders(y)),x.purge(),x=null)}return w=!1,y},destroy:function(){E=null,f.call(this),this.getRuntime().getShim().removeInstance(this.uid)}})}return e.Image=c}),n("moxie/runtime/flash/Runtime",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/runtime/Runtime"],function(e,t,i,n,o){function s(){var e;try{e=navigator.plugins["Shockwave Flash"],e=e.description}catch(t){try{e=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(i){e="0.0"}}return e=e.match(/\d+/g),parseFloat(e[0]+"."+e[1])}function a(e){var n=i.get(e);n&&"OBJECT"==n.nodeName&&("IE"===t.browser?(n.style.display="none",function r(){4==n.readyState?u(e):setTimeout(r,10)}()):n.parentNode.removeChild(n))}function u(e){var t=i.get(e);if(t){for(var n in t)"function"==typeof t[n]&&(t[n]=null);t.parentNode.removeChild(t)}}function c(u){var c,h=this;u=e.extend({swf_url:t.swf_url},u),o.call(this,u,l,{access_binary:function(e){return e&&"browser"===h.mode},access_image_binary:function(e){return e&&"browser"===h.mode},display_media:o.capTest(r("moxie/image/Image")),do_cors:o.capTrue,drag_and_drop:!1,report_upload_progress:function(){return"client"===h.mode},resize_image:o.capTrue,return_response_headers:!1,return_response_type:function(t){return"json"===t&&window.JSON?!0:!e.arrayDiff(t,["","text","document"])||"browser"===h.mode},return_status_code:function(t){return"browser"===h.mode||!e.arrayDiff(t,[200,404])},select_file:o.capTrue,select_multiple:o.capTrue,send_binary_string:function(e){return e&&"browser"===h.mode},send_browser_cookies:function(e){return e&&"browser"===h.mode},send_custom_headers:function(e){return e&&"browser"===h.mode},send_multipart:o.capTrue,slice_blob:function(e){return e&&"browser"===h.mode},stream_upload:function(e){return e&&"browser"===h.mode},summon_file_dialog:!1,upload_filesize:function(t){return e.parseSizeStr(t)<=2097152||"client"===h.mode},use_http_method:function(t){return!e.arrayDiff(t,["GET","POST"])}},{access_binary:function(e){return e?"browser":"client"},access_image_binary:function(e){return e?"browser":"client"},report_upload_progress:function(e){return e?"browser":"client"},return_response_type:function(t){return e.arrayDiff(t,["","text","json","document"])?"browser":["client","browser"]},return_status_code:function(t){return e.arrayDiff(t,[200,404])?"browser":["client","browser"]},send_binary_string:function(e){return e?"browser":"client"},send_browser_cookies:function(e){return e?"browser":"client"},send_custom_headers:function(e){return e?"browser":"client"},slice_blob:function(e){return e?"browser":"client"},stream_upload:function(e){return e?"client":"browser"},upload_filesize:function(t){return e.parseSizeStr(t)>=2097152?"client":"browser"}},"client"),s()<11.3&&(this.mode=!1),e.extend(this,{getShim:function(){return i.get(this.uid)},shimExec:function(e,t){var i=[].slice.call(arguments,2);return h.getShim().exec(this.uid,e,t,i)},init:function(){var i,r,o;o=this.getShimContainer(),e.extend(o.style,{position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),i='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+u.swf_url+'" ',"IE"===t.browser&&(i+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),i+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+u.swf_url+'" />'+'<param name="flashvars" value="uid='+escape(this.uid)+"&target="+t.global_event_dispatcher+'" />'+'<param name="wmode" value="transparent" />'+'<param name="allowscriptaccess" value="always" />'+"</object>","IE"===t.browser?(r=document.createElement("div"),o.appendChild(r),r.outerHTML=i,r=o=null):o.innerHTML=i,c=setTimeout(function(){h&&!h.initialized&&h.trigger("Error",new n.RuntimeError(n.RuntimeError.NOT_INIT_ERR))},5e3)},destroy:function(e){return function(){a(h.uid),e.call(h),clearTimeout(c),u=c=e=h=null}}(this.destroy)},d)}var l="flash",d={};return o.addConstructor(l,c),d}),n("moxie/runtime/flash/file/Blob",["moxie/runtime/flash/Runtime","moxie/file/Blob"],function(e,t){var i={slice:function(e,i,n,r){var o=this.getRuntime();return 0>i?i=Math.max(e.size+i,0):i>0&&(i=Math.min(i,e.size)),0>n?n=Math.max(e.size+n,0):n>0&&(n=Math.min(n,e.size)),e=o.shimExec.call(this,"Blob","slice",i,n,r||""),e&&(e=new t(o.uid,e)),e}};return e.Blob=i}),n("moxie/runtime/flash/file/FileInput",["moxie/runtime/flash/Runtime","moxie/file/File","moxie/core/utils/Basic"],function(e,t,i){var n={init:function(e){var n=this,r=this.getRuntime();this.bind("Change",function(){var e=r.shimExec.call(n,"FileInput","getFiles");n.files=[],i.each(e,function(e){n.files.push(new t(r.uid,e))})},999),this.getRuntime().shimExec.call(this,"FileInput","init",{accept:e.accept,multiple:e.multiple}),this.trigger("ready")}};return e.FileInput=n}),n("moxie/runtime/flash/file/FileReader",["moxie/runtime/flash/Runtime","moxie/core/utils/Encode"],function(e,t){function i(e,i){switch(i){case"readAsText":return t.atob(e,"utf8");case"readAsBinaryString":return t.atob(e);case"readAsDataURL":return e}return null}var n={read:function(e,t){var n=this;return n.result="","readAsDataURL"===e&&(n.result="data:"+(t.type||"")+";base64,"),n.bind("Progress",function(t,r){r&&(n.result+=i(r,e))},999),n.getRuntime().shimExec.call(this,"FileReader","readAsBase64",t.uid)}};return e.FileReader=n}),n("moxie/runtime/flash/file/FileReaderSync",["moxie/runtime/flash/Runtime","moxie/core/utils/Encode"],function(e,t){function i(e,i){switch(i){case"readAsText":return t.atob(e,"utf8");case"readAsBinaryString":return t.atob(e);case"readAsDataURL":return e}return null}var n={read:function(e,t){var n,r=this.getRuntime();return(n=r.shimExec.call(this,"FileReaderSync","readAsBase64",t.uid))?("readAsDataURL"===e&&(n="data:"+(t.type||"")+";base64,"+n),i(n,e,t.type)):null}};return e.FileReaderSync=n}),n("moxie/runtime/flash/runtime/Transporter",["moxie/runtime/flash/Runtime","moxie/file/Blob"],function(e,t){var i={getAsBlob:function(e){var i=this.getRuntime(),n=i.shimExec.call(this,"Transporter","getAsBlob",e);return n?new t(i.uid,n):null}};return e.Transporter=i}),n("moxie/runtime/flash/xhr/XMLHttpRequest",["moxie/runtime/flash/Runtime","moxie/core/utils/Basic","moxie/file/Blob","moxie/file/File","moxie/file/FileReaderSync","moxie/runtime/flash/file/FileReaderSync","moxie/xhr/FormData","moxie/runtime/Transporter","moxie/runtime/flash/runtime/Transporter"],function(e,t,i,n,r,o,s,a){var u={send:function(e,n){function r(){e.transport=l.mode,l.shimExec.call(c,"XMLHttpRequest","send",e,n)}function o(e,t){l.shimExec.call(c,"XMLHttpRequest","appendBlob",e,t.uid),n=null,r()}function u(e,t){var i=new a;i.bind("TransportingComplete",function(){t(this.result)}),i.transport(e.getSource(),e.type,{ruid:l.uid})}var c=this,l=c.getRuntime();if(t.isEmptyObj(e.headers)||t.each(e.headers,function(e,t){l.shimExec.call(c,"XMLHttpRequest","setRequestHeader",t,e.toString())}),n instanceof s){var d;if(n.each(function(e,t){e instanceof i?d=t:l.shimExec.call(c,"XMLHttpRequest","append",t,e)}),n.hasBlob()){var h=n.getBlob();h.isDetached()?u(h,function(e){h.destroy(),o(d,e)}):o(d,h)}else n=null,r()}else n instanceof i?n.isDetached()?u(n,function(e){n.destroy(),n=e.uid,r()}):(n=n.uid,r()):r()},getResponse:function(e){var i,o,s=this.getRuntime();if(o=s.shimExec.call(this,"XMLHttpRequest","getResponseAsBlob")){if(o=new n(s.uid,o),"blob"===e)return o;try{if(i=new r,~t.inArray(e,["","text"]))return i.readAsText(o);if("json"===e&&window.JSON)return JSON.parse(i.readAsText(o))}finally{o.destroy()}}return null},abort:function(){var e=this.getRuntime();e.shimExec.call(this,"XMLHttpRequest","abort"),this.dispatchEvent("readystatechange"),this.dispatchEvent("abort")}};return e.XMLHttpRequest=u}),n("moxie/runtime/flash/image/Image",["moxie/runtime/flash/Runtime","moxie/core/utils/Basic","moxie/runtime/Transporter","moxie/file/Blob","moxie/file/FileReaderSync"],function(e,t,i,n,r){var o={loadFromBlob:function(e){function t(e){r.shimExec.call(n,"Image","loadFromBlob",e.uid),n=r=null}var n=this,r=n.getRuntime();if(e.isDetached()){var o=new i;o.bind("TransportingComplete",function(){t(o.result.getSource())}),o.transport(e.getSource(),e.type,{ruid:r.uid})}else t(e.getSource())},loadFromImage:function(e){var t=this.getRuntime();return t.shimExec.call(this,"Image","loadFromImage",e.uid)},getInfo:function(){var e=this.getRuntime(),t=e.shimExec.call(this,"Image","getInfo");return t.meta&&t.meta.thumb&&t.meta.thumb.data&&!(e.meta.thumb.data instanceof n)&&(t.meta.thumb.data=new n(e.uid,t.meta.thumb.data)),t},getAsBlob:function(e,t){var i=this.getRuntime(),r=i.shimExec.call(this,"Image","getAsBlob",e,t);return r?new n(i.uid,r):null},getAsDataURL:function(){var e,t=this.getRuntime(),i=t.Image.getAsBlob.apply(this,arguments);return i?(e=new r,e.readAsDataURL(i)):null}};return e.Image=o}),n("moxie/runtime/silverlight/Runtime",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/runtime/Runtime"],function(e,t,i,n,o){function s(e){var t,i,n,r,o,s=!1,a=null,u=0;try{try{a=new ActiveXObject("AgControl.AgControl"),a.IsVersionSupported(e)&&(s=!0),a=null}catch(c){var l=navigator.plugins["Silverlight Plug-In"];if(l){for(t=l.description,"1.0.30226.2"===t&&(t="2.0.30226.2"),i=t.split(".");i.length>3;)i.pop();for(;i.length<4;)i.push(0);for(n=e.split(".");n.length>4;)n.pop();do r=parseInt(n[u],10),o=parseInt(i[u],10),u++;while(u<n.length&&r===o);o>=r&&!isNaN(r)&&(s=!0)}}}catch(d){s=!1}return s}function a(a){var l,d=this;a=e.extend({xap_url:t.xap_url},a),o.call(this,a,u,{access_binary:o.capTrue,access_image_binary:o.capTrue,display_media:o.capTest(r("moxie/image/Image")),do_cors:o.capTrue,drag_and_drop:!1,report_upload_progress:o.capTrue,resize_image:o.capTrue,return_response_headers:function(e){return e&&"client"===d.mode},return_response_type:function(e){return"json"!==e?!0:!!window.JSON},return_status_code:function(t){return"client"===d.mode||!e.arrayDiff(t,[200,404])},select_file:o.capTrue,select_multiple:o.capTrue,send_binary_string:o.capTrue,send_browser_cookies:function(e){return e&&"browser"===d.mode},send_custom_headers:function(e){return e&&"client"===d.mode},send_multipart:o.capTrue,slice_blob:o.capTrue,stream_upload:!0,summon_file_dialog:!1,upload_filesize:o.capTrue,use_http_method:function(t){return"client"===d.mode||!e.arrayDiff(t,["GET","POST"])}},{return_response_headers:function(e){return e?"client":"browser"},return_status_code:function(t){return e.arrayDiff(t,[200,404])?"client":["client","browser"]},send_browser_cookies:function(e){return e?"browser":"client"},send_custom_headers:function(e){return e?"client":"browser"},use_http_method:function(t){return e.arrayDiff(t,["GET","POST"])?"client":["client","browser"]}}),s("2.0.31005.0")&&"Opera"!==t.browser||(this.mode=!1),e.extend(this,{getShim:function(){return i.get(this.uid).content.Moxie},shimExec:function(e,t){var i=[].slice.call(arguments,2);return d.getShim().exec(this.uid,e,t,i)},init:function(){var e;e=this.getShimContainer(),e.innerHTML='<object id="'+this.uid+'" data="data:application/x-silverlight," type="application/x-silverlight-2" width="100%" height="100%" style="outline:none;">'+'<param name="source" value="'+a.xap_url+'"/>'+'<param name="background" value="Transparent"/>'+'<param name="windowless" value="true"/>'+'<param name="enablehtmlaccess" value="true"/>'+'<param name="initParams" value="uid='+this.uid+",target="+t.global_event_dispatcher+'"/>'+"</object>",l=setTimeout(function(){d&&!d.initialized&&d.trigger("Error",new n.RuntimeError(n.RuntimeError.NOT_INIT_ERR))},"Windows"!==t.OS?1e4:5e3)},destroy:function(e){return function(){e.call(d),clearTimeout(l),a=l=e=d=null}}(this.destroy)},c)}var u="silverlight",c={};return o.addConstructor(u,a),c}),n("moxie/runtime/silverlight/file/Blob",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/file/Blob"],function(e,t,i){return e.Blob=t.extend({},i)}),n("moxie/runtime/silverlight/file/FileInput",["moxie/runtime/silverlight/Runtime","moxie/file/File","moxie/core/utils/Basic"],function(e,t,i){function n(e){for(var t="",i=0;i<e.length;i++)t+=(""!==t?"|":"")+e[i].title+" | *."+e[i].extensions.replace(/,/g,";*.");return t}var r={init:function(e){var r=this,o=this.getRuntime();this.bind("Change",function(){var e=o.shimExec.call(r,"FileInput","getFiles");r.files=[],i.each(e,function(e){r.files.push(new t(o.uid,e))})},999),o.shimExec.call(this,"FileInput","init",n(e.accept),e.multiple),this.trigger("ready")},setOption:function(e,t){"accept"==e&&(t=n(t)),this.getRuntime().shimExec.call(this,"FileInput","setOption",e,t)}};return e.FileInput=r}),n("moxie/runtime/silverlight/file/FileDrop",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Dom","moxie/core/utils/Events"],function(e,t,i){var n={init:function(){var e,n=this,r=n.getRuntime();return e=r.getShimContainer(),i.addEvent(e,"dragover",function(e){e.preventDefault(),e.stopPropagation(),e.dataTransfer.dropEffect="copy"},n.uid),i.addEvent(e,"dragenter",function(e){e.preventDefault();var i=t.get(r.uid).dragEnter(e);i&&e.stopPropagation()},n.uid),i.addEvent(e,"drop",function(e){e.preventDefault();var i=t.get(r.uid).dragDrop(e);i&&e.stopPropagation()},n.uid),r.shimExec.call(this,"FileDrop","init")}};return e.FileDrop=n}),n("moxie/runtime/silverlight/file/FileReader",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/file/FileReader"],function(e,t,i){return e.FileReader=t.extend({},i)}),n("moxie/runtime/silverlight/file/FileReaderSync",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/file/FileReaderSync"],function(e,t,i){return e.FileReaderSync=t.extend({},i)}),n("moxie/runtime/silverlight/runtime/Transporter",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/runtime/Transporter"],function(e,t,i){return e.Transporter=t.extend({},i)}),n("moxie/runtime/silverlight/xhr/XMLHttpRequest",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/xhr/XMLHttpRequest","moxie/runtime/silverlight/file/FileReaderSync","moxie/runtime/silverlight/runtime/Transporter"],function(e,t,i){return e.XMLHttpRequest=t.extend({},i)}),n("moxie/runtime/silverlight/image/Image",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/file/Blob","moxie/runtime/flash/image/Image"],function(e,t,i,n){return e.Image=t.extend({},n,{getInfo:function(){var e=this.getRuntime(),n=["tiff","exif","gps","thumb"],r={meta:{}},o=e.shimExec.call(this,"Image","getInfo");return o.meta&&(t.each(n,function(e){var t,i,n,s,a=o.meta[e];if(a&&a.keys)for(r.meta[e]={},i=0,n=a.keys.length;n>i;i++)t=a.keys[i],s=a[t],s&&(/^(\d|[1-9]\d+)$/.test(s)?s=parseInt(s,10):/^\d*\.\d+$/.test(s)&&(s=parseFloat(s)),r.meta[e][t]=s)}),r.meta&&r.meta.thumb&&r.meta.thumb.data&&!(e.meta.thumb.data instanceof i)&&(r.meta.thumb.data=new i(e.uid,r.meta.thumb.data))),r.width=parseInt(o.width,10),r.height=parseInt(o.height,10),r.size=parseInt(o.size,10),r.type=o.type,r.name=o.name,r},resize:function(e,t,i){this.getRuntime().shimExec.call(this,"Image","resize",e.x,e.y,e.width,e.height,t,i.preserveHeaders,i.resample)}})}),n("moxie/runtime/html4/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(e,t,i,n){function o(t){var o=this,u=i.capTest,c=i.capTrue;i.call(this,t,s,{access_binary:u(window.FileReader||window.File&&File.getAsDataURL),access_image_binary:!1,display_media:u((n.can("create_canvas")||n.can("use_data_uri_over32kb"))&&r("moxie/image/Image")),do_cors:!1,drag_and_drop:!1,filter_by_extension:u(function(){return!("Chrome"===n.browser&&n.verComp(n.version,28,"<")||"IE"===n.browser&&n.verComp(n.version,10,"<")||"Safari"===n.browser&&n.verComp(n.version,7,"<")||"Firefox"===n.browser&&n.verComp(n.version,37,"<"))
}()),resize_image:function(){return a.Image&&o.can("access_binary")&&n.can("create_canvas")},report_upload_progress:!1,return_response_headers:!1,return_response_type:function(t){return"json"===t&&window.JSON?!0:!!~e.inArray(t,["text","document",""])},return_status_code:function(t){return!e.arrayDiff(t,[200,404])},select_file:function(){return n.can("use_fileinput")},select_multiple:!1,send_binary_string:!1,send_custom_headers:!1,send_multipart:!0,slice_blob:!1,stream_upload:function(){return o.can("select_file")},summon_file_dialog:function(){return o.can("select_file")&&("Firefox"===n.browser&&n.verComp(n.version,4,">=")||"Opera"===n.browser&&n.verComp(n.version,12,">=")||"IE"===n.browser&&n.verComp(n.version,10,">=")||!!~e.inArray(n.browser,["Chrome","Safari"]))},upload_filesize:c,use_http_method:function(t){return!e.arrayDiff(t,["GET","POST"])}}),e.extend(this,{init:function(){this.trigger("Init")},destroy:function(e){return function(){e.call(o),e=o=null}}(this.destroy)}),e.extend(this.getShim(),a)}var s="html4",a={};return i.addConstructor(s,o),a}),n("moxie/runtime/html4/file/FileInput",["moxie/runtime/html4/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,t,i,n,r,o,s){function a(){function e(){var o,c,d,h,p,m,f=this,g=f.getRuntime();m=i.guid("uid_"),o=g.getShimContainer(),a&&(d=n.get(a+"_form"),d&&i.extend(d.style,{top:"100%"})),h=document.createElement("form"),h.setAttribute("id",m+"_form"),h.setAttribute("method","post"),h.setAttribute("enctype","multipart/form-data"),h.setAttribute("encoding","multipart/form-data"),i.extend(h.style,{overflow:"hidden",position:"absolute",top:0,left:0,width:"100%",height:"100%"}),p=document.createElement("input"),p.setAttribute("id",m),p.setAttribute("type","file"),p.setAttribute("accept",l.join(",")),i.extend(p.style,{fontSize:"999px",opacity:0}),h.appendChild(p),o.appendChild(h),i.extend(p.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),"IE"===s.browser&&s.verComp(s.version,10,"<")&&i.extend(p.style,{filter:"progid:DXImageTransform.Microsoft.Alpha(opacity=0)"}),p.onchange=function(){var i;if(this.value){if(this.files){if(i=this.files[0],0===i.size)return h.parentNode.removeChild(h),void 0}else i={name:this.value};i=new t(g.uid,i),this.onchange=function(){},e.call(f),f.files=[i],p.setAttribute("id",i.uid),h.setAttribute("id",i.uid+"_form"),f.trigger("change"),p=h=null}},g.can("summon_file_dialog")&&(c=n.get(u.browse_button),r.removeEvent(c,"click",f.uid),r.addEvent(c,"click",function(e){p&&!p.disabled&&p.click(),e.preventDefault()},f.uid)),a=m,o=d=c=null}var a,u,c,l=[];i.extend(this,{init:function(t){var i,s=this,a=s.getRuntime();u=t,l=o.extList2mimes(t.accept,a.can("filter_by_extension")),i=a.getShimContainer(),function(){var e,o,l;e=n.get(t.browse_button),c=n.getStyle(e,"z-index")||"auto",a.can("summon_file_dialog")&&("static"===n.getStyle(e,"position")&&(e.style.position="relative"),s.bind("Refresh",function(){o=parseInt(c,10)||1,n.get(u.browse_button).style.zIndex=o,this.getRuntime().getShimContainer().style.zIndex=o-1})),l=a.can("summon_file_dialog")?e:i,r.addEvent(l,"mouseover",function(){s.trigger("mouseenter")},s.uid),r.addEvent(l,"mouseout",function(){s.trigger("mouseleave")},s.uid),r.addEvent(l,"mousedown",function(){s.trigger("mousedown")},s.uid),r.addEvent(n.get(t.container),"mouseup",function(){s.trigger("mouseup")},s.uid),e=null}(),e.call(this),i=null,s.trigger({type:"ready",async:!0})},setOption:function(e,t){var i,r=this.getRuntime();"accept"==e&&(l=t.mimes||o.extList2mimes(t,r.can("filter_by_extension"))),i=n.get(a),i&&i.setAttribute("accept",l.join(","))},disable:function(e){var t;(t=n.get(a))&&(t.disabled=!!e)},destroy:function(){var e=this.getRuntime(),t=e.getShim(),i=e.getShimContainer(),o=u&&n.get(u.container),s=u&&n.get(u.browse_button);o&&r.removeAllEvents(o,this.uid),s&&(r.removeAllEvents(s,this.uid),s.style.zIndex=c),i&&(r.removeAllEvents(i,this.uid),i.innerHTML=""),t.removeInstance(this.uid),a=l=u=i=o=s=t=null}})}return e.FileInput=a}),n("moxie/runtime/html4/file/FileReader",["moxie/runtime/html4/Runtime","moxie/runtime/html5/file/FileReader"],function(e,t){return e.FileReader=t}),n("moxie/runtime/html4/xhr/XMLHttpRequest",["moxie/runtime/html4/Runtime","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Url","moxie/core/Exceptions","moxie/core/utils/Events","moxie/file/Blob","moxie/xhr/FormData"],function(e,t,i,n,r,o,s,a){function u(){function e(e){var t,n,r,s,a=this,u=!1;if(l){if(t=l.id.replace(/_iframe$/,""),n=i.get(t+"_form")){for(r=n.getElementsByTagName("input"),s=r.length;s--;)switch(r[s].getAttribute("type")){case"hidden":r[s].parentNode.removeChild(r[s]);break;case"file":u=!0}r=[],u||n.parentNode.removeChild(n),n=null}setTimeout(function(){o.removeEvent(l,"load",a.uid),l.parentNode&&l.parentNode.removeChild(l);var t=a.getRuntime().getShimContainer();t.children.length||t.parentNode.removeChild(t),t=l=null,e()},1)}}var u,c,l;t.extend(this,{send:function(d,h){function p(){var i=y.getShimContainer()||document.body,r=document.createElement("div");r.innerHTML='<iframe id="'+m+'_iframe" name="'+m+'_iframe" src="javascript:&quot;&quot;" style="display:none"></iframe>',l=r.firstChild,i.appendChild(l),o.addEvent(l,"load",function(){var i;try{i=l.contentWindow.document||l.contentDocument||window.frames[l.id].document,/^4(0[0-9]|1[0-7]|2[2346])\s/.test(i.title)?u=i.title.replace(/^(\d+).*$/,"$1"):(u=200,c=t.trim(i.body.innerHTML),v.trigger({type:"progress",loaded:c.length,total:c.length}),x&&v.trigger({type:"uploadprogress",loaded:x.size||1025,total:x.size||1025}))}catch(r){if(!n.hasSameOrigin(d.url))return e.call(v,function(){v.trigger("error")}),void 0;u=404}e.call(v,function(){v.trigger("load")})},v.uid)}var m,f,g,x,v=this,y=v.getRuntime();if(u=c=null,h instanceof a&&h.hasBlob()){if(x=h.getBlob(),m=x.uid,g=i.get(m),f=i.get(m+"_form"),!f)throw new r.DOMException(r.DOMException.NOT_FOUND_ERR)}else m=t.guid("uid_"),f=document.createElement("form"),f.setAttribute("id",m+"_form"),f.setAttribute("method",d.method),f.setAttribute("enctype","multipart/form-data"),f.setAttribute("encoding","multipart/form-data"),y.getShimContainer().appendChild(f);f.setAttribute("target",m+"_iframe"),h instanceof a&&h.each(function(e,i){if(e instanceof s)g&&g.setAttribute("name",i);else{var n=document.createElement("input");t.extend(n,{type:"hidden",name:i,value:e}),g?f.insertBefore(n,g):f.appendChild(n)}}),f.setAttribute("action",d.url),p(),f.submit(),v.trigger("loadstart")},getStatus:function(){return u},getResponse:function(e){if("json"===e&&"string"===t.typeOf(c)&&window.JSON)try{return JSON.parse(c.replace(/^\s*<pre[^>]*>/,"").replace(/<\/pre>\s*$/,""))}catch(i){return null}return c},abort:function(){var t=this;l&&l.contentWindow&&(l.contentWindow.stop?l.contentWindow.stop():l.contentWindow.document.execCommand?l.contentWindow.document.execCommand("Stop"):l.src="about:blank"),e.call(this,function(){t.dispatchEvent("abort")})}})}return e.XMLHttpRequest=u}),n("moxie/runtime/html4/image/Image",["moxie/runtime/html4/Runtime","moxie/runtime/html5/image/Image"],function(e,t){return e.Image=t}),s(["moxie/core/utils/Basic","moxie/core/I18n","moxie/core/utils/Env","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Url","moxie/core/Exceptions","moxie/core/EventTarget","moxie/runtime/Runtime","moxie/core/utils/Mime","moxie/runtime/RuntimeClient","moxie/file/FileInput","moxie/core/utils/Encode","moxie/file/Blob","moxie/file/FileReader","plupload","moxie/file/File","moxie/file/FileDrop","moxie/runtime/RuntimeTarget","moxie/xhr/FormData","moxie/xhr/XMLHttpRequest","plupload/FileUploader","moxie/runtime/Transporter","moxie/image/Image","plupload/File","plupload/Uploader","moxie/runtime/html5/image/ResizerCanvas"])}(this)});