<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        设置加价-<?php if(!empty($template['name'])){ ?><?php echo $template['name']; ?><?php } ?><br>日期：<?php if(!empty($expressFeeSurcharges['s_day'])){ ?><?php echo $expressFeeSurcharges['s_day']; ?><?php } ?>-<?php if(!empty($expressFeeSurcharges['e_day'])){ ?><?php echo $expressFeeSurcharges['e_day']; ?><?php } ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-mode"></label>
              <div class="box-tools" style="width: 15%;float: left;margin-left: 20px">
                <a class="btn btn-primary" href="javascript:addprovince();">添加</a>
              </div>
            </div>
            <div class="all-provinces-condition">
              <?php if(!empty($expressFeeSurcharges['provinces'])){ ?>
                <?php foreach ($expressFeeSurcharges['provinces'] as $province_key=>$province) { ?>
                  <div class="provinces-condition" style="padding-top: 20px;border-top: 1px solid #f39c12;position: relative">
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-quan">省：</label>
                      <div class="col-sm-8 keyword">
                        <div>
                          <input type="text" name="provinces[<?php echo $province_key; ?>][province]" value="<?php echo $province['province']; ?>" placeholder="请输入省" class="form-control" style="width: 70%;float: left" />
                        </div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-quan">市/区：</label>
                      <div class="col-sm-8 keyword">
                        <div>
                          <input type="text" name="provinces[<?php echo $province_key; ?>][city]" value="<?php echo $province['city']; ?>" placeholder="请输入市/区" class="form-control" style="width: 70%;float: left" />
                        </div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-quan">加价金额：</label>
                      <div class="col-sm-8 keyword">
                        <div>
                          <input type="text" name="provinces[<?php echo $province_key; ?>][price]" value="<?php echo $province['price']; ?>" placeholder="请输入加价金额" class="form-control" style="width: 70%;float: left" />
                        </div>
                      </div>
                    </div>
                    <a class="btn btn-danger" href="javascript:;" onclick="delprovince($(this))" style="position: absolute;float: left;top: 25px;left: 50px">删除省份</a>
                  </div>
                <?php } ?>
              <?php } else { ?>
                <div class="provinces-condition" style="padding-top: 20px;border-top: 1px solid #f39c12;position: relative">
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-quan">省：</label>
                    <div class="col-sm-8 keyword">
                      <div>
                        <input type="text" name="provinces[0][province]" value="" placeholder="请输入省" class="form-control" style="width: 70%;float: left" />
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-quan">市/区：</label>
                    <div class="col-sm-8 keyword">
                      <div>
                        <input type="text" name="provinces[0][city]" value="" placeholder="请输入市/区" class="form-control" style="width: 70%;float: left" />
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-quan">加价金额：</label>
                    <div class="col-sm-8 keyword">
                      <div>
                        <input type="text" name="provinces[0][price]" value="" placeholder="请输入加价金额" class="form-control" style="width: 70%;float: left" />
                      </div>
                    </div>
                  </div>

                </div>
              <?php } ?>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script>

  var num = parseInt('<?php echo $num; ?>');

  var province_json = '<?php echo $province_json; ?>'
  function addprovince() {
    num += 1;
    var addHtml ='<div class="provinces-condition" style="padding-top: 20px;border-top: 1px solid #f39c12;position: relative">'+
            '<div class="form-group">'+
            '<label class="col-sm-2 control-label" for="input-quan">省：</label>'+
            '<div class="col-sm-8 keyword">'+
            '<div>'+
            '<input type="text" name="provinces['+num+'][province]" value="" placeholder="请输入省" class="form-control" style="width: 70%;float: left" />'+
            '</div>'+
            '</div>'+
            '</div>'+
            '<div class="form-group">'+
            '<label class="col-sm-2 control-label" for="input-quan">市/区：</label>'+
            '<div class="col-sm-8 keyword">'+
            '<div>'+
            '<input type="text" name="provinces['+num+'][city]" value="" placeholder="请输入市/区" class="form-control" style="width: 70%;float: left" />'+
            '</div>'+
            '</div>'+
            '</div>'+
            '<div class="form-group">'+
            '<label class="col-sm-2 control-label" for="input-quan">加价金额：</label>'+
            '<div class="col-sm-8 keyword">'+
            '<div>'+
            '<input type="text" name="provinces['+num+'][price]" value="" placeholder="请输入加价金额" class="form-control" style="width: 70%;float: left" />'+
            '</div>'+
            '</div>'+
            '</div>'+

            '</div>';
    $(".all-provinces-condition").prepend(addHtml);

  }

  function delprovince(obj) {
    $(obj).parent().remove();
  }


  function toVaild() {
    return true;
  }


</script>
<?php echo $footer; ?>