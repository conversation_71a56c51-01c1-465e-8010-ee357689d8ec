<?php
class ModelAdminImport extends Model {
    public function addStoreSales($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "store_sales SET store_id = '" . (int)$data['store_id'] . "', store_name = '" . $this->db->escape($data['store_name']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', gsku = '" . $this->db->escape($data['gsku']) . "', sale_date = '" . $this->db->escape($data['sale_date']) . "', quantity = '" . (int)$data['quantity'] . "', total = '" . (float)$data['total'] . "', state = '0', date_added = NOW()");
    }

    public function getSalesExist($sale_date) {
        $query = $this->db->query("SELECT sales_id FROM " . DB_PREFIX . "store_sales WHERE sale_date = '" . $this->db->escape($sale_date) . "' LIMIT 1");

        return $query->num_rows;
    }

    public function addStoreShipment($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "store_shipment SET store_id = '" . (int)$data['store_id'] . "', store_name = '" . $this->db->escape($data['store_name']) . "', warehouse_id = '" . (int)$data['ware_id'] . "', warehouse_name = '" . $this->db->escape($data['ware_name']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', gsku = '" . $this->db->escape($data['gsku']) . "', ship_date = '" . $this->db->escape($data['ship_date']) . "', ship_quan = '" . (int)$data['ship_quan'] . "', ship_total = '" . (float)$data['ship_total'] . "', return_quan = '" . (int)$data['return_quan'] . "', return_total = '" . (float)$data['return_total'] . "', state = '0', date_added = NOW()");
    }

    public function getShipmentExist($ship_date) {
        $query = $this->db->query("SELECT ship_id FROM " . DB_PREFIX . "store_shipment WHERE ship_date = '" . $this->db->escape($ship_date) . "' LIMIT 1");

        return $query->num_rows;
    }
}