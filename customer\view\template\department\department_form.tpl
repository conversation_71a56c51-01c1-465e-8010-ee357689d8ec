<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-department_name">部门名称：</label>
              <div class="col-sm-8">
                <input type="text" name="department_name" value="<?php echo $department_name; ?>" placeholder="部门名称" id="input-department_name" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label">绑定用户：</label>
              <div class="col-sm-8">
                <div class="well well-sm" style="height: 150px; overflow: auto;">
                  <?php foreach ($users as $user) { ?>
                  <?php if (in_array($user['union_id'], $union_id)) { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="union_id[]" value="<?php echo $user['union_id']; ?>" checked="checked"><?php echo $user['real_name']; ?></label>
                  </div>
                  <?php } else { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="union_id[]" value="<?php echo $user['union_id']; ?>"><?php echo $user['real_name']; ?></label>
                  </div>
                  <?php } ?>
                  <?php } ?>
                </div>
              </div>
            </div>
            

            <!-- 主管 -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-current-node">主管：</label>
            <div class="col-sm-10">
              <select name="competent_id" id="input-current-node" class="form-control">
                <option value="">请选择主管</option>
                <?php if (!empty($users)) { ?>
                  <?php foreach ($users as $user) { ?>
                    <?php if ($user['union_id'] == $competent_id) { ?>
                      <option value="<?php echo $user['union_id']; ?>" selected="selected"><?php echo $user['real_name']; ?></option>
                    <?php } else { ?>
                      <option value="<?php echo $user['union_id']; ?>"><?php echo $user['real_name']; ?></option>
                    <?php } ?>
                  <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-status">状态：</label>
              <div class="col-sm-8">
                <select name="status" id="input-status" class="form-control">
                  <?php if ($status) { ?>
                  <option value="0"><?php echo $text_disabled; ?></option>
                  <option value="1" selected="selected"><?php echo $text_enabled; ?></option>
                  <?php } else { ?>
                  <option value="0" selected="selected"><?php echo $text_disabled; ?></option>
                  <option value="1"><?php echo $text_enabled; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>