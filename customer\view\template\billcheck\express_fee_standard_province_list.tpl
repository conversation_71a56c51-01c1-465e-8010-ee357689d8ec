<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        查看收费标准-<?php if(!empty($template['name'])){ ?><?php echo $template['name']; ?><?php } ?><br>日期：<?php if(!empty($expressFeeStandard['s_day'])){ ?><?php echo $expressFeeStandard['s_day']; ?><?php } ?>-<?php if(!empty($expressFeeStandard['e_day'])){ ?><?php echo $expressFeeStandard['e_day']; ?><?php } ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">收费标准列表</h3>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody>
               <tr>
                 <th>
                   <a href="javascript:;">省份</a>
                 </th>
                 <?php if(!empty($expressFeeStandard['weight'])){ ?>
                  <?php foreach ($expressFeeStandard['weight'] as $weight) { ?>
                    <th>
                      <a href="javascript:;"><?php echo $weight[0]; ?>-<?php echo $weight[1]; ?></a>
                    </th>
                  <?php } ?>
                 <?php } ?>
                 <th>
                   <a href="javascript:;">首重（<?php if(!empty($expressFeeStandard['starting_weight'])){ ?><?php echo $expressFeeStandard['starting_weight']; ?><?php } ?>KG）</a>
                 </th>
                 <th>
                   <a href="javascript:;">续重（<?php if(!empty($expressFeeStandard['starting_weight'])){ ?><?php echo $expressFeeStandard['starting_weight']; ?><?php } ?>KG）</a>
                 </th>
              </tr>

            <?php if (!empty($expressFeeStandard['provinces'])) { ?>
              <?php foreach ($expressFeeStandard['provinces'] as $standard_provinces) { ?>
                <tr>
                  <?php if (!empty($standard_provinces['province'])) { ?>
                    <td>
                      <?php foreach ($standard_provinces['province'] as $standard_province) { ?>
                      <span style="margin-right: 5px;padding: 3px;line-height:1.6;background-color: #3dd5f3;border-radius: 5px"><?php echo $provinces[$standard_province]; ?></span>
                      <?php } ?>
                    </td>
                  <?php } ?>

                  <?php if (!empty($standard_provinces['price'])) { ?>
                    <?php foreach ($standard_provinces['price'] as $price) { ?>
                      <td><?php echo $price; ?></td>
                    <?php } ?>
                  <?php } ?>

                  <?php if (!empty($standard_provinces['starting_price'])) { ?>
                    <?php foreach ($standard_provinces['starting_price'] as $starting_price) { ?>
                      <td><?php echo $starting_price; ?></td>
                    <?php } ?>
                  <?php } ?>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
      </div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {

  })()
</script>
<?php echo $footer; ?>