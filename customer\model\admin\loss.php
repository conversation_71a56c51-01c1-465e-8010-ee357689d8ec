<?php
class ModelAdminLoss extends Model {
    public function addLoss($data) {
        if (isset($data['order_img'])) {
            $data['order_img'] = implode(',', $data['order_img']);
        } else {
            $data['order_img'] = '';
        }

        if (isset($data['images'])) {
            $data['images'] = implode(',', $data['images']);
        } else {
            $data['images'] = '';
        }

        $this->db->query("INSERT INTO " . DB_PREFIX . "loss SET user_id = '" . (int)$this->user->user_id . "', store_id = '" . (int)$data['store_id'] . "', responsible = '" . $this->db->escape($data['responsible']) . "', reason = '" . $this->db->escape($data['reason']) . "', order_no = '" . $this->db->escape($data['order_no']) . "', buyer_id = '" . $this->db->escape($data['buyer_id']) . "', buyer_location = '" . $this->db->escape($data['buyer_location']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', spec_name = '" . $this->db->escape($data['spec_name']) . "', quantity = '" . abs((int)$data['quantity']) . "', cost_fee = '" . (float)$data['cost_fee'] . "', supplier = '" . $this->db->escape($data['supplier']) . "', handler = '" . $this->db->escape($data['handler']) . "', order_img = '" . $this->db->escape($data['order_img']) . "', order_fee = '" . (float)$data['order_fee'] . "', images = '" . $this->db->escape($data['images']) . "', solution = '" . $this->db->escape($data['solution']) . "', ship_name = '" . $this->db->escape($data['ship_name']) . "', ship_no = '" . $this->db->escape($data['ship_no']) . "', reship_name = '" . $this->db->escape($data['reship_name']) . "', reship_no = '" . $this->db->escape($data['reship_no']) . "', ship_fee = '" . (float)$data['ship_fee'] . "', reship_fee = '" . (float)$data['reship_fee'] . "', refund_fee = '" . (float)$data['refund_fee'] . "', loss_fee = '" . (float)$data['loss_fee'] . "', status = '0', remark = '" . $this->db->escape($data['remark']) . "', date_added = NOW(), date_modified = NOW()");

        $loss_id = $this->db->getLastId();

        return $loss_id;
    }

    public function editLoss($loss_id, $data) {
        if (isset($data['order_img'])) {
            $data['order_img'] = implode(',', $data['order_img']);
        } else {
            $data['order_img'] = '';
        }

        if (isset($data['images'])) {
            $data['images'] = implode(',', $data['images']);
        } else {
            $data['images'] = '';
        }

        $this->db->query("UPDATE " . DB_PREFIX . "loss SET store_id = '" . (int)$data['store_id'] . "', responsible = '" . $this->db->escape($data['responsible']) . "', reason = '" . $this->db->escape($data['reason']) . "', order_no = '" . $this->db->escape($data['order_no']) . "', buyer_id = '" . $this->db->escape($data['buyer_id']) . "', buyer_location = '" . $this->db->escape($data['buyer_location']) . "', bsku = '" . $this->db->escape($data['bsku']) . "', spec_name = '" . $this->db->escape($data['spec_name']) . "', quantity = '" . abs((int)$data['quantity']) . "', cost_fee = '" . (float)$data['cost_fee'] . "', supplier = '" . $this->db->escape($data['supplier']) . "', handler = '" . $this->db->escape($data['handler']) . "', order_img = '" . $this->db->escape($data['order_img']) . "', order_fee = '" . (float)$data['order_fee'] . "', images = '" . $this->db->escape($data['images']) . "', solution = '" . $this->db->escape($data['solution']) . "', ship_name = '" . $this->db->escape($data['ship_name']) . "', ship_no = '" . $this->db->escape($data['ship_no']) . "', reship_name = '" . $this->db->escape($data['reship_name']) . "', reship_no = '" . $this->db->escape($data['reship_no']) . "', ship_fee = '" . (float)$data['ship_fee'] . "', reship_fee = '" . (float)$data['reship_fee'] . "', refund_fee = '" . (float)$data['refund_fee'] . "', loss_fee = '" . (float)$data['loss_fee'] . "', remark = '" . $this->db->escape($data['remark']) . "', date_modified = NOW() WHERE loss_id = '" . (int)$loss_id . "'");
    }

    public function deleteLoss($loss_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "loss SET status = '-1' WHERE loss_id = '" . (int)$loss_id . "'");
        // $this->db->query("DELETE FROM " . DB_PREFIX . "loss WHERE loss_id = '" . (int)$loss_id . "'");
    }

    public function getLoss($loss_id) {
        $query = $this->db->query("SELECT *, cost_fee * quantity AS allcost FROM " . DB_PREFIX . "loss WHERE loss_id = '" . (int)$loss_id . "'");

        return $query->row;
    }

    public function getLosses($data = array()) {
        $sql = "SELECT *, cost_fee * quantity AS allcost FROM " . DB_PREFIX . "loss WHERE status >= '0'";

        if (in_array($this->user->real_name, array('优源工艺品有限公司', '豪丽工艺品', '万腾工艺品厂'))) {
            $sql .= " AND supplier = '" . $this->db->escape($this->user->real_name) . "' AND responsible = '工厂责任'";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(supplier, bsku, spec_name, buyer_id, buyer_location, order_no) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_reason'])) {
            $sql .= " AND CONCAT(responsible, '-', reason) LIKE '%" . $this->db->escape($data['filter_reason']) . "%'";
        }

        if (!empty($data['filter_solution'])) {
            $sql .= " AND solution = '" . $this->db->escape($data['filter_solution']) . "'";
        }

        if (!empty($data['filter_user'])) {
            $sql .= " AND user_id = '" . (int)$data['filter_user'] . "'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_reshipno'])) {
            $sql .= " AND reship_name != '' AND reship_no = ''";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $sort_data = array(
            'loss_id',
            'user_id',
            'responsible',
            'bsku',
            'supplier',
            'solution',
            'buyer_location',
            'allcost',
            'cost_fee',
            'loss_fee',
            'date_added'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY date_added";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalLosses($data = array()) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "loss WHERE status >= '0'";

        if (in_array($this->user->real_name, array('优源工艺品有限公司', '豪丽工艺品', '万腾工艺品厂'))) {
            $sql .= " AND supplier = '" . $this->db->escape($this->user->real_name) . "' AND responsible = '工厂责任'";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(supplier, bsku, spec_name, buyer_id, buyer_location, order_no) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_reason'])) {
            $sql .= " AND CONCAT(responsible, '-', reason) LIKE '%" . $this->db->escape($data['filter_reason']) . "%'";
        }

        if (!empty($data['filter_solution'])) {
            $sql .= " AND solution = '" . $this->db->escape($data['filter_solution']) . "'";
        }

        if (!empty($data['filter_user'])) {
            $sql .= " AND user_id = '" . (int)$data['filter_user'] . "'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_reshipno'])) {
            $sql .= " AND reship_name != '' AND reship_no = ''";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getRanks($data = array()) {
        $tmp1 = "SELECT bsku, spec_name, SUM(quantity) AS allquan, SUM(cost_fee * quantity) AS allcost FROM " . DB_PREFIX . "loss WHERE status >= '0'";

        if (!empty($data['filter_name'])) {
            $tmp1 .= " AND CONCAT(supplier, bsku, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_reason'])) {
            $tmp1 .= " AND CONCAT(responsible, '-', reason) LIKE '%" . $this->db->escape($data['filter_reason']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $tmp1 .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $tmp1 .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $tmp1 .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $tmp1 .= " GROUP BY bsku";

        $tmp2 = "SELECT bsku, SUM(ship_quan) AS allship FROM " . DB_PREFIX . "store_shipment WHERE 1";

        if (!empty($data['filter_store'])) {
            $tmp2 .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $tmp2 .= " AND DATE(ship_date) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $tmp2 .= " AND DATE(ship_date) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $tmp2 .= " GROUP BY bsku HAVING allship > '0'";

        $sql = "SELECT t1.*, t2.allship, t1.allquan / t2.allship AS ratio FROM (" . $tmp1 . ") t1 LEFT JOIN (" . $tmp2 . ") t2 ON (t1.bsku = t2.bsku)";

        $sort_data = array(
            'bsku',
            'spec_name',
            'allquan',
            'allcost',
            'allship',
            'ratio'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY ratio";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalRanks($data = array()) {
        $sql = "SELECT COUNT(loss_id) AS total FROM " . DB_PREFIX . "loss WHERE status >= '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(supplier, bsku, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_reason'])) {
            $sql .= " AND CONCAT(responsible, '-', reason) LIKE '%" . $this->db->escape($data['filter_reason']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $sql .= " GROUP BY bsku";

        $query = $this->db->query($sql);

        return $query->num_rows;
    }

    public function getWdtProviders($data) {
        $sql = "SELECT provider_name, spec_no, spec_name, CASE last_price WHEN 0 THEN price ELSE last_price END price FROM wdt_purchase_provider_goods_list WHERE provider_no IN (SELECT provider_no FROM wdt_purchase_provider WHERE is_disabled IS NULL AND deleted IS NULL)";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        $sql .= " ORDER BY spec_no ASC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getUsers() {
        $users = array();

        $query = $this->db->query("SELECT u.real_name, ui.user_id FROM _union u LEFT JOIN " . DB_PREFIX . "union_info ui ON (u.union_id = ui.union_id) WHERE ui.user_id IN (SELECT DISTINCT user_id FROM " . DB_PREFIX . "loss)");

        foreach ($query->rows as $row) {
            $users[$row['user_id']] = $row['real_name'];
        }

        return $users;
    }

    public function getReasons() {
        return array(
            array('name' => '工厂责任', 'subs' => array('破损', '瑕疵', '包装', '掉漆', '色差', '工厂装错货', '粘胶不沾', '批发退回')),
            array('name' => '快递责任', 'subs' => array('破损', '丢件', '投寄错误')),
            array('name' => '内部责任', 'subs' => array('错发', '漏发', '订单错误'))
        );
    }

    public function getLocations() {
        return array('北京市', '天津市', '河北省', '山西省', '内蒙古自治区', '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省', '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区', '海南省', '重庆市', '四川省', '贵州省', '云南省', '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区', '新疆维吾尔自治区', '台湾省', '香港特别行政区', '澳门特别行政区', '海外');
    }

    public function getSolutions() {
        return array('退货', '无退货');
    }

    public function getShipnames() {
        return array('申通', '韵达', '邮政', '顺丰', '京东', '壹米滴答', '汇森', '中通快运', '极兔');
    }

    public function getCellLink($str_img) {
        $link = array();

        if (!empty($str_img)) {
            $arr_img = explode(',', $str_img);
            foreach ($arr_img as $key => $img) {
                $arr_img[$key] = HTTP_IMAGE . $img;
            }

            $link['link'] = $arr_img[0];
            $link['text'] = implode(PHP_EOL, $arr_img);
        }

        return $link;
    }

    public function getLastWdtProviders($spec_no) {
        $last = $this->db->query("SELECT * FROM wdt_purchase_provider_goods_list WHERE spec_no = '".$spec_no."' ORDER BY rec_id DESC LIMIT 0,1");

        return $last->row;
    }
}
