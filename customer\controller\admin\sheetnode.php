<?php


class ControllerAdminSheetnode extends Controller
{
    private $error = array();
    private $userList = array();

    public function __construct(){
        $this->load->model('admin/sheetnode');
        $userList = $this->model_admin_sheetnode->userList();
        $userList[] = ['union_id'=>-1,'real_name'=>'自动通过,不审核'];
        $this->userList = array_column($userList,null,'union_id');
    }

    public function nodeList()
    {
        
        $this->load->model('admin/sheetnode');

        if (isset($this->request->get['filter_flow_id'])) {
            $filter_flow_id = $this->request->get['filter_flow_id'];
        } else {
            $filter_flow_id = 0;
        }


        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }


        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'create_time';
        }


        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

   

        if (isset($this->request->get['filter_flow_id'])) {
            $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

       

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }


        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        $data['add'] = $this->url->link('admin/sheetnode/sheetnode_add', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/sheetnode/sheetnode_del', 'token=' . $this->session->data['token'] . $url);




        $filter_data = array(
            'filter_name' => $filter_name,
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'filter_flow_id' => $filter_flow_id,
            'sort'              => $sort,
            'order'             => $order,
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );

        $flowList = $this->model_admin_sheetnode->getFlowList();
        $flowListById = $flowList ? array_column($flowList,'flow_name','flow_id') : [];
        $results = $this->model_admin_sheetnode->getNodes($filter_data);

        $data['flow_list'] = $flowList;

        foreach ($results as $k => $result) {
            $data['column'][$k] = array(
                'flow_name'=>$flowListById[$result['flow_id']] ?? '已删除',
                'node_id' => $result['node_id'],
                'name' => $result['node_name'],
                'day' => $result['day'],
                'sort' => $result['sort'],
                'entry' => date('Y-m-d H:i:s', $result['create_time']),
                'sponsor_status'=> $result['sponsor_status'] == 0 ? '下级审核' : '免审',
                'edit' => $this->url->link('admin/sheetnode/sheetnode_edit', 'token=' . $this->session->data['token'] . '&node_id=' . $result['node_id'] . $url),
            );
            if(strpos($result['_union_id'],',') !== false){
                $temp = explode(',',$result['_union_id']);
                $temp_arr = [];
                foreach($temp as $v){
                    $temp_arr[] = isset($this->userList[$v]['real_name']) ? $this->userList[$v]['real_name'] : '已离职';
                }
                $data['column'][$k]['real_name'] = implode(',',$temp_arr);
                unset($temp);unset($temp_arr);
            }else{
                $data['column'][$k]['real_name'] = $this->userList[$result['_union_id']]['real_name'];
            }
        }


        $total = $this->model_admin_sheetnode->getTotalNodes($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_flow_id'])) {
            $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }


       


        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }





        $data['sort_name'] = $this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token'] . '&sort=name' . $url);
        $data['sort_createTime'] = $this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token'] . '&sort=createTime' . $url);
        $data['sort_sort'] = $this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token'] . '&sort=sort' . $url);
        $data['sort_type'] = $this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token'] . '&sort=' . $url);


        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_flow_id'] = $filter_flow_id;

        $data['nofilter'] = $this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('sheetnode/sheetnode_list.tpl', $data));
    }



    public function sheetnode_add()
    {

        $this->load->model('admin/sheetnode');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateFlowForm()) {
            $this->model_admin_sheetnode->addNode($this->request->post);


            $this->session->data['success'] = '添加成功';

            $url = '';


            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }
    
            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }
    
            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }
    
            if (isset($this->request->get['filter_flow_id'])) {
                $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getFlowForm();
    }

    public function sheetnode_edit()
    {

        $this->load->model('admin/sheetnode');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateFlowForm($this->request->get['node_id'])) {
            $this->model_admin_sheetnode->editNode($this->request->get['node_id'], $this->request->post);


            $this->session->data['success'] = $this->language->get('编辑成功');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }
    
            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }
    
            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }
    
            if (isset($this->request->get['filter_flow_id'])) {
                $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getFlowForm();
    }

    public function sheetnode_del()
    {
        $this->load->model('admin/sheetnode');

        if (isset($this->request->post['selected']) ) {
            foreach ($this->request->post['selected'] as $node_id) {
                $this->model_admin_sheetnode->deleteNode(intval($node_id));
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';
    

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }
    
            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }
    
            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }
    
            if (isset($this->request->get['filter_flow_id'])) {
                $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->nodeList();
    }


    protected function getFlowForm()
    {

        $this->load->model('admin/sheetnode');
        $data['text_form'] = !isset($this->request->get['node_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_flow_id'])) {
            $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }
  

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }


        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        if (!isset($this->request->get['node_id'])) {
            $data['action'] = $this->url->link('admin/sheetnode/sheetnode_add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/sheetnode/sheetnode_edit', 'token=' . $this->session->data['token'] . '&node_id=' . $this->request->get['node_id'] . $url);
        }


        $data['cancel'] = $this->url->link('admin/sheetnode/nodeList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['node_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $node_info = $this->model_admin_sheetnode->getFlowInfo($this->request->get['node_id']);
        }

        if (isset($this->request->post['flow_id'])) {
            $data['flow_id'] = $this->request->post['flow_id'];
        } elseif (!empty($node_info)) {
            $data['flow_id'] = $node_info['flow_id'];
        } else {
            $data['flow_id'] = 0;
        }

        if (isset($this->request->post['day'])) {
            $data['day'] = $this->request->post['day'];
        } elseif (!empty($node_info)) {
            $data['day'] = $node_info['day'];
        } else {
            $data['day'] = 0;
        }

        
        if (isset($this->request->post['is_score'])) {
            $data['is_score'] = $this->request->post['is_score'];
        } elseif (!empty($node_info)) {
            $data['is_score'] = $node_info['is_score'];
        } else {
            $data['is_score'] = 1;
        }

        


        if (isset($this->request->post['name'])) {
            $data['name'] = $this->request->post['name'];
        } elseif (!empty($node_info)) {
            $data['name'] = $node_info['node_name'];
        } else {
            $data['name'] = '';
        }

        if (isset($this->request->post['union_id'])) {
            $data['union_id'] = $this->request->post['union_id'] ? implode(',',$this->request->post['union_id']) : '';
        } elseif (!empty($node_info)) {
            $data['union_id'] = $node_info['_union_id'];
        } else {
            $data['union_id'] = '';
        }
        
        

        if (isset($this->request->post['sort'])) {
            $data['sort'] = $this->request->post['sort'];
        } elseif (!empty($node_info)) {
            $data['sort'] = $node_info['sort'];
        } else {
            $data['sort'] = 0;
        }

        if (isset($this->request->post['sponsor_id'])) {
            $data['sponsor_id'] = $this->request->post['sponsor_id'];
        } elseif (!empty($node_info)) {
            $data['sponsor_id'] = $node_info['sponsor_id'];
        } else {
            $data['sponsor_id'] = 0;
        }

        if (isset($this->request->post['sponsor_status'])) {
            $data['sponsor_status'] = $this->request->post['sponsor_status'];
        } elseif (!empty($node_info)) {
            $data['sponsor_status'] = $node_info['sponsor_status'];
        } else {
            $data['sponsor_id'] = 0;
        }



        $data['users'] = $this->userList;


        $data['flow_list'] = $this->model_admin_sheetnode->getFlowList();


        if($data['union_id']){
            if(strpos($data['union_id'],',') !== false){
                $data['union_id'] = explode(',',$data['union_id']);
            }else{
                $data['union_id'] = [$data['union_id']];
            }
        }else{
            $data['union_id'] = array();
        }



   

        $data['node_id'] = isset($this->request->get['node_id']) ?? 0;


        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('sheetnode/sheetnode_form.tpl', $data));

    }

 
    protected function validateFlowForm($node_id = 0)
    {
        if (!$this->user->hasPermission('modify', 'admin/sheetnode')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (empty($this->request->post['flow_id'])) {
            $this->error['warning'] = '流程未选!';
            return false;
        }

        if (empty($this->request->post['name'])) {
            $this->error['warning'] = '步骤名称必填!';
            return false;
        }

        if (empty($this->request->post['union_id'])) {
            $this->error['warning'] = '参与人员必填!';
            return false;
        }

        if (empty($this->request->post['day'])) {
            $this->error['warning'] = '工时天数必填!';
            return false;
        }

        return !$this->error;
    }


    //    // 流程图
    //    public function nodeActImage(){
    //     $this->load->model('admin/sheetnode');
    //     $this->load->model('admin/sheetnodeact');
    //     $nodeList = $this->model_admin_sheetnode->getNodes(['filter_flow_id'=>2]);
    //     $nodeIds = $nodeList ? implode(',',array_column($nodeList,'node_id')) : 0;
    //     $nodeListById = array_column($nodeList,null,'node_id');
    //     $actList = $this->model_admin_sheetnodeact->getActList($nodeIds);

    //     foreach($actList as $key=>$val){
    //         $actList[$key]['node_name'] = $nodeListById[$val['child_node_id']]['node_name'] ?? '未设置';
    //         $actList[$key]['union_id'] = $nodeListById[$val['child_node_id']]['_union_id'] ?? '';
    //         $actList[$key]['day'] = $nodeListById[$val['child_node_id']]['day'] ?? '';

    //         if(strpos($nodeListById[$val['child_node_id']]['_union_id'],',') !== false){
    //             $temp = explode(',',$nodeListById[$val['child_node_id']]['_union_id']);
    //             $temp_arr = [];
    //             foreach($temp as $v){
    //                 $temp_arr[] = isset;
    //             }
    //             $actList[$key]['union_id'] = implode(',',$temp_arr);
    //             unset($temp);unset($temp_arr);
    //         }else{
    //             $actList[$key]['union_id'] = $this->userList[$nodeListById[$val['child_node_id']]['_union_id']]['real_name'];
    //         }
         

    //     }

    //     $data['actList'] = $actList; 

    //     $this->response->setOutput($this->load->view('sheetnode/sheetnodeact.html',$data));
    // }

}
