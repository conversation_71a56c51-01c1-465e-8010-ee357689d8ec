<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        客户信息
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>客户关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="客户/联系人/联系方式/客户店铺" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>业务归属：</label>
                  <select class="form-control" name="filter_store" onchange="$('#groups').load('<?php echo $getGroup; ?>&store_id=' + this.value + '&group_id=<?php echo $filter_group; ?>');">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>客户级别：</label>
                  <select class="form-control" name="filter_group" id="groups"></select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>首次下单时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_first_start) && !empty($filter_first_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" id="first" placeholder="起始时间 - 截止时间" value="<?php echo $filter_first_start; ?> - <?php echo $filter_first_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" id="first" placeholder="起始时间 - 截止时间" value="<?php echo $filter_first_start; ?><?php echo $filter_first_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_first_start" id="filter-start-first" placeholder="" value="<?php echo $filter_first_start; ?>">
                    <input type="text" class="hidden" name="filter_first_end" id="filter-end-first" placeholder="" value="<?php echo $filter_first_end; ?>">
                  </div>
                </div>
              </div>
          </div>
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>进货类型：</label>
                  <input type="text" class="form-control" name="filter_product" placeholder="进货类型" value="<?php echo $filter_product; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>销售渠道：</label>
                  <select class="form-control" name="filter_channel">
                    <option value="*">全部渠道</option>
                    <?php foreach($channels as $channel) { ?>
                    <?php if ($channel['channel'] == $filter_channel) { ?>
                    <option value="<?php echo $channel['channel']; ?>" selected="selected"><?php echo $channel['channel']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $channel['channel']; ?>"><?php echo $channel['channel']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>状态：</label>
                  <select class="form-control" name="filter_state">
                    <option value="*">全部状态</option>
                    <?php foreach($states as $state) { ?>
                    <?php if ($state['state'] == $filter_state) { ?>
                    <option value="<?php echo $state['state']; ?>" selected="selected"><?php echo $state['state']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $state['state']; ?>"><?php echo $state['state']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>最后下单时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_last_start) && !empty($filter_last_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" id="last" placeholder="起始时间 - 截止时间" value="<?php echo $filter_last_start; ?> - <?php echo $filter_last_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" id="last" placeholder="起始时间 - 截止时间" value="<?php echo $filter_last_start; ?><?php echo $filter_last_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_last_start" id="filter-start-last" placeholder="" value="<?php echo $filter_last_start; ?>">
                    <input type="text" class="hidden" name="filter_last_end" id="filter-end-last" placeholder="" value="<?php echo $filter_last_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">客户列表</h3>
          <div class="box-tools">
            <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
            <a class="btn btn-sm btn-danger" href="<?php echo $autoGroup; ?>">批量分级</a>
          </div>
        </div>
        <div class="box-body table-responsive no-padding">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'nickname') { ?>
                  <a href="<?php echo $sort_name; ?>" class="<?php echo strtolower($order); ?>">客户</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_name; ?>">客户</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">业务归属</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">业务归属</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'customer_group_id') { ?>
                  <a href="<?php echo $sort_group; ?>" class="<?php echo strtolower($order); ?>">客户级别</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_group; ?>">客户级别</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'realname') { ?>
                  <a href="<?php echo $sort_realname; ?>" class="<?php echo strtolower($order); ?>">联系人</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_realname; ?>">联系人</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'telephone') { ?>
                  <a href="<?php echo $sort_telephone; ?>" class="<?php echo strtolower($order); ?>">联系方式（电话和邮箱）</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_telephone; ?>">联系方式（电话和邮箱）</a>
                <?php } ?>
              </th>

              <th>
                <?php if ($sort == 'channel') { ?>
                  <a href="<?php echo $sort_channel; ?>" class="<?php echo strtolower($order); ?>">销售渠道</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_channel; ?>">销售渠道</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'product') { ?>
                  <a href="<?php echo $sort_product; ?>" class="<?php echo strtolower($order); ?>">进货类型</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_product; ?>">进货类型</a>
                <?php } ?>
              </th> 
              <th>
                <?php if ($sort == 'order_total') { ?>
                  <a href="<?php echo $sort_total; ?>" class="<?php echo strtolower($order); ?>">累计金额</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_total; ?>">累计金额</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'state') { ?>
                  <a href="<?php echo $sort_state; ?>" class="<?php echo strtolower($order); ?>">状态</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_state; ?>">状态</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'date_first') { ?>
                  <a href="<?php echo $sort_first; ?>" class="<?php echo strtolower($order); ?>">首次下单时间</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_first; ?>">首次下单时间</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'date_last') { ?>
                  <a href="<?php echo $sort_last; ?>" class="<?php echo strtolower($order); ?>">最后下单时间</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_last; ?>">最后下单时间</a>
                <?php } ?>
              </th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($customers)) { ?>
            <?php foreach ($customers as $customer) { ?>
            <tr data-id="<?php echo $customer['customer_id']; ?>">
              <td><?php echo $customer['nickname']; ?><br>
                <?php if (!empty($customer['shop_name'])) { ?>
                <?php echo $customer['shop_name']; ?>
                <?php } else{ ?>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">客户店铺</button>
                <?php } ?>
              </td>
              <td><?php echo $customer['storename']; ?></td>
              <td><?php echo $customer['groupname']; ?></td>
              <td><?php echo $customer['realname']; ?></td>
              <td><?php echo $customer['telephone']; ?></td>
              <td><?php echo $customer['channel']; ?></td>
              <td><?php echo $customer['product']; ?></td>
              <td><?php echo $currency_symbol; ?> <?php echo $customer['order_total']; ?></td>
              <td><?php echo $customer['state']; ?></td>
              <td><?php echo $customer['date_first']; ?></td>
              <td>
                <?php echo $customer['date_last']; ?><br>
                <?php echo $currency_symbol; ?> <?php echo $customer['total_last']; ?>
              </td>
              <td class="text-right">
                <a class="btn btn-success" href="<?php echo $customer['edit']; ?>">修改</a>
                
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="13" align="center"> 暂无客户数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
      <!-- 删除 -->
      <div class="modal modal-primary fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $editShop; ?>" method="post" enctype="multipart/form-data" id="form-del" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">客户店铺信息</h4>
              </div>
              <div class="modal-body">
                <input id="del-id" name="customer_id" type="hidden" value="">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-shopname">店铺名称：</label>
                  <div class="col-sm-8">
                    <input type="text" name="shop_name" value="" placeholder="请输入店铺名称" id="input-shopname" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-shopplatform">店铺平台：</label>
                  <div class="col-sm-8">
                    <input type="text" name="shop_platform" value="" placeholder="请输入店铺平台" id="input-platform" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-shopurl">店铺链接：</label>
                  <div class="col-sm-8">
                    <input type="text" name="shop_url" value="" placeholder="请输入店铺链接" id="input-shopurl" class="form-control" />
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">关闭</button>
                <button id="del-yes" type="button" class="btn btn-outline">保存</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript"><!--
$('#groups').load('<?php echo $getGroup; ?>&store_id=<?php echo $filter_store; ?>&group_id=<?php echo $filter_group; ?>');
//--></script>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      var objid = $(this).attr('id')
      $('#filter-start-' + objid).val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end-' + objid).val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      var objid = $(this).attr('id')
      $(this).val('')
      $('#filter-start-' + objid).val('')
      $('#filter-end-' + objid).val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_group = $('select[name=\'filter_group\']').val();

      if (filter_group != '*') {
        url += '&filter_group=' + encodeURIComponent(filter_group);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_channel = $('select[name=\'filter_channel\']').val();

      if (filter_channel != '*') {
        url += '&filter_channel=' + encodeURIComponent(filter_channel);
      }

      var filter_product = $('input[name=\'filter_product\']').val();

      if (filter_product != '*') {
        url += '&filter_product=' + encodeURIComponent(filter_product);
      }

      var filter_state = $('select[name=\'filter_state\']').val();

      if (filter_state != '*') {
        url += '&filter_state=' + encodeURIComponent(filter_state);
      }
  
      var filter_first_start = $('input[name=\'filter_first_start\']').val();
  
      if (filter_first_start) {
        url += '&filter_first_start=' + encodeURIComponent(filter_first_start);
      }

      var filter_first_end = $('input[name=\'filter_first_end\']').val();
  
      if (filter_first_end) {
        url += '&filter_first_end=' + encodeURIComponent(filter_first_end);
      }

      var filter_last_start = $('input[name=\'filter_last_start\']').val();
  
      if (filter_last_start) {
        url += '&filter_last_start=' + encodeURIComponent(filter_last_start);
      }

      var filter_last_end = $('input[name=\'filter_last_end\']').val();
  
      if (filter_last_end) {
        url += '&filter_last_end=' + encodeURIComponent(filter_last_end);
      }

      location.href = url;
    });
  })()
</script>
<script type="text/javascript">
  (function() {
    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
  })()
</script>
<?php echo $footer; ?>