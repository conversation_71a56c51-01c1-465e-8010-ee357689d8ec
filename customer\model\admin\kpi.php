<?php
class ModelAdminKpi extends Model {
	public function addItem($data) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "kpi_item SET user_id = '" . (int)$this->user->user_id . "', rater_ids = '" . $this->db->escape(implode(',', $data['rater_ids'])) . "', item_score = '" . (int)$data['item_score'] . "', item_name = '" . $this->db->escape($data['item_name']) . "', item_desc = '" . $this->db->escape($data['item_desc']) . "', rule = '', status = '" . (int)$data['status'] . "', date_added = NOW()");
	}

	public function editItem($item_id, $data) {
		$this->db->query("UPDATE " . DB_PREFIX . "kpi_item SET rater_ids = '" . $this->db->escape(implode(',', $data['rater_ids'])) . "', item_score = '" . (int)$data['item_score'] . "', item_name = '" . $this->db->escape($data['item_name']) . "', item_desc = '" . $this->db->escape($data['item_desc']) . "', status = '" . (int)$data['status'] . "' WHERE item_id = '" . (int)$item_id . "'");
	}

	public function deleteItem($item_id) {
		$this->db->query("UPDATE " . DB_PREFIX . "kpi_item SET status = '-1' WHERE item_id = '" . (int)$item_id . "'");
	}

	public function getItem($item_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "kpi_item WHERE item_id = '" . (int)$item_id . "'");

		return $query->row;
	}

	public function getItems($data = array()) {
		$sql = "SELECT * FROM " . DB_PREFIX . "kpi_item WHERE status >= '0'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND item_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$sql .= " AND status = '" . (int)$data['filter_status'] . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

		$sort_data = array(
			'item_id',
			'rater_ids',
			'item_name',
			'item_score',
			'status',
			'date_added'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY date_added";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalItems($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "kpi_item WHERE status >= '0'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND item_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$sql .= " AND status = '" . (int)$data['filter_status'] . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function addUser($data) {
		$this->db->query("UPDATE " . DB_PREFIX . "union_info SET kpi_item_ids = '" . $this->db->escape(implode(',', $data['kpi_item_ids'])) . "', date_modified = NOW() WHERE user_id = '" . (int)$data['user_id'] . "'");
	}

	public function editUser($user_id, $data) {
		$this->db->query("UPDATE " . DB_PREFIX . "union_info SET kpi_item_ids = '" . $this->db->escape(implode(',', $data['kpi_item_ids'])) . "', date_modified = NOW() WHERE user_id = '" . (int)$user_id . "'");
	}
	
	public function deleteUser($user_id) {
		$this->db->query("UPDATE " . DB_PREFIX . "union_info SET kpi_item_ids = '', date_modified = NOW() WHERE user_id = '" . (int)$user_id . "'");
	}

	public function getUser($user_id) {
		$query = $this->db->query("SELECT user_id, kpi_item_ids FROM " . DB_PREFIX . "union_info WHERE user_id = '" . (int)$user_id . "'");

		return $query->row;
	}

	public function getUsers($data = array()) {
		$sql = "SELECT *, (SELECT real_name FROM _union WHERE union_id = ui.union_id) AS real_name FROM `" . DB_PREFIX . "union_info` ui  WHERE kpi_item_ids != '' ORDER BY user_id DESC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalUsers() {
		$sql = "SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "union_info` WHERE kpi_item_ids != ''";
		
		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function addUserMonthScore() {
		$month = date('Ym', strtotime('-1 month'));
		$raters = array();
		$results = $this->getItems(['filter_status' => 1]);

		foreach ($results as $result) {
        	$raters[$result['item_id']] = $result['rater_ids'];
        }

		$query = $this->db->query("SELECT user_id, kpi_item_ids FROM `" . DB_PREFIX . "union_info` WHERE kpi_item_ids != ''");

		foreach ($query->rows as $row) {
			foreach ((array)explode(',', $row['kpi_item_ids']) as $item_id) {
        		if (isset($raters[$item_id])) {
        			foreach ((array)explode(',', $raters[$item_id]) as $rater_id) {
						$this->db->query("INSERT INTO " . DB_PREFIX . "kpi_score SET item_id = '" . (int)$item_id . "', user_id = '" . (int)$row['user_id'] . "', rater_id = '" . (int)$rater_id . "', month = '" . (int)$month . "', score = '0', remark = '', status = '0', date_added = NOW()");
					}
        		}
        	}
		}
	}

	public function addPerson($score_id) {
		$total = 0;

		$rate_info = $this->db->query("SELECT item_id, user_id, `month` FROM " . DB_PREFIX . "kpi_score WHERE score_id = '" . (int)$score_id . "' AND rater_id = '" . (int)$this->user->user_id . "'")->row;

		if (!empty($rate_info)) {
			$results = $this->db->query("SELECT score FROM " . DB_PREFIX . "kpi_score WHERE item_id = '" . (int)$rate_info['item_id'] . "' AND user_id = '" . (int)$rate_info['user_id'] . "' AND month = '" . (int)$rate_info['month'] . "' AND status = '1' ORDER BY score DESC")->rows;

			if (count($results) > 5) {
				array_shift($results);
				array_pop($results);
			}

			foreach ($results as $result) {
				$total += (int)$result['score'];
			}

			$score = moneyformat($total / count($results));

			$kpi = -1;
			$item = $this->getItem($rate_info['item_id']);

			foreach ((array)json_decode($item['rule'], true) as $rule) {
				if ($score >= $rule['min'] && $score <= $rule['max']) {
					$kpi = moneyformat($item['item_score'] * $rule['kpi'] / 100);
					break;
				}
			}

			if ($kpi == -1) {
				$kpi = moneyformat($item['item_score'] * $score / 100);
			}

			$person = $this->db->query("SELECT person_id FROM " . DB_PREFIX . "kpi_person WHERE item_id = '" . (int)$rate_info['item_id'] . "' AND user_id = '" . (int)$rate_info['user_id'] . "' AND month = '" . (int)$rate_info['month'] . "'")->row;

			if (!empty($person)) {
				$this->db->query("UPDATE " . DB_PREFIX . "kpi_person SET score = '" . (float)$score . "', kpi = '" . (float)$kpi . "', date_modified = NOW() WHERE person_id = '" . (int)$person['person_id'] . "'");
			} else {
				$this->db->query("INSERT INTO " . DB_PREFIX . "kpi_person SET item_id = '" . (int)$rate_info['item_id'] . "', user_id = '" . (int)$rate_info['user_id'] . "', score = '" . (float)$score . "', kpi = '" . (float)$kpi . "', month = '" . (int)$rate_info['month'] . "', date_modified = NOW()");
			}
		}
	}

	public function addRate($data) {
		$this->db->query("UPDATE " . DB_PREFIX . "kpi_score SET score = '" . (int)$data['score'] . "', remark = '" . $this->db->escape($data['remark']) . "', status = '1', date_added = NOW() WHERE score_id = '" . (int)$data['score_id'] . "' AND rater_id = '" . (int)$this->user->user_id . "'");

		$this->addPerson($data['score_id']);
	}

	public function getRates($data = array()) {
		$sql = "SELECT * FROM " . DB_PREFIX . "kpi_score";

		if ($this->user->hasPermission('modify', 'admin/kpi')) {
			$sql .= " WHERE 1";
		} else {
			$sql .= " WHERE rater_id = '" . (int)$this->user->user_id . "'";
		}

		if (!empty($data['filter_name'])) {
			$sql .= " AND (user_id IN (SELECT user_id FROM `" . DB_PREFIX . "union_info` ui LEFT JOIN _union u ON (ui.union_id = u.union_id) WHERE u.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%') OR rater_id IN (SELECT user_id FROM `" . DB_PREFIX . "union_info` ui LEFT JOIN _union u ON (ui.union_id = u.union_id) WHERE u.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'))";
		}

		if (!empty($data['filter_item'])) {
			$sql .= " AND item_id = '" . (int)$data['filter_item'] . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND `month` >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND `month` <= '" . (int)$data['filter_date_end'] . "'";
        }

        if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$sql .= " AND status = '" . (int)$data['filter_status'] . "'";
		}

		$sql .= " ORDER BY score_id DESC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalRates($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "kpi_score";

		if ($this->user->hasPermission('modify', 'admin/kpi')) {
			$sql .= " WHERE 1";
		} else {
			$sql .= " WHERE rater_id = '" . (int)$this->user->user_id . "'";
		}

		if (!empty($data['filter_name'])) {
			$sql .= " AND (user_id IN (SELECT user_id FROM `" . DB_PREFIX . "union_info` ui LEFT JOIN _union u ON (ui.union_id = u.union_id) WHERE u.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%') OR rater_id IN (SELECT user_id FROM `" . DB_PREFIX . "union_info` ui LEFT JOIN _union u ON (ui.union_id = u.union_id) WHERE u.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'))";
		}

		if (!empty($data['filter_item'])) {
			$sql .= " AND item_id = '" . (int)$data['filter_item'] . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND `month` >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND `month` <= '" . (int)$data['filter_date_end'] . "'";
        }

        if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$sql .= " AND status = '" . (int)$data['filter_status'] . "'";
		}

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function getScores($data = array()) {
		$sql = "SELECT * FROM " . DB_PREFIX . "kpi_person";

		if ($this->user->hasPermission('modify', 'admin/kpi')) {
			$sql .= " WHERE 1";
		} else {
			$sql .= " WHERE user_id = '" . (int)$this->user->user_id . "'";
		}

		if (!empty($data['filter_name'])) {
			$sql .= " AND user_id IN (SELECT user_id FROM `" . DB_PREFIX . "union_info` ui LEFT JOIN _union u ON (ui.union_id = u.union_id) WHERE u.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%')";
		}

		if (!empty($data['filter_item'])) {
			$sql .= " AND item_id = '" . (int)$data['filter_item'] . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND `month` >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND `month` <= '" . (int)$data['filter_date_end'] . "'";
        }

		$sql .= " ORDER BY `month` DESC, user_id ASC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalScores($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "kpi_person";

		if ($this->user->hasPermission('modify', 'admin/kpi')) {
			$sql .= " WHERE 1";
		} else {
			$sql .= " WHERE user_id = '" . (int)$this->user->user_id . "'";
		}

		if (!empty($data['filter_name'])) {
			$sql .= " AND user_id IN (SELECT user_id FROM `" . DB_PREFIX . "union_info` ui LEFT JOIN _union u ON (ui.union_id = u.union_id) WHERE u.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%')";
		}

		if (!empty($data['filter_item'])) {
			$sql .= " AND item_id = '" . (int)$data['filter_item'] . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND `month` >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND `month` <= '" . (int)$data['filter_date_end'] . "'";
        }

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function getMyItems($item_ids) {
		if (empty($item_ids)) {
            return array();
        }

		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "kpi_item WHERE status = '1' AND FIND_IN_SET(item_id, '" . $this->db->escape($item_ids) . "')");

		return $query->rows;
	}

    public function getMonths() {
        $months = array();

        for ($i=1; $i <= 24; $i++) { 
            $months[] = date('Ym', strtotime('-' . $i . ' month'));
        }

        return $months;
    }
}
