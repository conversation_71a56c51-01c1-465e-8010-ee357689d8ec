<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-store">设计图：</label>
              <div class="col-sm-8">
                <img width="180" src="<?php echo HTTP_IMAGE . $design_image; ?>" class="img-thumbnail" />
                <input type="hidden" name="design_image" value="<?php echo $design_image; ?>" />
                <button type="button" class="btn-upload btn btn-success"><i class="fa fa-upload"></i> 上传</button>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-design-name">类别：</label>
              <div class="col-sm-8">
                <select class="form-control" id="type" name="type">
                  <?php foreach($types as $type_key => $type) { ?>
                  <option <?php if($this_type == $type_key) { ?>selected<?php } ?> value="<?php echo $type_key; ?>"><?php echo $type; ?></option>
                  <?php } ?>
                </select>

              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-design-name">设计名称：</label>
              <div class="col-sm-8">
                <input type="text" name="design_name" value="<?php echo $design_name; ?>" placeholder="请输入设计名称" id="input-design-name" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-designer">设计师：</label>
              <div class="col-sm-8">
                <select class="form-control" id="designer" name="designer">
                  <?php foreach($stylists as $stylist_key => $stylist) { ?>
                  <option <?php if($this_designer == $stylist_key) { ?>selected<?php } ?> value="<?php echo $stylist_key; ?>"><?php echo $stylist; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-designer">材质：</label>
              <div class="col-sm-8">
                <select class="form-control" id="texture" name="texture">
                  <?php foreach($textures as $texture_key => $texture) { ?>
                  <option <?php if($this_texture == $texture_key) { ?>selected<?php } ?> value="<?php echo $texture_key; ?>"><?php echo $texture; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-birth">设计时间：</label>
              <div class="col-sm-8">
                <input type="text" name="design_date" value="<?php echo ($design_date == '0000-00-00') ? '' : $design_date; ?>" placeholder="设计时间" id="input-birth" class="input-date form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-goods_no">货品编号：</label>
              <div class="col-sm-8">
                <input type="text" name="goods_no" value="<?php echo $goods_no; ?>" placeholder="货品编号" id="input-goods_no" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-design-name">投票情况：</label>
              <div class="col-sm-8">
                <select class="form-control" id="vote" name="vote">
                  <option value="0" <?php if($vote == 0) { ?>selected<?php } ?>>请选择投票情况</option>
                  <?php foreach($votes as $vote_key => $vote_value) { ?>
                  <option <?php if($vote == $vote_key) { ?>selected<?php } ?> value="<?php echo $vote_key; ?>"><?php echo $vote_value; ?></option>
                  <?php } ?>
                </select>

              </div>
            </div>

            <div class="form-group" style="display: none">
              <label class="col-sm-2 control-label" for="input-birth">入仓时间：</label>
              <div class="col-sm-8">
                <input type="text" name="warehousing_date" value="<?php echo ($warehousing_date == '0000-00-00') ? '' : $warehousing_date; ?>" placeholder="入仓时间" id="input-birth" class="input-date form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-category">分类：</label>
              <div class="col-sm-8">
                <select id="w1" class="form-control"  name="category[]" style="width: 80%;float: left " multiple data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">
                  <?php foreach($categories as $category) { ?>
                  <option value="<?php echo $category; ?>"><?php echo $category; ?></option>
                  <?php } ?>
                </select>

              </div>
            </div>


            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-design-name">售卖店铺：</label>
              <div class="col-sm-8">
                <div class="well well-sm" style="height: 250px; color: black; overflow: auto;">
                  <?php foreach($stores as $store) { ?>
                  <?php if (!empty($store['class_name'])) { ?>
                  <div class="checkbox">
                    <label>
                      <input type="checkbox" name="store_ids[]" data-class="<?php echo $store['class_name']; ?>" value="<?php echo $store['store_id']; ?>" <?php if(in_array($store['store_id'],$store_ids)) { ?>checked="checked"<?php } ?>>
                      <?php echo $store['name']; ?>
                    </label>
                  </div>
                  <?php } ?>
                  <?php } ?>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-remark">备注：</label>
              <div class="col-sm-8">
                <input type="text" name="remark" value="<?php echo $remark; ?>" placeholder="备注" id="input-remark" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>

<script>
  var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
  window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"35px","placeholder":"请选择分类","language":"zh-CN"};

  if (jQuery('#w1').data('select2')) { jQuery('#w1').select2('destroy'); }
  jQuery.when(jQuery('#w1').select2(select2_5eaa6d36)).done(initS2Loading('w1','s2options_c4acac00'));

  var access = '<?php echo $categoryjson; ?>'
  $("#w1").val($.parseJSON(access)).trigger("change");


  $('#w1').on('change', function(e) {
    var selectedValues = $('#w1').val();
    console.log(selectedValues); // 输出选中的值数组


    $('input[type="checkbox"]').prop('checked', false);
    selectedValues.forEach(function(item, index) {
      var class_name = item;
      if (class_name != ''){
        $('input[type="checkbox"]').each(function() {
          if (($(this).data('class').indexOf('综合') >= 0) || ($(this).data('class').indexOf(class_name) >= 0)) {
            $(this).prop('checked', true);
          }
        });
      } else {
        $('input[type="checkbox"]').prop('checked', false);
      }
    });
  });


</script>

<script type="text/javascript">

  $('.input-date').daterangepicker({
    autoApply: true,
    autoUpdateInput: false,
    singleDatePicker: true,
    timePicker: false,
    timePicker24Hour: false,
    locale: {
      format: 'YYYY-MM-DD',
      applyLabel: '确定',
      cancelLabel: '清除'
    }
  })
  $('.box-body').on('apply.daterangepicker', '.input-date', function(ev, picker) {
    $(this).val(picker.startDate.format('YYYY-MM-DD'))
  })
  $('.box-body').on('cancel.daterangepicker', '.input-date', function(ev, picker) {
    $(this).val('')
  })

  $('.btn-upload').on('click', function() {
    $('#form-upload').remove();

    var target = $(this);

    $('body').prepend('<form enctype="multipart/form-data" id="form-upload" style="display: none;"><input type="file" name="file" value="" accept="image/*, video/*" /><input type="hidden" name="token" value="" /></form>');

    $('#form-upload input[name=\'file\']').trigger('click');

    if (typeof timer != 'undefined') {
      clearInterval(timer);
    }

    timer = setInterval(function() {
      if ($('#form-upload input[name=\'file\']').val() != '') {
        clearInterval(timer);

        $.ajax({
          url: '<?php echo $getToken; ?>',
          type: 'get',
          dataType: 'json',
          success: function(json) {
            $('#form-upload input[name=\'token\']').val(json.uploadToken);
            $.ajax({
              url: 'https://up-z2.qiniup.com',
              type: 'post',
              dataType: 'json',
              data: new FormData($('#form-upload')[0]),
              cache: false,
              contentType: false,
              processData: false,
              beforeSend: function() {
                target.find('i').replaceWith('<i class="fa fa-circle-o-notch fa-spin"></i>');
                target.prop('disabled', true);
              },
              complete: function() {
                target.find('i').replaceWith('<i class="fa fa-upload"></i>');
                target.prop('disabled', false);
              },
              success: function(res) {
                target.parent().find('input[type=\'hidden\']').val(res.key);
                target.parent().find('img').attr('src', json.httpHost + res.key);
              },
              error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
              }
            });
          },
          error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
          }
        });
      }
    }, 500);
  });

  function toVaild() {
    var isValid = true;
    var inputName = $("#input-name").val()
    if (inputName == '') {
      isValid = false;
      $("#input-name").siblings('.text-danger').show();
    } else {
      $("#input-name").siblings('.text-danger').hide();
    }

    var selectedValues = $("#w1").select2('data');
    if (selectedValues.length > 0) {
      $("#w1").siblings('.text-danger').hide();
    } else {
      isValid = false;
      $("#w1").siblings('.text-danger').show();
    }

    var selectedValues2 = $("#w2").select2('data');
    if (selectedValues2.length > 0) {
      $("#w2").siblings('.text-danger').hide();
    } else {
      isValid = false;
      $("#w2").siblings('.text-danger').show();
    }

    var inputRoute = $("#input-route").val()
    if (inputRoute == '') {
      isValid = false;
      $("#input-route").siblings('.text-danger').show();
    } else {
      $("#input-route").siblings('.text-danger').hide();
    }

    if (isValid) {
      return true;
    } else {
      return false;
    }
  }
</script>
<?php echo $footer; ?>