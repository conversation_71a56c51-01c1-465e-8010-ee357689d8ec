<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">快递公司：</label>
              <div class="col-sm-8">
                <select  class="form-control select-province"  name="name">
                  <?php foreach ($all_template as $template) { ?>
                  <option <?php if(!empty($expressFeeSurcharges['name']) && $expressFeeSurcharges['name']==$template['express_fee_template_id']){ ?>selected<?php } ?> value="<?php echo $template['express_fee_template_id']; ?>"><?php echo $template['name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">执行月份：</label>
              <div class="col-sm-8">
                <input type="number" name="month" value="<?php if(!empty($expressFeeSurcharges['month'])){ ?><?php echo $expressFeeSurcharges['month']; ?><?php }else{ ?><?php } ?>" placeholder="请输入执行月份" id="input-month" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">开始日期：</label>
              <div class="col-sm-8">
                <input type="number" name="s_day" value="<?php if(!empty($expressFeeSurcharges['s_day'])){ ?><?php echo $expressFeeSurcharges['s_day']; ?><?php }else{ ?><?php } ?>" placeholder="请输入开始日期" id="input-s_day" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">结束日期：</label>
              <div class="col-sm-8">
                <input type="number" max="31" name="e_day" value="<?php if(!empty($expressFeeSurcharges['e_day'])){ ?><?php echo $expressFeeSurcharges['e_day']; ?><?php }else{ ?><?php } ?>" placeholder="请输入结束日期" id="input-e_day" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script type="text/javascript">
  function toVaild() {
    // if ($('.glyphicon-info-sign').length) {
    //   confirm('数据不存在')
    //   return false;
    // }
    var name = document.getElementById("input-name").value;
    if (name == "") {
      confirm('请输入快递公司')
      return false;
    }
    return true;
  }


</script>
<?php echo $footer; ?>