<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        绩效管理
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索考核人" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>考核指标：</label>
                  <select class="form-control" name="filter_item">
                    <option value="*">全部指标</option>
                    <?php foreach ($items as $item) { ?>
                    <?php if ($item['item_id'] == $filter_item) { ?>
                    <option value="<?php echo $item['item_id']; ?>" selected="selected"><?php echo $item['item_name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $item['item_id']; ?>"><?php echo $item['item_name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>起始年月：</label>
                  <select class="form-control" name="filter_date_start">
                    <option value="*">不限年月</option>
                    <?php foreach($months as $month) { ?>
                    <?php if ($month == $filter_date_start) { ?>
                    <option value="<?php echo $month; ?>" selected="selected"><?php echo $month; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $month; ?>"><?php echo $month; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>截止年月：</label>
                  <select class="form-control" name="filter_date_end">
                    <option value="*">不限年月</option>
                    <?php foreach($months as $month) { ?>
                    <?php if ($month == $filter_date_end) { ?>
                    <option value="<?php echo $month; ?>" selected="selected"><?php echo $month; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $month; ?>"><?php echo $month; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">绩效列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>考核指标</th>
              <th>考核人</th>
              <th>所属年月</th>
              <th>考核分数</th>
              <th>绩效分值</th>
              <th>更新时间</th>
            </tr>
            <?php if (!empty($scores)) { ?>
              <?php foreach ($scores as $score) { ?>
                <tr>
                  <td><?php echo $score['item']; ?></td>
                  <td><?php echo $score['user']; ?></td>
                  <td><?php echo $score['month']; ?></td>
                  <td><?php echo $score['score']; ?></td>
                  <td><?php echo $score['kpi']; ?></td>
                  <td><?php echo $score['modified']; ?></td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="5" align="center"> 暂无绩效数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_item = $('select[name=\'filter_item\']').val();

      if (filter_item != '*') {
        url += '&filter_item=' + encodeURIComponent(filter_item);
      }

      var filter_date_start = $('select[name=\'filter_date_start\']').val();
  
      if (filter_date_start != '*') {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('select[name=\'filter_date_end\']').val();
  
      if (filter_date_end != '*') {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>