!function(e){function t(n){if(i[n])return i[n].exports;var r=i[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var i={};t.m=e,t.c=i,t.d=function(e,i,n){t.o(e,i)||Object.defineProperty(e,i,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var i=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(i,"a",i),i},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=2)}([function(e,t,i){var n,r,o,a=!0;!function(i,a){var s=function(){var e={};return a.apply(e,arguments),e.moxie};r=[],n=s,void 0!==(o="function"==typeof n?n.apply(t,r):n)&&(e.exports=o)}(this||window,function(){!function(e,t){"use strict";function i(e,t){for(var i,n=[],r=0;r<e.length;++r){if(!(i=s[e[r]]||o(e[r])))throw"module definition dependecy not found: "+e[r];n.push(i)}t.apply(null,n)}function n(e,n,r){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(n===t)throw"invalid module definition, dependencies must be specified";if(r===t)throw"invalid module definition, definition function must be specified";i(n,function(){s[e]=r.apply(null,arguments)})}function r(e){return!!s[e]}function o(t){for(var i=e,n=t.split(/[.\/]/),r=0;r<n.length;++r){if(!i[n[r]])return;i=i[n[r]]}return i}var s={};n("moxie/core/utils/Basic",[],function(){function e(e){return void 0===e?"undefined":null===e?"null":e.nodeType?"node":{}.toString.call(e).match(/\s([a-z|A-Z]+)/)[1].toLowerCase()}function t(){return a(!1,!1,arguments)}function i(){return a(!0,!1,arguments)}function n(){return a(!1,!0,arguments)}function r(){return a(!0,!0,arguments)}function o(i){switch(e(i)){case"array":return Array.prototype.slice.call(i);case"object":return t({},i)}return i}function a(t,i,n){var r=n[0];return u(n,function(n,s){s>0&&u(n,function(n,s){var u=-1!==f(e(n),["array","object"]);if(void 0===n||t&&void 0===r[s])return!0;u&&i&&(n=o(n)),e(r[s])===e(n)&&u?a(t,i,[r[s],n]):r[s]=n})}),r}function s(e,t){function i(){this.constructor=e}for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n]);return i.prototype=t.prototype,e.prototype=new i,e.__parent__=t.prototype,e}function u(e,t){var i,n,r;if(e){try{i=e.length}catch(e){i=void 0}if(void 0===i||"number"!=typeof i){for(n in e)if(e.hasOwnProperty(n)&&!1===t(e[n],n))return}else for(r=0;r<i;r++)if(!1===t(e[r],r))return}}function l(t){var i;if(!t||"object"!==e(t))return!0;for(i in t)return!1;return!0}function c(t,i){function n(o){"function"===e(t[o])&&t[o](function(e){++o<r&&!e?n(o):i(e)})}var r=t.length;"function"!==e(i)&&(i=function(){}),t&&t.length||i(),n(0)}function d(e,t){var i=0,n=e.length,r=new Array(n);u(e,function(e,o){e(function(e){if(e)return t(e);var a=[].slice.call(arguments);a.shift(),r[o]=a,++i===n&&(r.unshift(null),t.apply(this,r))})})}function f(e,t){if(t){if(Array.prototype.indexOf)return Array.prototype.indexOf.call(t,e);for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i}return-1}function p(t,i){var n=[];"array"!==e(t)&&(t=[t]),"array"!==e(i)&&(i=[i]);for(var r in t)-1===f(t[r],i)&&n.push(t[r]);return!!n.length&&n}function h(e,t){var i=[];return u(e,function(e){-1!==f(e,t)&&i.push(e)}),i.length?i:null}function m(e){var t,i=[];for(t=0;t<e.length;t++)i[t]=e[t];return i}function g(e){return e?String.prototype.trim?String.prototype.trim.call(e):e.toString().replace(/^\s*/,"").replace(/\s*$/,""):e}function v(e){if("string"!=typeof e)return e;var t,i={t:1099511627776,g:1073741824,m:1048576,k:1024};return e=/^([0-9\.]+)([tmgk]?)$/.exec(e.toLowerCase().replace(/[^0-9\.tmkg]/g,"")),t=e[2],e=+e[1],i.hasOwnProperty(t)&&(e*=i[t]),Math.floor(e)}function x(t){var i=[].slice.call(arguments,1);return t.replace(/%[a-z]/g,function(){var t=i.shift();return"undefined"!==e(t)?t:""})}function _(e,t){var i=this;setTimeout(function(){e.call(i)},t||1)}return{guid:function(){var e=0;return function(t){var i,n=(new Date).getTime().toString(32);for(i=0;i<5;i++)n+=Math.floor(65535*Math.random()).toString(32);return(t||"o_")+n+(e++).toString(32)}}(),typeOf:e,extend:t,extendIf:i,extendImmutable:n,extendImmutableIf:r,inherit:s,each:u,isEmptyObj:l,inSeries:c,inParallel:d,inArray:f,arrayDiff:p,arrayIntersect:h,toArray:m,trim:g,sprintf:x,parseSizeStr:v,delay:_}}),n("moxie/core/utils/Encode",[],function(){var e=function(e){return unescape(encodeURIComponent(e))},t=function(e){return decodeURIComponent(escape(e))};return{utf8_encode:e,utf8_decode:t,atob:function(e,i){if("function"==typeof window.atob)return i?t(window.atob(e)):window.atob(e);var n,r,o,a,s,u,l,c,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",f=0,p=0,h="",m=[];if(!e)return e;e+="";do{a=d.indexOf(e.charAt(f++)),s=d.indexOf(e.charAt(f++)),u=d.indexOf(e.charAt(f++)),l=d.indexOf(e.charAt(f++)),c=a<<18|s<<12|u<<6|l,n=c>>16&255,r=c>>8&255,o=255&c,m[p++]=64==u?String.fromCharCode(n):64==l?String.fromCharCode(n,r):String.fromCharCode(n,r,o)}while(f<e.length);return h=m.join(""),i?t(h):h},btoa:function(t,i){if(i&&(t=e(t)),"function"==typeof window.btoa)return window.btoa(t);var n,r,o,a,s,u,l,c,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",f=0,p=0,h="",m=[];if(!t)return t;do{n=t.charCodeAt(f++),r=t.charCodeAt(f++),o=t.charCodeAt(f++),c=n<<16|r<<8|o,a=c>>18&63,s=c>>12&63,u=c>>6&63,l=63&c,m[p++]=d.charAt(a)+d.charAt(s)+d.charAt(u)+d.charAt(l)}while(f<t.length);h=m.join("");var g=t.length%3;return(g?h.slice(0,g-3):h)+"===".slice(g||3)}}}),n("moxie/core/utils/Env",["moxie/core/utils/Basic"],function(e){function t(e,t,i){var n=0,r=0,o=0,a={dev:-6,alpha:-5,a:-5,beta:-4,b:-4,RC:-3,rc:-3,"#":-2,p:1,pl:1},s=function(e){return e=(""+e).replace(/[_\-+]/g,"."),e=e.replace(/([^.\d]+)/g,".$1.").replace(/\.{2,}/g,"."),e.length?e.split("."):[-8]},u=function(e){return e?isNaN(e)?a[e]||-7:parseInt(e,10):0};for(e=s(e),t=s(t),r=Math.max(e.length,t.length),n=0;n<r;n++)if(e[n]!=t[n]){if(e[n]=u(e[n]),t[n]=u(t[n]),e[n]<t[n]){o=-1;break}if(e[n]>t[n]){o=1;break}}if(!i)return o;switch(i){case">":case"gt":return o>0;case">=":case"ge":return o>=0;case"<=":case"le":return o<=0;case"==":case"=":case"eq":return 0===o;case"<>":case"!=":case"ne":return 0!==o;case"":case"<":case"lt":return o<0;default:return null}}var i=function(e){var t="name",i="version",n={has:function(e,t){return-1!==t.toLowerCase().indexOf(e.toLowerCase())},lowerize:function(e){return e.toLowerCase()}},r={rgx:function(){for(var e,t,i,n,r,o,a,s=0,u=arguments;s<u.length;s+=2){var l=u[s],c=u[s+1];if(void 0===e){e={};for(n in c)r=c[n],"object"==typeof r?e[r[0]]=void 0:e[r]=void 0}for(t=i=0;t<l.length;t++)if(o=l[t].exec(this.getUA())){for(n=0;n<c.length;n++)a=o[++i],r=c[n],"object"==typeof r&&r.length>0?2==r.length?"function"==typeof r[1]?e[r[0]]=r[1].call(this,a):e[r[0]]=r[1]:3==r.length?"function"!=typeof r[1]||r[1].exec&&r[1].test?e[r[0]]=a?a.replace(r[1],r[2]):void 0:e[r[0]]=a?r[1].call(this,a,r[2]):void 0:4==r.length&&(e[r[0]]=a?r[3].call(this,a.replace(r[1],r[2])):void 0):e[r]=a||void 0;break}if(o)break}return e},str:function(e,t){for(var i in t)if("object"==typeof t[i]&&t[i].length>0){for(var r=0;r<t[i].length;r++)if(n.has(t[i][r],e))return"?"===i?void 0:i}else if(n.has(t[i],e))return"?"===i?void 0:i;return e}},o={browser:{oldsafari:{major:{1:["/8","/1","/3"],2:"/4","?":"/"},version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}}},device:{sprint:{model:{"Evo Shift 4G":"7373KT"},vendor:{HTC:"APA",Sprint:"Sprint"}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2000:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",RT:"ARM"}}}},a={browser:[[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]+).+version\/([\w\.-]+)/i,/(opera).+version\/([\w\.]+)/i,/(opera)[\/\s]+([\w\.]+)/i],[t,i],[/\s(opr)\/([\w\.]+)/i],[[t,"Opera"],i],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]+)*/i,/(avant\s|iemobile|slim|baidu)(?:browser)?[\/\s]?([\w\.]*)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(rekonq)\/([\w\.]+)*/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi)\/([\w\.-]+)/i],[t,i],[/(trident).+rv[:\s]([\w\.]+).+like\sgecko/i],[[t,"IE"],i],[/(edge)\/((\d+)?[\w\.]+)/i],[t,i],[/(yabrowser)\/([\w\.]+)/i],[[t,"Yandex"],i],[/(comodo_dragon)\/([\w\.]+)/i],[[t,/_/g," "],i],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i,/(uc\s?browser|qqbrowser)[\/\s]?([\w\.]+)/i],[t,i],[/(dolfin)\/([\w\.]+)/i],[[t,"Dolphin"],i],[/((?:android.+)crmo|crios)\/([\w\.]+)/i],[[t,"Chrome"],i],[/XiaoMi\/MiuiBrowser\/([\w\.]+)/i],[i,[t,"MIUI Browser"]],[/android.+version\/([\w\.]+)\s+(?:mobile\s?safari|safari)/i],[i,[t,"Android Browser"]],[/FBAV\/([\w\.]+);/i],[i,[t,"Facebook"]],[/version\/([\w\.]+).+?mobile\/\w+\s(safari)/i],[i,[t,"Mobile Safari"]],[/version\/([\w\.]+).+?(mobile\s?safari|safari)/i],[i,t],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[t,[i,r.str,o.browser.oldsafari.version]],[/(konqueror)\/([\w\.]+)/i,/(webkit|khtml)\/([\w\.]+)/i],[t,i],[/(navigator|netscape)\/([\w\.-]+)/i],[[t,"Netscape"],i],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix)\/([\w\.-]+)/i,/(mozilla)\/([\w\.]+).+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]+)*/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[t,i]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[i,[t,"EdgeHTML"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[t,i],[/rv\:([\w\.]+).*(gecko)/i],[i,t]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[t,i],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*|windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[t,[i,r.str,o.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[t,"Windows"],[i,r.str,o.os.windows.version]],[/\((bb)(10);/i],[[t,"BlackBerry"],i],[/(blackberry)\w*\/?([\w\.]+)*/i,/(tizen)[\/\s]([\w\.]+)/i,/(android|webos|palm\os|qnx|bada|rim\stablet\sos|meego|contiki)[\/\s-]?([\w\.]+)*/i,/linux;.+(sailfish);/i],[t,i],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]+)*/i],[[t,"Symbian"],i],[/\((series40);/i],[t],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[t,"Firefox OS"],i],[/(nintendo|playstation)\s([wids3portablevu]+)/i,/(mint)[\/\s\(]?(\w+)*/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\/\s-]?([\w\.-]+)*/i,/(hurd|linux)\s?([\w\.]+)*/i,/(gnu)\s?([\w\.]+)*/i],[t,i],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[t,"Chromium OS"],i],[/(sunos)\s?([\w\.]+\d)*/i],[[t,"Solaris"],i],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]+)*/i],[t,i],[/(ip[honead]+)(?:.*os\s*([\w]+)*\slike\smac|;\sopera)/i],[[t,"iOS"],[i,/_/g,"."]],[/(mac\sos\sx)\s?([\w\s\.]+\w)*/i,/(macintosh|mac(?=_powerpc)\s)/i],[[t,"Mac OS"],[i,/_/g,"."]],[/((?:open)?solaris)[\/\s-]?([\w\.]+)*/i,/(haiku)\s(\w+)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.]*)*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms)/i,/(unix)\s?([\w\.]+)*/i],[t,i]]};return function(e){var t=e||(window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:"");this.getBrowser=function(){return r.rgx.apply(this,a.browser)},this.getEngine=function(){return r.rgx.apply(this,a.engine)},this.getOS=function(){return r.rgx.apply(this,a.os)},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS()}},this.getUA=function(){return t},this.setUA=function(e){return t=e,this},this.setUA(t)}}(),n=function(){var t={define_property:!1,create_canvas:function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))}(),return_response_type:function(t){try{if(-1!==e.inArray(t,["","text","document"]))return!0;if(window.XMLHttpRequest){var i=new XMLHttpRequest;if(i.open("get","/"),"responseType"in i)return i.responseType=t,i.responseType===t}}catch(e){}return!1},use_data_uri:function(){var e=new Image;return e.onload=function(){t.use_data_uri=1===e.width&&1===e.height},setTimeout(function(){e.src="data:image/gif;base64,R0lGODlhAQABAIAAAP8AAAAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw=="},1),!1}(),use_data_uri_over32kb:function(){return t.use_data_uri&&("IE"!==o.browser||o.version>=9)},use_data_uri_of:function(e){return t.use_data_uri&&e<33e3||t.use_data_uri_over32kb()},use_fileinput:function(){if(navigator.userAgent.match(/(Android (1.0|1.1|1.5|1.6|2.0|2.1))|(Windows Phone (OS 7|8.0))|(XBLWP)|(ZuneWP)|(w(eb)?OSBrowser)|(webOS)|(Kindle\/(1.0|2.0|2.5|3.0))/))return!1;var e=document.createElement("input");return e.setAttribute("type","file"),!e.disabled}};return function(i){var n=[].slice.call(arguments);return n.shift(),"function"===e.typeOf(t[i])?t[i].apply(this,n):!!t[i]}}(),r=(new i).getResult(),o={can:n,uaParser:i,browser:r.browser.name,version:r.browser.version,os:r.os.name,osVersion:r.os.version,verComp:t,swf_url:"../flash/Moxie.swf",xap_url:"../silverlight/Moxie.xap",global_event_dispatcher:"moxie.core.EventTarget.instance.dispatchEvent"};return o.OS=o.os,a&&(o.debug={runtime:!0,events:!1},o.log=function(){var t=arguments[0];if("string"===e.typeOf(t)&&(t=e.sprintf.apply(this,arguments)),window&&window.console&&window.console.log)window.console.log(t);else if(document){var i=document.getElementById("moxie-console");i||(i=document.createElement("pre"),i.id="moxie-console",document.body.appendChild(i)),-1!==e.inArray(e.typeOf(t),["object","array"])?function(e){i.appendChild(document.createTextNode(e+"\n"))}(t):i.appendChild(document.createTextNode(t+"\n"))}}),o}),n("moxie/core/Exceptions",["moxie/core/utils/Basic"],function(e){function t(e,t){var i;for(i in e)if(e[i]===t)return i;return null}return{RuntimeError:function(){function i(e,i){this.code=e,this.name=t(n,e),this.message=this.name+(i||": RuntimeError "+this.code)}var n={NOT_INIT_ERR:1,EXCEPTION_ERR:3,NOT_SUPPORTED_ERR:9,JS_ERR:4};return e.extend(i,n),i.prototype=Error.prototype,i}(),OperationNotAllowedException:function(){function t(e){this.code=e,this.name="OperationNotAllowedException"}return e.extend(t,{NOT_ALLOWED_ERR:1}),t.prototype=Error.prototype,t}(),ImageError:function(){function i(e){this.code=e,this.name=t(n,e),this.message=this.name+": ImageError "+this.code}var n={WRONG_FORMAT:1,MAX_RESOLUTION_ERR:2,INVALID_META_ERR:3};return e.extend(i,n),i.prototype=Error.prototype,i}(),FileException:function(){function i(e){this.code=e,this.name=t(n,e),this.message=this.name+": FileException "+this.code}var n={NOT_FOUND_ERR:1,SECURITY_ERR:2,ABORT_ERR:3,NOT_READABLE_ERR:4,ENCODING_ERR:5,NO_MODIFICATION_ALLOWED_ERR:6,INVALID_STATE_ERR:7,SYNTAX_ERR:8};return e.extend(i,n),i.prototype=Error.prototype,i}(),DOMException:function(){function i(e){this.code=e,this.name=t(n,e),this.message=this.name+": DOMException "+this.code}var n={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25};return e.extend(i,n),i.prototype=Error.prototype,i}(),EventException:function(){function t(e){this.code=e,this.name="EventException"}return e.extend(t,{UNSPECIFIED_EVENT_TYPE_ERR:0}),t.prototype=Error.prototype,t}()}}),n("moxie/core/utils/Dom",["moxie/core/utils/Env"],function(e){var t=function(e){return"string"!=typeof e?e:document.getElementById(e)},i=function(e,t){return!!e.className&&new RegExp("(^|\\s+)"+t+"(\\s+|$)").test(e.className)};return{get:t,hasClass:i,addClass:function(e,t){i(e,t)||(e.className=e.className?e.className.replace(/\s+$/,"")+" "+t:t)},removeClass:function(e,t){if(e.className){var i=new RegExp("(^|\\s+)"+t+"(\\s+|$)");e.className=e.className.replace(i,function(e,t,i){return" "===t&&" "===i?" ":""})}},getStyle:function(e,t){return e.currentStyle?e.currentStyle[t]:window.getComputedStyle?window.getComputedStyle(e,null)[t]:void 0},getPos:function(t,i){function n(e){var t,i,n=0,r=0;return e&&(i=e.getBoundingClientRect(),t="CSS1Compat"===l.compatMode?l.documentElement:l.body,n=i.left+t.scrollLeft,r=i.top+t.scrollTop),{x:n,y:r}}var r,o,a,s=0,u=0,l=document;if(t=t,i=i||l.body,t&&t.getBoundingClientRect&&"IE"===e.browser&&(!l.documentMode||l.documentMode<8))return o=n(t),a=n(i),{x:o.x-a.x,y:o.y-a.y};for(r=t;r&&r!=i&&r.nodeType;)s+=r.offsetLeft||0,u+=r.offsetTop||0,r=r.offsetParent;for(r=t.parentNode;r&&r!=i&&r.nodeType;)s-=r.scrollLeft||0,u-=r.scrollTop||0,r=r.parentNode;return{x:s,y:u}},getSize:function(e){return{w:e.offsetWidth||e.clientWidth,h:e.offsetHeight||e.clientHeight}}}}),n("moxie/core/EventTarget",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic"],function(e,t,i){function n(){this.uid=i.guid()}var r={};return i.extend(n.prototype,{init:function(){this.uid||(this.uid=i.guid("uid_"))},addEventListener:function(e,t,n,o){var a,s=this;if(this.hasOwnProperty("uid")||(this.uid=i.guid("uid_")),e=i.trim(e),/\s/.test(e))return void i.each(e.split(/\s+/),function(e){s.addEventListener(e,t,n,o)});e=e.toLowerCase(),n=parseInt(n,10)||0,a=r[this.uid]&&r[this.uid][e]||[],a.push({fn:t,priority:n,scope:o||this}),r[this.uid]||(r[this.uid]={}),r[this.uid][e]=a},hasEventListener:function(e){var t;return e?(e=e.toLowerCase(),t=r[this.uid]&&r[this.uid][e]):t=r[this.uid],t||!1},removeEventListener:function(e,t){var n,o,a=this;if(e=e.toLowerCase(),/\s/.test(e))return void i.each(e.split(/\s+/),function(e){a.removeEventListener(e,t)});if(n=r[this.uid]&&r[this.uid][e]){if(t){for(o=n.length-1;o>=0;o--)if(n[o].fn===t){n.splice(o,1);break}}else n=[];n.length||(delete r[this.uid][e],i.isEmptyObj(r[this.uid])&&delete r[this.uid])}},removeAllEventListeners:function(){r[this.uid]&&delete r[this.uid]},dispatchEvent:function(n){var o,s,u,l,c={},d=!0;if("string"!==i.typeOf(n)){if(l=n,"string"!==i.typeOf(l.type))throw new t.EventException(t.EventException.UNSPECIFIED_EVENT_TYPE_ERR);n=l.type,void 0!==l.total&&void 0!==l.loaded&&(c.total=l.total,c.loaded=l.loaded),c.async=l.async||!1}if(-1!==n.indexOf("::")?function(e){o=e[0],n=e[1]}(n.split("::")):o=this.uid,n=n.toLowerCase(),s=r[o]&&r[o][n]){s.sort(function(e,t){return t.priority-e.priority}),u=[].slice.call(arguments),u.shift(),c.type=n,u.unshift(c),a&&e.debug.events&&e.log("Event '%s' fired on %u",c.type,o);var f=[];i.each(s,function(e){u[0].target=e.scope,c.async?f.push(function(t){setTimeout(function(){t(!1===e.fn.apply(e.scope,u))},1)}):f.push(function(t){t(!1===e.fn.apply(e.scope,u))})}),f.length&&i.inSeries(f,function(e){d=!e})}return d},bindOnce:function(e,t,i,n){var r=this;r.bind.call(this,e,function i(){return r.unbind(e,i),t.apply(this,arguments)},i,n)},bind:function(){this.addEventListener.apply(this,arguments)},unbind:function(){this.removeEventListener.apply(this,arguments)},unbindAll:function(){this.removeAllEventListeners.apply(this,arguments)},trigger:function(){return this.dispatchEvent.apply(this,arguments)},handleEventProps:function(e){var t=this;this.bind(e.join(" "),function(e){var t="on"+e.type.toLowerCase();"function"===i.typeOf(this[t])&&this[t].apply(this,arguments)}),i.each(e,function(e){e="on"+e.toLowerCase(e),"undefined"===i.typeOf(t[e])&&(t[e]=null)})}}),n.instance=new n,n}),n("moxie/runtime/Runtime",["moxie/core/utils/Env","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/EventTarget"],function(e,t,i,n){function r(n,o,u,l,c){var d,f=this,p=t.guid(o+"_"),h=c||"browser";n=n||{},s[p]=this,u=t.extend({access_binary:!1,access_image_binary:!1,display_media:!1,do_cors:!1,drag_and_drop:!1,filter_by_extension:!0,resize_image:!1,report_upload_progress:!1,return_response_headers:!1,return_response_type:!1,return_status_code:!0,send_custom_headers:!1,select_file:!1,select_folder:!1,select_multiple:!0,send_binary_string:!1,send_browser_cookies:!0,send_multipart:!0,slice_blob:!1,stream_upload:!1,summon_file_dialog:!1,upload_filesize:!0,use_http_method:!0},u),n.preferred_caps&&(h=r.getMode(l,n.preferred_caps,h)),a&&e.debug.runtime&&e.log("\tdefault mode: %s",h),d=function(){var e={};return{exec:function(t,i,n,r){if(d[i]&&(e[t]||(e[t]={context:this,instance:new d[i]}),e[t].instance[n]))return e[t].instance[n].apply(this,r)},removeInstance:function(t){delete e[t]},removeAllInstances:function(){var i=this;t.each(e,function(e,n){"function"===t.typeOf(e.instance.destroy)&&e.instance.destroy.call(e.context),i.removeInstance(n)})}}}(),t.extend(this,{initialized:!1,uid:p,type:o,mode:r.getMode(l,n.required_caps,h),shimid:p+"_container",clients:0,options:n,can:function(e,i){var n=arguments[2]||u;if("string"===t.typeOf(e)&&"undefined"===t.typeOf(i)&&(e=r.parseCaps(e)),"object"===t.typeOf(e)){for(var o in e)if(!this.can(o,e[o],n))return!1;return!0}return"function"===t.typeOf(n[e])?n[e].call(this,i):i===n[e]},getShimContainer:function(){var e,n=i.get(this.shimid);return n||(e=i.get(this.options.container)||document.body,n=document.createElement("div"),n.id=this.shimid,n.className="moxie-shim moxie-shim-"+this.type,t.extend(n.style,{position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.appendChild(n),e=null),n},getShim:function(){return d},shimExec:function(e,t){var i=[].slice.call(arguments,2);return f.getShim().exec.call(this,this.uid,e,t,i)},exec:function(e,t){var i=[].slice.call(arguments,2);return f[e]&&f[e][t]?f[e][t].apply(this,i):f.shimExec.apply(this,arguments)},destroy:function(){if(f){var e=i.get(this.shimid);e&&e.parentNode.removeChild(e),d&&d.removeAllInstances(),this.unbindAll(),delete s[this.uid],this.uid=null,p=f=d=e=null}}}),this.mode&&n.required_caps&&!this.can(n.required_caps)&&(this.mode=!1)}var o={},s={};return r.order="html5,flash,silverlight,html4",r.getRuntime=function(e){return!!s[e]&&s[e]},r.addConstructor=function(e,t){t.prototype=n.instance,o[e]=t},r.getConstructor=function(e){return o[e]||null},r.getInfo=function(e){var t=r.getRuntime(e);return t?{uid:t.uid,type:t.type,mode:t.mode,can:function(){return t.can.apply(t,arguments)}}:null},r.parseCaps=function(e){var i={};return"string"!==t.typeOf(e)?e||{}:(t.each(e.split(","),function(e){i[e]=!0}),i)},r.can=function(e,t){var i,n,o=r.getConstructor(e);return!!o&&(i=new o({required_caps:t}),n=i.mode,i.destroy(),!!n)},r.thatCan=function(e,t){var i=(t||r.order).split(/\s*,\s*/);for(var n in i)if(r.can(i[n],e))return i[n];return null},r.getMode=function(i,n,r){var o=null;if("undefined"===t.typeOf(r)&&(r="browser"),n&&!t.isEmptyObj(i)){if(t.each(n,function(n,r){if(i.hasOwnProperty(r)){var s=i[r](n);if("string"==typeof s&&(s=[s]),o){if(!(o=t.arrayIntersect(o,s)))return a&&e.debug.runtime&&e.log("\t\t%c: %v (conflicting mode requested: %s)",r,n,s),o=!1}else o=s}a&&e.debug.runtime&&e.log("\t\t%c: %v (compatible modes: %s)",r,n,o)}),o)return-1!==t.inArray(r,o)?r:o[0];if(!1===o)return!1}return r},r.capTrue=function(){return!0},r.capFalse=function(){return!1},r.capTest=function(e){return function(){return!!e}},r}),n("moxie/runtime/RuntimeClient",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/Runtime"],function(e,t,i,n){return function(){var r;i.extend(this,{connectRuntime:function(o){function s(i){var u,c;return i.length?(u=i.shift().toLowerCase(),(c=n.getConstructor(u))?(a&&e.debug.runtime&&(e.log("Trying runtime: %s",u),e.log(o)),r=new c(o),r.bind("Init",function(){r.initialized=!0,a&&e.debug.runtime&&e.log("Runtime '%s' initialized",r.type),setTimeout(function(){r.clients++,l.ruid=r.uid,l.trigger("RuntimeInit",r)},1)}),r.bind("Error",function(){a&&e.debug.runtime&&e.log("Runtime '%s' failed to initialize",r.type),r.destroy(),s(i)}),r.bind("Exception",function(i,n){var r=n.name+"(#"+n.code+")"+(n.message?", from: "+n.message:"");a&&e.debug.runtime&&e.log("Runtime '%s' has thrown an exception: %s",this.type,r),l.trigger("RuntimeError",new t.RuntimeError(t.RuntimeError.EXCEPTION_ERR,r))}),a&&e.debug.runtime&&e.log("\tselected mode: %s",r.mode),r.mode?void r.init():void r.trigger("Error")):(a&&e.debug.runtime&&e.log("Constructor for '%s' runtime is not available.",u),void s(i))):(l.trigger("RuntimeError",new t.RuntimeError(t.RuntimeError.NOT_INIT_ERR)),void(r=null))}var u,l=this;if("string"===i.typeOf(o)?u=o:"string"===i.typeOf(o.ruid)&&(u=o.ruid),u){if(r=n.getRuntime(u))return l.ruid=u,r.clients++,r;throw new t.RuntimeError(t.RuntimeError.NOT_INIT_ERR)}s((o.runtime_order||n.order).split(/\s*,\s*/))},disconnectRuntime:function(){r&&--r.clients<=0&&r.destroy(),r=null},getRuntime:function(){return r&&r.uid?r:r=null},exec:function(){return r?r.exec.apply(this,arguments):null},can:function(e){return!!r&&r.can(e)}})}}),n("moxie/file/Blob",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient"],function(e,t,i){function n(o,a){function s(t,i,o){var a,s=r[this.uid];return"string"===e.typeOf(s)&&s.length?(a=new n(null,{type:o,size:i-t}),a.detach(s.substr(t,a.size)),a):null}i.call(this),o&&this.connectRuntime(o),a?"string"===e.typeOf(a)&&(a={data:a}):a={},e.extend(this,{uid:a.uid||e.guid("uid_"),ruid:o,size:a.size||0,type:a.type||"",slice:function(e,t,i){return this.isDetached()?s.apply(this,arguments):this.getRuntime().exec.call(this,"Blob","slice",this.getSource(),e,t,i)},getSource:function(){return r[this.uid]?r[this.uid]:null},detach:function(e){if(this.ruid&&(this.getRuntime().exec.call(this,"Blob","destroy"),this.disconnectRuntime(),this.ruid=null),e=e||"","data:"==e.substr(0,5)){var i=e.indexOf(";base64,");this.type=e.substring(5,i),e=t.atob(e.substring(i+8))}this.size=e.length,r[this.uid]=e},isDetached:function(){return!this.ruid&&"string"===e.typeOf(r[this.uid])},destroy:function(){this.detach(),delete r[this.uid]}}),a.data?this.detach(a.data):r[this.uid]=a}var r={};return n}),n("moxie/core/I18n",["moxie/core/utils/Basic"],function(e){var t={};return{addI18n:function(i){return e.extend(t,i)},translate:function(e){return t[e]||e},_:function(e){return this.translate(e)},sprintf:function(t){var i=[].slice.call(arguments,1);return t.replace(/%[a-z]/g,function(){var t=i.shift();return"undefined"!==e.typeOf(t)?t:""})}}}),n("moxie/core/utils/Mime",["moxie/core/utils/Basic","moxie/core/I18n"],function(e,t){var i={mimes:{},extensions:{},addMimeType:function(e){var t,i,n,r=e.split(/,/);for(t=0;t<r.length;t+=2){for(n=r[t+1].split(/ /),i=0;i<n.length;i++)this.mimes[n[i]]=r[t];this.extensions[r[t]]=n}},extList2mimes:function(t,i){var n,r,o,a,s=this,u=[];for(r=0;r<t.length;r++)for(n=t[r].extensions.toLowerCase().split(/\s*,\s*/),o=0;o<n.length;o++){if("*"===n[o])return[];if(a=s.mimes[n[o]],i&&/^\w+$/.test(n[o]))u.push("."+n[o]);else if(a&&-1===e.inArray(a,u))u.push(a);else if(!a)return[]}return u},mimes2exts:function(t){var i=this,n=[];return e.each(t,function(t){if("*"===(t=t.toLowerCase()))return n=[],!1;var r=t.match(/^(\w+)\/(\*|\w+)$/);r&&("*"===r[2]?e.each(i.extensions,function(e,t){new RegExp("^"+r[1]+"/").test(t)&&[].push.apply(n,i.extensions[t])}):i.extensions[t]&&[].push.apply(n,i.extensions[t]))}),n},mimes2extList:function(i){var n=[],r=[];return"string"===e.typeOf(i)&&(i=e.trim(i).split(/\s*,\s*/)),r=this.mimes2exts(i),n.push({title:t.translate("Files"),extensions:r.length?r.join(","):"*"}),n.mimes=i,n},getFileExtension:function(e){var t=e&&e.match(/\.([^.]+)$/);return t?t[1].toLowerCase():""},getFileMime:function(e){return this.mimes[this.getFileExtension(e)]||""}};return i.addMimeType("application/msword,doc dot,application/pdf,pdf,application/pgp-signature,pgp,application/postscript,ps ai eps,application/rtf,rtf,application/vnd.ms-excel,xls xlb,application/vnd.ms-powerpoint,ppt pps pot,application/zip,zip,application/x-shockwave-flash,swf swfl,application/vnd.openxmlformats-officedocument.wordprocessingml.document,docx,application/vnd.openxmlformats-officedocument.wordprocessingml.template,dotx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,xlsx,application/vnd.openxmlformats-officedocument.presentationml.presentation,pptx,application/vnd.openxmlformats-officedocument.presentationml.template,potx,application/vnd.openxmlformats-officedocument.presentationml.slideshow,ppsx,application/x-javascript,js,application/json,json,audio/mpeg,mp3 mpga mpega mp2,audio/x-wav,wav,audio/x-m4a,m4a,audio/ogg,oga ogg,audio/aiff,aiff aif,audio/flac,flac,audio/aac,aac,audio/ac3,ac3,audio/x-ms-wma,wma,image/bmp,bmp,image/gif,gif,image/jpeg,jpg jpeg jpe,image/photoshop,psd,image/png,png,image/svg+xml,svg svgz,image/tiff,tiff tif,text/plain,asc txt text diff log,text/html,htm html xhtml,text/css,css,text/csv,csv,text/rtf,rtf,video/mpeg,mpeg mpg mpe m2v,video/quicktime,qt mov,video/mp4,mp4,video/x-m4v,m4v,video/x-flv,flv,video/x-ms-wmv,wmv,video/avi,avi,video/webm,webm,video/3gpp,3gpp 3gp,video/3gpp2,3g2,video/vnd.rn-realvideo,rv,video/ogg,ogv,video/x-matroska,mkv,application/vnd.oasis.opendocument.formula-template,otf,application/octet-stream,exe"),i}),n("moxie/file/FileInput",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Mime","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/I18n","moxie/runtime/Runtime","moxie/runtime/RuntimeClient"],function(e,t,i,n,r,o,s,u,l){function c(o){a&&t.log("Instantiating FileInput...");var c,f,p;if(-1!==e.inArray(e.typeOf(o),["string","node"])&&(o={browse_button:o}),!(f=n.get(o.browse_button)))throw new r.DOMException(r.DOMException.NOT_FOUND_ERR);p={accept:[{title:s.translate("All Files"),extensions:"*"}],multiple:!1,required_caps:!1,container:f.parentNode||document.body},o=e.extend({},p,o),"string"==typeof o.required_caps&&(o.required_caps=u.parseCaps(o.required_caps)),"string"==typeof o.accept&&(o.accept=i.mimes2extList(o.accept)),c=n.get(o.container),c||(c=document.body),"static"===n.getStyle(c,"position")&&(c.style.position="relative"),c=f=null,l.call(this),e.extend(this,{uid:e.guid("uid_"),ruid:null,shimid:null,files:null,init:function(){var t=this;t.bind("RuntimeInit",function(i,r){t.ruid=r.uid,t.shimid=r.shimid,t.bind("Ready",function(){t.trigger("Refresh")},999),t.bind("Refresh",function(){var t,i,a,s,u;a=n.get(o.browse_button),s=n.get(r.shimid),a&&(t=n.getPos(a,n.get(o.container)),i=n.getSize(a),u=parseInt(n.getStyle(a,"z-index"),10)||0,s&&e.extend(s.style,{top:t.y+"px",left:t.x+"px",width:i.w+"px",height:i.h+"px",zIndex:u+1})),s=a=null}),r.exec.call(t,"FileInput","init",o)}),t.connectRuntime(e.extend({},o,{required_caps:{select_file:!0}}))},getOption:function(e){return o[e]},setOption:function(e,t){if(o.hasOwnProperty(e)){var n=o[e];switch(e){case"accept":"string"==typeof t&&(t=i.mimes2extList(t));break;case"container":case"required_caps":throw new r.FileException(r.FileException.NO_MODIFICATION_ALLOWED_ERR)}o[e]=t,this.exec("FileInput","setOption",e,t),this.trigger("OptionChanged",e,t,n)}},disable:function(t){this.getRuntime()&&this.exec("FileInput","disable","undefined"===e.typeOf(t)||t)},refresh:function(){this.trigger("Refresh")},destroy:function(){var t=this.getRuntime();t&&(t.exec.call(this,"FileInput","destroy"),this.disconnectRuntime()),"array"===e.typeOf(this.files)&&e.each(this.files,function(e){e.destroy()}),this.files=null,this.unbindAll()}}),this.handleEventProps(d)}var d=["ready","change","cancel","mouseenter","mouseleave","mousedown","mouseup"];return c.prototype=o.instance,c}),n("moxie/file/File",["moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/file/Blob"],function(e,t,i){function n(n,r){r||(r={}),i.apply(this,arguments),this.type||(this.type=t.getFileMime(r.name));var o;if(r.name)o=r.name.replace(/\\/g,"/"),o=o.substr(o.lastIndexOf("/")+1);else if(this.type){var a=this.type.split("/")[0];o=e.guid((""!==a?a:"file")+"_"),t.extensions[this.type]&&(o+="."+t.extensions[this.type][0])}e.extend(this,{name:o||e.guid("file_"),relativePath:"",lastModifiedDate:r.lastModifiedDate||(new Date).toLocaleString()})}return n.prototype=i.prototype,n}),n("moxie/file/FileDrop",["moxie/core/I18n","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/core/utils/Env","moxie/file/File","moxie/runtime/RuntimeClient","moxie/core/EventTarget","moxie/core/utils/Mime"],function(e,t,i,n,r,o,s,u,l){function c(i){a&&r.log("Instantiating FileDrop...");var o,u=this;"string"==typeof i&&(i={drop_zone:i}),o={accept:[{title:e.translate("All Files"),extensions:"*"}],required_caps:{drag_and_drop:!0}},i="object"==typeof i?n.extend({},o,i):o,i.container=t.get(i.drop_zone)||document.body,"static"===t.getStyle(i.container,"position")&&(i.container.style.position="relative"),"string"==typeof i.accept&&(i.accept=l.mimes2extList(i.accept)),s.call(u),n.extend(u,{uid:n.guid("uid_"),ruid:null,files:null,init:function(){u.bind("RuntimeInit",function(e,t){u.ruid=t.uid,t.exec.call(u,"FileDrop","init",i),u.dispatchEvent("ready")}),u.connectRuntime(i)},destroy:function(){var e=this.getRuntime();e&&(e.exec.call(this,"FileDrop","destroy"),this.disconnectRuntime()),this.files=null,this.unbindAll()}}),this.handleEventProps(d)}var d=["ready","dragenter","dragleave","drop","error"];return c.prototype=u.instance,c}),n("moxie/file/FileReader",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/core/Exceptions","moxie/core/EventTarget","moxie/file/Blob","moxie/runtime/RuntimeClient"],function(e,t,i,n,r,o){function a(){function n(e,n){if(this.trigger("loadstart"),this.readyState===a.LOADING)return this.trigger("error",new i.DOMException(i.DOMException.INVALID_STATE_ERR)),void this.trigger("loadend");if(!(n instanceof r))return this.trigger("error",new i.DOMException(i.DOMException.NOT_FOUND_ERR)),void this.trigger("loadend");if(this.result=null,this.readyState=a.LOADING,n.isDetached()){var o=n.getSource();switch(e){case"readAsText":case"readAsBinaryString":this.result=o;break;case"readAsDataURL":this.result="data:"+n.type+";base64,"+t.btoa(o)}this.readyState=a.DONE,this.trigger("load"),this.trigger("loadend")}else this.connectRuntime(n.ruid),this.exec("FileReader","read",e,n)}o.call(this),e.extend(this,{uid:e.guid("uid_"),readyState:a.EMPTY,result:null,error:null,readAsBinaryString:function(e){n.call(this,"readAsBinaryString",e)},readAsDataURL:function(e){n.call(this,"readAsDataURL",e)},readAsText:function(e){n.call(this,"readAsText",e)},abort:function(){this.result=null,-1===e.inArray(this.readyState,[a.EMPTY,a.DONE])&&(this.readyState===a.LOADING&&(this.readyState=a.DONE),this.exec("FileReader","abort"),this.trigger("abort"),this.trigger("loadend"))},destroy:function(){this.abort(),this.exec("FileReader","destroy"),this.disconnectRuntime(),this.unbindAll()}}),this.handleEventProps(s),this.bind("Error",function(e,t){this.readyState=a.DONE,this.error=t},999),this.bind("Load",function(e){this.readyState=a.DONE},999)}var s=["loadstart","progress","load","abort","error","loadend"];return a.EMPTY=0,a.LOADING=1,a.DONE=2,a.prototype=n.instance,a}),n("moxie/core/utils/Url",["moxie/core/utils/Basic"],function(e){var t=function(i,n){var r,o=["source","scheme","authority","userInfo","user","pass","host","port","relative","path","directory","file","query","fragment"],a=o.length,s={http:80,https:443},u={},l=/^(?:([^:\/?#]+):)?(?:\/\/()(?:(?:()(?:([^:@\/]*):?([^:@\/]*))?@)?(\[[\da-fA-F:]+\]|[^:\/?#]*)(?::(\d*))?))?()(?:(()(?:(?:[^?#\/]*\/)*)()(?:[^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,c=l.exec(i||""),d=/^\/\/\w/.test(i);switch(e.typeOf(n)){case"undefined":n=t(document.location.href,!1);break;case"string":n=t(n,!1)}for(;a--;)c[a]&&(u[o[a]]=c[a]);if(r=!d&&!u.scheme,(d||r)&&(u.scheme=n.scheme),r){u.host=n.host,u.port=n.port;var f="";/^[^\/]/.test(u.path)&&(f=n.path,f=/\/[^\/]*\.[^\/]*$/.test(f)?f.replace(/\/[^\/]+$/,"/"):f.replace(/\/?$/,"/")),u.path=f+(u.path||"")}return u.port||(u.port=s[u.scheme]||80),u.port=parseInt(u.port,10),u.path||(u.path="/"),delete u.source,u};return{parseUrl:t,resolveUrl:function(e){var i={http:80,https:443},n="object"==typeof e?e:t(e);return n.scheme+"://"+n.host+(n.port!==i[n.scheme]?":"+n.port:"")+n.path+(n.query?n.query:"")},hasSameOrigin:function(e){function i(e){return[e.scheme,e.host,e.port].join("/")}return"string"==typeof e&&(e=t(e)),i(t())===i(e)}}}),n("moxie/runtime/RuntimeTarget",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(e,t,i){function n(){this.uid=e.guid("uid_"),t.call(this),this.destroy=function(){this.disconnectRuntime(),this.unbindAll()}}return n.prototype=i.instance,n}),n("moxie/file/FileReaderSync",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/utils/Encode"],function(e,t,i){return function(){function n(e,t){if(!t.isDetached()){var n=this.connectRuntime(t.ruid).exec.call(this,"FileReaderSync","read",e,t);return this.disconnectRuntime(),n}var r=t.getSource();switch(e){case"readAsBinaryString":return r;case"readAsDataURL":return"data:"+t.type+";base64,"+i.btoa(r);case"readAsText":for(var o="",a=0,s=r.length;a<s;a++)o+=String.fromCharCode(r[a]);return o}}t.call(this),e.extend(this,{uid:e.guid("uid_"),readAsBinaryString:function(e){return n.call(this,"readAsBinaryString",e)},readAsDataURL:function(e){return n.call(this,"readAsDataURL",e)},readAsText:function(e){return n.call(this,"readAsText",e)}})}}),n("moxie/xhr/FormData",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/file/Blob"],function(e,t,i){function n(){var e,n=[];t.extend(this,{append:function(r,o){var a=this,s=t.typeOf(o);o instanceof i?e={name:r,value:o}:"array"===s?(r+="[]",t.each(o,function(e){a.append(r,e)})):"object"===s?t.each(o,function(e,t){a.append(r+"["+t+"]",e)}):"null"===s||"undefined"===s||"number"===s&&isNaN(o)?a.append(r,"false"):n.push({name:r,value:o.toString()})},hasBlob:function(){return!!this.getBlob()},getBlob:function(){return e&&e.value||null},getBlobName:function(){return e&&e.name||null},each:function(i){t.each(n,function(e){i(e.value,e.name)}),e&&i(e.value,e.name)},destroy:function(){e=null,n=[]}})}return n}),n("moxie/xhr/XMLHttpRequest",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/utils/Encode","moxie/core/utils/Url","moxie/runtime/Runtime","moxie/runtime/RuntimeTarget","moxie/file/Blob","moxie/file/FileReaderSync","moxie/xhr/FormData","moxie/core/utils/Env","moxie/core/utils/Mime"],function(e,t,i,n,r,o,a,s,u,l,c,d){function f(){this.uid=e.guid("uid_")}function p(){function i(e,t){if(T.hasOwnProperty(e))return 1===arguments.length?c.can("define_property")?T[e]:S[e]:void(c.can("define_property")?T[e]=t:S[e]=t)}function u(t){function n(){b&&(b.destroy(),b=null),s.dispatchEvent("loadend"),s=null}function r(r){b.bind("LoadStart",function(e){i("readyState",p.LOADING),s.dispatchEvent("readystatechange"),s.dispatchEvent(e),k&&s.upload.dispatchEvent(e)}),b.bind("Progress",function(e){i("readyState")!==p.LOADING&&(i("readyState",p.LOADING),s.dispatchEvent("readystatechange")),s.dispatchEvent(e)}),b.bind("UploadProgress",function(e){k&&s.upload.dispatchEvent({type:"progress",lengthComputable:!1,total:e.total,loaded:e.loaded})}),b.bind("Load",function(t){i("readyState",p.DONE),i("status",Number(r.exec.call(b,"XMLHttpRequest","getStatus")||0)),i("statusText",h[i("status")]||""),i("response",r.exec.call(b,"XMLHttpRequest","getResponse",i("responseType"))),~e.inArray(i("responseType"),["text",""])?i("responseText",i("response")):"document"===i("responseType")&&i("responseXML",i("response")),P=r.exec.call(b,"XMLHttpRequest","getAllResponseHeaders"),s.dispatchEvent("readystatechange"),i("status")>0?(k&&s.upload.dispatchEvent(t),s.dispatchEvent(t)):(F=!0,s.dispatchEvent("error")),n()}),b.bind("Abort",function(e){s.dispatchEvent(e),n()}),b.bind("Error",function(e){F=!0,i("readyState",p.DONE),s.dispatchEvent("readystatechange"),L=!0,s.dispatchEvent(e),n()}),r.exec.call(b,"XMLHttpRequest","send",{url:v,method:x,async:A,user:_,password:y,headers:I,mimeType:D,encoding:O,responseType:s.responseType,withCredentials:s.withCredentials,options:z},t)}var s=this;w=(new Date).getTime(),b=new a,"string"==typeof z.required_caps&&(z.required_caps=o.parseCaps(z.required_caps)),z.required_caps=e.extend({},z.required_caps,{return_response_type:s.responseType}),t instanceof l&&(z.required_caps.send_multipart=!0),e.isEmptyObj(I)||(z.required_caps.send_custom_headers=!0),M||(z.required_caps.do_cors=!0),z.ruid?r(b.connectRuntime(z)):(b.bind("RuntimeInit",function(e,t){r(t)}),b.bind("RuntimeError",function(e,t){s.dispatchEvent("RuntimeError",t)}),b.connectRuntime(z))}function g(){i("responseText",""),i("responseXML",null),i("response",null),i("status",0),i("statusText",""),w=E=null}var v,x,_,y,w,E,b,R,S=this,T={timeout:0,readyState:p.UNSENT,withCredentials:!1,status:0,statusText:"",responseType:"",responseXML:null,responseText:null,response:null},A=!0,I={},O=null,D=null,C=!1,N=!1,k=!1,L=!1,F=!1,M=!1,U=null,B=null,z={},P="";e.extend(this,T,{uid:e.guid("uid_"),upload:new f,open:function(o,a,s,u,l){var c;if(!o||!a)throw new t.DOMException(t.DOMException.SYNTAX_ERR);if(/[\u0100-\uffff]/.test(o)||n.utf8_encode(o)!==o)throw new t.DOMException(t.DOMException.SYNTAX_ERR);if(~e.inArray(o.toUpperCase(),["CONNECT","DELETE","GET","HEAD","OPTIONS","POST","PUT","TRACE","TRACK"])&&(x=o.toUpperCase()),~e.inArray(x,["CONNECT","TRACE","TRACK"]))throw new t.DOMException(t.DOMException.SECURITY_ERR);if(a=n.utf8_encode(a),c=r.parseUrl(a),M=r.hasSameOrigin(c),v=r.resolveUrl(a),(u||l)&&!M)throw new t.DOMException(t.DOMException.INVALID_ACCESS_ERR);if(_=u||c.user,y=l||c.pass,!1===(A=s||!0)&&(i("timeout")||i("withCredentials")||""!==i("responseType")))throw new t.DOMException(t.DOMException.INVALID_ACCESS_ERR);C=!A,N=!1,I={},g.call(this),i("readyState",p.OPENED),this.dispatchEvent("readystatechange")},setRequestHeader:function(r,o){var a=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","content-transfer-encoding","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","user-agent","via"];if(i("readyState")!==p.OPENED||N)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(/[\u0100-\uffff]/.test(r)||n.utf8_encode(r)!==r)throw new t.DOMException(t.DOMException.SYNTAX_ERR);return r=e.trim(r).toLowerCase(),!~e.inArray(r,a)&&!/^(proxy\-|sec\-)/.test(r)&&(I[r]?I[r]+=", "+o:I[r]=o,!0)},hasRequestHeader:function(e){return e&&I[e.toLowerCase()]||!1},getAllResponseHeaders:function(){return P||""},getResponseHeader:function(t){return t=t.toLowerCase(),F||~e.inArray(t,["set-cookie","set-cookie2"])?null:P&&""!==P&&(R||(R={},e.each(P.split(/\r\n/),function(t){var i=t.split(/:\s+/);2===i.length&&(i[0]=e.trim(i[0]),R[i[0].toLowerCase()]={header:i[0],value:e.trim(i[1])})})),R.hasOwnProperty(t))?R[t].header+": "+R[t].value:null},overrideMimeType:function(n){var r,o;if(~e.inArray(i("readyState"),[p.LOADING,p.DONE]))throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(n=e.trim(n.toLowerCase()),/;/.test(n)&&(r=n.match(/^([^;]+)(?:;\scharset\=)?(.*)$/))&&(n=r[1],r[2]&&(o=r[2])),!d.mimes[n])throw new t.DOMException(t.DOMException.SYNTAX_ERR);U=n,B=o},send:function(i,r){if(z="string"===e.typeOf(r)?{ruid:r}:r||{},this.readyState!==p.OPENED||N)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(i instanceof s)z.ruid=i.ruid,D=i.type||"application/octet-stream";else if(i instanceof l){if(i.hasBlob()){var o=i.getBlob();z.ruid=o.ruid,D=o.type||"application/octet-stream"}}else"string"==typeof i&&(O="UTF-8",D="text/plain;charset=UTF-8",i=n.utf8_encode(i));this.withCredentials||(this.withCredentials=z.required_caps&&z.required_caps.send_browser_cookies&&!M),k=!C&&this.upload.hasEventListener(),F=!1,L=!i,C||(N=!0),u.call(this,i)},abort:function(){if(F=!0,C=!1,~e.inArray(i("readyState"),[p.UNSENT,p.OPENED,p.DONE]))i("readyState",p.UNSENT);else{if(i("readyState",p.DONE),N=!1,!b)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);b.getRuntime().exec.call(b,"XMLHttpRequest","abort",L),L=!0}},destroy:function(){b&&("function"===e.typeOf(b.destroy)&&b.destroy(),b=null),this.unbindAll(),this.upload&&(this.upload.unbindAll(),this.upload=null)}}),this.handleEventProps(m.concat(["readystatechange"])),this.upload.handleEventProps(m)}var h={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"Reserved",307:"Temporary Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Requested Range Not Satisfiable",417:"Expectation Failed",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",426:"Upgrade Required",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",510:"Not Extended"};f.prototype=i.instance;var m=["loadstart","progress","abort","error","load","timeout","loadend"];return p.UNSENT=0,p.OPENED=1,p.HEADERS_RECEIVED=2,p.LOADING=3,p.DONE=4,p.prototype=i.instance,p}),n("moxie/runtime/Transporter",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(e,t,i,n){function r(){function n(){c=d=0,l=this.result=null}function o(t,i){var n=this;u=i,n.bind("TransportingProgress",function(t){(d=t.loaded)<c&&-1===e.inArray(n.state,[r.IDLE,r.DONE])&&a.call(n)},999),n.bind("TransportingComplete",function(){d=c,n.state=r.DONE,l=null,n.result=u.exec.call(n,"Transporter","getAsBlob",t||"")},999),n.state=r.BUSY,n.trigger("TransportingStarted"),a.call(n)}function a(){var e,i=this,n=c-d;f>n&&(f=n),e=t.btoa(l.substr(d,f)),u.exec.call(i,"Transporter","receive",e,c)}var s,u,l,c,d,f;i.call(this),e.extend(this,{uid:e.guid("uid_"),state:r.IDLE,result:null,transport:function(t,i,r){var a=this;if(r=e.extend({chunk_size:204798},r),(s=r.chunk_size%3)&&(r.chunk_size+=3-s),f=r.chunk_size,n.call(this),l=t,c=t.length,"string"===e.typeOf(r)||r.ruid)o.call(a,i,this.connectRuntime(r));else{var u=function(e,t){a.unbind("RuntimeInit",u),o.call(a,i,t)};this.bind("RuntimeInit",u),this.connectRuntime(r)}},abort:function(){var e=this;e.state=r.IDLE,u&&(u.exec.call(e,"Transporter","clear"),e.trigger("TransportingAborted")),n.call(e)},destroy:function(){this.unbindAll(),u=null,this.disconnectRuntime(),n.call(this)}})}return r.IDLE=0,r.BUSY=1,r.DONE=2,r.prototype=n.instance,r}),n("moxie/image/Image",["moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/file/FileReaderSync","moxie/xhr/XMLHttpRequest","moxie/runtime/Runtime","moxie/runtime/RuntimeClient","moxie/runtime/Transporter","moxie/core/utils/Env","moxie/core/EventTarget","moxie/file/Blob","moxie/file/File","moxie/core/utils/Encode"],function(e,t,i,n,r,o,a,s,u,l,c,d,f){function p(){function n(e){try{return e||(e=this.exec("Image","getInfo")),this.size=e.size,this.width=e.width,this.height=e.height,this.type=e.type,this.meta=e.meta,""===this.name&&(this.name=e.name),!0}catch(e){return this.trigger("error",e.code),!1}}function l(t){var n=e.typeOf(t);try{if(t instanceof p){if(!t.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);m.apply(this,arguments)}else if(t instanceof c){if(!~e.inArray(t.type,["image/jpeg","image/png"]))throw new i.ImageError(i.ImageError.WRONG_FORMAT);g.apply(this,arguments)}else if(-1!==e.inArray(n,["blob","file"]))l.call(this,new d(null,t),arguments[1]);else if("string"===n)"data:"===t.substr(0,5)?l.call(this,new c(null,{data:t}),arguments[1]):v.apply(this,arguments);else{if("node"!==n||"img"!==t.nodeName.toLowerCase())throw new i.DOMException(i.DOMException.TYPE_MISMATCH_ERR);l.call(this,t.src,arguments[1])}}catch(e){this.trigger("error",e.code)}}function m(t,i){var n=this.connectRuntime(t.ruid);this.ruid=n.uid,n.exec.call(this,"Image","loadFromImage",t,"undefined"===e.typeOf(i)||i)}function g(t,i){function n(e){r.ruid=e.uid,e.exec.call(r,"Image","loadFromBlob",t)}var r=this;r.name=t.name||"",t.isDetached()?(this.bind("RuntimeInit",function(e,t){n(t)}),i&&"string"==typeof i.required_caps&&(i.required_caps=o.parseCaps(i.required_caps)),this.connectRuntime(e.extend({required_caps:{access_image_binary:!0,resize_image:!0}},i))):n(this.connectRuntime(t.ruid))}function v(e,t){var i,n=this;i=new r,i.open("get",e),i.responseType="blob",i.onprogress=function(e){n.trigger(e)},i.onload=function(){g.call(n,i.response,!0)},i.onerror=function(e){n.trigger(e)},i.onloadend=function(){i.destroy()},i.bind("RuntimeError",function(e,t){n.trigger("RuntimeError",t)}),i.send(null,t)}a.call(this),e.extend(this,{uid:e.guid("uid_"),ruid:null,name:"",size:0,width:0,height:0,type:"",meta:{},clone:function(){this.load.apply(this,arguments)},load:function(){l.apply(this,arguments)},resize:function(t){var n,r,o=this,a={x:0,y:0,width:o.width,height:o.height},s=e.extendIf({width:o.width,height:o.height,type:o.type||"image/jpeg",quality:90,crop:!1,fit:!0,preserveHeaders:!0,resample:"default",multipass:!0},t);try{if(!o.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);if(o.width>p.MAX_RESIZE_WIDTH||o.height>p.MAX_RESIZE_HEIGHT)throw new i.ImageError(i.ImageError.MAX_RESOLUTION_ERR);if(n=o.meta&&o.meta.tiff&&o.meta.tiff.Orientation||1,-1!==e.inArray(n,[5,6,7,8])){var u=s.width;s.width=s.height,s.height=u}if(s.crop){switch(r=Math.max(s.width/o.width,s.height/o.height),t.fit?(a.width=Math.min(Math.ceil(s.width/r),o.width),a.height=Math.min(Math.ceil(s.height/r),o.height),r=s.width/a.width):(a.width=Math.min(s.width,o.width),a.height=Math.min(s.height,o.height),r=1),"boolean"==typeof s.crop&&(s.crop="cc"),s.crop.toLowerCase().replace(/_/,"-")){case"rb":case"right-bottom":a.x=o.width-a.width,a.y=o.height-a.height;break;case"cb":case"center-bottom":a.x=Math.floor((o.width-a.width)/2),a.y=o.height-a.height;break;case"lb":case"left-bottom":a.x=0,a.y=o.height-a.height;break;case"lt":case"left-top":a.x=0,a.y=0;break;case"ct":case"center-top":a.x=Math.floor((o.width-a.width)/2),a.y=0;break;case"rt":case"right-top":a.x=o.width-a.width,a.y=0;break;case"rc":case"right-center":case"right-middle":a.x=o.width-a.width,a.y=Math.floor((o.height-a.height)/2);break;case"lc":case"left-center":case"left-middle":a.x=0,a.y=Math.floor((o.height-a.height)/2);break;case"cc":case"center-center":case"center-middle":default:a.x=Math.floor((o.width-a.width)/2),a.y=Math.floor((o.height-a.height)/2)}a.x=Math.max(a.x,0),a.y=Math.max(a.y,0)}else r=Math.min(s.width/o.width,s.height/o.height);this.exec("Image","resize",a,r,s)}catch(e){o.trigger("error",e.code)}},downsize:function(t){var i,n={width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90,crop:!1,preserveHeaders:!0,resample:"default"};i="object"==typeof t?e.extend(n,t):e.extend(n,{width:arguments[0],height:arguments[1],crop:arguments[2],preserveHeaders:arguments[3]}),this.resize(i)},crop:function(e,t,i){this.downsize(e,t,!0,i)},getAsCanvas:function(){if(!u.can("create_canvas"))throw new i.RuntimeError(i.RuntimeError.NOT_SUPPORTED_ERR);return this.exec("Image","getAsCanvas")},getAsBlob:function(e,t){if(!this.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);return this.exec("Image","getAsBlob",e||"image/jpeg",t||90)},getAsDataURL:function(e,t){if(!this.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);return this.exec("Image","getAsDataURL",e||"image/jpeg",t||90)},getAsBinaryString:function(e,t){var i=this.getAsDataURL(e,t);return f.atob(i.substring(i.indexOf("base64,")+7))},embed:function(n,r){function o(t,r){var o=this;if(u.can("create_canvas")){var c=o.getAsCanvas();if(c)return n.appendChild(c),c=null,o.destroy(),void l.trigger("embedded")}var d=o.getAsDataURL(t,r);if(!d)throw new i.ImageError(i.ImageError.WRONG_FORMAT);if(u.can("use_data_uri_of",d.length))n.innerHTML='<img src="'+d+'" width="'+o.width+'" height="'+o.height+'" />',o.destroy(),l.trigger("embedded");else{var p=new s;p.bind("TransportingComplete",function(){a=l.connectRuntime(this.result.ruid),l.bind("Embedded",function(){e.extend(a.getShimContainer().style,{top:"0px",left:"0px",width:o.width+"px",height:o.height+"px"}),a=null},999),a.exec.call(l,"ImageView","display",this.result.uid,width,height),o.destroy()}),p.transport(f.atob(d.substring(d.indexOf("base64,")+7)),t,{required_caps:{display_media:!0},runtime_order:"flash,silverlight",container:n})}}var a,l=this,c=e.extend({width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90},r);try{if(!(n=t.get(n)))throw new i.DOMException(i.DOMException.INVALID_NODE_TYPE_ERR);if(!this.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);this.width>p.MAX_RESIZE_WIDTH||(this.height,p.MAX_RESIZE_HEIGHT);var d=new p;return d.bind("Resize",function(){o.call(this,c.type,c.quality)}),d.bind("Load",function(){this.downsize(c)}),this.meta.thumb&&this.meta.thumb.width>=c.width&&this.meta.thumb.height>=c.height?d.load(this.meta.thumb.data):d.clone(this,!1),d}catch(e){this.trigger("error",e.code)}},destroy:function(){this.ruid&&(this.getRuntime().exec.call(this,"Image","destroy"),this.disconnectRuntime()),this.meta&&this.meta.thumb&&this.meta.thumb.data.destroy(),this.unbindAll()}}),this.handleEventProps(h),this.bind("Load Resize",function(){return n.call(this)},999)}var h=["progress","load","error","resize","embedded"];return p.MAX_RESIZE_WIDTH=8192,p.MAX_RESIZE_HEIGHT=8192,p.prototype=l.instance,p}),n("moxie/runtime/html5/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(e,t,i,n){function o(t){var o=this,u=i.capTest,l=i.capTrue,c=e.extend({access_binary:u(window.FileReader||window.File&&window.File.getAsDataURL),access_image_binary:function(){return o.can("access_binary")&&!!s.Image},display_media:u((n.can("create_canvas")||n.can("use_data_uri_over32kb"))&&r("moxie/image/Image")),do_cors:u(window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest),drag_and_drop:u(function(){var e=document.createElement("div");return("draggable"in e||"ondragstart"in e&&"ondrop"in e)&&("IE"!==n.browser||n.verComp(n.version,9,">"))}()),filter_by_extension:u(function(){return!("Chrome"===n.browser&&n.verComp(n.version,28,"<")||"IE"===n.browser&&n.verComp(n.version,10,"<")||"Safari"===n.browser&&n.verComp(n.version,7,"<")||"Firefox"===n.browser&&n.verComp(n.version,37,"<"))}()),return_response_headers:l,return_response_type:function(e){return!("json"!==e||!window.JSON)||n.can("return_response_type",e)},return_status_code:l,report_upload_progress:u(window.XMLHttpRequest&&(new XMLHttpRequest).upload),resize_image:function(){return o.can("access_binary")&&n.can("create_canvas")},select_file:function(){return n.can("use_fileinput")&&window.File},select_folder:function(){return o.can("select_file")&&("Chrome"===n.browser&&n.verComp(n.version,21,">=")||"Firefox"===n.browser&&n.verComp(n.version,42,">="))},select_multiple:function(){return o.can("select_file")&&!("Safari"===n.browser&&"Windows"===n.os)&&!("iOS"===n.os&&n.verComp(n.osVersion,"7.0.0",">")&&n.verComp(n.osVersion,"8.0.0","<"))},send_binary_string:u(window.XMLHttpRequest&&((new XMLHttpRequest).sendAsBinary||window.Uint8Array&&window.ArrayBuffer)),send_custom_headers:u(window.XMLHttpRequest),send_multipart:function(){return!!(window.XMLHttpRequest&&(new XMLHttpRequest).upload&&window.FormData)||o.can("send_binary_string")},slice_blob:u(window.File&&(File.prototype.mozSlice||File.prototype.webkitSlice||File.prototype.slice)),stream_upload:function(){return o.can("slice_blob")&&o.can("send_multipart")},summon_file_dialog:function(){return o.can("select_file")&&("Firefox"===n.browser&&n.verComp(n.version,4,">=")||"Opera"===n.browser&&n.verComp(n.version,12,">=")||"IE"===n.browser&&n.verComp(n.version,10,">=")||!!~e.inArray(n.browser,["Chrome","Safari","Edge"]))},upload_filesize:l,use_http_method:l},arguments[2]);i.call(this,t,arguments[1]||a,c),e.extend(this,{init:function(){this.trigger("Init")},destroy:function(e){return function(){e.call(o),e=o=null}}(this.destroy)}),e.extend(this.getShim(),s)}var a="html5",s={};return i.addConstructor(a,o),s}),n("moxie/runtime/html5/file/Blob",["moxie/runtime/html5/Runtime","moxie/file/Blob"],function(e,t){function i(){function e(e,t,i){var n;if(!window.File.prototype.slice)return(n=window.File.prototype.webkitSlice||window.File.prototype.mozSlice)?n.call(e,t,i):null;try{return e.slice(),e.slice(t,i)}catch(n){return e.slice(t,i-t)}}this.slice=function(){return new t(this.getRuntime().uid,e.apply(this,arguments))}}return e.Blob=i}),n("moxie/core/utils/Events",["moxie/core/utils/Basic"],function(e){function t(){this.returnValue=!1}function i(){this.cancelBubble=!0}var n={},r="moxie_"+e.guid(),o=function(o,a,s,u){var l,c;a=a.toLowerCase(),o.addEventListener?(l=s,o.addEventListener(a,l,!1)):o.attachEvent&&(l=function(){var e=window.event;e.target||(e.target=e.srcElement),e.preventDefault=t,e.stopPropagation=i,s(e)},o.attachEvent("on"+a,l)),o[r]||(o[r]=e.guid()),n.hasOwnProperty(o[r])||(n[o[r]]={}),c=n[o[r]],c.hasOwnProperty(a)||(c[a]=[]),c[a].push({func:l,orig:s,key:u})},a=function(t,i,o){var a;if(i=i.toLowerCase(),t[r]&&n[t[r]]&&n[t[r]][i]){a=n[t[r]][i];for(var s=a.length-1;s>=0&&(a[s].orig!==o&&a[s].key!==o||(t.removeEventListener?t.removeEventListener(i,a[s].func,!1):t.detachEvent&&t.detachEvent("on"+i,a[s].func),a[s].orig=null,a[s].func=null,a.splice(s,1),void 0===o));s--);if(a.length||delete n[t[r]][i],e.isEmptyObj(n[t[r]])){delete n[t[r]];try{delete t[r]}catch(e){t[r]=void 0}}}};return{addEvent:o,removeEvent:a,removeAllEvents:function(t,i){t&&t[r]&&e.each(n[t[r]],function(e,n){a(t,n,i)})}}}),n("moxie/runtime/html5/file/FileInput",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,t,i,n,r,o,a){function s(){var e,s;i.extend(this,{init:function(u){var l,c,d,f,p,h,m=this,g=m.getRuntime();e=u,d=e.accept.mimes||o.extList2mimes(e.accept,g.can("filter_by_extension")),c=g.getShimContainer(),c.innerHTML='<input id="'+g.uid+'" type="file" style="font-size:999px;opacity:0;"'+(e.multiple&&g.can("select_multiple")?"multiple":"")+(e.directory&&g.can("select_folder")?"webkitdirectory directory":"")+(d?' accept="'+d.join(",")+'"':"")+" />",l=n.get(g.uid),i.extend(l.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),f=n.get(e.browse_button),s=n.getStyle(f,"z-index")||"auto",g.can("summon_file_dialog")&&("static"===n.getStyle(f,"position")&&(f.style.position="relative"),r.addEvent(f,"click",function(e){var t=n.get(g.uid);t&&!t.disabled&&t.click(),e.preventDefault()},m.uid),m.bind("Refresh",function(){p=parseInt(s,10)||1,n.get(e.browse_button).style.zIndex=p,this.getRuntime().getShimContainer().style.zIndex=p-1})),h=g.can("summon_file_dialog")?f:c,r.addEvent(h,"mouseover",function(){m.trigger("mouseenter")},m.uid),r.addEvent(h,"mouseout",function(){m.trigger("mouseleave")},m.uid),r.addEvent(h,"mousedown",function(){m.trigger("mousedown")},m.uid),r.addEvent(n.get(e.container),"mouseup",function(){m.trigger("mouseup")},m.uid),l.onchange=function n(r){if(m.files=[],i.each(this.files,function(i){var n="";if(e.directory&&"."==i.name)return!0;i.webkitRelativePath&&(n="/"+i.webkitRelativePath.replace(/^\//,"")),i=new t(g.uid,i),i.relativePath=n,m.files.push(i)}),"IE"!==a.browser&&"IEMobile"!==a.browser)this.value="";else{var o=this.cloneNode(!0);this.parentNode.replaceChild(o,this),o.onchange=n}m.files.length&&m.trigger("change")},m.trigger({type:"ready",async:!0}),c=null},setOption:function(e,t){var i=this.getRuntime(),r=n.get(i.uid);switch(e){case"accept":if(t){var a=t.mimes||o.extList2mimes(t,i.can("filter_by_extension"));r.setAttribute("accept",a.join(","))}else r.removeAttribute("accept");break;case"directory":t&&i.can("select_folder")?(r.setAttribute("directory",""),r.setAttribute("webkitdirectory","")):(r.removeAttribute("directory"),r.removeAttribute("webkitdirectory"));break;case"multiple":t&&i.can("select_multiple")?r.setAttribute("multiple",""):r.removeAttribute("multiple")}},disable:function(e){var t,i=this.getRuntime();(t=n.get(i.uid))&&(t.disabled=!!e)},destroy:function(){var t=this.getRuntime(),i=t.getShim(),o=t.getShimContainer(),a=e&&n.get(e.container),u=e&&n.get(e.browse_button);a&&r.removeAllEvents(a,this.uid),u&&(r.removeAllEvents(u,this.uid),u.style.zIndex=s),o&&(r.removeAllEvents(o,this.uid),o.innerHTML=""),i.removeInstance(this.uid),e=o=a=u=i=null}})}return e.FileInput=s}),n("moxie/runtime/html5/file/FileDrop",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime"],function(e,t,i,n,r,o){function a(){function e(e){if(!e.dataTransfer||!e.dataTransfer.types)return!1;var t=i.toArray(e.dataTransfer.types||[]);return-1!==i.inArray("Files",t)||-1!==i.inArray("public.file-url",t)||-1!==i.inArray("application/x-moz-file",t)}function a(e,i){if(u(e)){var n=new t(h,e);n.relativePath=i||"",m.push(n)}}function s(e){for(var t=[],n=0;n<e.length;n++)[].push.apply(t,e[n].extensions.split(/\s*,\s*/));return-1===i.inArray("*",t)?t:[]}function u(e){if(!g.length)return!0;var t=o.getFileExtension(e.name);return!t||-1!==i.inArray(t,g)}function l(e,t){var n=[];i.each(e,function(e){var t=e.webkitGetAsEntry();t&&(t.isFile?a(e.getAsFile(),t.fullPath):n.push(t))}),n.length?c(n,t):t()}function c(e,t){var n=[];i.each(e,function(e){n.push(function(t){d(e,t)})}),i.inSeries(n,function(){t()})}function d(e,t){e.isFile?e.file(function(i){a(i,e.fullPath),t()},function(){t()}):e.isDirectory?f(e,t):t()}function f(e,t){function i(e){r.readEntries(function(t){t.length?([].push.apply(n,t),i(e)):e()},e)}var n=[],r=e.createReader();i(function(){c(n,t)})}var p,h,m=[],g=[];i.extend(this,{init:function(t){var n,o=this;p=t,h=o.ruid,g=s(p.accept),n=p.container,r.addEvent(n,"dragover",function(t){e(t)&&(t.preventDefault(),t.dataTransfer.dropEffect="copy")},o.uid),r.addEvent(n,"drop",function(t){e(t)&&(t.preventDefault(),m=[],t.dataTransfer.items&&t.dataTransfer.items[0].webkitGetAsEntry?l(t.dataTransfer.items,function(){o.files=m,o.trigger("drop")}):(i.each(t.dataTransfer.files,function(e){a(e)}),o.files=m,o.trigger("drop")))},o.uid),r.addEvent(n,"dragenter",function(e){o.trigger("dragenter")},o.uid),r.addEvent(n,"dragleave",function(e){o.trigger("dragleave")},o.uid)},destroy:function(){r.removeAllEvents(p&&n.get(p.container),this.uid),h=m=g=p=null}})}return e.FileDrop=a}),n("moxie/runtime/html5/file/FileReader",["moxie/runtime/html5/Runtime","moxie/core/utils/Encode","moxie/core/utils/Basic"],function(e,t,i){function n(){function e(e){return t.atob(e.substring(e.indexOf("base64,")+7))}var n,r=!1;i.extend(this,{read:function(t,o){var a=this;a.result="",n=new window.FileReader,n.addEventListener("progress",function(e){a.trigger(e)}),n.addEventListener("load",function(t){a.result=r?e(n.result):n.result,a.trigger(t)}),n.addEventListener("error",function(e){a.trigger(e,n.error)}),n.addEventListener("loadend",function(e){n=null,a.trigger(e)}),"function"===i.typeOf(n[t])?(r=!1,n[t](o.getSource())):"readAsBinaryString"===t&&(r=!0,n.readAsDataURL(o.getSource()))},abort:function(){n&&n.abort()},destroy:function(){n=null}})}return e.FileReader=n}),n("moxie/runtime/html5/xhr/XMLHttpRequest",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/core/utils/Url","moxie/file/File","moxie/file/Blob","moxie/xhr/FormData","moxie/core/Exceptions","moxie/core/utils/Env"],function(e,t,i,n,r,o,a,s,u){function l(){function e(e,t){var i,n,r=this;i=t.getBlob().getSource(),n=new window.FileReader,n.onload=function(){t.append(t.getBlobName(),new o(null,{type:i.type,data:n.result})),h.send.call(r,e,t)},n.readAsBinaryString(i)}function l(){return!window.XMLHttpRequest||"IE"===u.browser&&u.verComp(u.version,8,"<")?function(){for(var e=["Msxml2.XMLHTTP.6.0","Microsoft.XMLHTTP"],t=0;t<e.length;t++)try{return new ActiveXObject(e[t])}catch(e){}}():new window.XMLHttpRequest}function c(e){var t=e.responseXML,i=e.responseText;return"IE"===u.browser&&i&&t&&!t.documentElement&&/[^\/]+\/[^\+]+\+xml/.test(e.getResponseHeader("Content-Type"))&&(t=new window.ActiveXObject("Microsoft.XMLDOM"),t.async=!1,t.validateOnParse=!1,t.loadXML(i)),t&&("IE"===u.browser&&0!==t.parseError||!t.documentElement||"parsererror"===t.documentElement.tagName)?null:t}function d(e){var t="----moxieboundary"+(new Date).getTime(),i="\r\n",n="";if(!this.getRuntime().can("send_binary_string"))throw new s.RuntimeError(s.RuntimeError.NOT_SUPPORTED_ERR);return f.setRequestHeader("Content-Type","multipart/form-data; boundary="+t),e.each(function(e,r){n+=e instanceof o?"--"+t+i+'Content-Disposition: form-data; name="'+r+'"; filename="'+unescape(encodeURIComponent(e.name||"blob"))+'"'+i+"Content-Type: "+(e.type||"application/octet-stream")+i+i+e.getSource()+i:"--"+t+i+'Content-Disposition: form-data; name="'+r+'"'+i+i+unescape(encodeURIComponent(e))+i}),n+="--"+t+"--"+i}var f,p,h=this;t.extend(this,{send:function(i,r){var s=this,c="Mozilla"===u.browser&&u.verComp(u.version,4,">=")&&u.verComp(u.version,7,"<"),h="Android Browser"===u.browser,m=!1;if(p=i.url.replace(/^.+?\/([\w\-\.]+)$/,"$1").toLowerCase(),f=l(),f.open(i.method,i.url,i.async,i.user,i.password),r instanceof o)r.isDetached()&&(m=!0),r=r.getSource();else if(r instanceof a){if(r.hasBlob())if(r.getBlob().isDetached())r=d.call(s,r),m=!0;else if((c||h)&&"blob"===t.typeOf(r.getBlob().getSource())&&window.FileReader)return void e.call(s,i,r);if(r instanceof a){var g=new window.FormData;r.each(function(e,t){e instanceof o?g.append(t,e.getSource()):g.append(t,e)}),r=g}}f.upload?(i.withCredentials&&(f.withCredentials=!0),f.addEventListener("load",function(e){s.trigger(e)}),f.addEventListener("error",function(e){s.trigger(e)}),f.addEventListener("progress",function(e){s.trigger(e)}),f.upload.addEventListener("progress",function(e){s.trigger({type:"UploadProgress",loaded:e.loaded,total:e.total})})):f.onreadystatechange=function(){switch(f.readyState){case 1:case 2:break;case 3:var e,t;try{n.hasSameOrigin(i.url)&&(e=f.getResponseHeader("Content-Length")||0),f.responseText&&(t=f.responseText.length)}catch(i){e=t=0}s.trigger({type:"progress",lengthComputable:!!e,total:parseInt(e,10),loaded:t});break;case 4:f.onreadystatechange=function(){},0===f.status?s.trigger("error"):s.trigger("load")}},t.isEmptyObj(i.headers)||t.each(i.headers,function(e,t){f.setRequestHeader(t,e)}),""!==i.responseType&&"responseType"in f&&("json"!==i.responseType||u.can("return_response_type","json")?f.responseType=i.responseType:f.responseType="text"),m?f.sendAsBinary?f.sendAsBinary(r):function(){for(var e=new Uint8Array(r.length),t=0;t<r.length;t++)e[t]=255&r.charCodeAt(t);f.send(e.buffer)}():f.send(r),s.trigger("loadstart")},getStatus:function(){try{if(f)return f.status}catch(e){}return 0},getResponse:function(e){var t=this.getRuntime();try{switch(e){case"blob":var n=new r(t.uid,f.response),o=f.getResponseHeader("Content-Disposition");if(o){var a=o.match(/filename=([\'\"'])([^\1]+)\1/);a&&(p=a[2])}return n.name=p,n.type||(n.type=i.getFileMime(p)),n;case"json":return u.can("return_response_type","json")?f.response:200===f.status&&window.JSON?JSON.parse(f.responseText):null;case"document":return c(f);default:return""!==f.responseText?f.responseText:null}}catch(e){return null}},getAllResponseHeaders:function(){try{return f.getAllResponseHeaders()}catch(e){}return""},abort:function(){f&&f.abort()},destroy:function(){h=p=null}})}return e.XMLHttpRequest=l}),n("moxie/runtime/html5/utils/BinaryReader",["moxie/core/utils/Basic"],function(e){function t(e){e instanceof ArrayBuffer?i.apply(this,arguments):n.apply(this,arguments)}function i(t){var i=new DataView(t);e.extend(this,{readByteAt:function(e){return i.getUint8(e)},writeByteAt:function(e,t){i.setUint8(e,t)},SEGMENT:function(e,n,r){switch(arguments.length){case 2:return t.slice(e,e+n);case 1:return t.slice(e);case 3:if(null===r&&(r=new ArrayBuffer),r instanceof ArrayBuffer){var o=new Uint8Array(this.length()-n+r.byteLength);e>0&&o.set(new Uint8Array(t.slice(0,e)),0),o.set(new Uint8Array(r),e),o.set(new Uint8Array(t.slice(e+n)),e+r.byteLength),this.clear(),t=o.buffer,i=new DataView(t);break}default:return t}},length:function(){return t?t.byteLength:0},clear:function(){i=t=null}})}function n(t){function i(e,i,n){n=3===arguments.length?n:t.length-i-1,t=t.substr(0,i)+e+t.substr(n+i)}e.extend(this,{readByteAt:function(e){return t.charCodeAt(e)},writeByteAt:function(e,t){i(String.fromCharCode(t),e,1)},SEGMENT:function(e,n,r){switch(arguments.length){case 1:return t.substr(e);case 2:return t.substr(e,n);case 3:i(null!==r?r:"",e,n);break;default:return t}},length:function(){return t?t.length:0},clear:function(){t=null}})}return e.extend(t.prototype,{littleEndian:!1,read:function(e,t){var i,n,r;if(e+t>this.length())throw new Error("You are trying to read outside the source boundaries.");for(n=this.littleEndian?0:-8*(t-1),r=0,i=0;r<t;r++)i|=this.readByteAt(e+r)<<Math.abs(n+8*r);return i},write:function(e,t,i){var n,r;if(e>this.length())throw new Error("You are trying to write outside the source boundaries.");for(n=this.littleEndian?0:-8*(i-1),r=0;r<i;r++)this.writeByteAt(e+r,t>>Math.abs(n+8*r)&255)},BYTE:function(e){return this.read(e,1)},SHORT:function(e){return this.read(e,2)},LONG:function(e){return this.read(e,4)},SLONG:function(e){var t=this.read(e,4);return t>2147483647?t-4294967296:t},CHAR:function(e){return String.fromCharCode(this.read(e,1))},STRING:function(e,t){return this.asArray("CHAR",e,t).join("")},asArray:function(e,t,i){for(var n=[],r=0;r<i;r++)n[r]=this[e](t+r);return n}}),t}),n("moxie/runtime/html5/image/JPEGHeaders",["moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(e,t){return function i(n){var r,o,a,s=[],u=0;if(r=new e(n),65496!==r.SHORT(0))throw r.clear(),new t.ImageError(t.ImageError.WRONG_FORMAT);for(o=2;o<=r.length();)if((a=r.SHORT(o))>=65488&&a<=65495)o+=2;else{if(65498===a||65497===a)break;u=r.SHORT(o+2)+2,a>=65505&&a<=65519&&s.push({hex:a,name:"APP"+(15&a),start:o,length:u,segment:r.SEGMENT(o,u)}),o+=u}return r.clear(),{headers:s,restore:function(t){var i,n,r;for(r=new e(t),o=65504==r.SHORT(2)?4+r.SHORT(4):2,n=0,i=s.length;n<i;n++)r.SEGMENT(o,0,s[n].segment),o+=s[n].length;return t=r.SEGMENT(),r.clear(),t},strip:function(t){var n,r,o,a;for(o=new i(t),r=o.headers,o.purge(),n=new e(t),a=r.length;a--;)n.SEGMENT(r[a].start,r[a].length,"");return t=n.SEGMENT(),n.clear(),t},get:function(e){for(var t=[],i=0,n=s.length;i<n;i++)s[i].name===e.toUpperCase()&&t.push(s[i].segment);return t},set:function(e,t){var i,n,r,o=[];for("string"==typeof t?o.push(t):o=t,i=n=0,r=s.length;i<r&&(s[i].name===e.toUpperCase()&&(s[i].segment=o[n],s[i].length=o[n].length,n++),!(n>=o.length));i++);},purge:function(){this.headers=s=[]}}}}),n("moxie/runtime/html5/image/ExifParser",["moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(e,i,n){function r(o){function a(i,r){var o,a,s,u,l,f,p,h,m=this,g=[],v={},x={1:"BYTE",7:"UNDEFINED",2:"ASCII",3:"SHORT",4:"LONG",5:"RATIONAL",9:"SLONG",10:"SRATIONAL"},_={BYTE:1,UNDEFINED:1,ASCII:1,SHORT:2,LONG:4,RATIONAL:8,SLONG:4,SRATIONAL:8};for(o=m.SHORT(i),a=0;a<o;a++)if(g=[],p=i+2+12*a,(s=r[m.SHORT(p)])!==t){if(u=x[m.SHORT(p+=2)],l=m.LONG(p+=2),!(f=_[u]))throw new n.ImageError(n.ImageError.INVALID_META_ERR);if(p+=4,f*l>4&&(p=m.LONG(p)+d.tiffHeader),p+f*l>=this.length())throw new n.ImageError(n.ImageError.INVALID_META_ERR);"ASCII"!==u?(g=m.asArray(u,p,l),h=1==l?g[0]:g,c.hasOwnProperty(s)&&"object"!=typeof h?v[s]=c[s][h]:v[s]=h):v[s]=e.trim(m.STRING(p,l).replace(/\0$/,""))}return v}function s(e,t,i){var n,r,o,a=0;if("string"==typeof t){var s=l[e.toLowerCase()];for(var u in s)if(s[u]===t){t=u;break}}n=d[e.toLowerCase()+"IFD"],r=this.SHORT(n);for(var c=0;c<r;c++)if(o=n+12*c+2,this.SHORT(o)==t){a=o+8;break}if(!a)return!1;try{this.write(a,i,4)}catch(e){return!1}return!0}var u,l,c,d,f,p;if(i.call(this,o),l={tiff:{274:"Orientation",270:"ImageDescription",271:"Make",272:"Model",305:"Software",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer"},exif:{36864:"ExifVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",36867:"DateTimeOriginal",33434:"ExposureTime",33437:"FNumber",34855:"ISOSpeedRatings",37377:"ShutterSpeedValue",37378:"ApertureValue",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37386:"FocalLength",41986:"ExposureMode",41987:"WhiteBalance",41990:"SceneCaptureType",41988:"DigitalZoomRatio",41992:"Contrast",41993:"Saturation",41994:"Sharpness"},gps:{0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude"},thumb:{513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength"}},c={ColorSpace:{1:"sRGB",0:"Uncalibrated"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{1:"Daylight",2:"Fliorescent",3:"Tungsten",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 -5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},ExposureMode:{0:"Auto exposure",1:"Manual exposure",2:"Auto bracket"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},GPSLatitudeRef:{N:"North latitude",S:"South latitude"},GPSLongitudeRef:{E:"East longitude",W:"West longitude"}},d={tiffHeader:10},f=d.tiffHeader,u={clear:this.clear},e.extend(this,{read:function(){try{return r.prototype.read.apply(this,arguments)}catch(e){throw new n.ImageError(n.ImageError.INVALID_META_ERR)}},write:function(){try{return r.prototype.write.apply(this,arguments)}catch(e){throw new n.ImageError(n.ImageError.INVALID_META_ERR)}},UNDEFINED:function(){return this.BYTE.apply(this,arguments)},RATIONAL:function(e){return this.LONG(e)/this.LONG(e+4)},SRATIONAL:function(e){return this.SLONG(e)/this.SLONG(e+4)},ASCII:function(e){return this.CHAR(e)},TIFF:function(){return p||null},EXIF:function(){var t=null;if(d.exifIFD){try{t=a.call(this,d.exifIFD,l.exif)}catch(e){return null}if(t.ExifVersion&&"array"===e.typeOf(t.ExifVersion)){for(var i=0,n="";i<t.ExifVersion.length;i++)n+=String.fromCharCode(t.ExifVersion[i]);t.ExifVersion=n}}return t},GPS:function(){var t=null;if(d.gpsIFD){try{t=a.call(this,d.gpsIFD,l.gps)}catch(e){return null}t.GPSVersionID&&"array"===e.typeOf(t.GPSVersionID)&&(t.GPSVersionID=t.GPSVersionID.join("."))}return t},thumb:function(){if(d.IFD1)try{var e=a.call(this,d.IFD1,l.thumb);if("JPEGInterchangeFormat"in e)return this.SEGMENT(d.tiffHeader+e.JPEGInterchangeFormat,e.JPEGInterchangeFormatLength)}catch(e){}return null},setExif:function(e,t){return("PixelXDimension"===e||"PixelYDimension"===e)&&s.call(this,"exif",e,t)},clear:function(){u.clear(),o=l=c=p=d=u=null}}),65505!==this.SHORT(0)||"EXIF\0"!==this.STRING(4,5).toUpperCase())throw new n.ImageError(n.ImageError.INVALID_META_ERR);if(this.littleEndian=18761==this.SHORT(f),42!==this.SHORT(f+=2))throw new n.ImageError(n.ImageError.INVALID_META_ERR);d.IFD0=d.tiffHeader+this.LONG(f+=2),p=a.call(this,d.IFD0,l.tiff),"ExifIFDPointer"in p&&(d.exifIFD=d.tiffHeader+p.ExifIFDPointer,delete p.ExifIFDPointer),"GPSInfoIFDPointer"in p&&(d.gpsIFD=d.tiffHeader+p.GPSInfoIFDPointer,delete p.GPSInfoIFDPointer),e.isEmptyObj(p)&&(p=null);var h=this.LONG(d.IFD0+12*this.SHORT(d.IFD0)+2);h&&(d.IFD1=d.tiffHeader+h)}return r.prototype=i.prototype,r}),n("moxie/runtime/html5/image/JPEG",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEGHeaders","moxie/runtime/html5/utils/BinaryReader","moxie/runtime/html5/image/ExifParser"],function(e,t,i,n,r){function o(o){function a(e){var t,i,n=0;for(e||(e=u);n<=e.length();){if((t=e.SHORT(n+=2))>=65472&&t<=65475)return n+=5,{height:e.SHORT(n),width:e.SHORT(n+=2)};i=e.SHORT(n+=2),n+=i-2}return null}function s(){c&&l&&u&&(c.clear(),l.purge(),u.clear(),d=l=c=u=null)}var u,l,c,d;if(u=new n(o),65496!==u.SHORT(0))throw new t.ImageError(t.ImageError.WRONG_FORMAT);l=new i(o);try{c=new r(l.get("app1")[0])}catch(e){}d=a.call(this),e.extend(this,{type:"image/jpeg",size:u.length(),width:d&&d.width||0,height:d&&d.height||0,setExif:function(t,i){if(!c)return!1;"object"===e.typeOf(t)?e.each(t,function(e,t){c.setExif(t,e)}):c.setExif(t,i),l.set("app1",c.SEGMENT())},writeHeaders:function(){return arguments.length?l.restore(arguments[0]):l.restore(o)},stripHeaders:function(e){return l.strip(e)},purge:function(){s.call(this)}}),c&&(this.meta={tiff:c.TIFF(),exif:c.EXIF(),gps:c.GPS(),thumb:function(){var e,t,i=c.thumb();return i&&(e=new n(i),t=a(e),e.clear(),t)?(t.data=i,t):null}()})}return o}),n("moxie/runtime/html5/image/PNG",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader"],function(e,t,i){function n(n){function r(){var e,t;return e=a.call(this,8),"IHDR"==e.type?(t=e.start,{width:s.LONG(t),height:s.LONG(t+=4)}):null}function o(){s&&(s.clear(),n=c=u=l=s=null)}function a(e){var t,i,n,r;return t=s.LONG(e),i=s.STRING(e+=4,4),n=e+=4,r=s.LONG(e+t),{length:t,type:i,start:n,CRC:r}}var s,u,l,c;s=new i(n),function(){var t=0,i=0,n=[35152,20039,3338,6666];for(i=0;i<n.length;i++,t+=2)if(n[i]!=s.SHORT(t))throw new e.ImageError(e.ImageError.WRONG_FORMAT)}(),c=r.call(this),t.extend(this,{type:"image/png",size:s.length(),width:c.width,height:c.height,purge:function(){o.call(this)}}),o.call(this)}return n}),n("moxie/runtime/html5/image/ImageInfo",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEG","moxie/runtime/html5/image/PNG"],function(e,t,i,n){return function(r){var o,a=[i,n];o=function(){for(var e=0;e<a.length;e++)try{return new a[e](r)}catch(e){}throw new t.ImageError(t.ImageError.WRONG_FORMAT)}(),e.extend(this,{type:"",size:0,width:0,height:0,setExif:function(){},writeHeaders:function(e){return e},stripHeaders:function(e){return e},purge:function(){r=null}}),e.extend(this,o),this.purge=function(){o.purge(),o=null}}}),n("moxie/runtime/html5/image/ResizerCanvas",[],function(){function e(i,n){var r=i.width,o=Math.floor(r*n),a=!1;(n<.5||n>2)&&(n=n<.5?.5:2,a=!0);var s=t(i,n);return a?e(s,o/s.width):s}function t(e,t){var i=e.width,n=e.height,r=Math.floor(i*t),o=Math.floor(n*t),a=document.createElement("canvas");return a.width=r,a.height=o,a.getContext("2d").drawImage(e,0,0,i,n,0,0,r,o),e=null,a}return{scale:e}}),n("moxie/runtime/html5/image/Image",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/utils/Encode","moxie/file/Blob","moxie/file/File","moxie/runtime/html5/image/ImageInfo","moxie/runtime/html5/image/ResizerCanvas","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,t,i,n,r,o,a,s,u,l){function c(){function e(){if(!x&&!g)throw new i.ImageError(i.DOMException.INVALID_STATE_ERR);return x||g}function l(){var t=e();return"canvas"==t.nodeName.toLowerCase()?t:(x=document.createElement("canvas"),x.width=t.width,x.height=t.height,x.getContext("2d").drawImage(t,0,0),x)}function c(e){return n.atob(e.substring(e.indexOf("base64,")+7))}function d(e,t){return"data:"+(t||"")+";base64,"+n.btoa(e)}function f(e){var t=this;g=new Image,g.onerror=function(){m.call(this),t.trigger("error",i.ImageError.WRONG_FORMAT)},g.onload=function(){t.trigger("load")},g.src="data:"==e.substr(0,5)?e:d(e,y.type)}function p(e,t){var n,r=this;if(!window.FileReader)return t.call(this,e.getAsDataURL());n=new FileReader,n.onload=function(){t.call(r,this.result)},n.onerror=function(){r.trigger("error",i.ImageError.WRONG_FORMAT)},n.readAsDataURL(e)}function h(e,i){var n=Math.PI/180,r=document.createElement("canvas"),o=r.getContext("2d"),a=e.width,s=e.height;switch(t.inArray(i,[5,6,7,8])>-1?(r.width=s,r.height=a):(r.width=a,r.height=s),i){case 2:o.translate(a,0),o.scale(-1,1);break;case 3:o.translate(a,s),o.rotate(180*n);break;case 4:o.translate(0,s),o.scale(1,-1);break;case 5:o.rotate(90*n),o.scale(1,-1);break;case 6:o.rotate(90*n),o.translate(0,-s);break;case 7:o.rotate(90*n),o.translate(a,-s),o.scale(-1,1);break;case 8:o.rotate(-90*n),o.translate(-a,0)}return o.drawImage(e,0,0,a,s),r}function m(){v&&(v.purge(),v=null),_=g=x=y=null,E=!1}var g,v,x,_,y,w=this,E=!1,b=!0;t.extend(this,{loadFromBlob:function(e){var t=this.getRuntime(),n=!(arguments.length>1)||arguments[1];if(!t.can("access_binary"))throw new i.RuntimeError(i.RuntimeError.NOT_SUPPORTED_ERR);if(y=e,e.isDetached())return _=e.getSource(),void f.call(this,_);p.call(this,e.getSource(),function(e){n&&(_=c(e)),f.call(this,e)})},loadFromImage:function(e,t){this.meta=e.meta,y=new o(null,{name:e.name,size:e.size,type:e.type}),f.call(this,t?_=e.getAsBinaryString():e.getAsDataURL())},getInfo:function(){var t,i=this.getRuntime();return!v&&_&&i.can("access_image_binary")&&(v=new a(_)),t={width:e().width||0,height:e().height||0,type:y.type||u.getFileMime(y.name),size:_&&_.length||y.size||0,name:y.name||"",meta:null},b&&(t.meta=v&&v.meta||this.meta||{},!t.meta||!t.meta.thumb||t.meta.thumb.data instanceof r||(t.meta.thumb.data=new r(null,{type:"image/jpeg",data:t.meta.thumb.data}))),t},resize:function(t,i,n){var r=document.createElement("canvas");if(r.width=t.width,r.height=t.height,r.getContext("2d").drawImage(e(),t.x,t.y,t.width,t.height,0,0,r.width,r.height),x=s.scale(r,i),!(b=n.preserveHeaders)){var o=this.meta&&this.meta.tiff&&this.meta.tiff.Orientation||1;x=h(x,o)}this.width=x.width,this.height=x.height,E=!0,this.trigger("Resize")},getAsCanvas:function(){return x||(x=l()),x.id=this.uid+"_canvas",x},getAsBlob:function(e,t){return e!==this.type?(E=!0,new o(null,{name:y.name||"",type:e,data:w.getAsDataURL(e,t)})):new o(null,{name:y.name||"",type:e,data:w.getAsBinaryString(e,t)})},getAsDataURL:function(e){var t=arguments[1]||90;if(!E)return g.src;if(l(),"image/jpeg"!==e)return x.toDataURL("image/png");try{return x.toDataURL("image/jpeg",t/100)}catch(e){return x.toDataURL("image/jpeg")}},getAsBinaryString:function(e,t){if(!E)return _||(_=c(w.getAsDataURL(e,t))),_;if("image/jpeg"!==e)_=c(w.getAsDataURL(e,t));else{var i;t||(t=90),l();try{i=x.toDataURL("image/jpeg",t/100)}catch(e){i=x.toDataURL("image/jpeg")}_=c(i),v&&(_=v.stripHeaders(_),b&&(v.meta&&v.meta.exif&&v.setExif({PixelXDimension:this.width,PixelYDimension:this.height}),_=v.writeHeaders(_)),v.purge(),v=null)}return E=!1,_},destroy:function(){w=null,m.call(this),this.getRuntime().getShim().removeInstance(this.uid)}})}return e.Image=c}),n("moxie/runtime/flash/Runtime",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/runtime/Runtime"],function(e,t,i,n,o){function s(){var e;try{e=navigator.plugins["Shockwave Flash"],e=e.description}catch(t){try{e=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(t){e="0.0"}}return e=e.match(/\d+/g),parseFloat(e[0]+"."+e[1])}function u(e){var n=i.get(e);n&&"OBJECT"==n.nodeName&&("IE"===t.browser?(n.style.display="none",function t(){4==n.readyState?l(e):setTimeout(t,10)}()):n.parentNode.removeChild(n))}function l(e){var t=i.get(e);if(t){for(var n in t)"function"==typeof t[n]&&(t[n]=null);t.parentNode.removeChild(t)}}function c(l){var c,p=this;l=e.extend({swf_url:t.swf_url},l),o.call(this,l,d,{access_binary:function(e){return e&&"browser"===p.mode},access_image_binary:function(e){return e&&"browser"===p.mode},display_media:o.capTest(r("moxie/image/Image")),do_cors:o.capTrue,drag_and_drop:!1,report_upload_progress:function(){return"client"===p.mode},resize_image:o.capTrue,return_response_headers:!1,return_response_type:function(t){return!("json"!==t||!window.JSON)||(!e.arrayDiff(t,["","text","document"])||"browser"===p.mode)},return_status_code:function(t){return"browser"===p.mode||!e.arrayDiff(t,[200,404])},select_file:o.capTrue,select_multiple:o.capTrue,send_binary_string:function(e){return e&&"browser"===p.mode},send_browser_cookies:function(e){return e&&"browser"===p.mode},send_custom_headers:function(e){return e&&"browser"===p.mode},send_multipart:o.capTrue,slice_blob:function(e){return e&&"browser"===p.mode},stream_upload:function(e){return e&&"browser"===p.mode},summon_file_dialog:!1,upload_filesize:function(t){return e.parseSizeStr(t)<=2097152||"client"===p.mode},use_http_method:function(t){return!e.arrayDiff(t,["GET","POST"])}},{access_binary:function(e){return e?"browser":"client"},access_image_binary:function(e){return e?"browser":"client"},report_upload_progress:function(e){return e?"browser":"client"},return_response_type:function(t){return e.arrayDiff(t,["","text","json","document"])?"browser":["client","browser"]},return_status_code:function(t){return e.arrayDiff(t,[200,404])?"browser":["client","browser"]},send_binary_string:function(e){return e?"browser":"client"},send_browser_cookies:function(e){return e?"browser":"client"},send_custom_headers:function(e){return e?"browser":"client"},slice_blob:function(e){return e?"browser":"client"},stream_upload:function(e){return e?"client":"browser"},upload_filesize:function(t){return e.parseSizeStr(t)>=2097152?"client":"browser"}},"client"),s()<11.3&&(a&&t.debug.runtime&&t.log("\tFlash didn't meet minimal version requirement (11.3)."),this.mode=!1),e.extend(this,{getShim:function(){return i.get(this.uid)},shimExec:function(e,t){var i=[].slice.call(arguments,2);return p.getShim().exec(this.uid,e,t,i)},init:function(){var i,r,o;o=this.getShimContainer(),e.extend(o.style,{position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),i='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+l.swf_url+'" ',"IE"===t.browser&&(i+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),i+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+l.swf_url+'" /><param name="flashvars" value="uid='+escape(this.uid)+"&target="+t.global_event_dispatcher+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>',"IE"===t.browser?(r=document.createElement("div"),o.appendChild(r),r.outerHTML=i,r=o=null):o.innerHTML=i,c=setTimeout(function(){p&&!p.initialized&&(p.trigger("Error",new n.RuntimeError(n.RuntimeError.NOT_INIT_ERR)),a&&t.debug.runtime&&t.log("\tFlash failed to initialize within a specified period of time (typically 5s)."))},5e3)},destroy:function(e){return function(){u(p.uid),e.call(p),clearTimeout(c),l=c=e=p=null}}(this.destroy)},f)}var d="flash",f={};return o.addConstructor(d,c),f}),n("moxie/runtime/flash/file/Blob",["moxie/runtime/flash/Runtime","moxie/file/Blob"],function(e,t){var i={slice:function(e,i,n,r){var o=this.getRuntime();return i<0?i=Math.max(e.size+i,0):i>0&&(i=Math.min(i,e.size)),n<0?n=Math.max(e.size+n,0):n>0&&(n=Math.min(n,e.size)),e=o.shimExec.call(this,"Blob","slice",i,n,r||""),e&&(e=new t(o.uid,e)),e}};return e.Blob=i}),n("moxie/runtime/flash/file/FileInput",["moxie/runtime/flash/Runtime","moxie/file/File","moxie/core/utils/Basic"],function(e,t,i){var n={init:function(e){var n=this,r=this.getRuntime();this.bind("Change",function(){var e=r.shimExec.call(n,"FileInput","getFiles");n.files=[],i.each(e,function(e){n.files.push(new t(r.uid,e))})},999),this.getRuntime().shimExec.call(this,"FileInput","init",{accept:e.accept,multiple:e.multiple}),this.trigger("ready")}};return e.FileInput=n}),n("moxie/runtime/flash/file/FileReader",["moxie/runtime/flash/Runtime","moxie/core/utils/Encode"],function(e,t){function i(e,i){switch(i){case"readAsText":return t.atob(e,"utf8");case"readAsBinaryString":return t.atob(e);case"readAsDataURL":return e}return null}var n={read:function(e,t){var n=this;return n.result="","readAsDataURL"===e&&(n.result="data:"+(t.type||"")+";base64,"),n.bind("Progress",function(t,r){r&&(n.result+=i(r,e))},999),n.getRuntime().shimExec.call(this,"FileReader","readAsBase64",t.uid)}};return e.FileReader=n}),n("moxie/runtime/flash/file/FileReaderSync",["moxie/runtime/flash/Runtime","moxie/core/utils/Encode"],function(e,t){function i(e,i){switch(i){case"readAsText":return t.atob(e,"utf8");case"readAsBinaryString":return t.atob(e);case"readAsDataURL":return e}return null}var n={read:function(e,t){var n;return(n=this.getRuntime().shimExec.call(this,"FileReaderSync","readAsBase64",t.uid))?("readAsDataURL"===e&&(n="data:"+(t.type||"")+";base64,"+n),i(n,e,t.type)):null}};return e.FileReaderSync=n}),n("moxie/runtime/flash/runtime/Transporter",["moxie/runtime/flash/Runtime","moxie/file/Blob"],function(e,t){var i={getAsBlob:function(e){var i=this.getRuntime(),n=i.shimExec.call(this,"Transporter","getAsBlob",e);return n?new t(i.uid,n):null}};return e.Transporter=i}),n("moxie/runtime/flash/xhr/XMLHttpRequest",["moxie/runtime/flash/Runtime","moxie/core/utils/Basic","moxie/file/Blob","moxie/file/File","moxie/file/FileReaderSync","moxie/runtime/flash/file/FileReaderSync","moxie/xhr/FormData","moxie/runtime/Transporter","moxie/runtime/flash/runtime/Transporter"],function(e,t,i,n,r,o,a,s,u){var l={send:function(e,n){function r(){e.transport=c.mode,c.shimExec.call(l,"XMLHttpRequest","send",e,n)}function o(e,t){c.shimExec.call(l,"XMLHttpRequest","appendBlob",e,t.uid),n=null,r()}function u(e,t){var i=new s;i.bind("TransportingComplete",function(){t(this.result)}),i.transport(e.getSource(),e.type,{ruid:c.uid})}var l=this,c=l.getRuntime();if(t.isEmptyObj(e.headers)||t.each(e.headers,function(e,t){c.shimExec.call(l,"XMLHttpRequest","setRequestHeader",t,e.toString())}),n instanceof a){var d;if(n.each(function(e,t){e instanceof i?d=t:c.shimExec.call(l,"XMLHttpRequest","append",t,e)}),n.hasBlob()){var f=n.getBlob();f.isDetached()?u(f,function(e){f.destroy(),o(d,e)}):o(d,f)}else n=null,r()}else n instanceof i?n.isDetached()?u(n,function(e){n.destroy(),n=e.uid,r()}):(n=n.uid,r()):r()},getResponse:function(e){var i,o,a=this.getRuntime();if(o=a.shimExec.call(this,"XMLHttpRequest","getResponseAsBlob")){if(o=new n(a.uid,o),"blob"===e)return o;try{if(i=new r,~t.inArray(e,["","text"]))return i.readAsText(o);if("json"===e&&window.JSON)return JSON.parse(i.readAsText(o))}finally{o.destroy()}}return null},abort:function(e){this.getRuntime().shimExec.call(this,"XMLHttpRequest","abort"),this.dispatchEvent("readystatechange"),this.dispatchEvent("abort")}};return e.XMLHttpRequest=l}),n("moxie/runtime/flash/image/Image",["moxie/runtime/flash/Runtime","moxie/core/utils/Basic","moxie/runtime/Transporter","moxie/file/Blob","moxie/file/FileReaderSync"],function(e,t,i,n,r){var o={loadFromBlob:function(e){function t(e){r.shimExec.call(n,"Image","loadFromBlob",e.uid),n=r=null}var n=this,r=n.getRuntime();if(e.isDetached()){var o=new i;o.bind("TransportingComplete",function(){t(o.result.getSource())}),o.transport(e.getSource(),e.type,{ruid:r.uid})}else t(e.getSource())},loadFromImage:function(e){return this.getRuntime().shimExec.call(this,"Image","loadFromImage",e.uid)},getInfo:function(){var e=this.getRuntime(),t=e.shimExec.call(this,"Image","getInfo");return t.meta&&t.meta.thumb&&t.meta.thumb.data&&!(e.meta.thumb.data instanceof n)&&(t.meta.thumb.data=new n(e.uid,t.meta.thumb.data)),t},getAsBlob:function(e,t){var i=this.getRuntime(),r=i.shimExec.call(this,"Image","getAsBlob",e,t);return r?new n(i.uid,r):null},getAsDataURL:function(){var e,t=this.getRuntime(),i=t.Image.getAsBlob.apply(this,arguments);return i?(e=new r,e.readAsDataURL(i)):null}};return e.Image=o}),n("moxie/runtime/silverlight/Runtime",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/runtime/Runtime"],function(e,t,i,n,o){function s(e){var t,i,n,r,o,a=!1,s=null,u=0;try{try{s=new ActiveXObject("AgControl.AgControl"),s.IsVersionSupported(e)&&(a=!0),s=null}catch(s){var l=navigator.plugins["Silverlight Plug-In"];if(l){for(t=l.description,"1.0.30226.2"===t&&(t="2.0.30226.2"),i=t.split(".");i.length>3;)i.pop();for(;i.length<4;)i.push(0);for(n=e.split(".");n.length>4;)n.pop();do{r=parseInt(n[u],10),o=parseInt(i[u],10),u++}while(u<n.length&&r===o);r<=o&&!isNaN(r)&&(a=!0)}}}catch(e){a=!1}return a}function u(u){var d,f=this;u=e.extend({xap_url:t.xap_url},u),o.call(this,u,l,{access_binary:o.capTrue,access_image_binary:o.capTrue,display_media:o.capTest(r("moxie/image/Image")),do_cors:o.capTrue,drag_and_drop:!1,report_upload_progress:o.capTrue,resize_image:o.capTrue,return_response_headers:function(e){return e&&"client"===f.mode},return_response_type:function(e){return"json"!==e||!!window.JSON},return_status_code:function(t){return"client"===f.mode||!e.arrayDiff(t,[200,404])},select_file:o.capTrue,select_multiple:o.capTrue,send_binary_string:o.capTrue,send_browser_cookies:function(e){return e&&"browser"===f.mode},send_custom_headers:function(e){return e&&"client"===f.mode},send_multipart:o.capTrue,slice_blob:o.capTrue,stream_upload:!0,summon_file_dialog:!1,upload_filesize:o.capTrue,use_http_method:function(t){return"client"===f.mode||!e.arrayDiff(t,["GET","POST"])}},{return_response_headers:function(e){return e?"client":"browser"},return_status_code:function(t){return e.arrayDiff(t,[200,404])?"client":["client","browser"]},send_browser_cookies:function(e){return e?"browser":"client"},send_custom_headers:function(e){return e?"client":"browser"},use_http_method:function(t){return e.arrayDiff(t,["GET","POST"])?"client":["client","browser"]}}),s("2.0.31005.0")&&"Opera"!==t.browser||(a&&t.debug.runtime&&t.log("\tSilverlight is not installed or minimal version (2.0.31005.0) requirement not met (not likely)."),this.mode=!1),e.extend(this,{getShim:function(){return i.get(this.uid).content.Moxie},shimExec:function(e,t){var i=[].slice.call(arguments,2);return f.getShim().exec(this.uid,e,t,i)},init:function(){var e;e=this.getShimContainer(),e.innerHTML='<object id="'+this.uid+'" data="data:application/x-silverlight," type="application/x-silverlight-2" width="100%" height="100%" style="outline:none;"><param name="source" value="'+u.xap_url+'"/><param name="background" value="Transparent"/><param name="windowless" value="true"/><param name="enablehtmlaccess" value="true"/><param name="initParams" value="uid='+this.uid+",target="+t.global_event_dispatcher+'"/></object>',d=setTimeout(function(){f&&!f.initialized&&(f.trigger("Error",new n.RuntimeError(n.RuntimeError.NOT_INIT_ERR)),a&&t.debug.runtime&&t.log("Silverlight failed to initialize within a specified period of time (5-10s)."))},"Windows"!==t.OS?1e4:5e3)},destroy:function(e){return function(){e.call(f),clearTimeout(d),u=d=e=f=null}}(this.destroy)},c)}var l="silverlight",c={};return o.addConstructor(l,u),c}),n("moxie/runtime/silverlight/file/Blob",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/file/Blob"],function(e,t,i){return e.Blob=t.extend({},i)}),n("moxie/runtime/silverlight/file/FileInput",["moxie/runtime/silverlight/Runtime","moxie/file/File","moxie/core/utils/Basic"],function(e,t,i){function n(e){for(var t="",i=0;i<e.length;i++)t+=(""!==t?"|":"")+e[i].title+" | *."+e[i].extensions.replace(/,/g,";*.");return t}var r={init:function(e){var r=this,o=this.getRuntime();this.bind("Change",function(){var e=o.shimExec.call(r,"FileInput","getFiles");r.files=[],i.each(e,function(e){r.files.push(new t(o.uid,e))})},999),o.shimExec.call(this,"FileInput","init",n(e.accept),e.multiple),this.trigger("ready")},setOption:function(e,t){"accept"==e&&(t=n(t)),this.getRuntime().shimExec.call(this,"FileInput","setOption",e,t)}};return e.FileInput=r}),n("moxie/runtime/silverlight/file/FileDrop",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Dom","moxie/core/utils/Events"],function(e,t,i){var n={init:function(){var e,n=this,r=n.getRuntime();return e=r.getShimContainer(),i.addEvent(e,"dragover",function(e){e.preventDefault(),e.stopPropagation(),e.dataTransfer.dropEffect="copy"},n.uid),i.addEvent(e,"dragenter",function(e){e.preventDefault(),t.get(r.uid).dragEnter(e)&&e.stopPropagation()},n.uid),i.addEvent(e,"drop",function(e){e.preventDefault(),t.get(r.uid).dragDrop(e)&&e.stopPropagation()},n.uid),r.shimExec.call(this,"FileDrop","init")}};return e.FileDrop=n}),n("moxie/runtime/silverlight/file/FileReader",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/file/FileReader"],function(e,t,i){return e.FileReader=t.extend({},i)}),n("moxie/runtime/silverlight/file/FileReaderSync",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/file/FileReaderSync"],function(e,t,i){return e.FileReaderSync=t.extend({},i)}),n("moxie/runtime/silverlight/runtime/Transporter",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/runtime/Transporter"],function(e,t,i){return e.Transporter=t.extend({},i)}),n("moxie/runtime/silverlight/xhr/XMLHttpRequest",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/xhr/XMLHttpRequest","moxie/runtime/silverlight/file/FileReaderSync","moxie/runtime/silverlight/runtime/Transporter"],function(e,t,i,n,r){return e.XMLHttpRequest=t.extend({},i)}),n("moxie/runtime/silverlight/image/Image",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/file/Blob","moxie/runtime/flash/image/Image"],function(e,t,i,n){return e.Image=t.extend({},n,{getInfo:function(){var e=this.getRuntime(),n=["tiff","exif","gps","thumb"],r={meta:{}},o=e.shimExec.call(this,"Image","getInfo");return o.meta&&(t.each(n,function(e){var t,i,n,a,s=o.meta[e];if(s&&s.keys)for(r.meta[e]={},i=0,n=s.keys.length;i<n;i++)t=s.keys[i],(a=s[t])&&(/^(\d|[1-9]\d+)$/.test(a)?a=parseInt(a,10):/^\d*\.\d+$/.test(a)&&(a=parseFloat(a)),r.meta[e][t]=a)}),r.meta&&r.meta.thumb&&r.meta.thumb.data&&!(e.meta.thumb.data instanceof i)&&(r.meta.thumb.data=new i(e.uid,r.meta.thumb.data))),r.width=parseInt(o.width,10),r.height=parseInt(o.height,10),r.size=parseInt(o.size,10),r.type=o.type,r.name=o.name,r},resize:function(e,t,i){this.getRuntime().shimExec.call(this,"Image","resize",e.x,e.y,e.width,e.height,t,i.preserveHeaders,i.resample)}})}),n("moxie/runtime/html4/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(e,t,i,n){function o(t){var o=this,u=i.capTest,l=i.capTrue;i.call(this,t,a,{access_binary:u(window.FileReader||window.File&&File.getAsDataURL),access_image_binary:!1,display_media:u((n.can("create_canvas")||n.can("use_data_uri_over32kb"))&&r("moxie/image/Image")),do_cors:!1,drag_and_drop:!1,filter_by_extension:u(function(){return!("Chrome"===n.browser&&n.verComp(n.version,28,"<")||"IE"===n.browser&&n.verComp(n.version,10,"<")||"Safari"===n.browser&&n.verComp(n.version,7,"<")||"Firefox"===n.browser&&n.verComp(n.version,37,"<"))}()),resize_image:function(){return s.Image&&o.can("access_binary")&&n.can("create_canvas")},report_upload_progress:!1,return_response_headers:!1,return_response_type:function(t){return!("json"!==t||!window.JSON)||!!~e.inArray(t,["text","document",""])},return_status_code:function(t){return!e.arrayDiff(t,[200,404])},select_file:function(){return n.can("use_fileinput")},select_multiple:!1,send_binary_string:!1,send_custom_headers:!1,send_multipart:!0,slice_blob:!1,stream_upload:function(){return o.can("select_file")},summon_file_dialog:function(){return o.can("select_file")&&("Firefox"===n.browser&&n.verComp(n.version,4,">=")||"Opera"===n.browser&&n.verComp(n.version,12,">=")||"IE"===n.browser&&n.verComp(n.version,10,">=")||!!~e.inArray(n.browser,["Chrome","Safari"]))},upload_filesize:l,use_http_method:function(t){return!e.arrayDiff(t,["GET","POST"])}}),e.extend(this,{init:function(){this.trigger("Init")},destroy:function(e){return function(){e.call(o),e=o=null}}(this.destroy)}),e.extend(this.getShim(),s)}var a="html4",s={};return i.addConstructor(a,o),s}),n("moxie/runtime/html4/file/FileInput",["moxie/runtime/html4/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,t,i,n,r,o,a){function s(){function e(){var o,l,d,f,p,h,m=this,g=m.getRuntime();h=i.guid("uid_"),o=g.getShimContainer(),s&&(d=n.get(s+"_form"))&&i.extend(d.style,{top:"100%"}),f=document.createElement("form"),f.setAttribute("id",h+"_form"),f.setAttribute("method","post"),f.setAttribute("enctype","multipart/form-data"),f.setAttribute("encoding","multipart/form-data"),i.extend(f.style,{overflow:"hidden",position:"absolute",top:0,left:0,width:"100%",height:"100%"}),p=document.createElement("input"),p.setAttribute("id",h),p.setAttribute("type","file"),p.setAttribute("accept",c.join(",")),i.extend(p.style,{fontSize:"999px",opacity:0}),f.appendChild(p),o.appendChild(f),i.extend(p.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),"IE"===a.browser&&a.verComp(a.version,10,"<")&&i.extend(p.style,{filter:"progid:DXImageTransform.Microsoft.Alpha(opacity=0)"}),p.onchange=function(){var i;if(this.value){if(this.files){if(i=this.files[0],0===i.size)return void f.parentNode.removeChild(f)}else i={name:this.value};i=new t(g.uid,i),this.onchange=function(){},e.call(m),m.files=[i],p.setAttribute("id",i.uid),f.setAttribute("id",i.uid+"_form"),m.trigger("change"),p=f=null}},g.can("summon_file_dialog")&&(l=n.get(u.browse_button),r.removeEvent(l,"click",m.uid),r.addEvent(l,"click",function(e){p&&!p.disabled&&p.click(),e.preventDefault()},m.uid)),s=h,o=d=l=null}var s,u,l,c=[];i.extend(this,{init:function(t){var i,a=this,s=a.getRuntime();u=t,c=t.accept.mimes||o.extList2mimes(t.accept,s.can("filter_by_extension")),i=s.getShimContainer(),function(){var e,o,c;e=n.get(t.browse_button),l=n.getStyle(e,"z-index")||"auto",s.can("summon_file_dialog")&&("static"===n.getStyle(e,"position")&&(e.style.position="relative"),a.bind("Refresh",function(){o=parseInt(l,10)||1,n.get(u.browse_button).style.zIndex=o,this.getRuntime().getShimContainer().style.zIndex=o-1})),c=s.can("summon_file_dialog")?e:i,r.addEvent(c,"mouseover",function(){a.trigger("mouseenter")},a.uid),r.addEvent(c,"mouseout",function(){a.trigger("mouseleave")},a.uid),r.addEvent(c,"mousedown",function(){a.trigger("mousedown")},a.uid),r.addEvent(n.get(t.container),"mouseup",function(){a.trigger("mouseup")},a.uid),e=null}(),e.call(this),i=null,a.trigger({type:"ready",async:!0})},setOption:function(e,t){var i,r=this.getRuntime();"accept"==e&&(c=t.mimes||o.extList2mimes(t,r.can("filter_by_extension"))),(i=n.get(s))&&i.setAttribute("accept",c.join(","))},disable:function(e){var t;(t=n.get(s))&&(t.disabled=!!e)},destroy:function(){var e=this.getRuntime(),t=e.getShim(),i=e.getShimContainer(),o=u&&n.get(u.container),a=u&&n.get(u.browse_button);o&&r.removeAllEvents(o,this.uid),a&&(r.removeAllEvents(a,this.uid),a.style.zIndex=l),i&&(r.removeAllEvents(i,this.uid),i.innerHTML=""),t.removeInstance(this.uid),s=c=u=i=o=a=t=null}})}return e.FileInput=s}),n("moxie/runtime/html4/file/FileReader",["moxie/runtime/html4/Runtime","moxie/runtime/html5/file/FileReader"],function(e,t){return e.FileReader=t}),n("moxie/runtime/html4/xhr/XMLHttpRequest",["moxie/runtime/html4/Runtime","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Url","moxie/core/Exceptions","moxie/core/utils/Events","moxie/file/Blob","moxie/xhr/FormData"],function(e,t,i,n,r,o,a,s){function u(){function e(e){var t,n,r,a,s=this,u=!1;if(c){if(t=c.id.replace(/_iframe$/,""),n=i.get(t+"_form")){for(r=n.getElementsByTagName("input"),a=r.length;a--;)switch(r[a].getAttribute("type")){case"hidden":r[a].parentNode.removeChild(r[a]);break;case"file":u=!0}r=[],u||n.parentNode.removeChild(n),n=null}setTimeout(function(){o.removeEvent(c,"load",s.uid),c.parentNode&&c.parentNode.removeChild(c);var t=s.getRuntime().getShimContainer();t.children.length||t.parentNode.removeChild(t),t=c=null,e()},1)}}var u,l,c;t.extend(this,{send:function(d,f){var p,h,m,g,v=this,x=v.getRuntime();if(u=l=null,f instanceof s&&f.hasBlob()){if(g=f.getBlob(),p=g.uid,m=i.get(p),!(h=i.get(p+"_form")))throw new r.DOMException(r.DOMException.NOT_FOUND_ERR)}else p=t.guid("uid_"),h=document.createElement("form"),h.setAttribute("id",p+"_form"),h.setAttribute("method",d.method),h.setAttribute("enctype","multipart/form-data"),h.setAttribute("encoding","multipart/form-data"),x.getShimContainer().appendChild(h);h.setAttribute("target",p+"_iframe"),f instanceof s&&f.each(function(e,i){if(e instanceof a)m&&m.setAttribute("name",i);else{var n=document.createElement("input");t.extend(n,{type:"hidden",name:i,value:e}),m?h.insertBefore(n,m):h.appendChild(n)}}),h.setAttribute("action",d.url),function(){var i=x.getShimContainer()||document.body,r=document.createElement("div");r.innerHTML='<iframe id="'+p+'_iframe" name="'+p+'_iframe" src="javascript:&quot;&quot;" style="display:none"></iframe>',c=r.firstChild,i.appendChild(c),o.addEvent(c,"load",function(){var i;try{i=c.contentWindow.document||c.contentDocument||window.frames[c.id].document,/^4(0[0-9]|1[0-7]|2[2346])\s/.test(i.title)?u=i.title.replace(/^(\d+).*$/,"$1"):(u=200,l=t.trim(i.body.innerHTML),v.trigger({type:"progress",loaded:l.length,total:l.length}),g&&v.trigger({type:"uploadprogress",loaded:g.size||1025,total:g.size||1025}))}catch(t){if(!n.hasSameOrigin(d.url))return void e.call(v,function(){v.trigger("error")});u=404}e.call(v,function(){v.trigger("load")})},v.uid)}(),h.submit(),v.trigger("loadstart")},getStatus:function(){return u},getResponse:function(e){if("json"===e&&"string"===t.typeOf(l)&&window.JSON)try{return JSON.parse(l.replace(/^\s*<pre[^>]*>/,"").replace(/<\/pre>\s*$/,""))}catch(e){return null}return l},abort:function(){var t=this;c&&c.contentWindow&&(c.contentWindow.stop?c.contentWindow.stop():c.contentWindow.document.execCommand?c.contentWindow.document.execCommand("Stop"):c.src="about:blank"),e.call(this,function(){t.dispatchEvent("abort")})}})}return e.XMLHttpRequest=u}),n("moxie/runtime/html4/image/Image",["moxie/runtime/html4/Runtime","moxie/runtime/html5/image/Image"],function(e,t){return e.Image=t}),function(i){for(var n=0;n<i.length;n++){for(var r=e,o=i[n],a=o.split(/[.\/]/),u=0;u<a.length-1;++u)r[a[u]]===t&&(r[a[u]]={}),r=r[a[u]];r[a[a.length-1]]=s[o]}}(["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Dom","moxie/core/EventTarget","moxie/runtime/Runtime","moxie/runtime/RuntimeClient","moxie/file/Blob","moxie/core/I18n","moxie/core/utils/Mime","moxie/file/FileInput","moxie/file/File","moxie/file/FileDrop","moxie/file/FileReader","moxie/core/utils/Url","moxie/runtime/RuntimeTarget","moxie/xhr/FormData","moxie/xhr/XMLHttpRequest","moxie/runtime/Transporter","moxie/image/Image","moxie/core/utils/Events","moxie/runtime/html5/image/ResizerCanvas"])}(this)})},function(e,t,i){var n,r,o;!function(a,s){var u=function(){var e={};return s.apply(e,arguments),e.plupload};r=[i(0)],n=u,void 0!==(o="function"==typeof n?n.apply(t,r):n)&&(e.exports=o)}(this||window,function(e){!function(e,t,i){function n(e){function t(e,t,i){var r={chunks:"slice_blob",jpgresize:"send_binary_string",pngresize:"send_binary_string",progress:"report_upload_progress",multi_selection:"select_multiple",dragdrop:"drag_and_drop",drop_element:"drag_and_drop",headers:"send_custom_headers",urlstream_upload:"send_binary_string",canSendBinary:"send_binary",triggerDialog:"summon_file_dialog"};r[e]?n[r[e]]=t:i||(n[e]=t)}var i=e.required_features,n={};return"string"==typeof i?u.each(i.split(/\s*,\s*/),function(e){t(e,!0)}):"object"==typeof i?u.each(i,function(e,i){t(i,e)}):!0===i&&(e.chunk_size&&e.chunk_size>0&&(n.slice_blob=!0),u.isEmptyObj(e.resize)&&!1!==e.multipart||(n.send_binary_string=!0),e.http_method&&(n.use_http_method=e.http_method),u.each(e,function(e,i){t(i,!!e,!0)})),n}var r=window.setTimeout,o={},a=t.core.utils,s=t.runtime.Runtime,u={VERSION:"2.3.1",STOPPED:1,STARTED:2,QUEUED:1,UPLOADING:2,FAILED:4,DONE:5,GENERIC_ERROR:-100,HTTP_ERROR:-200,IO_ERROR:-300,SECURITY_ERROR:-400,INIT_ERROR:-500,FILE_SIZE_ERROR:-600,FILE_EXTENSION_ERROR:-601,FILE_DUPLICATE_ERROR:-602,IMAGE_FORMAT_ERROR:-700,MEMORY_ERROR:-701,IMAGE_DIMENSIONS_ERROR:-702,mimeTypes:a.Mime.mimes,ua:a.Env,typeOf:a.Basic.typeOf,extend:a.Basic.extend,guid:a.Basic.guid,getAll:function(e){var t,i=[];"array"!==u.typeOf(e)&&(e=[e]);for(var n=e.length;n--;)(t=u.get(e[n]))&&i.push(t);return i.length?i:null},get:a.Dom.get,each:a.Basic.each,getPos:a.Dom.getPos,getSize:a.Dom.getSize,xmlEncode:function(e){var t={"<":"lt",">":"gt","&":"amp",'"':"quot","'":"#39"},i=/[<>&\"\']/g;return e?(""+e).replace(i,function(e){return t[e]?"&"+t[e]+";":e}):e},toArray:a.Basic.toArray,inArray:a.Basic.inArray,inSeries:a.Basic.inSeries,addI18n:t.core.I18n.addI18n,translate:t.core.I18n.translate,sprintf:a.Basic.sprintf,isEmptyObj:a.Basic.isEmptyObj,hasClass:a.Dom.hasClass,addClass:a.Dom.addClass,removeClass:a.Dom.removeClass,getStyle:a.Dom.getStyle,addEvent:a.Events.addEvent,removeEvent:a.Events.removeEvent,removeAllEvents:a.Events.removeAllEvents,cleanName:function(e){var t,i;for(i=[/[\300-\306]/g,"A",/[\340-\346]/g,"a",/\307/g,"C",/\347/g,"c",/[\310-\313]/g,"E",/[\350-\353]/g,"e",/[\314-\317]/g,"I",/[\354-\357]/g,"i",/\321/g,"N",/\361/g,"n",/[\322-\330]/g,"O",/[\362-\370]/g,"o",/[\331-\334]/g,"U",/[\371-\374]/g,"u"],t=0;t<i.length;t+=2)e=e.replace(i[t],i[t+1]);return e=e.replace(/\s+/g,"_"),e=e.replace(/[^a-z0-9_\-\.]+/gi,"")},buildUrl:function(e,t){var i="";return u.each(t,function(e,t){i+=(i?"&":"")+encodeURIComponent(t)+"="+encodeURIComponent(e)}),i&&(e+=(e.indexOf("?")>0?"&":"?")+i),e},formatSize:function(e){function t(e,t){return Math.round(e*Math.pow(10,t))/Math.pow(10,t)}if(e===i||/\D/.test(e))return u.translate("N/A");var n=Math.pow(1024,4);return e>n?t(e/n,1)+" "+u.translate("tb"):e>(n/=1024)?t(e/n,1)+" "+u.translate("gb"):e>(n/=1024)?t(e/n,1)+" "+u.translate("mb"):e>1024?Math.round(e/1024)+" "+u.translate("kb"):e+" "+u.translate("b")},parseSize:a.Basic.parseSizeStr,predictRuntime:function(e,t){var i,n;return i=new u.Uploader(e),n=s.thatCan(i.getOption().required_features,t||e.runtimes),i.destroy(),n},addFileFilter:function(e,t){o[e]=t}};u.addFileFilter("mime_types",function(e,t,i){e.length&&!e.regexp.test(t.name)?(this.trigger("Error",{code:u.FILE_EXTENSION_ERROR,message:u.translate("File extension error."),file:t}),i(!1)):i(!0)}),u.addFileFilter("max_file_size",function(e,t,i){e=u.parseSize(e),void 0!==t.size&&e&&t.size>e?(this.trigger("Error",{code:u.FILE_SIZE_ERROR,message:u.translate("File size error."),file:t}),i(!1)):i(!0)}),u.addFileFilter("prevent_duplicates",function(e,t,i){if(e)for(var n=this.files.length;n--;)if(t.name===this.files[n].name&&t.size===this.files[n].size)return this.trigger("Error",{code:u.FILE_DUPLICATE_ERROR,message:u.translate("Duplicate file error."),file:t}),void i(!1);i(!0)}),u.Uploader=function(e){function a(){var e,t,i=0;if(this.state==u.STARTED){for(t=0;t<D.length;t++)e||D[t].status!=u.QUEUED?i++:(e=D[t],this.trigger("BeforeUpload",e)&&(e.status=u.UPLOADING,this.trigger("UploadFile",e)));i==D.length&&(this.state!==u.STOPPED&&(this.state=u.STOPPED,this.trigger("StateChanged")),this.trigger("UploadComplete",D))}}function l(e){e.percent=e.size>0?Math.ceil(e.loaded/e.size*100):100,c()}function c(){var e,t,n,r=0;for(A.reset(),e=0;e<D.length;e++)t=D[e],t.size!==i?(A.size+=t.origSize,n=t.loaded*t.origSize/t.size,(!t.completeTimestamp||t.completeTimestamp>T)&&(r+=n),A.loaded+=n):A.size=i,t.status==u.DONE?A.uploaded++:t.status==u.FAILED?A.failed++:A.queued++;A.size===i?A.percent=D.length>0?Math.ceil(A.uploaded/D.length*100):0:(A.bytesPerSec=Math.ceil(r/((+new Date-T||1)/1e3)),A.percent=A.size>0?Math.ceil(A.loaded/A.size*100):0)}function d(){var e=N[0]||k[0];return!!e&&e.getRuntime().uid}function f(e,t){if(e.ruid){var i=s.getInfo(e.ruid);if(i)return i.can(t)}return!1}function p(){this.bind("FilesAdded FilesRemoved",function(e){e.trigger("QueueChanged"),e.refresh()}),this.bind("CancelUpload",w),this.bind("BeforeUpload",v),this.bind("UploadFile",x),this.bind("UploadProgress",_),this.bind("StateChanged",y),this.bind("QueueChanged",c),this.bind("Error",b),this.bind("FileUploaded",E),this.bind("Destroy",R)}function h(e,i){var n=this,r=0,o=[],a={runtime_order:e.runtimes,required_caps:e.required_features,preferred_caps:C,swf_url:e.flash_swf_url,xap_url:e.silverlight_xap_url};u.each(e.runtimes.split(/\s*,\s*/),function(t){e[t]&&(a[t]=e[t])}),e.browse_button&&u.each(e.browse_button,function(i){o.push(function(o){var l=new t.file.FileInput(u.extend({},a,{accept:e.filters.mime_types,name:e.file_data_name,multiple:e.multi_selection,container:e.container,browse_button:i}));l.onready=function(){var e=s.getInfo(this.ruid);u.extend(n.features,{chunks:e.can("slice_blob"),multipart:e.can("send_multipart"),multi_selection:e.can("select_multiple")}),r++,N.push(this),o()},l.onchange=function(){n.addFile(this.files)},l.bind("mouseenter mouseleave mousedown mouseup",function(t){L||(e.browse_button_hover&&("mouseenter"===t.type?u.addClass(i,e.browse_button_hover):"mouseleave"===t.type&&u.removeClass(i,e.browse_button_hover)),e.browse_button_active&&("mousedown"===t.type?u.addClass(i,e.browse_button_active):"mouseup"===t.type&&u.removeClass(i,e.browse_button_active)))}),l.bind("mousedown",function(){n.trigger("Browse")}),l.bind("error runtimeerror",function(){l=null,o()}),l.init()})}),e.drop_element&&u.each(e.drop_element,function(e){o.push(function(i){var o=new t.file.FileDrop(u.extend({},a,{drop_zone:e}));o.onready=function(){var e=s.getInfo(this.ruid);u.extend(n.features,{chunks:e.can("slice_blob"),multipart:e.can("send_multipart"),dragdrop:e.can("drag_and_drop")}),r++,k.push(this),i()},o.ondrop=function(){n.addFile(this.files)},o.bind("error runtimeerror",function(){o=null,i()}),o.init()})}),u.inSeries(o,function(){"function"==typeof i&&i(r)})}function m(e,n,r){var o=new t.image.Image;try{o.onload=function(){if(n.width>this.width&&n.height>this.height&&n.quality===i&&n.preserve_headers&&!n.crop)return this.destroy(),r(e);o.downsize(n.width,n.height,n.crop,n.preserve_headers)},o.onresize=function(){r(this.getAsBlob(e.type,n.quality)),this.destroy()},o.onerror=function(){r(e)},o.load(e)}catch(t){r(e)}}function g(e,i,r){function o(e,i,n){var r=S[e];switch(e){case"max_file_size":"max_file_size"===e&&(S.max_file_size=S.filters.max_file_size=i);break;case"chunk_size":(i=u.parseSize(i))&&(S[e]=i,S.send_file_name=!0);break;case"multipart":S[e]=i,i||(S.send_file_name=!0);break;case"http_method":S[e]="PUT"===i.toUpperCase()?"PUT":"POST";break;case"unique_names":S[e]=i,i&&(S.send_file_name=!0);break;case"filters":"array"===u.typeOf(i)&&(i={mime_types:i}),n?u.extend(S.filters,i):S.filters=i,i.mime_types&&("string"===u.typeOf(i.mime_types)&&(i.mime_types=t.core.utils.Mime.mimes2extList(i.mime_types)),i.mime_types.regexp=function(e){var t=[];return u.each(e,function(e){u.each(e.extensions.split(/,/),function(e){/^\s*\*\s*$/.test(e)?t.push("\\.*"):t.push("\\."+e.replace(new RegExp("["+"/^$.*+?|()[]{}\\".replace(/./g,"\\$&")+"]","g"),"\\$&"))})}),new RegExp("("+t.join("|")+")$","i")}(i.mime_types),S.filters.mime_types=i.mime_types);break;case"resize":S.resize=!!i&&u.extend({preserve_headers:!0,crop:!1},i);break;case"prevent_duplicates":S.prevent_duplicates=S.filters.prevent_duplicates=!!i;break;case"container":case"browse_button":case"drop_element":i="container"===e?u.get(i):u.getAll(i);case"runtimes":case"multi_selection":case"flash_swf_url":case"silverlight_xap_url":S[e]=i,n||(l=!0);break;default:S[e]=i}n||a.trigger("OptionChanged",e,i,r)}var a=this,l=!1;"object"==typeof e?u.each(e,function(e,t){o(t,e,r)}):o(e,i,r),r?(S.required_features=n(u.extend({},S)),C=n(u.extend({},S,{required_features:!0}))):l&&(a.trigger("Destroy"),h.call(a,S,function(e){e?(a.runtime=s.getInfo(d()).type,a.trigger("Init",{runtime:a.runtime}),a.trigger("PostInit")):a.trigger("Error",{code:u.INIT_ERROR,message:u.translate("Init error.")})}))}function v(e,t){if(e.settings.unique_names){var i=t.name.match(/\.([^.]+)$/),n="part";i&&(n=i[1]),t.target_name=t.id+"."+n}}function x(e,i){function n(){d-- >0?r(o,1e3):(i.loaded=h,e.trigger("Error",{code:u.HTTP_ERROR,message:u.translate("HTTP Error."),file:i,response:I.responseText,status:I.status,responseHeaders:I.getAllResponseHeaders()}))}function o(){var t,n,r={};i.status===u.UPLOADING&&e.state!==u.STOPPED&&(e.settings.send_file_name&&(r.name=i.target_name||i.name),c&&p.chunks&&s.size>c?(n=Math.min(c,s.size-h),t=s.slice(h,h+n)):(n=s.size,t=s),c&&p.chunks&&(e.settings.send_chunk_number?(r.chunk=Math.ceil(h/c),r.chunks=Math.ceil(s.size/c)):(r.offset=h,r.total=s.size)),e.trigger("BeforeChunkUpload",i,r,t,h)&&a(r,t,n))}function a(a,c,f){var m;I=new t.xhr.XMLHttpRequest,I.upload&&(I.upload.onprogress=function(t){i.loaded=Math.min(i.size,h+t.loaded),e.trigger("UploadProgress",i)}),I.onload=function(){if(I.status>=400)return void n();d=e.settings.max_retries,f<s.size?(c.destroy(),h+=f,i.loaded=Math.min(h,s.size),e.trigger("ChunkUploaded",i,{offset:i.loaded,total:s.size,response:I.responseText,status:I.status,responseHeaders:I.getAllResponseHeaders()}),"Android Browser"===u.ua.browser&&e.trigger("UploadProgress",i)):i.loaded=i.size,c=m=null,!h||h>=s.size?(i.size!=i.origSize&&(s.destroy(),s=null),e.trigger("UploadProgress",i),i.status=u.DONE,i.completeTimestamp=+new Date,e.trigger("FileUploaded",i,{response:I.responseText,status:I.status,responseHeaders:I.getAllResponseHeaders()})):r(o,1)},I.onerror=function(){n()},I.onloadend=function(){this.destroy(),I=null},e.settings.multipart&&p.multipart?(I.open(e.settings.http_method,l,!0),u.each(e.settings.headers,function(e,t){I.setRequestHeader(t,e)}),m=new t.xhr.FormData,u.each(u.extend(a,e.settings.multipart_params),function(e,t){m.append(t,e)}),m.append(e.settings.file_data_name,c),I.send(m,{runtime_order:e.settings.runtimes,required_caps:e.settings.required_features,preferred_caps:C,swf_url:e.settings.flash_swf_url,xap_url:e.settings.silverlight_xap_url})):(l=u.buildUrl(e.settings.url,u.extend(a,e.settings.multipart_params)),I.open(e.settings.http_method,l,!0),u.each(e.settings.headers,function(e,t){I.setRequestHeader(t,e)}),I.hasRequestHeader("Content-Type")||I.setRequestHeader("Content-Type","application/octet-stream"),I.send(c,{runtime_order:e.settings.runtimes,required_caps:e.settings.required_features,preferred_caps:C,swf_url:e.settings.flash_swf_url,xap_url:e.settings.silverlight_xap_url}))}var s,l=e.settings.url,c=e.settings.chunk_size,d=e.settings.max_retries,p=e.features,h=0;i.loaded&&(h=i.loaded=c?c*Math.floor(i.loaded/c):0),s=i.getSource(),!u.isEmptyObj(e.settings.resize)&&f(s,"send_binary_string")&&-1!==u.inArray(s.type,["image/jpeg","image/png"])?m.call(this,s,e.settings.resize,function(e){s=e,i.size=e.size,o()}):o()}function _(e,t){l(t)}function y(e){if(e.state==u.STARTED)T=+new Date;else if(e.state==u.STOPPED)for(var t=e.files.length-1;t>=0;t--)e.files[t].status==u.UPLOADING&&(e.files[t].status=u.QUEUED,c())}function w(){I&&I.abort()}function E(e){c(),r(function(){a.call(e)},1)}function b(e,t){t.code===u.INIT_ERROR?e.destroy():t.code===u.HTTP_ERROR&&(t.file.status=u.FAILED,t.file.completeTimestamp=+new Date,l(t.file),e.state==u.STARTED&&(e.trigger("CancelUpload"),r(function(){a.call(e)},1)))}function R(e){e.stop(),u.each(D,function(e){e.destroy()}),D=[],N.length&&(u.each(N,function(e){e.destroy()}),N=[]),k.length&&(u.each(k,function(e){e.destroy()}),k=[]),C={},L=!1,T=I=null,A.reset()}var S,T,A,I,O=u.guid(),D=[],C={},N=[],k=[],L=!1;S={chunk_size:0,file_data_name:"file",filters:{mime_types:[],prevent_duplicates:!1,max_file_size:0},flash_swf_url:"js/Moxie.swf",http_method:"POST",max_retries:0,multipart:!0,multi_selection:!0,resize:!1,runtimes:s.order,send_file_name:!0,send_chunk_number:!0,silverlight_xap_url:"js/Moxie.xap"},g.call(this,e,null,!0),A=new u.QueueProgress,u.extend(this,{id:O,uid:O,state:u.STOPPED,features:{},runtime:null,files:D,settings:S,total:A,init:function(){var e,t,i=this;return e=i.getOption("preinit"),"function"==typeof e?e(i):u.each(e,function(e,t){i.bind(t,e)}),p.call(i),u.each(["container","browse_button","drop_element"],function(e){if(null===i.getOption(e))return t={code:u.INIT_ERROR,message:u.sprintf(u.translate("%s specified, but cannot be found."),e)},!1}),t?i.trigger("Error",t):S.browse_button||S.drop_element?void h.call(i,S,function(e){var t=i.getOption("init");"function"==typeof t?t(i):u.each(t,function(e,t){i.bind(t,e)}),e?(i.runtime=s.getInfo(d()).type,i.trigger("Init",{runtime:i.runtime}),i.trigger("PostInit")):i.trigger("Error",{code:u.INIT_ERROR,message:u.translate("Init error.")})}):i.trigger("Error",{code:u.INIT_ERROR,message:u.translate("You must specify either browse_button or drop_element.")})},setOption:function(e,t){g.call(this,e,t,!this.runtime)},getOption:function(e){return e?S[e]:S},refresh:function(){N.length&&u.each(N,function(e){e.trigger("Refresh")}),this.trigger("Refresh")},start:function(){this.state!=u.STARTED&&(this.state=u.STARTED,this.trigger("StateChanged"),a.call(this))},stop:function(){this.state!=u.STOPPED&&(this.state=u.STOPPED,this.trigger("StateChanged"),this.trigger("CancelUpload"))},disableBrowse:function(){L=arguments[0]===i||arguments[0],N.length&&u.each(N,function(e){e.disable(L)}),this.trigger("DisableBrowse",L)},getFile:function(e){var t;for(t=D.length-1;t>=0;t--)if(D[t].id===e)return D[t]},addFile:function(e,i){function n(e,t){var i=[];u.each(l.settings.filters,function(t,n){o[n]&&i.push(function(i){o[n].call(l,t,e,function(e){i(!e)})})}),u.inSeries(i,t)}function a(e){var o=u.typeOf(e);if(e instanceof t.file.File){if(!e.ruid&&!e.isDetached()){if(!s)return!1;e.ruid=s,e.connectRuntime(s)}a(new u.File(e))}else e instanceof t.file.Blob?(a(e.getSource()),e.destroy()):e instanceof u.File?(i&&(e.name=i),c.push(function(t){n(e,function(i){i||(D.push(e),f.push(e),l.trigger("FileFiltered",e)),r(t,1)})})):-1!==u.inArray(o,["file","blob"])?a(new t.file.File(null,e)):"node"===o&&"filelist"===u.typeOf(e.files)?u.each(e.files,a):"array"===o&&(i=null,u.each(e,a))}var s,l=this,c=[],f=[];s=d(),a(e),c.length&&u.inSeries(c,function(){f.length&&l.trigger("FilesAdded",f)})},removeFile:function(e){for(var t="string"==typeof e?e:e.id,i=D.length-1;i>=0;i--)if(D[i].id===t)return this.splice(i,1)[0]},splice:function(e,t){var n=D.splice(e===i?0:e,t===i?D.length:t),r=!1;return this.state==u.STARTED&&(u.each(n,function(e){if(e.status===u.UPLOADING)return r=!0,!1}),r&&this.stop()),this.trigger("FilesRemoved",n),u.each(n,function(e){e.destroy()}),r&&this.start(),n},dispatchEvent:function(e){var t,i;if(e=e.toLowerCase(),t=this.hasEventListener(e)){t.sort(function(e,t){return t.priority-e.priority}),i=[].slice.call(arguments),i.shift(),i.unshift(this);for(var n=0;n<t.length;n++)if(!1===t[n].fn.apply(t[n].scope,i))return!1}return!0},bind:function(e,t,i,n){u.Uploader.prototype.bind.call(this,e,t,n,i)},destroy:function(){this.trigger("Destroy"),S=A=null,this.unbindAll()}})},u.Uploader.prototype=t.core.EventTarget.instance,u.File=function(){function e(e){u.extend(this,{id:u.guid(),name:e.name||e.fileName,type:e.type||"",size:e.size||e.fileSize,origSize:e.size||e.fileSize,loaded:0,percent:0,status:u.QUEUED,lastModifiedDate:e.lastModifiedDate||(new Date).toLocaleString(),completeTimestamp:0,getNative:function(){var e=this.getSource().getSource();return-1!==u.inArray(u.typeOf(e),["blob","file"])?e:null},getSource:function(){return t[this.id]?t[this.id]:null},destroy:function(){var e=this.getSource();e&&(e.destroy(),delete t[this.id])}}),t[this.id]=e}var t={};return e}(),u.QueueProgress=function(){var e=this;e.size=0,e.loaded=0,e.uploaded=0,e.failed=0,e.queued=0,e.percent=0,e.bytesPerSec=0,e.reset=function(){e.size=e.loaded=e.uploaded=e.failed=e.queued=e.percent=e.bytesPerSec=0}},e.plupload=u}(this,e)})},function(module,exports,__webpack_require__){var __WEBPACK_AMD_DEFINE_ARRAY__,__WEBPACK_AMD_DEFINE_RESULT__;!function(global){function createCookie(e,t,i){var n=new Date;n.setTime(n.getTime()+24*i*60*60*1e3);var r="; expires="+n.toGMTString();document.cookie=e+"="+t+r+"; path=/"}function readCookie(e){for(var t=e+"=",i=document.cookie.split(";"),n=0,r=i.length;n<r;n++){for(var o=i[n];" "===o.charAt(0);)o=o.substring(1,o.length);if(0===o.indexOf(t))return o.substring(t.length,o.length)}return null}function QiniuJsSDK(){function log(e,t){for(var i="[qiniu-js-sdk]["+e+"]",n=i,r=0;r<t.length;r++)"string"==typeof t[r]?n+=" "+t[r]:n+=" "+that.stringifyJSON(t[r]);that.detectIEVersion()?console.log(n):(t.unshift(i),console.log.apply(console,t)),document.getElementById("qiniu-js-sdk-log")&&(document.getElementById("qiniu-js-sdk-log").innerHTML+="<p>"+n+"</p>")}function makeLogFunc(e){var t=e.toLowerCase();logger[t]=function(){if(window.console&&window.console.log&&logger.level>=logger[e]){var i=Array.prototype.slice.call(arguments);log(t,i)}}}function StatisticsLogger(){function e(){for(var e=[],i=0;i<n.length;i++)n[i].status!==r.finished&&e.push(n[i]),n[i].status===r.waiting&&t(n[i]);n=e}function t(e){e.status=r.processing;var t=that.createAjax();t.open("POST",i,!0),t.setRequestHeader("Content-type","application/x-www-form-urlencoded"),t.setRequestHeader("Authorization","UpToken "+that.token),t.onreadystatechange=function(){4===t.readyState&&(200===t.status?(logger.debug("[STATISTICS] successfully report log to server"),e.status=r.finished):(logger.debug("[STATISTICS] report log to server failed"),e.status=r.waiting))},t.send(e.log)}var i="https://uplog.qbox.me/log/3",n=[],r={waiting:0,processing:1,finished:2};this.log=function(e,t,i,o,a,s,u,l,c,d){var f=Array.prototype.join.call(arguments,",");n.push({log:f,status:r.waiting}),logger.debug("[STATISTICS] send log to statistics server",f)},setInterval(e,1e3)}var moxie=__webpack_require__(0);window.moxie=moxie;var plupload=__webpack_require__(1);window.plupload=plupload;var that=this;this.detectIEVersion=function(){for(var e=4,t=document.createElement("div"),i=t.getElementsByTagName("i");t.innerHTML="\x3c!--[if gt IE "+e+"]><i></i><![endif]--\x3e",i[0];)e++;return e>4&&e};var logger={MUTE:0,FATA:1,ERROR:2,WARN:3,INFO:4,DEBUG:5,TRACE:6,level:0};for(var property in logger)logger.hasOwnProperty(property)&&"number"==typeof logger[property]&&!logger.hasOwnProperty(property.toLowerCase())&&makeLogFunc(property);var qiniuUploadUrl;qiniuUploadUrl="https:"===window.location.protocol?"https://upload.qiniup.com":"http://upload.qiniup.com";var qiniuUploadUrls=["http://upload.qiniup.com","http://up.qiniup.com"],qiniuUpHosts={http:["http://upload.qiniup.com","http://up.qiniup.com"],https:["https://upload.qiniup.com"]},changeUrlTimes=0,statisticsLogger=new StatisticsLogger,ExtraErrors={ZeroSizeFile:-6,InvalidToken:-5,InvalidArgument:-4,InvalidFile:-3,Cancelled:-2,NetworkError:-1,UnknownError:0,TimedOut:-1001,UnknownHost:-1003,CannotConnectToHost:-1004,NetworkConnectionLost:-1005};this.resetUploadUrl=function(e){if(logger.debug("num: "+e),0==e){logger.debug("use main uphost");var t=qiniuUpHosts.main;qiniuUploadUrl="https:"===window.location.protocol?"https://"+t[0]:"http://"+t[0]}else{logger.debug("use backup uphost");var t=qiniuUpHosts.backup;qiniuUploadUrl=e%2==0?"https:"===window.location.protocol?"https://"+t[1]:"http://"+t[1]:"https:"===window.location.protocol?"https://"+t[0]:"http://"+t[0]}logger.debug("resetUploadUrl: "+qiniuUploadUrl)},this.isImage=function(e){return e=e.split(/[?#]/)[0],/\.(png|jpg|jpeg|gif|bmp)$/i.test(e)},this.getFileExtension=function(e){var t=e.split(".");return 1===t.length||""===t[0]&&2===t.length?"":t.pop().toLowerCase()},this.utf8_encode=function(e){if(null===e||void 0===e)return"";var t,i,n=e+"",r="",o=0;t=i=0,o=n.length;for(var a=0;a<o;a++){var s=n.charCodeAt(a),u=null;if(s<128)i++;else if(s>127&&s<2048)u=String.fromCharCode(s>>6|192,63&s|128);else if(63488&s^!0)u=String.fromCharCode(s>>12|224,s>>6&63|128,63&s|128);else{if(64512&s^!0)throw new RangeError("Unmatched trail surrogate at "+a);var l=n.charCodeAt(++a);if(64512&l^!0)throw new RangeError("Unmatched lead surrogate at "+(a-1));s=((1023&s)<<10)+(1023&l)+65536,u=String.fromCharCode(s>>18|240,s>>12&63|128,s>>6&63|128,63&s|128)}null!==u&&(i>t&&(r+=n.slice(t,i)),r+=u,t=i=a+1)}return i>t&&(r+=n.slice(t,o)),r},this.base64_decode=function(e){var t,i,n,r,o,a,s,u,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",c=0,d=0,f=[];if(!e)return e;e+="";do{r=l.indexOf(e.charAt(c++)),o=l.indexOf(e.charAt(c++)),a=l.indexOf(e.charAt(c++)),s=l.indexOf(e.charAt(c++)),u=r<<18|o<<12|a<<6|s,t=u>>16&255,i=u>>8&255,n=255&u,f[d++]=64===a?String.fromCharCode(t):64===s?String.fromCharCode(t,i):String.fromCharCode(t,i,n)}while(c<e.length);return f.join("")},this.base64_encode=function(e){var t,i,n,r,o,a,s,u,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",c=0,d=0,f="",p=[];if(!e)return e;e=this.utf8_encode(e+"");do{t=e.charCodeAt(c++),i=e.charCodeAt(c++),n=e.charCodeAt(c++),u=t<<16|i<<8|n,r=u>>18&63,o=u>>12&63,a=u>>6&63,s=63&u,p[d++]=l.charAt(r)+l.charAt(o)+l.charAt(a)+l.charAt(s)}while(c<e.length);switch(f=p.join(""),e.length%3){case 1:f=f.slice(0,-2)+"==";break;case 2:f=f.slice(0,-1)+"="}return f},this.URLSafeBase64Encode=function(e){return e=this.base64_encode(e),e.replace(/\//g,"_").replace(/\+/g,"-")},this.URLSafeBase64Decode=function(e){return e=e.replace(/_/g,"/").replace(/-/g,"+"),this.base64_decode(e)},this.createAjax=function(e){return window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP")},this.parseJSON=function(data){if(window.JSON&&window.JSON.parse)return window.JSON.parse(data);var rx_dangerous=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,text=String(data);return rx_dangerous.lastIndex=0,rx_dangerous.test(text)&&(text=text.replace(rx_dangerous,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),eval("("+text+")")},this.stringifyJSON=function(e){if(window.JSON&&window.JSON.stringify)return window.JSON.stringify(e);switch(typeof e){case"string":return'"'+e.replace(/(["\\])/g,"\\$1")+'"';case"array":return"["+e.map(that.stringifyJSON).join(",")+"]";case"object":if(e instanceof Array){for(var t=[],i=e.length,n=0;n<i;n++)t.push(that.stringifyJSON(e[n]));return"["+t.join(",")+"]"}if(null===e)return"null";var r=[];for(var o in e)e.hasOwnProperty(o)&&r.push(that.stringifyJSON(o)+":"+that.stringifyJSON(e[o]));return"{"+r.join(",")+"}";case"number":case!1:case"boolean":return e}},this.trim=function(e){return null===e?"":e.replace(/^\s+|\s+$/g,"")},this.uploader=function(e){var t=function(e){var t=e.split(":"),i=t[0],n=that.parseJSON(that.URLSafeBase64Decode(t[2]));return n.ak=i,n.scope.indexOf(":")>=0?(n.bucket=n.scope.split(":")[0],n.key=n.scope.split(":")[1]):n.bucket=n.scope,n},i=function(i){var n=t(i),r=window.location.protocol+"//api.qiniu.com/v2/query?ak="+n.ak+"&bucket="+n.bucket;logger.debug("putPolicy: ",n),logger.debug("get uphosts from: ",r);var o,a=that.detectIEVersion();a&&a<=9?(o=new moxie.xhr.XMLHttpRequest,moxie.core.utils.Env.swf_url=e.flash_swf_url):o=that.createAjax(),o.open("GET",r,!1);var s=function(){if(logger.debug("ajax.readyState: ",o.readyState),4===o.readyState)if(logger.debug("ajax.status: ",o.status),o.status<400){var e=that.parseJSON(o.responseText);qiniuUpHosts.main=e.up.acc.main,qiniuUpHosts.backup=e.up.acc.backup,logger.debug("get new uphosts: ",qiniuUpHosts),that.resetUploadUrl(0)}else logger.error("get uphosts error: ",o.responseText)};a&&a<=9?o.bind("readystatechange",s):o.onreadystatechange=s,o.send()},n=function(t){return!that.token||e.uptoken_url&&that.tokenInfo.isExpired()?r(t):that.token},r=function(t){if(e.uptoken)that.token=e.uptoken;else if(e.uptoken_url){logger.debug("get uptoken from: ",that.uptoken_url);var n=that.createAjax();if(n.open("GET",that.uptoken_url,!1),n.send(),200===n.status){var r=that.parseJSON(n.responseText);that.token=r.uptoken;var o=that.token.split(":"),a=that.parseJSON(that.URLSafeBase64Decode(o[2]));that.tokenMap||(that.tokenMap={});var s=function(e){return Math.ceil(e.getTime()/1e3)},u=s(new Date(n.getResponseHeader("date"))),l=s(new Date);that.tokenInfo={serverDelay:l-u,deadline:a.deadline,isExpired:function(){return this.deadline-s(new Date)+this.serverDelay<600}},logger.debug("get new uptoken: ",that.token),logger.debug("get token info: ",that.tokenInfo)}else logger.error("get uptoken error: ",n.responseText)}else e.uptoken_func?(logger.debug("get uptoken from uptoken_func"),that.token=e.uptoken_func(t),logger.debug("get new uptoken: ",that.token)):logger.error("one of [uptoken, uptoken_url, uptoken_func] settings in options is required!");return that.token&&i(that.token),that.token},o=function(t,i,n){var r="",o=!1;if(!e.save_key)if(o=t.getOption&&t.getOption("unique_names"),o=o||t.settings&&t.settings.unique_names){var a=that.getFileExtension(i.name);r=a?i.id+"."+a:i.id}else r="function"==typeof n?n(t,i):i.name;return r},a=function(e){if(e&&e.match){var t=e.match(/^https?:\/\/([^:^\/]*)/);return t?t[1]:""}return""},s=function(e){if(e&&e.match){var t=e.match(/(^https?)/);if(!t)return"";var i=t[1];return t=e.match(/^https?:\/\/([^:^\/]*):(\d*)/),t?t[2]:"http"===i?"80":"443"}return""};if(e.log_level&&(logger.level=e.log_level),!e.domain)throw"domain setting in options is required!";if(!e.browse_button)throw"browse_button setting in options is required!";if(!e.uptoken&&!e.uptoken_url&&!e.uptoken_func)throw"one of [uptoken, uptoken_url, uptoken_func] settings in options is required!";logger.debug("init uploader start"),logger.debug("environment: ",moxie.core.utils.Env),logger.debug("userAgent: ",navigator.userAgent);var u={},l=e.init&&e.init.Error,c=e.init&&e.init.FileUploaded;e.init.Error=function(){},e.init.FileUploaded=function(){},that.uptoken_url=e.uptoken_url,that.token="",that.key_handler="function"==typeof e.init.Key?e.init.Key:"",this.domain=e.domain;var d="",f={isResumeUpload:!1,resumeFilesize:0,startTime:"",currentTime:""};!function(){var t,i,n=that.detectIEVersion(),r="Safari"===moxie.core.utils.Env.browser&&moxie.core.utils.Env.version<=5&&"Windows"===moxie.core.utils.Env.os&&"7"===moxie.core.utils.Env.osVersion||"Safari"===moxie.core.utils.Env.browser&&"iOS"===moxie.core.utils.Env.os&&"7"===moxie.core.utils.Env.osVersion;n&&n<9&&e.chunk_size&&e.runtimes.indexOf("flash")>=0?e.chunk_size=0:r?e.chunk_size=0:(t=20,i=4<<t,plupload.parseSize(e.chunk_size)>i&&(e.chunk_size=i))}(),logger.debug("invoke reset_chunk_size()"),logger.debug("op.chunk_size: ",e.chunk_size);var p={url:qiniuUploadUrl,multipart_params:{token:""}},h=that.detectIEVersion();h&&h<=9&&(p.multipart_params.accept="text/plain; charset=utf-8",logger.debug("add accept text/plain in multipart params")),plupload.extend(u,e,p),logger.debug("option: ",u);var m=new plupload.Uploader(u);logger.debug("new plupload.Uploader(option)"),m.bind("Init",function(t,i){logger.debug("Init event activated"),e.get_new_uptoken||r(null)}),logger.debug("bind Init event"),m.bind("FilesAdded",function(e,t){logger.debug("FilesAdded event activated");var i=e.getOption&&e.getOption("auto_start");i=i||e.settings&&e.settings.auto_start,logger.debug("auto_start: ",i),logger.debug("files: ",t);if(function(){return"ios"===moxie.core.utils.Env.OS.toLowerCase()}())for(var n=0;n<t.length;n++){var r=t[n],o=that.getFileExtension(r.name);r.name=r.id+"."+o}i&&setTimeout(function(){e.start(),logger.debug("invoke up.start()")},0),e.refresh()}),logger.debug("bind FilesAdded event"),m.bind("BeforeUpload",function(t,i){logger.debug("BeforeUpload event activated"),i._start_at=new Date,i.speed=i.speed||0,d="",e.get_new_uptoken&&r(i);var a=function(t,i,n){f.startTime=(new Date).getTime();var r;r=e.save_key?{token:that.token}:{key:o(t,i,n),token:that.token};var a=that.detectIEVersion();a&&a<=9&&(r.accept="text/plain; charset=utf-8",logger.debug("add accept text/plain in multipart params")),logger.debug("directUpload multipart_params_obj: ",r);var u=e.x_vars;if(void 0!==u&&"object"==typeof u)for(var l in u)u.hasOwnProperty(l)&&("function"==typeof u[l]?r["x:"+l]=u[l](t,i):"object"!=typeof u[l]&&(r["x:"+l]=u[l]));t.setOption({url:qiniuUploadUrl,multipart:!0,chunk_size:s()?e.max_file_size:void 0,multipart_params:r})},s=function(){var e=navigator.userAgent.toLowerCase();return!(!e.match(/MicroMessenger/i)&&"QQBrowser"!==moxie.core.utils.Env.browser&&!e.match(/V1_AND_SQ/i)||"android"!==moxie.core.utils.Env.OS.toLowerCase())},u=t.getOption&&t.getOption("chunk_size");if(u=u||t.settings&&t.settings.chunk_size,logger.debug("uploader.runtime: ",m.runtime),logger.debug("chunk_size: ",u),"html5"!==m.runtime&&"flash"!==m.runtime||!u)logger.debug("directUpload because uploader.runtime !== 'html5' || uploader.runtime !== 'flash' || !chunk_size"),a(t,i,that.key_handler);else if(i.size<u||s())logger.debug("directUpload because file.size < chunk_size || is_android_weixin_or_qq()"),a(t,i,that.key_handler);else{var l=localStorage.getItem(i.name),c=u;if(l){l=that.parseJSON(l);var p=(new Date).getTime(),h=l.time||0;p-h<864e5&&100!==l.percent&&i.size===l.total?(i.percent=l.percent,i.loaded=l.offset,d=l.ctx,f.isResumeUpload=!0,f.resumeFilesize=l.offset,l.offset+c>i.size&&(c=i.size-l.offset)):localStorage.removeItem(i.name)}f.startTime=(new Date).getTime();var g={},v=that.detectIEVersion();v&&v<=9&&(g.accept="text/plain; charset=utf-8",logger.debug("add accept text/plain in multipart params")),t.setOption({url:qiniuUploadUrl+"/mkblk/"+c,multipart:!1,chunk_size:u,required_features:"chunks",headers:{Authorization:"UpToken "+n(i)},multipart_params:g})}}),logger.debug("bind BeforeUpload event"),m.bind("UploadProgress",function(e,t){logger.trace("UploadProgress event activated"),f.currentTime=(new Date).getTime();var i=f.currentTime-f.startTime,n=t.loaded||0;f.isResumeUpload&&(n=t.loaded-f.resumeFilesize),t.speed=(n/i*1e3).toFixed(0)||0}),logger.debug("bind UploadProgress event"),m.bind("ChunkUploaded",function(e,t,i){logger.debug("ChunkUploaded event activated"),logger.debug("ChunkUploaded file: ",t),logger.debug("ChunkUploaded info: ",i);var r=that.parseJSON(i.response);logger.debug("ChunkUploaded res: ",r),d=d?d+","+r.ctx:r.ctx;var o=i.total-i.offset,a=e.getOption&&e.getOption("chunk_size");a=a||e.settings&&e.settings.chunk_size,o<a&&(e.setOption({url:qiniuUploadUrl+"/mkblk/"+o}),logger.debug("up.setOption url: ",qiniuUploadUrl+"/mkblk/"+o)),e.setOption({headers:{Authorization:"UpToken "+n(t)}}),localStorage.setItem(t.name,that.stringifyJSON({ctx:d,percent:t.percent,total:i.total,offset:i.offset,time:(new Date).getTime()}))}),logger.debug("bind ChunkUploaded event");var g=e.max_retries,v=function(e){return g-- >0?(setTimeout(function(){that.resetUploadUrl(g),e.status=plupload.QUEUED,m.stop(),m.start()},0),!0):(g=qiniuUploadUrls.length,!1)};return m.bind("Error",function(t){return function(i,n){logger.error("Error event activated"),logger.error("err: ",n);var r=new Date,o="",u=n.file;if(u){switch(n.code){case plupload.FAILED:o="上传失败。请稍后再试。";break;case plupload.FILE_SIZE_ERROR:var l=i.getOption&&i.getOption("max_file_size");l=l||i.settings&&i.settings.max_file_size,o="浏览器最大可上传"+l+"。更大文件请使用命令行工具。";break;case plupload.FILE_EXTENSION_ERROR:o="文件验证失败。请稍后重试。";break;case plupload.HTTP_ERROR:if(""===n.response){if(o=n.message||"未知网络错误。",!v(u))return;break}var c=that.parseJSON(n.response),d=c.error;switch(n.status){case 400:o="请求报文格式错误。";break;case 401:o="客户端认证授权失败。请重试或提交反馈。";break;case 405:o="客户端请求错误。请重试或提交反馈。";break;case 579:o="资源上传成功，但回调失败。";break;case 599:if(o="网络连接异常。请重试或提交反馈。",!v(u))return;break;case 614:o="文件已存在。";try{c=that.parseJSON(c.error),d=c.error||"file exists"}catch(e){d=c.error||"file exists"}break;case 631:o="指定空间不存在。";break;case 701:o="上传数据块校验出错。请重试或提交反馈。";break;default:if(o="未知错误。",!v(u))return}o=o+"("+n.status+"："+d+")";break;case plupload.SECURITY_ERROR:o="安全配置错误。请联系网站管理员。";break;case plupload.GENERIC_ERROR:case plupload.IO_ERROR:o="上传失败。请稍后再试。";break;case plupload.INIT_ERROR:o="网站配置错误。请联系网站管理员。",m.destroy();break;default:if(o=n.message+n.details,!v(u))return}t&&t(i,n,o)}if(i.refresh(),!e.disable_statistics_report){var f=n&&n.responseHeaders&&n.responseHeaders.match?n.responseHeaders.match(/(X-Reqid\:\ )([\w\.\%-]*)/):[];console.log(n);var p=f[2].replace(/[\r\n]/g,""),h=plupload.HTTP_ERROR?n.status:n.code,g=u._start_at?u._start_at.getTime():r.getTime();statisticsLogger.log(0===h?ExtraErrors.NetworkError:h,p,a(i.settings.url),void 0,s(i.settings.url),(r.getTime()-g)/1e3,parseInt(g/1e3),n.file.size*(n.file.percent/100),"jssdk-"+i.runtime,u.size)}}}(l)),logger.debug("bind Error event"),m.bind("FileUploaded",function(t){return function(i,n,r){logger.debug("FileUploaded event activated"),logger.debug("FileUploaded file: ",n),logger.debug("FileUploaded info: ",r);var u=new Date,l=function(i,n,r){if(logger.debug("FileUploaded last step:",r),e.downtoken_url){var o=that.createAjax();o.open("POST",e.downtoken_url,!0),o.setRequestHeader("Content-type","application/x-www-form-urlencoded"),o.onreadystatechange=function(){if(4===o.readyState)if(200===o.status){var e;try{e=that.parseJSON(o.responseText)}catch(e){throw"invalid json format"}var a={};plupload.extend(a,that.parseJSON(r.response),e),r.response=that.stringifyJSON(a),t&&t(i,n,r)}else m.trigger("Error",{status:o.status,response:o.responseText,file:n,code:plupload.HTTP_ERROR})},o.send("key="+that.parseJSON(r.response).key+"&domain="+e.domain)}else t&&t(i,n,r)},c=that.parseJSON(r.response);if(d=d||c.ctx,logger.debug("ctx: ",d),d){var f="";logger.debug("save_key: ",e.save_key),e.save_key||(f=o(i,n,that.key_handler),f=f?"/key/"+that.URLSafeBase64Encode(f):"");var p="/fname/"+that.URLSafeBase64Encode(n.name);logger.debug("op.x_vars: ",e.x_vars);var h=e.x_vars,g="",v="";if(void 0!==h&&"object"==typeof h)for(var x in h)h.hasOwnProperty(x)&&("function"==typeof h[x]?g=that.URLSafeBase64Encode(h[x](i,n)):"object"!=typeof h[x]&&(g=that.URLSafeBase64Encode(h[x])),v+="/x:"+x+"/"+g);var _,y=qiniuUploadUrl+"/mkfile/"+n.size+f+p+v,w=that.detectIEVersion();w&&w<=9?(_=new moxie.xhr.XMLHttpRequest,moxie.core.utils.Env.swf_url=e.flash_swf_url):_=that.createAjax(),_.open("POST",y,!0),_.setRequestHeader("Content-Type","text/plain;charset=UTF-8"),console.log("uptoken:"+that.token),_.setRequestHeader("Authorization","UpToken "+that.token);var E=function(){if(logger.debug("ajax.readyState: ",_.readyState),4===_.readyState){localStorage.removeItem(n.name);var e;200===_.status?(e={status:_.status,response:_.responseText,responseHeaders:_.getAllResponseHeaders()},logger.debug("mkfile is success: ",e),l(i,n,e)):(e={status:_.status,response:_.responseText,file:n,code:-200,responseHeaders:_.getAllResponseHeaders()},logger.debug("mkfile is error: ",e),m.trigger("Error",e))}};w&&w<=9?_.bind("readystatechange",E):_.onreadystatechange=E,_.send(d),logger.debug("mkfile: ",y)}else l(i,n,r);if(!e.disable_statistics_report){console.log(r.responseHeaders);var b=r.responseHeaders.match(/(X-Reqid\:\ )([\w\.\%-]*)/i)[2].replace(/[\r\n]/g,""),R=n._start_at?n._start_at.getTime():u.getTime();statisticsLogger.log(r.status,b,a(i.settings.url),void 0,s(i.settings.url),(u.getTime()-R)/1e3,parseInt(R/1e3),n.size,"jssdk-"+i.runtime,n.size)}}}(c)),logger.debug("bind FileUploaded event"),m.bind("FilesRemoved",function(t,i){var n=new Date;if(!e.disable_statistics_report)for(var r=0;r<i.length;r++)statisticsLogger.log(ExtraErrors.Cancelled,void 0,a(t.settings.url),void 0,s(t.settings.url),(n.getTime()-i[r]._start_at.getTime())/1e3,i[r]._start_at.getTime()/1e3,i[r].size*i[r].percent/100,"jssdk-"+t.runtime,i[r].size)}),logger.debug("bind FilesRemoved event"),m.init(),logger.debug("invoke uploader.init()"),logger.debug("init uploader end"),m},this.getUrl=function(e){if(!e)return!1;e=encodeURI(e);var t=this.domain;return"/"!==t.slice(t.length-1)&&(t+="/"),t+e},this.imageView2=function(e,t){if(!/^\d$/.test(e.mode))return!1;var i=e.mode,n=e.w||"",r=e.h||"",o=e.q||"",a=e.format||"";if(!n&&!r)return!1;var s="imageView2/"+i;return s+=n?"/w/"+n:"",s+=r?"/h/"+r:"",s+=o?"/q/"+o:"",s+=a?"/format/"+a:"",t&&(s=this.getUrl(t)+"?"+s),s},this.imageMogr2=function(e,t){var i=e["auto-orient"]||"",n=e.thumbnail||"",r=e.strip||"",o=e.gravity||"",a=e.crop||"",s=e.quality||"",u=e.rotate||"",l=e.format||"",c=e.blur||"",d="imageMogr2";return d+=i?"/auto-orient":"",d+=n?"/thumbnail/"+n:"",d+=r?"/strip":"",d+=o?"/gravity/"+o:"",d+=s?"/quality/"+s:"",d+=a?"/crop/"+a:"",d+=u?"/rotate/"+u:"",d+=l?"/format/"+l:"",d+=c?"/blur/"+c:"",t&&(d=this.getUrl(t)+"?"+d),d},this.watermark=function(e,t){var i=e.mode;if(!i)return!1;var n="watermark/"+i;if(1===i){var r=e.image||"";if(!r)return!1;n+=r?"/image/"+this.URLSafeBase64Encode(r):""}else{if(2!==i)return!1;var o=e.text?e.text:"",a=e.font?e.font:"",s=e.fontsize?e.fontsize:"",u=e.fill?e.fill:"";if(!o)return!1;n+=o?"/text/"+this.URLSafeBase64Encode(o):"",n+=a?"/font/"+this.URLSafeBase64Encode(a):"",n+=s?"/fontsize/"+s:"",n+=u?"/fill/"+this.URLSafeBase64Encode(u):""}var l=e.dissolve||"",c=e.gravity||"",d=e.dx||"",f=e.dy||"";return n+=l?"/dissolve/"+l:"",n+=c?"/gravity/"+c:"",n+=d?"/dx/"+d:"",n+=f?"/dy/"+f:"",t&&(n=this.getUrl(t)+"?"+n),n},this.imageInfo=function(e){if(!e)return!1;var t,i=this.getUrl(e)+"?imageInfo",n=this.createAjax(),r=this;return n.open("GET",i,!1),n.onreadystatechange=function(){4===n.readyState&&200===n.status&&(t=r.parseJSON(n.responseText))},n.send(),t},this.exif=function(e){if(!e)return!1;var t,i=this.getUrl(e)+"?exif",n=this.createAjax(),r=this;return n.open("GET",i,!1),n.onreadystatechange=function(){4===n.readyState&&200===n.status&&(t=r.parseJSON(n.responseText))},n.send(),t},this.get=function(e,t){return!(!t||!e)&&("exif"===e?this.exif(t):"imageInfo"===e&&this.imageInfo(t))},this.pipeline=function(e,t){var i,n,r="[object Array]"===Object.prototype.toString.call(e),o="";if(r){for(var a=0,s=e.length;a<s;a++){if(i=e[a],!i.fop)return!1;switch(i.fop){case"watermark":o+=this.watermark(i)+"|";break;case"imageView2":o+=this.imageView2(i)+"|";break;case"imageMogr2":o+=this.imageMogr2(i)+"|";break;default:n=!0}if(n)return!1}if(t){o=this.getUrl(t)+"?"+o;var u=o.length;"|"===o.slice(u-1)&&(o=o.slice(0,u-1))}return o}return!1}}window.localStorage||(window.localStorage={setItem:function(e,t){createCookie(e,t,30)},getItem:function(e){return readCookie(e)},removeItem:function(e){createCookie(e,"",-1)}});var Qiniu=new QiniuJsSDK;global.Qiniu=Qiniu,global.QiniuJsSDK=QiniuJsSDK,void 0!==module&&module.exports?module.exports=QiniuJsSDK:(__WEBPACK_AMD_DEFINE_ARRAY__=[__webpack_require__(0),__webpack_require__(1)],void 0!==(__WEBPACK_AMD_DEFINE_RESULT__=function(){return QiniuJsSDK}.apply(exports,__WEBPACK_AMD_DEFINE_ARRAY__))&&(module.exports=__WEBPACK_AMD_DEFINE_RESULT__))}(window)}]);