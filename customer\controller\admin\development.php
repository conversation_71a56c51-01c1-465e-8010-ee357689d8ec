<?php
class ControllerAdminDevelopment extends Controller {
	private $error = array();

	public function getDevelopments() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_provider'])) {
			$filter_provider = $this->request->get['filter_provider'];
		} else {
			$filter_provider = '';
		}

		if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . $this->request->get['filter_provider'];
		}

		if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$data['submit'] = $this->url->link('admin/development/addNodeSubmit', 'token=' . $this->session->data['token'] . $url);

        $data['order_person_users'] = array(
            '19' => '孔美豹',
            '82' => '张华',
        );

        $data['mould_users'] = array(
            '11' => '洪斌',
            '19' => '孔美豹',
            '82' => '张华',
        );

		$data['states'] = array(
			'1' => '正常',
			'2' => '超时',
		);

		$data['flows'] = array();

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_provider'	=> $filter_provider,
			'filter_state'		=> $filter_state,
			'filter_date_start'	=> $filter_date_start,
			'filter_date_end'	=> $filter_date_end,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);

		$this->load->model('admin/development');
		$results = $this->model_admin_development->getDevelopment($filter_data);

		foreach ($results as $result) {
			$data['flows'][$result['workflow_id']] = array(
				'workid'	=> $result['workflow_id'],
				'workname'	=> $result['workflow_name'],
				'workstate'	=> $data['states'][$result['state']] ?? '',
				'workdate'	=> date('Y-m-d', $result['start_time']),
                'worknode'  => $result['worknode'],
			);
		}

		$fields = $this->model_admin_development->getWorkFields(array_keys($data['flows']));
		$data['submit_list'] = $this->model_admin_development->getWorkSubmit(array_keys($data['flows']));

		foreach ($fields as $workflow_id => $field) {
			$data['flows'][$workflow_id]['workproduct'] = $field['product_name'] ?? '';
			$data['flows'][$workflow_id]['workproductimg'] = $field['product_img'] ?? '';
		}

		$total = $this->model_admin_development->getTotalProduces($filter_data);

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . $this->request->get['filter_provider'];
		}

		if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/development/getDevelopments', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

		$data['filter_name'] = $filter_name;
		$data['filter_provider'] = $filter_provider;
		$data['filter_state'] = $filter_state;
		$data['filter_date_start'] = $filter_date_start;
		$data['filter_date_end'] = $filter_date_end;

		$data['nofilter'] = $this->url->link('admin/development/getDevelopments', 'token=' . $this->session->data['token']);

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('development/produce_list.tpl', $data));
	}

	public function addNodeSubmit() {
		$this->load->model('admin/development');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && isset($this->request->post['sumbit'])) {
        	if ($this->user->union_id == '106') {
        		$actioin_user = '173';
        	} elseif ($this->user->union_id == '107') {
        		$actioin_user = '175';
        	} elseif ($this->user->union_id == '139') {
        		$actioin_user = '82';
        	} else {
        		$actioin_user = '1';
        	}

            $node_actions = array(
                '6' => [
                    'node_id' => 6, 'action_id' => 8, 'users' => [12 => $actioin_user]
                ],
                '7' => [
                    'node_id' => 7, 'action_id' => 9, 'users' => [12 => $actioin_user]
                ],
                '8' => [
                    'node_id' => 8, 'action_id' => 10, 'users' => [9 => $actioin_user]
                ],
                '9' => [
                    'node_id' => 9, 'action_id' => 11, 'users' => [10 => $actioin_user]
                ],
                '10' => [
                    'node_id' => 10, 'action_id' => 12, 'users' => [11 => $actioin_user,13 => $actioin_user]
                ],
                '11' => [
                    'node_id' => 11, 'action_id' => 13, 'users' => [0 => $actioin_user,'32'=>16]
                ],
                '12' => [
                    'node_id' => 12, 'action_id' => 15
                ],
                '13' => [
                    0 => ['node_id' => 13, 'action_id' => 23, 'users' => ['0' => $actioin_user]],
                    1 => ['node_id' => 13, 'action_id' => 13],
                ],
            );

            $add_submit_arr = [];
			foreach ($this->request->post['sumbit'] as $workflow_id => $fields) {

				if (!empty($fields['6']) && $fields['6'] == 1) {
                    $add_submit_arr[] = [
                        'workflow_id' => $workflow_id,
                        'node_id' => $node_actions[6]['node_id'],
                        'action_id' => $node_actions[6]['action_id'],
                        'user_id' => $actioin_user,
                        'fields' => json_encode([]),
                        'users' => json_encode($node_actions[6]['users']),
                    ];
                }

                if (!empty($fields['7']) && $fields['7'] == 1) {
                    $add_submit_arr[] = [
                        'workflow_id' => $workflow_id,
                        'node_id' => $node_actions[7]['node_id'],
                        'action_id' => $node_actions[7]['action_id'],
                        'user_id' => $actioin_user,
                        'fields' => json_encode([]),
                        'users' => json_encode($node_actions[7]['users']),
                    ];
                }

                if (!empty($fields['12'])) {
                    $add_submit_arr[] = [
                        'workflow_id' => $workflow_id,
                        'node_id' => $node_actions[12]['node_id'],
                        'action_id' => $node_actions[12]['action_id'],
                        'user_id' => $actioin_user,
                        'fields' => json_encode([]),
                        'users' => json_encode([8 => $fields['12']]),
                    ];


                    if (!empty($fields['8']) && $fields['8'] == 1) {
                        $add_submit_arr[] = [
                            'workflow_id' => $workflow_id,
                            'node_id' => $node_actions[8]['node_id'],
                            'action_id' => $node_actions[8]['action_id'],
                            'user_id' => $actioin_user,
                            'fields' => json_encode([]),
                            'users' => json_encode($node_actions[8]['users']),
                        ];
                    }

                    if (!empty($fields['9']) && $fields['9'] == 1) {
                        $add_submit_arr[] = [
                            'workflow_id' => $workflow_id,
                            'node_id' => $node_actions[9]['node_id'],
                            'action_id' => $node_actions[9]['action_id'],
                            'user_id' => $actioin_user,
                            'fields' => json_encode([]),
                            'users' => json_encode($node_actions[9]['users']),
                        ];
                    }

                    if (!empty($fields['10']) && $fields['10'] == 1) {
                        $add_submit_arr[] = [
                            'workflow_id' => $workflow_id,
                            'node_id' => $node_actions[10]['node_id'],
                            'action_id' => $node_actions[10]['action_id'],
                            'user_id' => $actioin_user,
                            'fields' => json_encode([]),
                            'users' => json_encode($node_actions[10]['users']),
                        ];
                    }

                    if (!empty($fields['11'])) {
                        $add_submit_arr[] = [
                            'workflow_id' => $workflow_id,
                            'node_id' => $node_actions[11]['node_id'],
                            'action_id' => $node_actions[11]['action_id'],
                            'user_id' => $actioin_user,
                            'fields' => json_encode(['product_name'=>$fields['11']],320),
                            'users' => json_encode($node_actions[11]['users']),
                        ];
                    }

                    if (!empty($fields['13'])) {
                        if (!empty($fields['13']['product_name']) && !empty($fields['13']['order_quantity']) && !empty($fields['13']['order_no']) && !empty($fields['13']['estimate_date'])  && !empty($fields['13']['order_way'])) {
                            if ($fields['13']['order_way'] == 1) {
                                if (!empty($fields['13']['order_person'])) {
                                    $add_submit_arr[] = [
                                        'workflow_id' => $workflow_id,
                                        'node_id' => $node_actions[13][1]['node_id'],
                                        'action_id' => $node_actions[13][1]['action_id'],
                                        'user_id' => $actioin_user,
                                        'fields' => json_encode(['product_name'=>$fields['13']['product_name'],'order_quantity'=>$fields['13']['order_quantity'],'order_no'=>$fields['13']['order_no'],'estimate_date'=>$fields['13']['estimate_date']],320),
                                        'users' => json_encode([0 => $actioin_user,15=>$fields['13']['order_person']]),
                                    ];
                                }
                            } else if ($fields['13']['order_way'] == 2) {
                                $add_submit_arr[] = [
                                    'workflow_id' => $workflow_id,
                                    'node_id' => $node_actions[13][0]['node_id'],
                                    'action_id' => $node_actions[13][0]['action_id'],
                                    'user_id' => $actioin_user,
                                    'fields' => json_encode(['product_name'=>$fields['13']['product_name'],'order_quantity'=>$fields['13']['order_quantity'],'order_no'=>$fields['13']['order_no'],'estimate_date'=>$fields['13']['estimate_date']],320),
                                    'users' => json_encode($node_actions[13][0]['users']),
                                ];
                            }
                        }
                    }
                }
			}

            if (!empty($add_submit_arr)) {
                foreach ($add_submit_arr as $v) {
                    $this->db->query("UPDATE cr_worknode_submit SET status = '-1' WHERE workflow_id = '" . (int)$v['workflow_id'] . "' AND node_id = '" . (int)$v['node_id'] . "' AND status = '0'");

                    $this->db->query("INSERT INTO cr_worknode_submit SET workflow_id = '" . (int)$v['workflow_id'] . "', node_id = '" . (int)$v['node_id'] . "', action_id = '" . (int)$v['action_id'] . "', user_id = '" . (int)$actioin_user . "', fields = '" . $this->db->escape($v['fields']) . "', users = '" . $this->db->escape($v['users']) . "', status = '0', date_added = NOW()");
                }
            }

            $url = '';

            if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_provider'])) {
				$url .= '&filter_provider=' . $this->request->get['filter_provider'];
			}

			if (isset($this->request->get['filter_state'])) {
	            $url .= '&filter_state=' . $this->request->get['filter_state'];
	        }

	        if (isset($this->request->get['filter_date_start'])) {
	            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
	        }

	        if (isset($this->request->get['filter_date_end'])) {
	            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
	        }

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

            $this->response->redirect($this->url->link('admin/development/getDevelopments', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getDevelopments();
	}
}
