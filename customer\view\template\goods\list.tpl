<!DOCTYPE html>

<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
  <title>如果家居货品目录</title>
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
  <base href="http://public.youranjian.cn/dz/" />
  <link rel="stylesheet" href="static/bower_components/bootstrap/dist/css/bootstrap.min.css">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="static/bower_components/font-awesome/css/font-awesome.min.css">
  <!-- daterange picker -->
  <link rel="stylesheet" href="static/bower_components/bootstrap-daterangepicker/daterangepicker.css">
  <!-- Pace style -->
  <link rel="stylesheet" href="static/bower_components/pace/pace.min.css">
  <!-- iCheck for checkboxes and radio inputs -->
  <link rel="stylesheet" href="static/plugins/iCheck/flat/_all.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="static/dist/css/AdminLTE.min.css">
  <link rel="stylesheet" href="static/dist/css/skins/_all-skins.min.css">
</head>
<style type="text/css">
.flex { display: flex; }
.flex.jc__sb { justify-content: space-between; }
.flex.jc__c { justify-content: center; }
.flex.ai__c { align-items: center; }
.flex.center { justify-content: center; align-items: center; }
</style>
<body class="fixed">
  <div class="content container-fluid" style="background-color: #ecf0f5;">
    <!-- Main content -->
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字keyword：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>分类category：</label>
                  <select class="form-control" name="filter_class">
                    <option value="*">全部分类ALL</option>
                    <?php foreach($classes as $class) { ?>
                    <?php if ($class['class_id'] == $filter_class) { ?>
                    <option value="<?php echo $class['class_id']; ?>" selected="selected"><?php echo $class['class_name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $class['class_id']; ?>"><?php echo $class['class_name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>创建时间create date：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间start - 截止时间end" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间start - 截止时间end" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选Filter</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">货品列表Product List</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr style="position: sticky; top: 0;">
              <th>图片picture</th>
              <th>名称name/编码code</th>
              <th>分类category</th>
              <th>尺寸size</th>
              <th>装箱CTN</th>
              <th>净重N.W.</th>
              <th>毛重G.W.</th>
              <th>价格price</th>
              <!-- <th>材质</th> -->
            </tr>
            <?php if (!empty($goods)) { ?>
            <?php foreach ($goods as $good) { ?>
            <tr>
              <td><img width="100" src="<?php echo $good['img_url']; ?>" data-toggle="popover" data-content="<img src='<?php echo $good['img_url']; ?>' width='480'>" class="img-thumbnail"></td>
              <td><?php echo $good['name_en']; ?><br><?php echo $good['spec_name']; ?><br><?php echo $good['spec_no']; ?></td>
              <td><?php echo $good['class_en']; ?><br><?php echo $good['class_name']; ?></td>
              <td>
                <?php if(!empty($good['bat_size'])){echo '外箱/Outer Box：'.$good['bat_size'].'<br>';} ?>
                <?php if(!empty($good['box_size'])){echo '内盒/Inner Box：'.$good['box_size'].'<br>';} ?>
                <?php if(!empty($good['spec_size'])){echo '产品/Product：'.$good['spec_size'].'<br>';} ?>
              </td>
              <td><?php echo $good['bat_quan']; ?></td>
              <td><?php echo !empty($good['weight']) ? number_format($good['weight'], 2) : ''; ?></td>
              <td><?php echo !empty($good['weight']) ? number_format($good['weight'] + 2, 2) : ''; ?></td>
              <td><?php foreach ($good['price'] as $price) { ?>
                <?php echo !empty($price) ? $currency_symbol . number_format($price, 2) . '<br>' : ''; ?>
              <?php } ?></td>
              <!-- <td></td> -->
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="9" align="center"> 暂无货品数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    <!-- /.content -->
  </div>

<!-- REQUIRED JS SCRIPTS -->

<!-- jQuery 3 -->
<script src="static/bower_components/jquery/dist/jquery.min.js"></script>
<!-- Bootstrap 3.3.7 -->
<script src="static/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
<!-- PACE -->
<script src="static/bower_components/pace/pace.min.js"></script>
<!-- date-range-picker -->
<script src="static/bower_components/moment/min/moment.min.js"></script>
<script src="static/bower_components/moment/locale/zh-cn.js"></script>
<script src="static/bower_components/bootstrap-daterangepicker/daterangepicker.js"></script>
<script type="text/javascript">
  (function () {
    $('[data-toggle="popover"]').popover({html:true, placement:'auto right', trigger: 'hover', template: '<div class="popover" style="max-width: 640px;"><div class="popover-content"></div></div>'});
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_class = $('select[name=\'filter_class\']').val();

      if (filter_class != '*') {
        url += '&filter_class=' + encodeURIComponent(filter_class);
      }
  
      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });
  })()
</script>
</body>
</html>