<body class="hold-transition skin-green fixed">
<div class="wrapper">

  <!-- Main Header -->
  <header class="main-header">

    <!-- Logo -->
    <a href="javascript:;" class="logo" style="background-color: #fff;">
      <!-- mini logo for sidebar mini 50x50 pixels -->
      <span class="logo-mini"><b></b></span>
      <!-- logo for regular state and mobile devices -->
      <img height="40px" src="<?php echo HTTP_SERVER.'logo.png'; ?>" alt="logo" class="logo-img">
    </a>

    <!-- Header Navbar -->
    <nav class="navbar navbar-static-top" style="background-color: #ae0e16;" role="navigation">
      <!-- Sidebar toggle button-->
      <a href="#" class="sidebar-toggle" style="background-color: #ae0e16;" data-toggle="push-menu" role="button">
        <span class="sr-only">切换菜单</span>
      </a>
      <!-- Navbar Right Menu -->
      <div class="navbar-custom-menu">
        <ul class="nav navbar-nav">
          <li><a href="https://team.roogo.cn/#/login?login_token=<?php echo $tasksUrlToken; ?>&v=<?php echo $tasksUrlTime; ?>" target="_blank"><i class="glyphicon glyphicon-tasks"></i> <span>待办事项</span></a></li>
          <li><a href="javascript:void(0);" data-roogo="file" data-filter=""><i class="glyphicon glyphicon-cloud"></i> <span>共享云盘</span></a></li>
          <li><a href="http://design.roogo.cn/" target="_blank"><i class="glyphicon glyphicon-picture"></i> <span>产品图片</span></a></li>
          <li><a href="http://forum.roogo.cn/" target="_blank"><i class="glyphicon glyphicon-blackboard"></i> <span>如果论坛</span></a></li>
          <li><a href="http://z.roogo.cn/" target="_blank"><i class="glyphicon glyphicon-modal-window"></i> <span>众筹系统</span></a></li>
          <li><a href="<?php echo $logout; ?>"><i class="glyphicon glyphicon-log-out"></i> <span class="hidden-xs">退出登录</span></a></li>
        </ul>
      </div>
    </nav>
  </header>
  <!-- Left side column. contains the logo and sidebar -->
  <aside class="main-sidebar">

    <!-- sidebar: style can be found in sidebar.less -->
    <section class="sidebar">
      <!-- Sidebar user panel (optional) -->
      <div class="user-panel">
        <div class="pull-left image">
          <div class="user-image" style="background-image: url('static/avatar.png')"></div>
        </div>
        <div class="pull-left info">
          <p><?php echo $customer_name; ?></p>
          <!-- Status -->
          <a href="#"><i class="fa fa-circle text-success"></i> Online</a>
        </div>
      </div>
      <!-- Sidebar Menu -->
      <ul class="sidebar-menu" data-widget="tree">
        <li class="header">菜单</li>
        <!-- Optionally, you can add icons to the links -->
        <?php if (in_array('admin/basic', $access)) { ?>
        <li class="treeview">
          <a href="#">
            <i class="glyphicon glyphicon-th-large"></i> <span>基础信息</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?php echo $goods; ?>" target="_blank"><i class="glyphicon glyphicon-tags"></i> <span>货品目录</span></a></li>
            <!-- <li><a href="<?php echo $basicGoods; ?>" data-scope="admin/basic/getGoods"><i class="glyphicon glyphicon-tags"></i> <span>货品信息</span></a></li> -->
            <li><a href="<?php echo $basicAuths; ?>" data-scope="admin/basic/getAuths"><i class="glyphicon glyphicon-lock"></i> <span>货品权限</span></a></li>
            <li><a href="<?php echo $basicInfos; ?>" data-scope="admin/basic/getInfos"><i class="glyphicon glyphicon-pencil"></i> <span>图纸信息</span></a></li>
            <li><a href="<?php echo $basicFreight; ?>" data-scope="admin/basic/getFreights"><i class="glyphicon glyphicon-queen"></i> <span>运费试算</span></a></li>
          </ul>
        </li>
        <?php } ?>
        <?php if (in_array('admin/kpi', $access)) { ?>
        <!-- <li class="treeview">
          <a href="#">
            <i class="glyphicon glyphicon-pencil"></i> <span>绩效管理</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <?php if (in_array('admin/kpi', $modify)) { ?>
            <li><a href="<?php echo $KpiItem; ?>" data-scope="kpi/item"><i class="glyphicon glyphicon-book"></i> <span>指标管理</span></a></li>
            <li><a href="<?php echo $KpiUser; ?>" data-scope="kpi/user"><i class="glyphicon glyphicon-floppy-disk"></i> <span>考核管理</span></a></li>
            <?php } ?>
            <li><a href="<?php echo $KpiRate; ?>" data-scope="kpi/rate"><i class="glyphicon glyphicon-pencil"></i> <span>评分管理</span></a></li>
            <li><a href="<?php echo $KpiScore; ?>" data-scope="kpi/score"><i class="glyphicon glyphicon-align-center"></i> <span>我的绩效</span></a></li>
            <li><a href="<?php echo $myKpiItem; ?>" data-scope="kpi/myItem"><i class="glyphicon glyphicon-star"></i> <span>我的考核</span></a></li>
          </ul>
        </li> -->
        <?php } ?>
        <?php if (in_array('admin/goods', $access)) { ?>
        <li class="treeview">
          <a href="#">
            <i class="glyphicon glyphicon-th-list"></i> <span>商品信息</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?php echo $goodsLabes; ?>" data-scope="goods/labes"><i class="glyphicon glyphicon-tags"></i> <span>标签管理</span></a></li>
            <li><a href="<?php echo $goodsLabesStatistics; ?>" data-scope="goods/labesStatistics"><i class="glyphicon glyphicon-tag"></i> <span>标签统计</span></a></li>
            <li><a href="<?php echo $goodsArchives; ?>" data-scope="goods/getArchives"><i class="glyphicon glyphicon-floppy-disk"></i> <span>档案管理</span></a></li>
            <li><a href="<?php echo $goodsStock; ?>" data-scope="goods/getStocks"><i class="glyphicon glyphicon-tags"></i> <span>库存管理</span></a></li>
            <li><a href="<?php echo $goodInTransits; ?>" data-scope="goods/getInTransit"><i class="glyphicon glyphicon-globe"></i> <span>在途库存</span></a></li>
            <!--<li><a href="<?php echo $goodsPackage; ?>" data-scope="goods/getPackage"><i class="glyphicon glyphicon-gift"></i> <span>包材管理</span></a></li>
            <?php if (in_array('admin/flow', $access)) { ?>
            <li><a href="<?php echo $flowProduce; ?>" data-scope="flow/getProduce"><i class="glyphicon glyphicon-asterisk"></i> <span>生产流程</span></a></li>
            <?php } ?>
            <?php if (in_array('admin/development', $access)) { ?>
            <li><a href="<?php echo $researchAndDevelopment; ?>" data-scope="development/getDevelopments"><i class="glyphicon glyphicon-certificate"></i> <span>新品研发</span></a></li>
            <?php } ?>-->
          </ul>
        </li>
        <?php } ?>
        <?php if (in_array('admin/employee', $access)) { ?>
        <li class="treeview">
          <a href="#">
            <i class="glyphicon glyphicon-pawn"></i> <span>人事管理</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?php echo $employeeList; ?>" data-scope="admin/employee"><i class="glyphicon glyphicon-duplicate"></i> <span>档案列表</span></a></li>
            <li><a href="<?php echo $employeeRemind; ?>" data-scope="admin/employee/getRemind"><i class="glyphicon glyphicon-bell"></i> <span>提醒事项</span></a></li>
          </ul>
        </li>
        <?php } ?>
        <?php if (in_array('admin/loss', $access)) { ?>
        <li class="treeview">
          <a href="#">
            <i class="glyphicon glyphicon-exclamation-sign"></i> <span>售后管理</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?php echo $lossList; ?>" data-scope="admin/loss"><i class="glyphicon glyphicon-retweet"></i> <span>质量反馈表</span></a></li>
            <li><a href="<?php echo $lossRank; ?>" data-scope="loss/getRankList"><i class="glyphicon glyphicon-signal"></i> <span>破损率排行</span></a></li>
          </ul>
        </li>
        <?php } ?>
        <?php if (in_array('admin/bill', $access)) { ?>
        <li class="treeview">
          <a href="#">
            <i class="glyphicon glyphicon-briefcase"></i> <span>财务管理</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?php echo $billList; ?>" data-scope="admin/bill"><i class="glyphicon glyphicon-equalizer"></i> <span>店铺损益</span></a></li>
            <?php if (in_array('admin/bill/fee', $access)) { ?>
            <li><a href="<?php echo $feeList; ?>" data-scope="Fee"><i class="glyphicon glyphicon-yen"></i> <span>店铺费用</span></a></li>
            <?php } ?>
            <li><a href="<?php echo $grossProfitRate; ?>" data-scope="bill/grossProfitRate"><i class="glyphicon glyphicon-stats"></i> <span>毛利率</span></a></li>
            <?php if (in_array('admin/shop', $access)) { ?>
            <li><a href="<?php echo $shopId; ?>" data-scope="shop/getShopId"><i class="glyphicon glyphicon-inbox"></i> <span>店铺链接</span></a></li>
            <?php } ?>
            <li class="treeview">
              <a href="#">
                <i class="glyphicon glyphicon-log-out"></i> <span>店铺成本核对</span>
                <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
              </a>
              <ul class="treeview-menu">
                <!--<li><a href="<?php echo $billCost; ?>" data-scope="bill/getCost"><span>店铺成本汇总</span></a></li>
                <li><a href="<?php echo $billShipCost; ?>" data-scope="bill/getShipCost"><span>店铺发货成本</span></a></li>
                <li><a href="<?php echo $billReturnCost; ?>" data-scope="bill/getReturnCost"><span>店铺退货成本</span></a></li>-->
                <li><a href="<?php echo $stockIn; ?>" data-scope="remainder/getIn"><span>店铺库存数据</span></a></li>
                <li><a href="<?php echo $stockOut; ?>" data-scope="remainder/getOut"><span>库存销售数据</span></a></li>
                <li><a href="<?php echo $stockCost; ?>" data-scope="remainder/getCost"><span>库存扣除成本</span></a></li>
                <li><a href="<?php echo $wmsInList; ?>" data-scope="outwms/getIn"><span>外仓入库数据</span></a></li>
                <li><a href="<?php echo $wmsOutList; ?>" data-scope="outwms/getOut"><span>外仓出库数据</span></a></li>
                <li><a href="<?php echo $wmsCostList; ?>" data-scope="outwms/getCost"><span>外仓库存成本</span></a></li>
              </ul>
            </li>
            <?php if (in_array('admin/billcheck', $access)) { ?>
            <li class="treeview treeview-alipay">
              <a href="#" class="alipay">
                <i class="glyphicon glyphicon-briefcase"></i> <span>支付宝账单核对</span>
                <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
              </a>
              <ul class="treeview-menu">
                <li><a href="<?php echo $alipayBillSortList; ?>" data-scope="alipayBillSortList"><span>账单列表</span></a></li>
                <li><a href="<?php echo $alipayBillSortKeyList; ?>" data-scope="alipayBillSortKey"><span>关键字管理</span></a></li>
                <li><a href="<?php echo $alipayBillSortUpload; ?>" data-scope="alipayBillSortUpload"><span>导入账单</span></a></li>
                <li><a href="<?php echo $alipayBillSortExportList; ?>" data-scope="alipayBillSortExport"><span>导出账单</span></a></li>
              </ul>
            </li>

            <li class="treeview treeview-alipay">
              <a href="#" class="alipay">
                <i class="glyphicon glyphicon-log-in"></i> <span>快递费核对</span>
                <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
              </a>
              <ul class="treeview-menu">
                <li><a href="<?php echo $expressFeeList; ?>" data-scope="expressFeeList"><span>快递费列表</span></a></li>
                <li><a href="<?php echo $expressFeeTemplateList; ?>" data-scope="expressFeeTemplate"><span>快递模板</span></a></li>
                <li><a href="<?php echo $expressFeeStandardList; ?>" data-scope="expressFeeStandard"><span>快递报价</span></a></li>
                <li><a href="<?php echo $expressFeeSurchargesList; ?>" data-scope="expressFeeSurcharges"><span>快递加价</span></a></li>
                <li><a href="<?php echo $expressFeeUpload; ?>" data-scope="expressFeeUpload"><span>导入发货明细</span></a></li>
                <li><a href="<?php echo $expressFeeExportList; ?>" data-scope="expressFeeExport"><span>导出快递费</span></a></li>
              </ul>
            </li>

            <li class="treeview treeview-alipay">
              <a href="#" class="alipay">
                <i class="glyphicon glyphicon-sort-by-attributes"></i> <span>货款核对</span>
                <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
              </a>
              <ul class="treeview-menu">
                <li><a href="<?php echo $goodsPriceList; ?>" data-scope="goodsPriceList"><span>货款列表</span></a></li>
                <li><a href="<?php echo $goodsPriceTemplateList; ?>" data-scope="goodsPriceTemplate"><span>工厂管理</span></a></li>
                <li><a href="<?php echo $goodsPriceUpload; ?>" data-scope="goodsPriceUpload"><span>导入账单</span></a></li>
                <li><a href="<?php echo $goodsPriceExportList; ?>" data-scope="goodsPriceExport"><span>导出货款</span></a></li>
              </ul>
            </li>
            <li><a href="<?php echo $performance; ?>" data-scope="bill/performance"><i class="glyphicon glyphicon-asterisk"></i> <span>岗位津贴</span></a></li>
            <?php } ?>
            <?php if (in_array('admin/sheet', $access)) { ?>
            <li><a href="<?php echo $sheetKpi; ?>" data-scope="sheet/sheetKpi"><i class="glyphicon glyphicon-hdd"></i> <span>协同绩效</span></a></li>
            <?php } ?>
          </ul>
        </li>
        <?php } ?>
        <?php if (in_array('admin/purchase', $access)) { ?>
        <li class="treeview active">
          <a href="#">
            <i class="glyphicon glyphicon-list-alt"></i> <span>备货管理</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?php echo $purchaseProduct; ?>" data-scope="purchase/storeProduct"><i class="glyphicon glyphicon-folder-open"></i> <span>店铺产品</span></a></li>
            <li><a href="<?php echo $purchaseStore; ?>" data-scope="purchase/storePlan"><i class="glyphicon glyphicon-book"></i> <span>店铺备货</span></a></li>
            <li><a href="<?php echo $purchasePlan; ?>" data-scope="purchase/getPlan"><i class="glyphicon glyphicon-tags"></i> <span>店铺采购</span></a></li>
            <li><a href="<?php echo $purchaseReturn; ?>" data-scope="purchase/getReturn"><i class="glyphicon glyphicon-repeat"></i> <span>店铺退货</span></a></li>
            <li><a href="<?php echo $transfers; ?>" data-scope="Transfer"><i class="glyphicon glyphicon-transfer"></i> <span>库存借调</span></a></li>
            <?php if (in_array('admin/purchase/order', $access)) { ?>
            <li><a href="<?php echo $purchaseNew; ?>" data-scope="purchase/newPlan"><i class="glyphicon glyphicon-gift"></i> <span>新品下单</span></a></li>
            <li><a href="<?php echo $purchaseOrder; ?>" data-scope="purchase/order"><i class="glyphicon glyphicon-download-alt"></i> <span>采购下单</span></a></li>
            <li><a href="<?php echo $purchaseList; ?>" data-scope="purchase/getList"><i class="glyphicon glyphicon-list"></i> <span>采购列表</span></a></li>
            <li><a href="<?php echo $purchaseStock; ?>" data-scope="purchase/getStock"><i class="glyphicon glyphicon-hdd"></i> <span>库存列表</span></a></li>
            <!--<li><a href="<?php echo $uploadSales; ?>" data-scope="purchase/uploadSales"><i class="glyphicon glyphicon-import"></i> <span>导入销量</span></a></li>-->
            <li><a href="<?php echo $purchaseContract; ?>" data-scope="purchase/contract"><i class="glyphicon glyphicon-th"></i> <span>采购合同</span></a></li>
            <li><a href="<?php echo $purchasePushList; ?>" data-scope="purchase/pushList"><i class="glyphicon glyphicon-leaf"></i> <span>采购入库</span></a></li>
            <li><a href="<?php echo $purchaseAntifakeList; ?>" data-scope="purchase/antifake"><i class="glyphicon glyphicon-barcode"></i> <span>防伪码</span></a></li>
            <?php } ?>
          </ul>
        </li>
        <?php } ?>
        <?php if (in_array('admin/customer', $access)) { ?>
        <li class="treeview">
          <a href="#">
            <i class="glyphicon glyphicon-user"></i> <span>客户管理</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?php echo $customer; ?>" data-scope="admin/customer"><i class="glyphicon glyphicon-th-list"></i> <span>客户列表</span></a></li>
            <li><a href="<?php echo $customerGroup; ?>" data-scope="Group"><i class="glyphicon glyphicon-tasks"></i> <span>客户级别</span></a></li>
            <li><a href="<?php echo $customerReport; ?>" data-scope="customer/getReport"><i class="glyphicon glyphicon-stats"></i> <span>客户统计</span></a></li>
            <li><a href="<?php echo $customerUpload; ?>" data-scope="customer/upload"><i class="glyphicon glyphicon-import"></i> <span>导入客户</span></a></li>
            <li><a href="<?php echo $customerSales; ?>" data-scope="customer/offSales"><i class="glyphicon glyphicon-credit-card"></i> <span>定制订单</span></a></li>
          </ul>
        </li>
        <?php } ?>
        <?php if (in_array('admin/sheet', $access)) { ?>
        <li class="treeview">
          <a href="#">
            <i class="glyphicon glyphicon-modal-window"></i> <span>待办管理</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?php echo $sheet; ?>" data-scope="admin/sheet"><i class="glyphicon glyphicon-th-list"></i> <span>表格列表</span></a></li>
            <li><a href="<?php echo $sheetColumn; ?>" data-scope="sheet/sheetColumn"><i class="glyphicon glyphicon-th"></i> <span>字段管理</span></a></li>
            <li><a href="<?php echo $sheetKpi; ?>" data-scope="sheet/sheetKpi"><i class="glyphicon glyphicon-hdd"></i> <span>表格绩效</span></a></li>
            <li><a href="<?php echo $sheetGroupUser; ?>" data-scope="sheet/sheetGroupUser"><i class="glyphicon glyphicon-user"></i> <span>用户群组</span></a></li>
            <li><a href="<?php echo $sheetFlowList; ?>" data-scope="sheetflow/flowList"><i class="glyphicon glyphicon-tree-deciduous"></i> <span>流程</span></a></li>
            <li><a href="<?php echo $sheetNodeList; ?>" data-scope="sheetnode/nodeList"><i class="glyphicon glyphicon-open-file"></i> <span>流程-环节</span></a></li>
            <li><a href="<?php echo $sheetNodeActList; ?>" data-scope="sheetnodeact/nodeActList"><i class="glyphicon glyphicon-save-file"></i> <span>流程-环节-规划</span></a></li>
          </ul>
        </li>
        <?php } ?>
        <li class="treeview">
          <a href="#">
            <i class="glyphicon glyphicon-cog"></i> <span>系统管理</span>
            <span class="pull-right-container"><i class="glyphicon glyphicon-menu-right"></i></span>
          </a>
          <ul class="treeview-menu">
            <?php if (in_array('admin/user', $access)) { ?>
            <li><a href="<?php echo $user; ?>" data-scope="user/"><i class="glyphicon glyphicon-user"></i> <span>账号管理</span></a></li>
            <li><a href="<?php echo $userGroup; ?>" data-scope="usergroup/"><i class="glyphicon glyphicon-plane"></i> <span>账号分组</span></a></li>
            <li><a href="<?php echo $errorLog; ?>" data-scope="common/errorLog"><i class="glyphicon glyphicon-floppy-disk"></i> <span>错误日记</span></a></li>
            <?php } ?>
            <?php if (in_array('admin/import', $access)) { ?>
            <li><a href="<?php echo $importSales; ?>" data-scope="import/uploadSales"><i class="glyphicon glyphicon-import"></i> <span>导入店铺销量</span></a></li>
            <li><a href="<?php echo $importShipment; ?>" data-scope="import/uploadShipment"><i class="glyphicon glyphicon-saved"></i> <span>导入店铺发货</span></a></li>
            <?php } ?>
            <li><a href="<?php echo $password; ?>" data-scope="common/password"><i class="glyphicon glyphicon-lock"></i> <span>修改密码</span></a></li>
            <?php if (in_array('admin/department', $access)) { ?>
            <li><a href="<?php echo $department; ?>" data-scope="department"><i class="glyphicon glyphicon-lock"></i> <span>部门管理</span></a></li>
            <?php } ?>
            <?php if (in_array('admin/role', $access)) { ?>
            <li><a href="<?php echo $role; ?>" data-scope="role"><i class="glyphicon glyphicon-lock"></i> <span>角色管理</span></a></li>
            <?php } ?>
            <?php if (in_array('admin/filemanagerpower', $access)) { ?>
            <li><a href="<?php echo $fileManagerPower; ?>" data-scope="filemanagerpower"><i class="glyphicon glyphicon-lock"></i> <span>权限管理</span></a></li>
            <?php } ?>
          </ul>
        </li>
      </ul>
      <!-- /.sidebar-menu -->
    </section>
    <!-- /.sidebar -->
  </aside>