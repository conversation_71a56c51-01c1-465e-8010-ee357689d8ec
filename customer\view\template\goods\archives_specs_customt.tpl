<style>
  .left-nav-tabs {
    float: none;
  }
  .col-sm-1>.left-nav-tabs>li>a {
    color:#444;
    border-right: 1px solid #ddd;
  }
  .col-sm-1>.left-nav-tabs>li.active>a{
    border-top: 1px solid #f4f4f4;
    border-bottom: 1px solid #f4f4f4;
    border-right-color:transparent;
  }
  .col-sm-1>.left-nav-tabs>li.active {
    width:100%;border-left: 3px solid transparent;border-left-color: #3c8dbc;border-right: 1px solid #fff;
  }

</style>
<?php if(!empty($specs)) { ?>
<div class="" style="overflow-x:auto">
  <div style="display: none" class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> 修改成功！</div>
  <div class="col-sm-1 " style="box-shadow:none">
    <ul class="nav left-nav-tabs" style="border-bottom :none">
      <?php foreach($specs as $spec_key => $spec) { ?>
          <li class="<?php if($spec_key == 0){ ?>active<?php } ?>"><a onclick="tab_spec($(this),'<?php echo $spec['spec_id']; ?>')" data-toggle="tab"><?php echo $spec['spec_no']; ?></a></li>
      <?php } ?>
    </ul>
  </div>
  <div class="col-sm-9">
    <div class="form-group required">
      <label class="col-sm-2 control-label" for="input-nickname">产品名称：</label>
      <div class="col-sm-8">
        <label style="text-align:left;width:100%" class="col-sm-2 control-label custom-value-name"><?php echo $this_spec_name; ?></label>
      </div>
    </div>

    <div class="form-group required">
      <label class="col-sm-2 control-label" for="input-nickname">图片：</label>
      <div class="col-sm-8">
        <img width="100" src="<?php echo $this_spec_img; ?>" class="img-thumbnail custom-value-img">
      </div>
    </div>

    <div class="form-group required">
      <label class="col-sm-2 control-label" for="input-nickname">自定义内容：</label>
      <div class="col-sm-8">
        <dl class="fieldlist">
          <dd>
            <ins style="width: 205px;display:inline-block;text-align: center">内容名</ins>
            <ins style="width: 305px;display:inline-block;text-align: center">内容值</ins>
          </dd>
          <div class="keyword">
            <?php foreach($this_spec_custom as $custom_key => $custom){ ?>
            <dd class="form-inline">
              <input type="text" name="spec[<?php echo $this_spec_id; ?>][<?php echo $custom_key; ?>][key]" class="form-control" value="<?php echo $custom['key']; ?>" title="<?php echo $custom['key']; ?>" placeholder="" style="width: 200px;margin: 5px 10px 5px 0">
              <input type="text" name="spec[<?php echo $this_spec_id; ?>][<?php echo $custom_key; ?>][value]" class="form-control" value="<?php echo $custom['value']; ?>" title="<?php echo $custom['value']; ?>" placeholder="" style="width: 300px;margin: 5px 10px 5px 0">
              <a class="btn btn-danger" href="javascript:;" onclick="delSpecKey($(this))">删除</a>
            </dd>
            <?php } ?>
          </div>
        </dl>

        <div class="box-tools" style="width: 15%;float: left;margin-left: 20px">
          <a class="btn btn-primary" href="javascript:addSpecKey();">添加</a>
        </div>

      </div>
    </div>

    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-8">
        <a class="btn btn-primary" href="javascript:postSpecKey();">提交保存</a>
      </div>
    </div>
  </div>
</div>
<?php } ?>

<script>
  var this_spec_id = '<?php echo $this_spec_id; ?>';
  var this_spec_key_num = <?php echo $this_spec_key_num; ?>;
  function tab_spec(obj,on_spec_id) {
    if (this_spec_id != on_spec_id) {
      $('.keyword').empty();
      $(obj).parent().addClass('left-active'); // 使用parent()获取当前按钮的父元素
      this_spec_id = on_spec_id;
      $.ajax({
        url: '<?php echo $getSpecCustom; ?>',
        type: 'post',
        data: {spec_id: on_spec_id},
        dataType: 'json',
        success: function(json) {
          $('.custom-value-name').html(json.this_spec_name);
          $('.custom-value-img').attr('src', json.this_spec_img);
          this_spec_key_num = json.this_spec_key_num
          var addHtml = '';

          $.each(json.this_spec_custom, function(index, value1) {
            addHtml += '<dd class="form-inline">' +
                    '<input type="text" name="spec['+this_spec_id+']['+index+'][key]" class="form-control" value="'+value1.key+'" title="'+value1.key+'" placeholder="" style="width: 200px;margin: 5px 10px 5px 0">'+
                    '<input type="text" name="spec['+this_spec_id+']['+index+'][value]" class="form-control" value="'+value1.value+'" title="'+value1.value+'" placeholder="" style="width: 300px;margin: 5px 10px 5px 0">'+
                    '<a class="btn btn-danger" href="javascript:;" onclick="delSpecKey($(this))">删除</a>'+
                    '</dd>';
          });

          $('.keyword').append(addHtml);
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }
  }

  function addSpecKey() {
    this_spec_key_num += 1;
    var addHtml = '<dd class="form-inline">' +
            '<input type="text" name="spec['+this_spec_id+']['+this_spec_key_num+'][key]" class="form-control" value="" placeholder="" style="width: 200px;margin: 5px 10px 5px 0">'+
            '<input type="text" name="spec['+this_spec_id+']['+this_spec_key_num+'][value]" class="form-control" value="" placeholder="" style="width: 300px;margin: 5px 10px 5px 0">'+
            '<a class="btn btn-danger" href="javascript:;" onclick="delSpecKey($(this))">删除</a>'+
            '</dd>';

    $('.keyword').append(addHtml);
  }

  function delSpecKey(obj) {
    $(obj).parent().remove();
  }

  function postSpecKey() {
    var $inputs = $('.keyword input');
    var inputsData = {spec_id:this_spec_id};

    $inputs.each(function(){
      var name = $(this).attr('name');
      var value = $(this).val();
      inputsData[name] = value;
    });

    $.ajax({
      url: '<?php echo $addSpecCustom; ?>',
      type: 'post',
      data: inputsData,
      dataType: 'json',
      success: function(json) {
        $('.alert-warning').show()
        // 设置3秒后隐藏提示层
        setTimeout(function() {
          $('.alert-warning').hide()
        }, 1500); // 1500毫秒等于1.5秒
      },
      error: function(xhr, ajaxOptions, thrownError) {
        alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
      }
    });

  }
</script>


