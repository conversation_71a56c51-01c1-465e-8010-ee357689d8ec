<?php
class ModelGoods extends Model {
    public function authentication($cardid, $telephone) {
        $query = $this->db->query("SELECT wholesale FROM " . DB_PREFIX . "customer_auth WHERE cardid = '" . $this->db->escape($cardid) . "' AND telephone = '" . $this->db->escape($telephone) . "'");

        $login_status = !empty($query->row) ? 1 : 0;

        $this->db->query("INSERT INTO " . DB_PREFIX . "customer_auth_ip SET cardid = '" . $this->db->escape($cardid) . "', login_ip = '" . $this->db->escape($this->request->server['REMOTE_ADDR']) . "', login_status = '" . $login_status . "', date_added = NOW()");

        return $query->row;
    }

    public function getWdtClass() {
        $query = $this->db->query("SELECT class_id, class_name FROM wdt_goods_class");

        return $query->rows;
    }

    public function getWdtGoods($data) {
        $sql = "SELECT gl.class_name, sl.spec_no, sl.spec_name, en.name_en, en.category_en, sl.img_url, sl.wholesale_price, sl.custom_price1, sl.custom_price2, sl.market_price, sl.weight, sl.prop1, sl.prop2, sl.prop3, sl.prop5, sl.prop6 FROM wdt_goods_list gl LEFT JOIN wdt_spec_list sl ON (gl.goods_id = sl.goods_id) LEFT JOIN wdt_spec_en en ON (sl.spec_no = en.spec_no) WHERE gl.deleted = '0' AND sl.deleted = '0' AND sl.member_price > '0' AND gl.short_name IS NOT NULL AND gl.short_name != '' AND replenish_type != '1'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(sl.spec_no, sl.spec_name, IFNULL(en.name_en, ''), IFNULL(en.category_en, '')) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_class']) && $data['filter_class'] != '') {
            $sql .= " AND gl.class_id = '" . $this->db->escape($data['filter_class']) . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(gl.goods_created) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(gl.goods_created) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " ORDER BY gl.goods_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalGoods($data) {
        $sql = "SELECT COUNT(*) AS total FROM wdt_goods_list gl LEFT JOIN wdt_spec_list sl ON (gl.goods_id = sl.goods_id) LEFT JOIN wdt_spec_en en ON (sl.spec_no = en.spec_no) WHERE gl.deleted = '0' AND sl.deleted = '0' AND sl.member_price > '0' AND gl.short_name IS NOT NULL AND gl.short_name != '' AND replenish_type != '1'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(sl.spec_no, sl.spec_name, IFNULL(en.name_en, ''), IFNULL(en.category_en, '')) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_class']) && $data['filter_class'] != '') {
            $sql .= " AND gl.class_id = '" . $this->db->escape($data['filter_class']) . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(gl.goods_created) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(gl.goods_created) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }
}