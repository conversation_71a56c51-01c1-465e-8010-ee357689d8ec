<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <h1>
            流程-环节
            <small></small>
        </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
        <?php if ($success) { ?>
        <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
        <?php } ?>
        <?php if ($warning) { ?>
        <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
        <?php } ?>
        <div class="box box-primary">
            <div class="box-body">
                <div class="row">


                    <div class="col-md-3">
                           <div class="form-group">
                            <label>状态：</label>
                            <select class="form-control" name="filter_flow_id">
                            <option value="0"  <?php if($filter_flow_id == 0) { ?>selected="selected"<?php } ?>> 显示全部</option>
                             <?php foreach($flow_list as $list) { ?>
                            <?php if ($list['flow_id'] == $filter_flow_id) { ?>
                            <option value="<?php echo $list['flow_id']; ?>" selected="selected"><?php echo $list['flow_name']; ?></option>
                            <?php } else { ?>
                            <option value="<?php echo  $list['flow_id']; ?>"><?php echo $list['flow_name']; ?></option>
                            <?php } ?>
                            <?php } ?>
                            </select>
                        </div>
                    </div>


                    <div class="col-md-3">
                        <div class="form-group">
                            <label>关键字：</label>
                            <input type="text" class="form-control" name="filter_name" placeholder="请输入环节"
                                   value="<?php echo $filter_name; ?>">
                        </div>
                    </div>

                     


                    


                    <div class="col-md-3">
                        <div class="form-group">
                            <label>发布时间：</label>
                            <div class="input-group">
                                <div class="input-group-addon">
                                    <i class="glyphicon glyphicon-calendar"></i>
                                </div>
                                <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                                <input type="text" class="form-control pull-right" id="reservation"
                                       placeholder="起始时间 - 截止时间"
                                       value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                                <?php } else{ ?>
                                <input type="text" class="form-control pull-right" id="reservation"
                                       placeholder="起始时间 - 截止时间"
                                       value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                                <?php } ?>
                                <input type="text" class="hidden" name="filter_date_start" id="filter-start-time"
                                       placeholder="" value="<?php echo $filter_date_start; ?>">
                                <input type="text" class="hidden" name="filter_date_end" id="filter-end-time"
                                       placeholder="" value="<?php echo $filter_date_end; ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.box-body -->
            <div class="box-footer">
                <button type="button" id="button-filter" class="btn bg-purple pull-right"><i
                            class="glyphicon glyphicon-search"></i> 筛选
                </button>
            </div>
        </div>
        <div class="box box-success">
            <div class="box-header with-border">
                <p class="box-title">列表</p>
                <div class="box-tools">
                    <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
                </div>
            </div>
            <div class="box-body table-responsive no-padding">
                <table class="table text-middle table-bordered table-hover table-striped">
                    <tbody>
                    <tr>

                        <th>
                           流程
                        </th>

                        <th>
                            <?php if ($sort == 'fullname') { ?>
                            <a href="<?php echo $sort_name; ?>" class="<?php echo strtolower($order); ?>">当前环节</a>
                            <?php } else { ?>
                            <a href="<?php echo $sort_name; ?>">当前环节</a>
                            <?php } ?>
                        </th>

                         <th>
                           下一环节
                        </th>

                        <th>
                           分支
                        </th>




      

                        <th>
                            <?php if ($sort == 'job_work_date') { ?>
                            <a href="<?php echo $sort_createTime; ?>" class="<?php echo strtolower($order); ?>">创建时间</a>
                            <?php } else { ?>
                            <a href="<?php echo $sort_createTime; ?>">创建时间</a>
                            <?php } ?>
                        </th>
                        <th class="text-right">操作</th>
                    </tr>
                    <?php if (!empty($column)) { ?>
                    <?php foreach ($column as $v) { ?>
                    <tr data-id="<?php echo $v['parent_node_id']; ?>">
                      <td><?php echo $v['flow_name']; ?></td>
                        <td><?php echo $v['parent_name']; ?></td>
                        <td><?php echo $v['child_node_name']; ?></td>
                        <td><?php echo $v['parallel_child_node_name']; ?></td>
                        <td><?php echo $v['entry']; ?></td>
                        <td class="text-right">
                            <a class="btn btn-success" href="<?php echo $v['edit']; ?>" title="">编辑</a>
                            <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">删除</button>
                        </td>
                    </tr>
                    <?php } ?>
                    <?php } else{ ?>
                    <td colspan="9" align="center"> 暂无数据</td>
                    <?php } ?>
                    </tbody>
                </table>
            </div>
            <div class="box-footer clearfix">
                <div class="flex ai__c jc__sb">
                    <div><?php echo $results; ?></div>
                    <?php echo $pagination; ?>
                </div>
            </div>
        </div>

        <!-- 删除 -->
        <div class="modal modal-danger fade" id="del-modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-del">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title">删除</h4>
                        </div>
                        <div class="modal-body">
                            <p>确定删除此吗？</p>
                            <input id="del-id" name="selected[]" type="hidden" value="">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                            <button id="del-yes" type="button" class="btn btn-outline">是</button>
                        </div>
                    </form>
                </div>
                <!-- /.modal-content -->
            </div>
            <!-- /.modal-dialog -->
        </div>
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
    .table a.asc:after, .table a.undesc:after {
        content: " \f106";
        font-family: FontAwesome;
    }

    .table a.desc:after, .table a.unasc:after {
        content: " \f107";
        font-family: FontAwesome;
    }
</style>


<script type="text/javascript">


    (function () {
        // 日期筛选
        $('#reservation').daterangepicker({
            autoUpdateInput: false,
            daterangepicker: true,
            locale: {
                applyLabel: '确定',
                cancelLabel: '清除'
            }
        })
        // 日期显示
        $('#reservation').on('apply.daterangepicker', function (ev, picker) {
            $('#filter-start-time').val(picker.startDate.format('YYYY-MM-DD'))
            $('#filter-end-time').val(picker.endDate.format('YYYY-MM-DD'))
            $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
        })
        $('#reservation').on('cancel.daterangepicker', function (ev, picker) {
            $(this).val('')
            $('#filter-start-time').val('')
            $('#filter-end-time').val('')
        })
        // 筛选
        $('#button-filter').on('click', function () {
            url = '<?php echo $nofilter; ?>';
            console.log(url);

            var filter_name = $('input[name=\'filter_name\']').val();
            var filter_flow_id = $('select[name=\'filter_flow_id\']').val();


            if (filter_name) {
                url += '&filter_name=' + encodeURIComponent(filter_name);
            }

            if (filter_flow_id) {
                url += '&filter_flow_id=' + encodeURIComponent(filter_flow_id);
            }




            var filter_date_start = $('input[name=\'filter_date_start\']').val();

            if (filter_date_start) {
                url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
            }

            var filter_date_end = $('input[name=\'filter_date_end\']').val();

            if (filter_date_end) {
                url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
            }

            location.href = url;
        });

        $('#del-modal').on('show.bs.modal', function (event) {
            $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
        })
        $('#del-yes').on('click', () => {$('#form-del').submit()})
    })()
</script>
<?php echo $footer; ?>