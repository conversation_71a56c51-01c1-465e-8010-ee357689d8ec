<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        毛利率
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label>关键字：</label>
                <input type="text" class="form-control" name="filter_name" placeholder="搜索编码/名称" value="<?php echo $filter_name; ?>">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>所属店铺：</label>
                <select id="w1" class="form-control" name="filter_store" multiple>
                  <?php foreach($stores as $store) { ?>
                  <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>维度：</label>
                  <select class="form-control" name="filter_state">
                    <?php if ($filter_state === '0') { ?>
                    <option value="0" selected="selected">店铺</option>
                    <?php } else { ?>
                    <option value="0">店铺</option>
                    <?php } ?>
                    <?php if ($filter_state == '1') { ?>
                    <option value="1" selected="selected">产品</option>
                    <?php } else { ?>
                    <option value="1">产品</option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>账单时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <a class="btn btn-danger" href="<?php echo $export; ?>"><i class="glyphicon glyphicon-download-alt"></i> 下载数据</a>
          <?php if(!empty($userStore)) { ?>
          <a class="btn btn-info" href="<?php echo $userStore; ?>"><i class="glyphicon glyphicon-user"></i> 设置查看权限</a>
          <?php } ?>
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title"><?php if($filter_state == 0) { ?>店铺<?php } else { ?>产品<?php } ?>毛利率</h3>
        </div>
        <?php if($filter_state == 0) { ?>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>店铺</th>
              <th>
                <?php if ($sort == 'total_sales') { ?>
                <a href="<?php echo $sort_total_sales; ?>" class="<?php echo strtolower($order); ?>">总销售额</a>
                <?php } else { ?>
                <a href="<?php echo $sort_total_sales; ?>">总销售额</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'total_quantity') { ?>
                <a href="<?php echo $sort_total_quantity; ?>" class="<?php echo strtolower($order); ?>">销售数量</a>
                <?php } else { ?>
                <a href="<?php echo $sort_total_quantity; ?>">销售数量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'total_cost') { ?>
                <a href="<?php echo $sort_total_cost; ?>" class="<?php echo strtolower($order); ?>">总成本</a>
                <?php } else { ?>
                <a href="<?php echo $sort_total_cost; ?>">总成本</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'gross_margin') { ?>
                <a href="<?php echo $sort_gross_margin; ?>" class="<?php echo strtolower($order); ?>">毛利率</a>
                <?php } else { ?>
                <a href="<?php echo $sort_gross_margin; ?>">毛利率</a>
                <?php } ?>
              </th>
            </tr>
            <?php if (!empty($lists)) { ?>
            <?php foreach ($lists as $list) { ?>
            <tr>
              <td><?php echo $stores_k[$list['store_id']]; ?></td>
              <td><?php echo $list['total_sales']; ?></td>
              <td><?php echo $list['total_quantity']; ?></td>
              <td><?php echo $list['total_cost']; ?></td>
              <td <?php if(!empty($list['gross_margin'])){ ?><?php if($list['gross_margin'] > 0){ ?>style="color: #0a5ba6"<?php } else { ?>style="color: #9f191f"<?php } ?><?php } ?>><?php if(!empty($list['gross_margin'])){ ?><?php echo $list['gross_margin']; ?>%<?php } else { ?>--<?php } ?></td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="10" align="center"> <?php if($search == 1) { ?>暂无店铺毛利率<?php } else { ?>请筛选<?php } ?> </td>
            <?php } ?>
          </tbody></table>
        </div>
        <?php } else { ?>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th style="max-width: 200px;">图片</th>
              <th>产品名称/编码</th>
              <th>
                <?php if ($sort == 'total_sales') { ?>
                <a href="<?php echo $sort_total_sales; ?>" class="<?php echo strtolower($order); ?>">总销售额</a>
                <?php } else { ?>
                <a href="<?php echo $sort_total_sales; ?>">总销售额</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'total_quantity') { ?>
                <a href="<?php echo $sort_total_quantity; ?>" class="<?php echo strtolower($order); ?>">销售数量</a>
                <?php } else { ?>
                <a href="<?php echo $sort_total_quantity; ?>">销售数量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'total_cost') { ?>
                <a href="<?php echo $sort_total_cost; ?>" class="<?php echo strtolower($order); ?>">总成本</a>
                <?php } else { ?>
                <a href="<?php echo $sort_total_cost; ?>">总成本</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'gross_margin') { ?>
                <a href="<?php echo $sort_gross_margin; ?>" class="<?php echo strtolower($order); ?>">毛利率</a>
                <?php } else { ?>
                <a href="<?php echo $sort_gross_margin; ?>">毛利率</a>
                <?php } ?>
              </th>
            </tr>
            <?php if (!empty($lists)) { ?>
            <?php foreach ($lists as $list) { ?>
            <tr>
              <td><img width="100" src="<?php echo $list['product_image']; ?>" class="img-thumbnail"></td>
              <td><?php echo $list['spec_name']; ?><br><?php echo $list['product_code']; ?></td>
              <td><?php echo $list['total_sales']; ?></td>
              <td><?php echo $list['total_quantity']; ?></td>
              <td><?php echo $list['total_cost']; ?></td>
              <td <?php if(!empty($list['gross_margin'])){ ?><?php if($list['gross_margin'] > 0){ ?>style="color: #0a5ba6"<?php } else { ?>style="color: #9f191f"<?php } ?><?php } ?>><?php if(!empty($list['gross_margin'])){ ?><?php echo $list['gross_margin']; ?>%<?php } else { ?>--<?php } ?></td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="10" align="center"> 暂无产品毛利率 </td>
            <?php } ?>
            </tbody></table>
        </div>
        <?php } ?>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>

<script>
  var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
  window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"35px","placeholder":"请选择店铺","language":"zh-CN"};

  if (jQuery('#w1').data('select2')) { jQuery('#w1').select2('destroy'); }
  jQuery.when(jQuery('#w1').select2(select2_5eaa6d36)).done(initS2Loading('w1','s2options_c4acac00'));

  var filter_store = '<?php echo $filter_store_json; ?>'
  $("#w1").val($.parseJSON(filter_store)).trigger("change");
</script>
<style type="text/css">
  .table a.asc:after {
    content: " \f106";
    font-family: FontAwesome;
  }
  .table a.desc:after {
    content: " \f107";
    font-family: FontAwesome;
  }
  .select2-container--krajee .select2-selection--multiple .select2-search--inline .select2-search__field {
    height: 26px;
  }
  /*.select2-selection {*/
/*  height: 34px;*/
/*}*/
  /*.select2-container .select2-selection--multiple .select2-selection__rendered {*/
/*  padding-top: 0;*/
/*}*/
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();

      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_state = $('select[name=\'filter_state\']').val();

      if (filter_state != '0') {
        url += '&filter_state=' + encodeURIComponent(filter_state);
      }
  
      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      url += '&search=' + encodeURIComponent('1');
      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>