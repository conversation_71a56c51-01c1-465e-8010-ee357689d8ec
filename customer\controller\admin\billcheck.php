<?php

class ControllerAdminbillcheck extends Controller {
	private $error = array();

    /**
     * 支付宝账单-关键字列表
     */
    public function alipayBillSortKeyList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }
        if (isset($this->request->get['filter_type'])) {
            $filter_type = $this->request->get['filter_type'];
        } else {
            $filter_type = '';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'export_sort';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';
        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name='.$this->request->get['filter_name'];
        }
        if (isset($this->request->get['filter_type'])) {
            $url .= '&filter_type='.$this->request->get['filter_type'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_type'       => $filter_type,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $data['fields'] = $this->model_admin_billcheck->getAlipayBillSortKeyFields();
        $data['operations'] = $this->model_admin_billcheck->getAlipayBillSortKeyOperations();
        $keys = $this->model_admin_billcheck->getAlipayBillSortKeyAll($filter_data);
        foreach ($keys as $k => $v) {
            $keyword = json_decode($v['keyword'],true);
            foreach ($keyword as $key => $val) {
                foreach ($val['keyword'] as $value) {
                    $keys[$k]['keyword_arr'][] = $data['fields'][$val['field']][0].''.$data['operations'][$val['operation']].$value;
                }
            }
            $keys[$k]['edit_url'] = $this->url->link('admin/billcheck/alipayBillSortKeySet', 'token=' . $this->session->data['token'].'&alipay_bill_sort_id='.$v['alipay_bill_sort_id']);
        }
        $data['keys'] = $keys;
        $total = $this->model_admin_billcheck->getAlipayBillSortKeyTotal($filter_data);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/alipayBillSortKeyList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['sort_url'] = $this->url->link('admin/billcheck/alipayBillSortKeyList', 'token=' . $this->session->data['token'] . '&sort=sort'. $url);
        $data['export_sort_url'] = $this->url->link('admin/billcheck/alipayBillSortKeyList', 'token=' . $this->session->data['token'] . '&sort=export_sort'. $url);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['filter_name'] = $filter_name;
        $data['filter_type'] = $filter_type;

        $data['nofilter'] = $this->url->link('admin/billcheck/alipayBillSortKeyList', 'token=' . $this->session->data['token']);
        $data['add'] = $this->url->link('admin/billcheck/alipayBillSortKeySet', 'token=' . $this->session->data['token']);
        $data['delete'] = $this->url->link('admin/billcheck/alipayBillSortKeyDel', 'token=' . $this->session->data['token']. $url. '&page='.$page);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/alipay_bill_sort_key_list.tpl', $data));
    }

    /**
     * 支付宝账单-设置关键字
     */
    public function alipayBillSortKeySet() {
        $this->load->model('admin/billcheck');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            !isset($this->request->get['alipay_bill_sort_id']) ? $this->model_admin_billcheck->addAlipayBillSortKey($this->request->post) : $this->model_admin_billcheck->editAlipayBillSortKey($this->request->post,$this->request->get['alipay_bill_sort_id']);
            $this->response->redirect($this->url->link('admin/billcheck/alipayBillSortKeyList', 'token=' . $this->session->data['token']));
        }

        $data['text_form'] = !isset($this->request->get['alipay_bill_sort_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        $data['num'] = 0;
        if (isset($this->request->get['alipay_bill_sort_id'])) {
            $alipayBillSortKey = $this->model_admin_billcheck->getAlipayBillSortKey($this->request->get['alipay_bill_sort_id']);
            if (empty($alipayBillSortKey['alipay_bill_sort_id'])) {
                $data['warning'] = '分类不存在！';
            } else {
                $alipayBillSortKey['keyword'] = json_decode($alipayBillSortKey['keyword'],true);
                $data['num'] = count($alipayBillSortKey['keyword']) - 1;
                $data['alipayBillSortKey'] = $alipayBillSortKey;
            }
        }

        $this->load->model('admin/billcheck');
        $data['fields'] = $this->model_admin_billcheck->getAlipayBillSortKeyFields();
        $data['operations'] = $this->model_admin_billcheck->getAlipayBillSortKeyOperations();
        $data['fields_json'] = json_encode($data['fields']);
        $data['operations_json'] = json_encode($data['operations']);

        if (!isset($this->request->get['alipay_bill_sort_id'])) {
            $data['action'] = $this->url->link('admin/billcheck/alipayBillSortKeySet', 'token=' . $this->session->data['token']);
        } else {
            $data['action'] = $this->url->link('admin/billcheck/alipayBillSortKeySet', 'token=' . $this->session->data['token'] . '&alipay_bill_sort_id=' . $this->request->get['alipay_bill_sort_id']);
        }
        $data['cancel'] = $this->url->link('admin/billcheck/alipayBillSortKeyList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/alipay_bill_sort_key_form.tpl', $data));
    }

    /**
     * 支付宝账单-删除关键字
     */
    public function alipayBillSortKeyDel() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->post['selected']) && $this->validateDel()) {
            foreach ($this->request->post['selected'] as $alipay_bill_sort_id) {
                $this->model_admin_billcheck->delAlipayBillSortKey($alipay_bill_sort_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name='.$this->request->get['filter_name'];
            }
            if (isset($this->request->get['filter_type'])) {
                $url .= '&filter_type='.$this->request->get['filter_type'];
            }

            if (isset($this->request->get['order'])) {
                if ($this->request->get['order'] == 'ASC') {
                    $url .= '&order=DESC';
                } else {
                    $url .= '&order=ASC';
                }
            }
            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/billcheck/alipayBillSortKeyList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->alipayBillSortKeyList();
    }

    /**
     * 支付宝账单-上传文件列表
     */
    public function alipayBillSortUploadList() {
        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'date_added';
        }
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $filter_data = array(
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $data['file_lists'] = $this->model_admin_billcheck->getAlipayBillFileList($filter_data);
        $total = $this->model_admin_billcheck->getAlipayBillFileTotal($filter_data);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/alipayBillSortUploadList', 'token=' . $this->session->data['token']  . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['text'] = '支付宝账单';
        $data['data_id'] = 'alipay_bill_file_id';

        $data['add'] = $this->url->link('admin/billcheck/alipayBillSortUpload', 'token=' . $this->session->data['token']);
        $data['delete'] = $this->url->link('admin/billcheck/alipayBillSortUploadDel', 'token=' . $this->session->data['token']);

        $data['status_list'] = [
            0 => '待处理',
            1 => '已下载',
            2 => '下载失败',
            3 => '解压成功',
            4 => '已导入'
        ];

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('billcheck/upload_list.tpl', $data));
    }

    /**
     * 支付宝账单-删除上传文件
     */
    public function alipayBillSortUploadDel() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->post['selected']) && $this->validateDel()) {
            foreach ($this->request->post['selected'] as $alipay_bill_file_id) {
                $this->model_admin_billcheck->delAlipayBillUpload($alipay_bill_file_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $this->response->redirect($this->url->link('admin/billcheck/alipayBillSortUploadList', 'token=' . $this->session->data['token']));
        }

        $this->alipayBillSortUploadList();
    }

    /**
     * 支付宝账单-上传文件
     */
    public function alipayBillSortUpload() {
        if (isset($this->request->post['bill_file']) && isset($this->request->post['bill_file_hash'])) {
            $this->load->model('admin/billcheck');
            $getAlipayBillFile = $this->model_admin_billcheck->getAlipayBillFile($this->request->post['bill_file']);
            if ($getAlipayBillFile > 0) {
                $json = array();
                $json['status'] = 2;
                $this->response->setOutJson($json);
            } else {
                $return = $this->model_admin_billcheck->addAlipayBillFile($this->request->post['bill_file'],$this->request->post['bill_file_hash'],$this->request->post['original_name']);
                if ($return) {
                    $json = array();
                    $json['status'] = 1;
                    $this->response->setOutJson($json);
                }
            }
        } else {
            $uptoken = QiNiu::getUploadToken();
            $data['gettoken'] = $uptoken;

            $data['text'] = '导入支付宝账单';
            $data['action'] = $this->url->link('admin/billcheck/alipayBillSortUpload', 'token=' . $this->session->data['token']);

            $data['header'] = $this->load->controller('admin/template/header');
            $data['content_top'] = $this->load->controller('admin/template/top');
            $data['content_bottom'] = $this->load->controller('admin/template/bottom');
            $data['footer'] = $this->load->controller('admin/template/footer');

            $this->response->setOutput($this->load->view('billcheck/upload_form.tpl', $data));
        }
    }

    public function getUploadToken() {
        $json = array();

        $json['uptoken'] = QiNiu::getUploadToken();

        $this->response->setOutJson($json);
    }

    /**
     * 支付宝账单-账单列表
     */
	public function alipayBillSortList() {
        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['filter_bill_key_id'])) {
            $filter_bill_key_id = $this->request->get['filter_bill_key_id'];
        } else {
            $filter_bill_key_id = '';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'month,store_id';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';
        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store='.$this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start='.$this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end='.$this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['filter_bill_key_id'])) {
            $url .= '&filter_bill_key_id='.$this->request->get['filter_bill_key_id'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        $filter_data = array(
            'filter_store'		=> $filter_store,
            'filter_date_start'	=> $filter_date_start ? date("Ym",strtotime($filter_date_start)) : '',
            'filter_date_end'	=> $filter_date_end ? date("Ym",strtotime($filter_date_end)) : '',
            'filter_bill_key_id'=> $filter_bill_key_id,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $total = $this->model_admin_billcheck->getAlipayBillSortAllTotal($filter_data);
        $results = $this->model_admin_billcheck->getAlipayBillSortAll($filter_data);

        $data['stores'] = $this->model_admin_billcheck->getStores();

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $data['bill_keys'] = $this->model_admin_billcheck->getAllAlipayBillSortKey();
        foreach ($data['bill_keys'] as $keys) {
            $bill_keys[$keys['alipay_bill_sort_id']] = $keys['name'];
        }

        foreach ($results as $k=>$result) {
            $data['bills'][$k]['store'] = $stores[$result['store_id']];
            $data['bills'][$k]['keys'] = $bill_keys[$result['alipay_bill_sort_id']];
            $data['bills'][$k]['date'] = $result['month'];
            $data['bills'][$k]['sum_income'] = $result['sum_income'];
            $data['bills'][$k]['sum_disburse'] = $result['sum_disburse'];
            $data['bills'][$k]['detail'] = $this->url->link('admin/billcheck/alipayBillSortListDetail', 'token=' . $this->session->data['token'] . '&alipay_bill_sort_id=' . $result['alipay_bill_sort_id']. '&store_id=' . $result['store_id']. '&month=' . $result['month']);
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/alipayBillSortList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['sort_store'] = $this->url->link('admin/billcheck/alipayBillSortList', 'token=' . $this->session->data['token'] . '&sort=store_id'. $url);
        $data['month'] = $this->url->link('admin/billcheck/alipayBillSortList', 'token=' . $this->session->data['token'] . '&sort=month'. $url);

        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/billcheck/alipayBillSortList', 'token=' . $this->session->data['token']);
        $data['addtosort'] = $this->url->link('admin/billcheck/alipayBillSortListAdd', 'token=' . $this->session->data['token']);
        $data['deltosort'] = $this->url->link('admin/billcheck/alipayBillSortDeleteAdd', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['filter_store'] = $filter_store;
        $data['filter_bill_key_id'] = $filter_bill_key_id;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/alipay_bill_sort_list.tpl', $data));
	}

    /**
     * 支付宝账单-导出列表
     */
    public function alipayBillSortExportList() {
        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'month';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';
        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store='.$this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start='.$this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end='.$this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['filter_bill_key_id'])) {
            $url .= '&filter_bill_key_id='.$this->request->get['filter_bill_key_id'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $filter_data = array(
            'filter_store'		=> $filter_store,
            'filter_date_start'	=> $filter_date_start ? date("Ym",strtotime($filter_date_start)) : '',
            'filter_date_end'	=> $filter_date_end ? date("Ym",strtotime($filter_date_end)) : '',
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $total = $this->model_admin_billcheck->getAlipayBillSortExportTotal($filter_data);
        $results = $data['exports'] = $this->model_admin_billcheck->getAlipayBillSortExportAll($filter_data);

        $data['stores'] = $this->model_admin_billcheck->getStores();
        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        foreach ($results as $k => $v) {
            $export_stores = array_filter(explode(',',$v['stores']));
            $export_stores_name = [];
            $s_month = str_split($v['s_month'],4);
            $e_month = str_split($v['e_month'],4);
            $NotSortListWhere = [
                'filter_date_start' => date("Y-m-01 00:00:00",strtotime($s_month[0].'-'.$s_month[1])),
                'filter_date_end' => date("Y-m-t 23:59:59",strtotime($e_month[0].'-'.$e_month[1])),
            ];
            foreach ($export_stores as $val) {
                $NotSortListWhere['filter_store'] = $val;
                $getAlipayBillNotSortList = $this->model_admin_billcheck->getAlipayBillNotSortList($NotSortListWhere);
                $isNotSort = !empty($getAlipayBillNotSortList) ? 2 : 1;
                $export_stores_name[] = [$stores[$val],$isNotSort];
            }
            $data['exports'][$k]['stores'] = $export_stores_name;
            $data['exports'][$k]['download'] = $this->url->link('admin/billcheck/alipayBillSortExportDownload', 'token=' . $this->session->data['token'] . '&alipay_bill_export_id='.$v['alipay_bill_export_id']);
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/alipayBillSortExportList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['nofilter'] = $this->url->link('admin/billcheck/alipayBillSortExportList', 'token=' . $this->session->data['token']);
        $data['add'] = $this->url->link('admin/billcheck/alipayBillSortExportAdd', 'token=' . $this->session->data['token']);

        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_store'] = $filter_store;

        $data['sort'] = $sort;
        $data['order'] = $order;


        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/alipay_bill_sort_export_list.tpl', $data));
    }

    /**
     * 支付宝账单-新增导出
     */
    public function alipayBillSortExportAdd() {
        $this->load->model('admin/billcheck');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm6()) {
            $this->model_admin_billcheck->addAlipayBillSortExport($this->request->post);
            $this->response->redirect($this->url->link('admin/billcheck/alipayBillSortExportList', 'token=' . $this->session->data['token']));
        }

        $data['stores'] = $this->model_admin_billcheck->getStores();

        $data['action'] = $this->url->link('admin/billcheck/alipayBillSortExportAdd', 'token=' . $this->session->data['token']);
        $data['cancel'] = $this->url->link('admin/billcheck/alipayBillSortExportList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/alipay_bill_sort_export_form.tpl', $data));
    }

    /**
     * 支付宝账单-导出下载
     */
    public function alipayBillSortExportDownload() {
        $file = DIR_DOWNLOAD . 'alipay_bill_export/支付宝账单_'.$this->request->get['alipay_bill_export_id'].'.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }

    /**
     * 支付宝账单-添加到账单
     */
    public function alipayBillSortListAdd() {
        $this->validateGet2();
        $url = '';
        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
            if (isset($this->request->get['store_id'])) {
                $filter_store = $this->request->get['store_id'];
            } else {
                $filter_store = '';
            }

            if (isset($this->request->get['alipay_bill_sort_id'])) {
                $filter_alipay_bill_sort_id = $this->request->get['alipay_bill_sort_id'];
            } else {
                $filter_alipay_bill_sort_id = '';
            }

            if (isset($this->request->get['filter_date_start'])) {
                $filter_date_start = $this->request->get['filter_date_start'];
            } else {
                $filter_date_start = '';
            }

            if (isset($this->request->get['filter_date_end'])) {
                $filter_date_end = $this->request->get['filter_date_end'];
            } else {
                $filter_date_end = '';
            }

            if (!empty($this->request->get['filter_remark'])) {
                $filter_remark = $this->request->get['filter_remark'];
            } else {
                $filter_remark = '';
            }

            if (!empty($this->request->get['filter_business_type'])) {
                $filter_business_type = $this->request->get['filter_business_type'];
            } else {
                $filter_business_type = '';
            }

            if (isset($this->request->get['store_id'])) {
                $url .= '&store_id='.$this->request->get['store_id'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start='.$this->request->get['filter_date_start'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end='.$this->request->get['filter_date_end'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['alipay_bill_sort_id'])) {
                $url .= '&alipay_bill_sort_id='.$this->request->get['alipay_bill_sort_id'];
            } else {
                $url .= '';
            }

            $this->load->model('admin/billcheck');

            if (isset($this->request->get['alipay_bill_sort_id'])) {
                $data['alipay_bill_sort_id'] = $this->request->get['alipay_bill_sort_id'];
                $data['bill_keys'] = '';
            } else {
                $data['alipay_bill_sort_id'] = '';
                $data['bill_keys'] = $this->model_admin_billcheck->getAllAlipayBillSortKey();
            }

            $filter_data = array(
                'filter_store'		=> $filter_store,
                'filter_alipay_bill_sort_id'=> $filter_alipay_bill_sort_id,
                'filter_date_start'	=> $filter_date_end ? $filter_date_start.' 00:00:00' : '',
                'filter_date_end'	=> $filter_date_end ? $filter_date_end.' 23:59:59' : '',
            );

            if (isset($this->request->post['selected'])) {
                $num = 20;
                $count=ceil(count($this->request->post['selected'])/$num);
                for ($i=1;$i<=$count;$i++){
                    $offset=($i-1)*($num);
                    $add_list=array_slice($this->request->post['selected'],$offset,$num);
                    $this->model_admin_billcheck->addAlipayBillToSore($filter_data,implode(',',$add_list));
                }
                //计算统计数据
                $this->model_admin_billcheck->setAlipayBillStatistic($filter_data,implode(',',$this->request->post['selected']));
            }

            if (isset($this->request->post['add_sort_type']) && isset($this->request->post['accounting_serial_number'])) {
                $insert_data = $filter_data;
                if ($this->request->post['add_sort_type'] == 1) {
                    $insert_data['filter_alipay_bill_sort_id'] = $this->request->post['alipay_bill_sort_id'];
                } else {
                    $insert_data['name'] = $this->request->post['add_sort'];
                    $insert_data['fields'] = [[
                        'field' => '1',
                        'operation' => '0',
                        'type' => '2',
                        'keyword' => ['']
                    ]];

                    $alipay_bill_sort_id = $this->model_admin_billcheck->addAlipayBillSort($insert_data,implode(',',[$this->request->post['accounting_serial_number']]));
                    $insert_data['filter_alipay_bill_sort_id'] = $alipay_bill_sort_id;
                }
                $this->model_admin_billcheck->addAlipayBillToSore($insert_data,implode(',',[$this->request->post['accounting_serial_number']]));
                //计算统计数据
                $this->model_admin_billcheck->setAlipayBillStatisticTwo($insert_data,implode(',',[$this->request->post['accounting_serial_number']]));
            }

            $filter_data['filter_remark'] = $filter_remark;
            $filter_data['filter_business_type'] = $filter_business_type;
            $data['details'] = $this->model_admin_billcheck->getAlipayBillNotSortList($filter_data);

            $data['filter_remark'] = $filter_remark;
            $data['business_types'] = $this->model_admin_billcheck->getAlipayBillBusinessTypes();
            $data['business_type'] = $filter_business_type;

            $data['nofilter'] = $this->url->link('admin/billcheck/alipayBillSortListAdd', 'token=' . $this->session->data['token'].$url);
        }

        $data['action'] = $this->url->link('admin/billcheck/alipayBillSortListAdd', 'token=' . $this->session->data['token'].$url);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/alipay_bill_add_to_sort.tpl', $data));
    }

    /**
     * 支付宝账单-账单详情
     */
    public function alipayBillSortListDetail() {
        $this->validateGet();
        $url = '';
        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
            if (isset($this->request->get['store_id'])) {
                $filter_store = $this->request->get['store_id'];
            } else {
                $filter_store = '';
            }

            if (isset($this->request->get['month'])) {
                $filter_month = $this->request->get['month'];
            } else {
                $filter_month = '';
            }

            if (isset($this->request->get['alipay_bill_sort_id'])) {
                $filter_alipay_bill_sort_id = $this->request->get['alipay_bill_sort_id'];
            } else {
                $filter_alipay_bill_sort_id = '';
            }

            if (isset($this->request->get['filter_accounting_serial_number'])) {
                $filter_accounting_serial_number = $this->request->get['filter_accounting_serial_number'];
            } else {
                $filter_accounting_serial_number = '';
            }

            if (isset($this->request->get['filter_service_serial_number'])) {
                $filter_service_serial_number = $this->request->get['filter_service_serial_number'];
            } else {
                $filter_service_serial_number = '';
            }

            if (isset($this->request->get['filter_date_start'])) {
                $filter_date_start = $this->request->get['filter_date_start'];
            } else {
                $filter_date_start = '';
            }

            if (isset($this->request->get['filter_date_end'])) {
                $filter_date_end = $this->request->get['filter_date_end'];
            } else {
                $filter_date_end = '';
            }

            if (isset($this->request->get['page'])) {
                $page = $this->request->get['page'];
            } else {
                $page = 1;
            }

            if (isset($this->request->get['store_id'])) {
                $url .= '&store_id='.$this->request->get['store_id'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['month'])) {
                $url .= '&month='.$this->request->get['month'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['alipay_bill_sort_id'])) {
                $url .= '&alipay_bill_sort_id='.$this->request->get['alipay_bill_sort_id'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['filter_accounting_serial_number'])) {
                $url .= '&filter_accounting_serial_number='.$this->request->get['filter_accounting_serial_number'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['filter_service_serial_number'])) {
                $url .= '&filter_service_serial_number='.$this->request->get['filter_service_serial_number'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start='.$this->request->get['filter_date_start'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end='.$this->request->get['filter_date_end'];
            } else {
                $url .= '';
            }

            $this->load->model('admin/billcheck');
            $filter_data = array(
                'filter_store'		=> $filter_store,
                'filter_month'		=> $filter_month,
                'filter_alipay_bill_sort_id'=> $filter_alipay_bill_sort_id,
                'filter_accounting_serial_number' => $filter_accounting_serial_number,
                'filter_service_serial_number' => $filter_service_serial_number,
                'filter_date_start'	=> $filter_date_end ? $filter_date_start.' 00:00:00' : '',
                'filter_date_end'	=> $filter_date_end ? $filter_date_end.' 23:59:59' : '',
                'start'			=> ($page - 1) * $this->config->get('config_limit'),
                'limit'			=> $this->config->get('config_limit')
            );
            $data['details'] = $this->model_admin_billcheck->getAlipayBillSortDetail($filter_data);
            $total = $this->model_admin_billcheck->getAlipayBillSortDetailTotal($filter_data);

            $pagination = new Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $this->config->get('config_limit');
            $pagination->url = $this->url->link('admin/billcheck/alipayBillSortListDetail', 'token=' . $this->session->data['token'] . $url . '&page={page}');

            $pagination_template = $this->load->view('common/pagination.tpl');
            $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

            $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        }
        $data['filter_accounting_serial_number'] = $filter_accounting_serial_number;
        $data['filter_service_serial_number'] = $filter_service_serial_number;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['nofilter'] = $this->url->link('admin/billcheck/alipayBillSortListDetail', 'token=' . $this->session->data['token'].$url);
        $data['addCostAffiliation'] = $this->url->link('admin/billcheck/addAlipayBillCostAffiliation', 'token=' . $this->session->data['token']);
        $data['delSortGather'] = $this->url->link('admin/billcheck/delAlipayBillSortGather', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/alipay_bill_sort_detail.tpl', $data));
    }

    /**
     * 支付宝账单-添加费用归属（后台备注）
     */
    public function addAlipayBillCostAffiliation() {
        $json = array();

       if ($this->request->server['REQUEST_METHOD'] == 'POST' && !empty($this->request->post['accounting_serial_number']) && !empty($this->request->post['cost_affiliation'])) {
           $this->load->model('admin/billcheck');
           $this->model_admin_billcheck->editAlipayBillCostAffiliation($this->request->post);
           $json['success'] = true;
       } else {
           $json['error'] = true;
       }

        $this->response->setOutJson($json);
    }

    /**
     * 支付宝账单-从类别中删除
     */
    public function delAlipayBillSortGather() {
        $json = array();

        if ($this->request->server['REQUEST_METHOD'] == 'POST' && !empty($this->request->post['accounting_serial_number'])) {
            $this->load->model('admin/billcheck');
            $this->model_admin_billcheck->delAlipayBillSortGather($this->request->post['accounting_serial_number']);
            $json['success'] = true;
        } else {
            $json['error'] = true;
        }

        $this->response->setOutJson($json);
    }

    /**
     * 支付宝账单-删除已分类数据
     */
    public function alipayBillSortDeleteAdd() {
        $this->load->model('admin/billcheck');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm6()) {
            $this->model_admin_billcheck->addAlipayBillSortDel($this->request->post);
            $this->response->redirect($this->url->link('admin/billcheck/alipayBillSortList', 'token=' . $this->session->data['token']));
        }

        $data['stores'] = $this->model_admin_billcheck->getStores();

        $data['action'] = $this->url->link('admin/billcheck/alipayBillSortDeleteAdd', 'token=' . $this->session->data['token']);
        $data['cancel'] = $this->url->link('admin/billcheck/alipayBillSortExportList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/alipay_bill_sort_delete_form.tpl', $data));
    }

    /**
     * 快递费-上传文件列表
     */
    public function expressFeeUploadList() {
        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'date_added';
        }
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $filter_data = array(
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $data['file_lists'] = $this->model_admin_billcheck->getExpressFeeFileList($filter_data);
        $total = $this->model_admin_billcheck->getExpressFeeFileTotal($filter_data);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/expressFeeUploadList', 'token=' . $this->session->data['token']  . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['text'] = '快递费';
        $data['data_id'] = 'express_fee_file_id';

        $data['add'] = $this->url->link('admin/billcheck/expressFeeUpload', 'token=' . $this->session->data['token']);
        $data['delete'] = $this->url->link('admin/billcheck/expressFeeUploadDel', 'token=' . $this->session->data['token']);

        $data['status_list'] = [
            0 => '待处理',
            1 => '已下载',
            2 => '下载失败',
            3 => '解压成功',
            4 => '已导入'
        ];

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('billcheck/upload_list.tpl', $data));
    }

    /**
     * 快递费-删除上传文件
     */
    public function expressFeeUploadDel() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->post['selected']) && $this->validateDel()) {
            foreach ($this->request->post['selected'] as $express_fee_file_id) {
                $this->model_admin_billcheck->delExpressFeeUpload($express_fee_file_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $this->response->redirect($this->url->link('admin/billcheck/expressFeeUploadList', 'token=' . $this->session->data['token']));
        }

        $this->expressFeeUploadList();
    }

    /**
     * 快递费-上传文件
     */
    public function expressFeeUpload() {
        if (isset($this->request->post['bill_file']) && isset($this->request->post['bill_file_hash'])) {
            $this->load->model('admin/billcheck');
            $getAlipayBillFile = $this->model_admin_billcheck->getExpressFeeFile($this->request->post['bill_file']);
            if ($getAlipayBillFile > 0) {
                $json = array();
                $json['status'] = 2;
                $this->response->setOutJson($json);
            } else {
                $return = $this->model_admin_billcheck->addExpressFeeFile($this->request->post['bill_file'],$this->request->post['bill_file_hash'],$this->request->post['original_name']);
                if ($return) {
                    $json = array();
                    $json['status'] = 1;
                    $this->response->setOutJson($json);
                }
            }
        } else {
            $uptoken = QiNiu::getUploadToken();
            $data['gettoken'] = $uptoken;

            $data['text'] = '导入发货明细';
            $data['action'] = $this->url->link('admin/billcheck/expressFeeUpload', 'token=' . $this->session->data['token']);

            $data['header'] = $this->load->controller('admin/template/header');
            $data['content_top'] = $this->load->controller('admin/template/top');
            $data['content_bottom'] = $this->load->controller('admin/template/bottom');
            $data['footer'] = $this->load->controller('admin/template/footer');

            $this->response->setOutput($this->load->view('billcheck/upload_form.tpl', $data));
        }
    }

    /**
     * 快递费-快递费用报价列表
     */
    public function expressFeeStandardList() {
        $this->load->model('admin/billcheck');
        $data['standards'] = $this->model_admin_billcheck->getExpressFeeStandardAll();

        $data['add'] = $this->url->link('admin/billcheck/expressFeeStandardSet', 'token=' . $this->session->data['token']);
        $data['edit_url'] = $this->url->link('admin/billcheck/expressFeeStandardSet', 'token=' . $this->session->data['token'].'&express_fee_standard_id=');
        $data['detail_url'] = $this->url->link('admin/billcheck/expressFeeStandardDetail', 'token=' . $this->session->data['token'].'&express_fee_standard_id=');
        $data['add_province_url'] = $this->url->link('admin/billcheck/expressFeeStandardProvinceSet', 'token=' . $this->session->data['token'].'&express_fee_standard_id=');
        $data['copy_province_url'] = $this->url->link('admin/billcheck/expressFeeStandardSet', 'token=' . $this->session->data['token'].'&iscopy=1&express_fee_standard_id=');
        $data['delete'] = $this->url->link('admin/billcheck/expressFeeStandardDel', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_standard_list.tpl', $data));
    }

    /**
     * 快递费-添加快递费用报价
     */
    public function expressFeeStandardSet() {
        $this->load->model('admin/billcheck');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm2()) {
            if (!isset($this->request->get['express_fee_standard_id'])) {
                $this->model_admin_billcheck->addExpressFeeStandard($this->request->post);
            } else {
                if (!isset($this->request->get['iscopy'])) {
                    $this->model_admin_billcheck->editExpressFeeStandard($this->request->post,$this->request->get['express_fee_standard_id']);
                } else {
                    $this->model_admin_billcheck->copyExpressFeeStandard($this->request->post,$this->request->get['express_fee_standard_id']);
                }
            }
            $this->response->redirect($this->url->link('admin/billcheck/expressFeeStandardList', 'token=' . $this->session->data['token']));
        }

        if (!isset($this->request->get['express_fee_standard_id'])) {
            $data['text_form'] = $this->language->get('text_add');
        } else {
            $data['text_form'] = !isset($this->request->get['iscopy']) ? $this->language->get('text_edit') : '复制';
        }

        $data['all_template'] = $this->model_admin_billcheck->getExpressFeeTemplateAll();
        if (empty($data['all_template'])) {
            $this->error['warning'] = '请到快递模板添加快递公司';
        }

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        $data['num'] = 0;
        if (isset($this->request->get['express_fee_standard_id'])) {
            $expressFeeStandard = $this->model_admin_billcheck->getExpressFeeStandard($this->request->get['express_fee_standard_id']);
            if (empty($expressFeeStandard['express_fee_standard_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                $expressFeeStandard['weight'] = json_decode($expressFeeStandard['weight'],true);
                $data['num'] = count($expressFeeStandard['weight']) - 1;
                $data['expressFeeStandard'] = $expressFeeStandard;
            }
        }

        $data['provinces'] = $this->model_admin_billcheck->getExpressFeeProvince();

        if (!isset($this->request->get['express_fee_standard_id'])) {
            $data['action'] = $this->url->link('admin/billcheck/expressFeeStandardSet', 'token=' . $this->session->data['token']);
        } else {
            if (!isset($this->request->get['iscopy'])) {
                $data['action'] = $this->url->link('admin/billcheck/expressFeeStandardSet', 'token=' . $this->session->data['token'] . '&express_fee_standard_id=' . $this->request->get['express_fee_standard_id']);
            } else {
                $data['action'] = $this->url->link('admin/billcheck/expressFeeStandardSet', 'token=' . $this->session->data['token'] . '&iscopy=1&express_fee_standard_id=' . $this->request->get['express_fee_standard_id']);
            }
        }
        $data['cancel'] = $this->url->link('admin/billcheck/expressFeeStandardList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_standard_form.tpl', $data));
    }

    /**
     * 快递费-添加省份费用详情
     */
    public function expressFeeStandardProvinceSet() {
        $this->load->model('admin/billcheck');
        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->model_admin_billcheck->editExpressFeeStandardProvince($this->request->post,$this->request->get['express_fee_standard_id']);
            $this->response->redirect($this->url->link('admin/billcheck/expressFeeStandardList', 'token=' . $this->session->data['token']));
        }

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        if (empty($this->request->get['express_fee_standard_id'])) {
            $data['warning'] = '数据不存在';
        }

        if (!empty($this->request->get['express_fee_standard_id'])) {
            $expressFeeStandard = $this->model_admin_billcheck->getExpressFeeStandard($this->request->get['express_fee_standard_id']);
            if (empty($expressFeeStandard['express_fee_standard_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                $data['weight_json'] = $expressFeeStandard['weight'];
                $expressFeeStandard['weight'] = json_decode($expressFeeStandard['weight'],true);
                $expressFeeStandard['provinces'] = json_decode($expressFeeStandard['provinces'],true);
                if ($expressFeeStandard['provinces']) {
                    $data['num'] = count($expressFeeStandard['provinces']) - 1;
                    $data['standard_provinces_json'] = json_encode($expressFeeStandard['provinces']);
                } else {
                    $data['num'] = 0;
                    $data['standard_provinces_json'] = json_encode([]);
                }
                $data['expressFeeStandard'] = $expressFeeStandard;
                $data['template'] = $this->model_admin_billcheck->getExpressFeeTemplate($expressFeeStandard['name']);
            }
        }

        $data['provinces'] = $this->model_admin_billcheck->getExpressFeeProvince();
        $data['province_json'] = json_encode($data['provinces']);

        $data['action'] = $this->url->link('admin/billcheck/expressFeeStandardProvinceSet', 'token=' . $this->session->data['token'] . '&express_fee_standard_id=' . $this->request->get['express_fee_standard_id']);
        $data['cancel'] = $this->url->link('admin/billcheck/expressFeeStandardList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_standard_province_form.tpl', $data));
    }

    /**
     * 快递费-快递费用报价详情
     */
    public function expressFeeStandardDetail() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->get['express_fee_standard_id'])) {
            $expressFeeStandard = $this->model_admin_billcheck->getExpressFeeStandard($this->request->get['express_fee_standard_id']);
            if (empty($expressFeeStandard['express_fee_standard_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                $expressFeeStandard['weight'] = json_decode($expressFeeStandard['weight'],true);
                $expressFeeStandard['provinces'] = json_decode($expressFeeStandard['provinces'],true);
                $data['expressFeeStandard'] = $expressFeeStandard;
                $data['template'] = $this->model_admin_billcheck->getExpressFeeTemplate($expressFeeStandard['name']);
            }
        }
        $data['provinces'] = $this->model_admin_billcheck->getExpressFeeProvince();

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_standard_province_list.tpl', $data));
    }

    /**
     * 快递费-删除快递费用报价
     */
    public function expressFeeStandardDel() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->post['selected']) && $this->validateDel()) {
            foreach ($this->request->post['selected'] as $express_fee_standard_id) {
                $this->model_admin_billcheck->delExpressFeeStandard($express_fee_standard_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            $this->response->redirect($this->url->link('admin/billcheck/expressFeeStandardList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->expressFeeStandardList();
    }

    /**
     * 快递费-加价列表
     */
    public function expressFeeSurchargesList() {
        $this->load->model('admin/billcheck');
        $data['surchargess'] = $this->model_admin_billcheck->getExpressFeeSurchargesAll();

        $data['add'] = $this->url->link('admin/billcheck/expressFeeSurchargesSet', 'token=' . $this->session->data['token']);
        $data['edit_url'] = $this->url->link('admin/billcheck/expressFeeSurchargesSet', 'token=' . $this->session->data['token'].'&express_fee_surcharges_id=');
        $data['detail_url'] = $this->url->link('admin/billcheck/expressFeeSurchargesDetail', 'token=' . $this->session->data['token'].'&express_fee_surcharges_id=');
        $data['add_province_url'] = $this->url->link('admin/billcheck/expressFeeSurchargesProvinceSet', 'token=' . $this->session->data['token'].'&express_fee_surcharges_id=');
        $data['delete'] = $this->url->link('admin/billcheck/expressFeeSurchargesDel', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_surcharges_list.tpl', $data));
    }

    /**
     * 快递费-添加快递加价
     */
    public function expressFeeSurchargesSet() {
        $this->load->model('admin/billcheck');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm3()) {
            !isset($this->request->get['express_fee_surcharges_id']) ? $this->model_admin_billcheck->addExpressFeeSurcharges($this->request->post) : $this->model_admin_billcheck->editExpressFeeSurcharges($this->request->post,$this->request->get['express_fee_surcharges_id']);
            $this->response->redirect($this->url->link('admin/billcheck/expressFeeSurchargesList', 'token=' . $this->session->data['token']));
        }

        $data['text_form'] = !isset($this->request->get['express_fee_surcharges_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
        $data['all_template'] = $this->model_admin_billcheck->getExpressFeeTemplateAll();
        if (empty($data['all_template'])) {
            $this->error['warning'] = '请到快递模板添加快递公司';
        }

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        if (isset($this->request->get['express_fee_surcharges_id'])) {
            $expressFeeSurcharges = $this->model_admin_billcheck->getExpressFeeSurcharges($this->request->get['express_fee_surcharges_id']);
            if (empty($expressFeeSurcharges['express_fee_surcharges_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                $expressFeeSurcharges['provinces'] = json_decode($expressFeeSurcharges['provinces'],true);
                $data['expressFeeSurcharges'] = $expressFeeSurcharges;
                $data['template'] = $this->model_admin_billcheck->getExpressFeeTemplate($expressFeeSurcharges['name']);
            }
        }

        if (!isset($this->request->get['express_fee_surcharges_id'])) {
            $data['action'] = $this->url->link('admin/billcheck/expressFeeSurchargesSet', 'token=' . $this->session->data['token']);
        } else {
            $data['action'] = $this->url->link('admin/billcheck/expressFeeSurchargesSet', 'token=' . $this->session->data['token'] . '&express_fee_surcharges_id=' . $this->request->get['express_fee_surcharges_id']);
        }
        $data['cancel'] = $this->url->link('admin/billcheck/expressFeeSurchargesList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_surcharges_form.tpl', $data));
    }

    /**
     * 快递费-添加省份加价
     */
    public function expressFeeSurchargesProvinceSet() {
        $this->load->model('admin/billcheck');
        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->model_admin_billcheck->editExpressFeeSurchargesProvince($this->request->post,$this->request->get['express_fee_surcharges_id']);
            $this->response->redirect($this->url->link('admin/billcheck/expressFeeSurchargesList', 'token=' . $this->session->data['token']));
        }

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (empty($this->request->get['express_fee_surcharges_id'])) {
            $data['warning'] = '数据不存在';
        }

        if (!empty($this->request->get['express_fee_surcharges_id'])) {
            $expressFeeSurcharges = $this->model_admin_billcheck->getExpressFeeSurcharges($this->request->get['express_fee_surcharges_id']);
            if (empty($expressFeeSurcharges['express_fee_surcharges_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                $expressFeeSurcharges['provinces'] = json_decode($expressFeeSurcharges['provinces'], true);
                if ($expressFeeSurcharges['provinces']) {
                    $data['num'] = count($expressFeeSurcharges['provinces']) - 1;
                    $data['standard_provinces_json'] = json_encode($expressFeeSurcharges['provinces']);
                } else {
                    $data['num'] = 0;
                    $data['standard_provinces_json'] = json_encode([]);
                }
                $data['expressFeeSurcharges'] = $expressFeeSurcharges;
                $data['template'] = $this->model_admin_billcheck->getExpressFeeTemplate($expressFeeSurcharges['name']);
            }
        }

        $data['provinces'] = $this->model_admin_billcheck->getExpressFeeProvince();
        $data['province_json'] = json_encode($data['provinces']);

        $data['action'] = $this->url->link('admin/billcheck/expressFeeSurchargesProvinceSet', 'token=' . $this->session->data['token'] . '&express_fee_surcharges_id=' . $this->request->get['express_fee_surcharges_id']);
        $data['cancel'] = $this->url->link('admin/billcheck/expressFeeSurchargesList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_surcharges_province_form.tpl', $data));
    }

    /**
     * 快递费-快递加价详情
     */
    public function expressFeeSurchargesDetail() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->get['express_fee_surcharges_id'])) {
            $expressFeeSurcharges = $this->model_admin_billcheck->getExpressFeeSurcharges($this->request->get['express_fee_surcharges_id']);
            if (empty($expressFeeSurcharges['express_fee_surcharges_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                $expressFeeSurcharges['provinces'] = json_decode($expressFeeSurcharges['provinces'],true);
                $data['expressFeeSurcharges'] = $expressFeeSurcharges;
                $data['template'] = $this->model_admin_billcheck->getExpressFeeTemplate($expressFeeSurcharges['name']);
            }
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_surcharges_province_list.tpl', $data));
    }

    /**
     * 快递费-删除快递加价
     */
    public function expressFeeSurchargesDel() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->post['selected']) && $this->validateDel()) {
            foreach ($this->request->post['selected'] as $express_fee_surcharges_id) {
                $this->model_admin_billcheck->delExpressFeeSurcharges($express_fee_surcharges_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            $this->response->redirect($this->url->link('admin/billcheck/expressFeeSurchargesList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->expressFeeSurchargesList();
    }

    /**
     * 快递费-快递模板列表
     */
    public function expressFeeTemplateList() {
        $this->load->model('admin/billcheck');
        $data['templates'] = $this->model_admin_billcheck->getExpressFeeTemplateAll();

        $data['add'] = $this->url->link('admin/billcheck/expressFeeTemplateSet', 'token=' . $this->session->data['token']);
        $data['edit_url'] = $this->url->link('admin/billcheck/expressFeeTemplateSet', 'token=' . $this->session->data['token'].'&express_fee_template_id=');
        $data['delete'] = $this->url->link('admin/billcheck/expressFeeTemplateDel', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_template_list.tpl', $data));
    }

    /**
     * 快递费-添加快递模板
     */
    public function expressFeeTemplateSet() {
        $this->load->model('admin/billcheck');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm4()) {
            !isset($this->request->get['express_fee_template_id']) ? $this->model_admin_billcheck->addExpressFeeTemplate($this->request->post) : $this->model_admin_billcheck->editExpressFeeTemplate($this->request->post,$this->request->get['express_fee_template_id']);
            $this->response->redirect($this->url->link('admin/billcheck/expressFeeTemplateList', 'token=' . $this->session->data['token']));
        }

        $data['text_form'] = !isset($this->request->get['express_fee_template_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        if (isset($this->request->get['express_fee_template_id'])) {
            $expressFeeTemplate = $this->model_admin_billcheck->getExpressFeeTemplate($this->request->get['express_fee_template_id']);
            if (empty($expressFeeTemplate['express_fee_template_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                $expressFeeTemplate['setting'] = json_decode($expressFeeTemplate['setting'],true);
                $data['expressFeeTemplate'] = $expressFeeTemplate;
            }
        }

        if (!isset($this->request->get['express_fee_template_id'])) {
            $data['action'] = $this->url->link('admin/billcheck/expressFeeTemplateSet', 'token=' . $this->session->data['token']);
        } else {
            $data['action'] = $this->url->link('admin/billcheck/expressFeeTemplateSet', 'token=' . $this->session->data['token'] . '&express_fee_template_id=' . $this->request->get['express_fee_template_id']);
        }
        $data['cancel'] = $this->url->link('admin/billcheck/expressFeeTemplateList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_template_form.tpl', $data));
    }

    /**
     * 快递费-删除快递模板
     */
    public function expressFeeTemplateDel() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->post['selected']) && $this->validateDel()) {
            foreach ($this->request->post['selected'] as $express_fee_template_id) {
                $this->model_admin_billcheck->delExpressFeeTemplate($express_fee_template_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            $this->response->redirect($this->url->link('admin/billcheck/expressFeeTemplateList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->expressFeeTemplateList();
    }

    /**
     * 快递费-快递费列表
     */
    public function expressFeeList() {
        if (isset($this->request->get['filter_template_id'])) {
            $filter_template_id = $this->request->get['filter_template_id'];
        } else {
            $filter_template_id = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'delivery_date';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';
        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start='.$this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end='.$this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status='.$this->request->get['filter_status'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $filter_data = array(
            'filter_template_id' => $filter_template_id,
            'filter_date_start'	=> $filter_date_start ? date("Y-m-d",strtotime($filter_date_start)) : '',
            'filter_date_end'	=> $filter_date_end ? date("Y-m-d",strtotime($filter_date_end)) : '',
            'filter_status' => $filter_status,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $total = $this->model_admin_billcheck->getexpressFeeListAllTotal($filter_data);
        $data['express_fees'] = $this->model_admin_billcheck->getexpressFeeListAll($filter_data);

        $data['status_list'] = $this->model_admin_billcheck->getExpressFeeStatus();

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/expressFeeList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['nofilter'] = $this->url->link('admin/billcheck/expressFeeList', 'token=' . $this->session->data['token']);

        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_template_id'] = $filter_template_id;

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['templates'] = $this->model_admin_billcheck->getExpressFeeTemplateAll();
        $data['filter_status'] = $filter_status;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_list.tpl', $data));
    }

    /**
     * 快递费-快递费导出列表
     */
    public function expressFeeExportList() {
        if (isset($this->request->get['filter_template_id'])) {
            $filter_template_id = $this->request->get['filter_template_id'];
        } else {
            $filter_template_id = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'delivery_date';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';
        if (isset($this->request->get['filter_template_id'])) {
            $url .= '&filter_template_id='.$this->request->get['filter_template_id'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start='.$this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end='.$this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['filter_bill_key_id'])) {
            $url .= '&filter_bill_key_id='.$this->request->get['filter_bill_key_id'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $filter_data = array(
            'filter_template_id'=> $filter_template_id,
            'filter_date_start'	=> $filter_date_start ? date("Ym",strtotime($filter_date_start)) : '',
            'filter_date_end'	=> $filter_date_end ? date("Ym",strtotime($filter_date_end)) : '',
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $total = $this->model_admin_billcheck->getExpressFeeExportTotal($filter_data);
        $results = $data['exports'] = $this->model_admin_billcheck->getExpressFeeExportAll($filter_data);

        $data['templates'] = $this->model_admin_billcheck->getExpressFeeTemplateAll();
        $templates = array();

        foreach ($data['templates'] as $store) {
            $templates[$store['express_fee_template_id']] = $store['name'];
        }

        foreach ($results as $k => $v) {
            $export_templates = array_filter(explode(',',$v['templates']));
            $export_templates_name = [];
            foreach ($export_templates as $val) {
                $export_templates_name[] = $templates[$val];
            }
            $data['exports'][$k]['templates'] = $export_templates_name;
            $data['exports'][$k]['download'] = $this->url->link('admin/billcheck/expressFeeExportDownload', 'token=' . $this->session->data['token'] . '&express_fee_export_id='.$v['express_fee_export_id']);
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/expressFeeExportList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_template_id'] = $filter_template_id;

        $data['sort'] = $sort;
        $data['order'] = $order;
        $data['nofilter'] = $this->url->link('admin/billcheck/expressFeeExportList', 'token=' . $this->session->data['token']);
        $data['add'] = $this->url->link('admin/billcheck/expressFeeExportAdd', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_export_list.tpl', $data));
    }

    /**
     * 快递费-新增导出
     */
    public function expressFeeExportAdd() {
        $this->load->model('admin/billcheck');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm7()) {
            $this->model_admin_billcheck->addExpressFeeExport($this->request->post);
            $this->response->redirect($this->url->link('admin/billcheck/expressFeeExportList', 'token=' . $this->session->data['token']));
        }

        $data['templates'] = $this->model_admin_billcheck->getExpressFeeTemplateAll();

        $data['action'] = $this->url->link('admin/billcheck/expressFeeExportAdd', 'token=' . $this->session->data['token']);
        $data['cancel'] = $this->url->link('admin/billcheck/expressFeeExportList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/express_fee_export_form.tpl', $data));
    }

    /**
     * 快递费-导出下载
     */
    public function expressFeeExportDownload() {
        $file = DIR_DOWNLOAD . 'express_fee_export/快递费_'.$this->request->get['express_fee_export_id'].'.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }


    /**
     * 货款核对-上传文件列表
     */
    public function goodsPriceUploadList() {
        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'date_added';
        }
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $filter_data = array(
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $data['file_lists'] = $this->model_admin_billcheck->getGoodsPriceFileList($filter_data);
        $total = $this->model_admin_billcheck->getGoodsPriceFileTotal($filter_data);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/goodsPriceUploadList', 'token=' . $this->session->data['token']  . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['text'] = '货款';
        $data['data_id'] = 'goods_price_file_id';

        $data['add'] = $this->url->link('admin/billcheck/goodsPriceUpload', 'token=' . $this->session->data['token']);
        $data['delete'] = $this->url->link('admin/billcheck/goodsPriceUploadDel', 'token=' . $this->session->data['token']);

        $data['status_list'] = [
            0 => '待处理',
            1 => '已下载',
            2 => '下载失败',
            3 => '解压成功',
            4 => '已导入'
        ];

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('billcheck/upload_list.tpl', $data));
    }

    /**
     * 货款核对-删除上传文件
     */
    public function goodsPriceUploadDel() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->post['selected']) && $this->validateDel()) {
            foreach ($this->request->post['selected'] as $goods_price_file_id) {
                $this->model_admin_billcheck->delGoodsPriceUpload($goods_price_file_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $this->response->redirect($this->url->link('admin/billcheck/goodsPriceUploadList', 'token=' . $this->session->data['token']));
        }

        $this->goodsPriceUploadList();
    }

    /**
     * 货款核对-上传文件
     */
    public function goodsPriceUpload() {
        if (isset($this->request->post['bill_file']) && isset($this->request->post['bill_file_hash'])) {
            $this->load->model('admin/billcheck');
            $getAlipayBillFile = $this->model_admin_billcheck->getGoodsPriceFile($this->request->post['bill_file']);
            if ($getAlipayBillFile > 0) {
                $json = array();
                $json['status'] = 2;
                $this->response->setOutJson($json);
            } else {
                $return = $this->model_admin_billcheck->addGoodsPriceFile($this->request->post['bill_file'],$this->request->post['bill_file_hash'],$this->request->post['original_name']);
                if ($return) {
                    $json = array();
                    $json['status'] = 1;
                    $this->response->setOutJson($json);
                }
            }
        } else {
            $uptoken = QiNiu::getUploadToken();
            $data['gettoken'] = $uptoken;

            $data['text'] = '导入货款账单';
            $data['action'] = $this->url->link('admin/billcheck/goodsPriceUpload', 'token=' . $this->session->data['token']);

            $data['header'] = $this->load->controller('admin/template/header');
            $data['content_top'] = $this->load->controller('admin/template/top');
            $data['content_bottom'] = $this->load->controller('admin/template/bottom');
            $data['footer'] = $this->load->controller('admin/template/footer');

            $this->response->setOutput($this->load->view('billcheck/upload_form.tpl', $data));
        }
    }

    /**
     * 货款核对-工厂模板管理
     */
    public function goodsPriceTemplateList() {
        $this->load->model('admin/billcheck');
        $data['templates'] = $this->model_admin_billcheck->getGoodsPriceTemplateList();

        $data['add'] = $this->url->link('admin/billcheck/goodsPriceTemplateListSet', 'token=' . $this->session->data['token']);
        $data['edit_url'] = $this->url->link('admin/billcheck/goodsPriceTemplateListSet', 'token=' . $this->session->data['token'].'&goods_price_template_id=');
        $data['delete'] = $this->url->link('admin/billcheck/goodsPriceTemplateListDel', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/goods_price_template_list.tpl', $data));
    }

    /**
     * 货款核对-添加工厂模板
     */
    public function goodsPriceTemplateListSet() {
        $this->load->model('admin/billcheck');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm5()) {
            !isset($this->request->get['goods_price_template_id']) ? $this->model_admin_billcheck->addGoodsPriceTemplate($this->request->post) : $this->model_admin_billcheck->editGoodsPriceTemplate($this->request->post,$this->request->get['goods_price_template_id']);
            $this->response->redirect($this->url->link('admin/billcheck/goodsPriceTemplateList', 'token=' . $this->session->data['token']));
        }

        $data['text_form'] = !isset($this->request->get['goods_price_template_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        if (isset($this->request->get['goods_price_template_id'])) {
            $goodsPriceTemplate = $this->model_admin_billcheck->getGoodsPriceTemplate($this->request->get['goods_price_template_id']);
            if (empty($goodsPriceTemplate['goods_price_template_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                $goodsPriceTemplate['setting'] = json_decode($goodsPriceTemplate['setting'],true);
                $data['goodsPriceTemplate'] = $goodsPriceTemplate;
            }
        }

        if (!isset($this->request->get['goods_price_template_id'])) {
            $data['action'] = $this->url->link('admin/billcheck/goodsPriceTemplateListSet', 'token=' . $this->session->data['token']);
        } else {
            $data['action'] = $this->url->link('admin/billcheck/goodsPriceTemplateListSet', 'token=' . $this->session->data['token'] . '&goods_price_template_id=' . $this->request->get['goods_price_template_id']);
        }
        $data['cancel'] = $this->url->link('admin/billcheck/goodsPriceTemplateList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/goods_price_template_form.tpl', $data));
    }

    /**
     * 货款核对-删除工厂模板
     */
    public function goodsPriceTemplateListDel() {
        $this->load->model('admin/billcheck');
        if (isset($this->request->post['selected']) && $this->validateDel()) {
            foreach ($this->request->post['selected'] as $goods_price_template_id) {
                $this->model_admin_billcheck->delGoodsPriceTemplate($goods_price_template_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            $this->response->redirect($this->url->link('admin/billcheck/goodsPriceTemplateList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->goodsPriceTemplateList();
    }

    /**
     * 货款核对-货款列表
     */
    public function goodsPriceList() {
        if (isset($this->request->get['filter_template_id'])) {
            $filter_template_id = $this->request->get['filter_template_id'];
        } else {
            $filter_template_id = '';
        }
        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'check_date';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';
        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start='.$this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end='.$this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status='.$this->request->get['filter_status'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }


        $this->load->model('admin/setting');

        $filter_data = array(
            'filter_template_id' => $filter_template_id,
            'filter_date_start'	=> $filter_date_start ? date("Ym",strtotime($filter_date_start)) : '',
            'filter_date_end'	=> $filter_date_end ? date("Ym",strtotime($filter_date_end)) : '',
            'filter_status' => $filter_status,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $total = $this->model_admin_billcheck->getgoodsPriceListAllTotal($filter_data);
        $data['goods_prices'] = $this->model_admin_billcheck->getgoodsPriceListAll($filter_data);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/goodsPriceList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['templates'] = $this->model_admin_billcheck->getGoodsPriceTemplateList();

        $data['nofilter'] = $this->url->link('admin/billcheck/goodsPriceList', 'token=' . $this->session->data['token']. $url);

        $data['detail'] = $this->url->link('admin/billcheck/goodsPriceListDetail', 'token=' . $this->session->data['token']);
        $data['check'] = $this->url->link('admin/billcheck/goodsPriceListCheck', 'token=' . $this->session->data['token']);
        $data['update_import'] = $this->url->link('admin/billcheck/goodsPriceListUpdate', 'token=' . $this->session->data['token']);

        $data['filter_template_id'] = $filter_template_id;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;


        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['filter_status'] = $filter_status;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/goods_price_list.tpl', $data));
    }

    /**
     * 货款核对-修改总数量，总金额
     */
    public function goodsPriceListUpdate() {
        if (isset($this->request->post['quantity']) && isset($this->request->post['total_prices']) && isset($this->request->post['goods_price_statistics_id'])) {
            $this->load->model('admin/billcheck');
            $this->model_admin_billcheck->editGoodsPriceImport($this->request->post['goods_price_statistics_id'],$this->request->post['quantity'],$this->request->post['total_prices']);
        }
        $this->goodsPriceList();
    }

    /**
     * 货款核对-货款核对
     */
    public function goodsPriceListCheck() {
        $this->validateGet3();
        $url = '';
        $data['success'] = '';
        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
            if (isset($this->request->get['supplier'])) {
                $url .= '&supplier='.$this->request->get['supplier'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['check_date'])) {
                $url .= '&check_date='.$this->request->get['check_date'];
            } else {
                $url .= '';
            }

            if (isset($this->request->get['bsku'])) {
                $url .= '&bsku='.$this->request->get['bsku'];
            } else {
                $url .= '';
            }

            $this->load->model('admin/billcheck');
            $data['goodsPriceList'] = $this->model_admin_billcheck->getgoodsPriceList($this->request->get['supplier'],$this->request->get['check_date'],$this->request->get['bsku']);

            if (isset($this->request->post['selected']) && $data['goodsPriceList']['status']==2) {
                $getgoodsPriceListCheck = $this->model_admin_billcheck->getgoodsPriceListCheck($data['goodsPriceList']['name'],$this->request->get['check_date'],$this->request->get['bsku'],$this->request->post['selected']);

                if ($getgoodsPriceListCheck['quantity'] == $data['goodsPriceList']['quantity'] && $getgoodsPriceListCheck['total_prices'] == $data['goodsPriceList']['total_prices']) {
                    $this->model_admin_billcheck->getgoodsPriceCheck($this->request->get['supplier'],$this->request->get['check_date'],$this->request->get['bsku'],$this->request->post['selected']);
//                    $this->response->redirect($this->url->link('admin/billcheck/goodsPriceList', 'token=' . $this->session->data['token']));
                    $data['success'] = '核对成功';
                } else {
                    $data['warning'] = '数量，总金额不正确';
                }
            }
            $data['goodsPriceListStockin'] = $this->model_admin_billcheck->getgoodsPriceListStockin($data['goodsPriceList']['name'],$this->request->get['check_date'],$this->request->get['bsku']);
        }

        $data['action'] = $this->url->link('admin/billcheck/goodsPriceListCheck', 'token=' . $this->session->data['token'].$url);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/goods_price_list_check.tpl', $data));
    }

    /**
     * 货款核对-货款详情
     */
    public function goodsPriceListDetail() {
        $this->validateGet3();
        $url = '';
        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';

            $this->load->model('admin/billcheck');
            $data['details'] = $this->model_admin_billcheck->getGoodsPriceDetailList($this->request->get['supplier'],$this->request->get['check_date'],$this->request->get['bsku']);
            $data['template'] = $this->model_admin_billcheck->getGoodsPriceTemplate($this->request->get['supplier']);
            $data['check_date'] = $this->request->get['check_date'];

            $data['updateSku'] = $this->url->link('admin/billcheck/goodsPriceListUpdateSku', 'token=' . $this->session->data['token']);
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/goods_price_list_detail.tpl', $data));
    }

    /**
     * 货款核对-修改sku
     */
    public function goodsPriceListUpdateSku() {
        $json = array();
        if ($this->request->server['REQUEST_METHOD'] == 'POST' && !empty($this->request->post['sku_detail']) && !empty($this->request->post['sku'])) {
            $sku_detail = explode(',',$this->request->post['sku_detail']);
            $this->load->model('admin/billcheck');
            $this->model_admin_billcheck->editGoodsPriceSku($sku_detail[0],$sku_detail[1],$sku_detail[2],$sku_detail[3],$this->request->post['sku']);
            $json['success'] = true;
        } else {
            $json['error'] = true;
        }

        $this->response->setOutJson($json);
    }

    /**
     * 货款核对-快递费导出列表
     */
    public function goodsPriceExportList() {
        if (isset($this->request->get['filter_template_id'])) {
            $filter_template_id = $this->request->get['filter_template_id'];
        } else {
            $filter_template_id = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'delivery_date';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';
        if (isset($this->request->get['filter_template_id'])) {
            $url .= '&filter_template_id='.$this->request->get['filter_template_id'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start='.$this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end='.$this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['filter_bill_key_id'])) {
            $url .= '&filter_bill_key_id='.$this->request->get['filter_bill_key_id'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $filter_data = array(
            'filter_template_id'=> $filter_template_id,
            'filter_date_start'	=> $filter_date_start ? date("Ym",strtotime($filter_date_start)) : '',
            'filter_date_end'	=> $filter_date_end ? date("Ym",strtotime($filter_date_end)) : '',
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/billcheck');
        $total = $this->model_admin_billcheck->getGoodsPriceExportTotal($filter_data);
        $results = $data['exports'] = $this->model_admin_billcheck->getGoodsPriceExportAll($filter_data);

        $data['templates'] = $this->model_admin_billcheck->getGoodsPriceTemplateList();
        $templates = array();

        foreach ($data['templates'] as $store) {
            $templates[$store['goods_price_template_id']] = $store['name'];
        }

        foreach ($results as $k => $v) {
            $export_templates = array_filter(explode(',',$v['templates']));
            $export_templates_name = [];
            foreach ($export_templates as $val) {
                $export_templates_name[] = $templates[$val];
            }
            $data['exports'][$k]['templates'] = $export_templates_name;
            $data['exports'][$k]['download'] = $this->url->link('admin/billcheck/goodsPriceExportDownload', 'token=' . $this->session->data['token'] . '&goods_price_export_id='.$v['goods_price_export_id']);
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/billcheck/goodsPriceExportList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_template_id'] = $filter_template_id;

        $data['sort'] = $sort;
        $data['order'] = $order;
        $data['nofilter'] = $this->url->link('admin/billcheck/goodsPriceExportList', 'token=' . $this->session->data['token']);
        $data['add'] = $this->url->link('admin/billcheck/goodsPriceExportAdd', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/goods_price_export_list.tpl', $data));
    }

    /**
     * 货款核对-新增导出
     */
    public function goodsPriceExportAdd() {
        $this->load->model('admin/billcheck');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm7()) {
            $this->model_admin_billcheck->addGoodsPriceExport($this->request->post);
            $this->response->redirect($this->url->link('admin/billcheck/goodsPriceExportList', 'token=' . $this->session->data['token']));
        }

        $data['templates'] = $this->model_admin_billcheck->getGoodsPriceTemplateList();

        $data['action'] = $this->url->link('admin/billcheck/goodsPriceExportAdd', 'token=' . $this->session->data['token']);
        $data['cancel'] = $this->url->link('admin/billcheck/goodsPriceExportList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('billcheck/goods_price_export_form.tpl', $data));
    }

    /**
     * 货款核对-导出下载
     */
    public function goodsPriceExportDownload() {
        $file = DIR_DOWNLOAD . 'goods_price_export/货款_'.$this->request->get['goods_price_export_id'].'.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }


    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (!isset($this->request->post['name'])) {
            $this->error['warning'] = '类别不能为空！';
            return false;
        }
        if (!isset($this->request->post['sort'])) {
            $this->error['warning'] = '排序不能为空！';
            return false;
        }
        if (!isset($this->request->post['type'])) {
            $this->error['warning'] = '执行方式不能为空！';
            return false;
        }
        return !$this->error;
    }

    protected function validateGet() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (empty($this->request->get['alipay_bill_sort_id'])) {
            $this->error['warning'] = '类别不能为空！';
            return false;
        }
        if (empty($this->request->get['store_id'])) {
            $this->error['warning'] = '店铺不能为空！';
            return false;
        }
        if (empty($this->request->get['month'])) {
            $this->error['warning'] = '月份不能为空！';
            return false;
        }
        return !$this->error;
    }

    protected function validateGet2() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
//        if (empty($this->request->get['alipay_bill_sort_id'])) {
//            $this->error['warning'] = '类别不能为空！';
//            return false;
//        }
        if (empty($this->request->get['store_id'])) {
            $this->error['warning'] = '店铺不能为空！';
            return false;
        }
        if (empty($this->request->get['filter_date_start'])) {
            $this->error['warning'] = '起止时间不能为空！';
            return false;
        }
        if (empty($this->request->get['filter_date_end'])) {
            $this->error['warning'] = '起止时间不能为空！';
            return false;
        }
        return !$this->error;
    }

    protected function validateDel() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        return !$this->error;
    }

    protected function validateForm2() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (!isset($this->request->post['name'])) {
            $this->error['warning'] = '物流公司不能为空！';
            return false;
        }
        if (!isset($this->request->post['weight'])) {
            $this->error['warning'] = '重量区间不能为空！';
            return false;
        }
        if (empty($this->request->post['starting_weight'])) {
            $this->error['warning'] = '起步不能为空！';
            return false;
        }
        if (empty($this->request->post['first_weight'])) {
            $this->error['warning'] = '首重不能为空！';
            return false;
        }
        if (empty($this->request->post['continue_weight'])) {
            $this->error['warning'] = '续重不能为空！';
            return false;
        }
        if (empty($this->request->post['s_day'])) {
            $this->error['warning'] = '开始时间不能为空！';
            return false;
        }
        if (empty($this->request->post['e_day'])) {
            $this->error['warning'] = '结束时间不能为空！';
            return false;
        }
        if (!isset($this->request->post['type'])) {
            $this->error['warning'] = '材积重量不能为空！';
            return false;
        }
        return !$this->error;
    }

    protected function validateForm3() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (!isset($this->request->post['name'])) {
            $this->error['warning'] = '物流公司不能为空！';
            return false;
        }
        if (empty($this->request->post['s_day'])) {
            $this->error['warning'] = '开始时间不能为空！';
            return false;
        }
        if (empty($this->request->post['e_day'])) {
            $this->error['warning'] = '结束时间不能为空！';
            return false;
        }
        return !$this->error;
    }

    protected function validateForm4() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (!isset($this->request->post['name'])) {
            $this->error['warning'] = '物流公司不能为空！';
            return false;
        }
        if (empty($this->request->post['setting']['courier_number'])) {
            $this->error['warning'] = '快递单号不能为空！';
            return false;
        }
        if (empty($this->request->post['setting']['province'])) {
            $this->error['warning'] = '省不能为空！';
            return false;
        }
        if (empty($this->request->post['setting']['delivery_date'])) {
            $this->error['warning'] = '发货日期不能为空！';
            return false;
        }
        if (empty($this->request->post['setting']['weight'])) {
            $this->error['warning'] = '重量不能为空！';
            return false;
        }
        if (empty($this->request->post['setting']['price'])) {
            $this->error['warning'] = '快递费不能为空！';
            return false;
        }
        if (!isset($this->request->post['setting']['s_line'])) {
            $this->error['warning'] = '开始删除行数不能为空！';
            return false;
        }
        if (!isset($this->request->post['setting']['e_line'])) {
            $this->error['warning'] = '结尾删除行数不能为空！';
            return false;
        }
        return !$this->error;
    }

    protected function validateForm5() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (!isset($this->request->post['name'])) {
            $this->error['warning'] = '工厂不能为空！';
            return false;
        }
        if (empty($this->request->post['setting']['bsku'])) {
            $this->error['warning'] = '编码不能为空！';
            return false;
        }
        if (empty($this->request->post['setting']['quantity'])) {
            $this->error['warning'] = '数量不能为空！';
            return false;
        }
        if (empty($this->request->post['setting']['price'])) {
            $this->error['warning'] = '价格不能为空！';
            return false;
        }
        if (empty($this->request->post['setting']['total_prices'])) {
            $this->error['warning'] = '总价不能为空！';
            return false;
        }
        if (!isset($this->request->post['setting']['s_line'])) {
            $this->error['warning'] = '开始删除行数不能为空！';
            return false;
        }
        if (!isset($this->request->post['setting']['e_line'])) {
            $this->error['warning'] = '结尾删除行数不能为空！';
            return false;
        }
        return !$this->error;
    }

    protected function validateGet3() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (empty($this->request->get['supplier'])) {
            $this->error['warning'] = '工厂不能为空！';
            return false;
        }
        if (empty($this->request->get['check_date'])) {
            $this->error['warning'] = '结算日期不能为空！';
            return false;
        }
        if (empty($this->request->get['bsku'])) {
            $this->error['warning'] = '规格不能为空！';
            return false;
        }
        return !$this->error;
    }

    protected function validateForm6() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (!isset($this->request->post['stores'])) {
            $this->error['warning'] = '店铺不能为空！';
            return false;
        }
        if (!isset($this->request->post['date_start'])) {
            $this->error['warning'] = '账单时间不能为空！';
            return false;
        }
        if (!isset($this->request->post['date_end'])) {
            $this->error['warning'] = '账单时间不能为空！';
            return false;
        }
        return !$this->error;
    }

    protected function validateForm7() {
        if (!$this->user->hasPermission('modify', 'admin/billcheck')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (!isset($this->request->post['templates'])) {
            $this->error['warning'] = '快递不能为空！';
            return false;
        }
        if (!isset($this->request->post['date_start'])) {
            $this->error['warning'] = '账单时间不能为空！';
            return false;
        }
        if (!isset($this->request->post['date_end'])) {
            $this->error['warning'] = '账单时间不能为空！';
            return false;
        }
        return !$this->error;
    }

}
