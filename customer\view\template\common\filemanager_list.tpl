<style>
  .file-tags-right{
    height: 90px;
    border: 10px solid rgba(123,125,222,0);
    /* padding-left: 10px; */
    overflow-wrap: break-word;
    background-color: rgb(123 208 222 / 50%);
    color: #fff;
    border-radius: 5px;
  }

  .file-tags-right::after{
    height: 0;
    width: 0;
    content: '';
    position: absolute;
    border:10px solid transparent;
    border-right: 20px solid rgb(123 208 222 / 50%);
    top:0px;
    left: -40px;
  }
  .file-tags-right::before{
    height: 0;
    width: 0;
    content: '';
    position: absolute;
    top:10px;
    z-index:1;
    left: -55px;
  }

  .file-tags-left{
    height: 90px;
    border: 10px solid rgba(123,125,222,0);
    /* padding-left: 10px; */
    overflow-wrap: break-word;
    background-color: rgb(123 208 222 / 50%);
    color: #fff;
    border-radius: 5px;
  }

  .file-tags-left::after{
    height: 0;
    width: 0;
    content: '';
    position: absolute;
    border:10px solid transparent;
    border-left: 20px solid rgb(123 208 222 / 50%);
    top:0px;
    right: -40px;
  }
  .file-tags-left::before{
    height: 0;
    width: 0;
    content: '';
    position: absolute;
    top:10px;
    z-index:1;
    left: -55px;
  }
</style>
<div class="row mb-3">
  <div class="col-sm-6">
    <a href="<?php echo $parent; ?>" id="button-parent" data-bs-toggle="tooltip" title="上一级" class="btn btn-default"><i class="glyphicon glyphicon-level-up"></i></a>
    <a href="<?php echo $refresh; ?>" id="button-refresh" data-bs-toggle="tooltip" title="刷新" class="btn btn-default"><i class="glyphicon glyphicon-refresh"></i></a>
    <button type="button" data-bs-toggle="tooltip" title="上传文件" id="button-upload" class="btn btn-primary"><i class="glyphicon glyphicon-cloud-upload"></i></button>
    <button type="button" data-bs-toggle="tooltip" title="下载文件" id="button-download" class="btn btn-success"><i class="glyphicon glyphicon-cloud-download"></i></button>
    <button type="button" data-bs-toggle="tooltip" title="新建文件夹" id="button-folder" class="btn btn-light"><i class="glyphicon glyphicon-folder-open"></i></button>
    <button type="button" data-bs-toggle="tooltip" title="删除" id="button-delete" class="btn btn-danger"><i class="glyphicon glyphicon-trash"></i></button>
    <button type="button" data-bs-toggle="tooltip" title="全选" id="button-checkall" class="btn btn-default"><i class="glyphicon glyphicon-check"></i></button>
    <button type="button" data-bs-toggle="tooltip" title="添加标签" id="button-tags" class="btn btn-default"><i class="glyphicon glyphicon-tags"></i></button>
  </div>
  <div class="col-sm-6">
    <div class="input-group">
      <input type="hidden" name="filter_folder" value="<?php echo $filter_folder; ?>" id="input-folder" />
      <input type="text" name="filter_name" value="<?php echo $filter_name; ?>" placeholder="搜索关键词" id="input-search" class="form-control">
      <span class="input-group-btn">
        <button type="button" id="button-search" data-bs-toggle="tooltip" title="搜索" class="btn btn-primary"><i class="glyphicon glyphicon-search"></i></button>
      </span>
    </div>
  </div>
</div>
<div id="modal-folder" class="row mb-3" style="display: none;">
  <div class="col-sm-12">
    <div class="input-group">
      <input type="text" name="folder_name" value="" placeholder="请输入文件夹名称" id="input-filename" class="form-control">        
      <span class="input-group-btn">
        <button type="button" title="增加文件夹" id="button-create" class="btn btn-primary"><i class="glyphicon glyphicon-plus"></i></button>
      </span>
    </div>
  </div>
</div>


<div id="modal-tags" class="row mb-3" style="display: none;">
  <div class="col-sm-12">
    <div class="input-group">
      <select id="tags_name" class="form-control select-province"  name="tags_name[]" style="width: 80%;float: left;min-width:10em" multiple data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36" data-placeholder="请选择或输入标签">
        <?php if(!empty($default_tags)){ ?>
        <?php foreach ($default_tags as $tag) { ?>
        <option value="file_tags_id_<?php echo $tag['file_tags_id']; ?>"><?php echo $tag['name']; ?></option>
        <?php } ?>
        <?php } ?>
      </select>

      <span class="input-group-btn">
        <button type="button" title="添加标签" id="button-tags-create" class="btn btn-primary"><i class="glyphicon glyphicon-plus"></i></button>
      </span>
    </div>
  </div>
</div>
<?php if(!empty($parent_folders)){ ?>
<div id="parent-folders" style="width: 100%;text-align: left;margin-bottom: -15px">
  <a style="margin-left: 3px" href="<?php echo $parent_folders_url; ?>0" class="parent-folde">/根目录</a>
  <?php foreach($parent_folders as $parent_folder){ ?>
  <a style="margin-left: 3px" href="<?php echo $parent_folders_url; ?><?php echo $parent_folder['file_id']; ?>" class="parent-folde">/<?php echo $parent_folder['file_name']; ?></a>
  <?php } ?>
</div>
<?php } ?>
<hr />
<div class="row text-center">
  <div class="prompt-dialog-box" style="width: 300px;height: 50px;position: absolute;top: -50px;left: 50%;color:#21e720;z-index: 2;margin-left: -150px">
  </div>
  <?php if ($files) { ?>
  <?php foreach ($files as $file_key => $file) { ?>
    <div class="col-sm-3 mb-3">
      <div style="min-height: 100px;">
      <?php if ($file['is_folder']) { ?>
        <a href="<?php echo $file['href']; ?>" class="directory"><i style="font-size: 6em; color: #00a65a;" class="glyphicon glyphicon-folder-close hover-a"></i></a>
      <?php } elseif (!empty($file['thumb'])) { ?>
        <a href="<?php echo $file['href']; ?>" target="_blank"><img style="max-height: 90px" src="<?php echo $file['thumb']; ?>" class="img-thumbnail hover-a"/></a>
      <?php } else { ?>
        <a href="<?php echo $file['href']; ?>" target="_blank"><i style="font-size: 6em;" class="glyphicon glyphicon-file hover-a"></i></a>
      <?php } ?>
        <!--<i style="position: absolute;top: 70px;right: 70px;" class="glyphicon glyphicon-tags"></i>-->
        <?php if(!empty($file['tags'])) { ?>
        <?php if(($file_key+1) % 4 == 0){ ?>
        <div class="file-tags file-tags-left" style="top: 10px;left: -50px;position: absolute;z-index: 2;display: none">
          <p style="text-align: left;width: 70px">
            <?php foreach($file['tags'] as $tag) { ?>
            #<?php echo $tag; ?><br>
            <?php } ?>
          </p>
        </div>
        <?php }else{ ?>
        <div class="file-tags file-tags-right" style="top: 10px;right: -50px;position: absolute;z-index: 2;display: none">
          <p style="text-align: left;width: 70px">
            <?php foreach($file['tags'] as $tag) { ?>
            #<?php echo $tag; ?><br>
            <?php } ?>
          </p>
        </div>
        <?php } ?>
        <?php } ?>
      </div>
      <div class="form-group">
        <label title="<?php echo $file['file_name']; ?>" style="white-space: nowrap;text-overflow: ellipsis;overflow: hidden"><input type="checkbox" name="path[]" value="<?php echo $file['file_id']; ?>" /> <?php echo $file['file_name']; ?></label>
      </div>
    </div>
  <?php } ?>
  <?php } else { ?>
    <div class="col-sm-12 mb-3"><h3>没有找到任何文件</h3></div>
  <?php } ?>
</div>
<?php if ($pagination) { ?>
<div class="modal-footer"><?php echo $pagination; ?></div>
<?php } ?>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>
<style>
  .select2-container--krajee .select2-selection--multiple .select2-search--inline .select2-search__field {
    min-width: 10em;
  }
</style>
<script>
  $(document).ready(function() {
    var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
    window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","placeholder":"请选择或输入标签","language":"zh-CN",'tags':true};
    $.when($('#tags_name').select2(select2_5eaa6d36)).done(initS2Loading('tags_name','s2options_c4acac00'));

    $('.hover-a').hover(function(){
      // var mouseX = event.pageX;
      // var mouseY = event.pageY;
      // var mouseWidth = mouseX - ($(window).width() - mouseX) / 2;
      // var mouseHeight = mouseY - ($(window).height() - mouseY) / 2;
      // console.log('浏览器宽度：' + mouseWidth,'浏览器高度：' + mouseHeight,'鼠标X坐标：' + mouseX, '鼠标Y坐标：' + mouseY);
      $(this).parent().parent().find('.file-tags').css({
        "display": "block"
      })
      var lineHeight = $(this).parent().parent().find('.file-tags p').height() + 20;
      if (lineHeight > 90) {
        $(this).parent().parent().find('.file-tags').css({
          "height": lineHeight+"px"
        })
      }
    }, function(){
      // 鼠标离开时的代码
      $(this).parent().parent().find('.file-tags').css({
        "display": "none"
      })
    });
  });
</script>