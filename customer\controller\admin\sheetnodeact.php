<?php


class ControllerAdminSheetnodeact extends Controller
{
    private $error = array();
    private $userList = array();

    public function __construct(){
        $this->load->model('admin/sheetnodeact');
        $this->load->model('admin/sheetnode');
        $userList = $this->model_admin_sheetnodeact->userList();
        $this->userList = array_column($userList,null,'union_id');
    }

    public function nodeActList()
    {

        if (isset($this->request->get['filter_flow_id'])) {
            $filter_flow_id = $this->request->get['filter_flow_id'];
        } else {
            $filter_flow_id = 0;
        }


        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }


        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'create_time';
        }


        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

   

        if (isset($this->request->get['filter_flow_id'])) {
            $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

       

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }


        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        $data['add'] = $this->url->link('admin/sheetnodeact/sheetnodeact_add', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/sheetnodeact/sheetnodeact_del', 'token=' . $this->session->data['token'] . $url);




        $filter_data = array(
            'filter_name' => $filter_name,
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'filter_flow_id' => $filter_flow_id,
            'sort'              => $sort,
            'order'             => $order,
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );

        $results = $this->model_admin_sheetnodeact->getNodesAct($filter_data);


        $flowList = $this->model_admin_sheetnodeact->getFlowList();
        $flowListById = $flowList ? array_column($flowList,'flow_name','flow_id') : [];
        $nodeList = $this->model_admin_sheetnode->getNodes([]);
        $nodeListById = $nodeList ? array_column($nodeList,null,'node_id') : [];



        $data['flow_list'] = $flowList;

        $temp = [];

        foreach ($results as $k => $result) {
            $parent_node_id = $result['parent_node_id'];
            $child_node_id = $result['child_node_id'];

            if(isset($temp[$parent_node_id])){
                if($result['is_parallel'] == 1){
                    $temp[$parent_node_id]['parallel_child_node_name'][] =$nodeListById[$child_node_id]['node_name']; //并行节点  
                }else{
                    $temp[$parent_node_id]['child_node_name'][] = $nodeListById[$child_node_id]['node_name']; //串行节点
                }
            }else{
                if(isset($nodeListById[$child_node_id]['flow_id'])){
                    $temp[$parent_node_id]['flow_name'] = isset($nodeListById[$child_node_id]['flow_id']) ? $flowListById[$nodeListById[$child_node_id]['flow_id']] : '已删除'; //流程
                    $temp[$parent_node_id]['parent_node_id'] = $result['parent_node_id']; //流程
                    $temp[$parent_node_id]['flow_id'] = $nodeListById[$child_node_id]['flow_id']; //流程
                    $temp[$parent_node_id]['create_time'] = $result['create_time']; //流程
                    $temp[$parent_node_id]['parent_name'] = $result['parent_node_id'] == 0 ? '' : $nodeListById[$result['parent_node_id']]['node_name'];//父节点也就是当前环节
                    $temp[$parent_node_id]['child_node_name'][] = isset($nodeListById[$child_node_id]['node_name']) ? $nodeListById[$child_node_id]['node_name'] : '不存在'; //串行节点
                }
                
            }

        }

        $temp = array_values($temp);


        foreach($temp as $kK => $vv){
            $data['column'][$kK] = array(
                'flow_name'=>$vv['flow_name'],
                'parent_node_id' => $vv['parent_node_id'],
                'parent_name' =>  $vv['parent_name'],
                'child_node_name' =>  $vv['child_node_name']  ? implode(',',$vv['child_node_name']) : '',
                'parallel_child_node_name' =>  isset($vv['parallel_child_node_name']) ? implode(',',$vv['parallel_child_node_name']) : '',
                'entry' => date('Y-m-d H:i:s', $vv['create_time']),
                'edit' => $this->url->link('admin/sheetnodeact/sheetnodeact_edit', 'token=' . $this->session->data['token'] . '&parent_node_id=' . $vv['parent_node_id'] .'&flow_id=' . $vv['flow_id']. $url),
            );
        }


        $total = $this->model_admin_sheetnodeact->getTotalNodesAct($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_flow_id'])) {
            $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }


       


        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }





        $data['sort_name'] = $this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token'] . '&sort=name' . $url);
        $data['sort_createTime'] = $this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token'] . '&sort=createTime' . $url);
        $data['sort_sort'] = $this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token'] . '&sort=sort' . $url);
        $data['sort_type'] = $this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token'] . '&sort=' . $url);


        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_flow_id'] = $filter_flow_id;

        $data['nofilter'] = $this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('sheetnodeact/sheetnodeact_list.tpl', $data));
    }



    public function sheetnodeact_add()
    {

        $this->load->model('admin/sheetnode');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateFlowForm()) {
            $this->model_admin_sheetnodeact->addNodeAct($this->request->post);


            $this->session->data['success'] = '添加成功';

            $url = '';


            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }
    
            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }
    
            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }
    
            if (isset($this->request->get['filter_flow_id'])) {
                $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getNodeActForm();
    }

    public function sheetnodeact_edit()
    {

        $this->load->model('admin/sheetnode');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateFlowForm()) {
            $this->model_admin_sheetnodeact->editNodeAct($this->request->post);


            $this->session->data['success'] = $this->language->get('编辑成功');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }
    
            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }
    
            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }
    
            if (isset($this->request->get['filter_flow_id'])) {
                $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getNodeActForm();
    }

    public function sheetnodeact_del()
    {
        $this->load->model('admin/sheetnode');

        if (isset($this->request->post['selected']) ) {
            foreach ($this->request->post['selected'] as $parent_node_id) {
                $this->model_admin_sheetnodeact->deleteNodeAct(intval($parent_node_id));
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';
    

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }
    
            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }
    
            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }
    
            if (isset($this->request->get['filter_flow_id'])) {
                $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->nodeList();
    }


    protected function getNodeActForm()
    {

        $this->load->model('admin/sheetnode');
        $data['text_form'] = !isset($this->request->get['node_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_flow_id'])) {
            $url .= '&filter_flow_id=' . $this->request->get['filter_flow_id'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }
  

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }


        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        if (!isset($this->request->get['parent_node_id'])) {
            $data['action'] = $this->url->link('admin/sheetnodeact/sheetnodeact_add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/sheetnodeact/sheetnodeact_edit', 'token=' . $this->session->data['token'] . '&parent_node_id=' . $this->request->get['parent_node_id'] . $url);
        }


        $data['cancel'] = $this->url->link('admin/sheetnodeact/nodeActList', 'token=' . $this->session->data['token'] . $url);
        
        // 添加token到模板数据
        $data['token'] = $this->session->data['token'];

        if (isset($this->request->get['parent_node_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $results = $this->model_admin_sheetnodeact->getPareInfo($this->request->get['parent_node_id']);
            

            foreach ($results as $k => $result) {
                // 设置flow_id，确保能加载到正确的流程
                if (!isset($node_info['flow_id']) && isset($result['flow_id'])) {
                    $node_info['flow_id'] = $result['flow_id'];
                }
    
                if($result['is_parallel'] == 1){
                    $node_info['branches'][] = $result['child_node_id']; //并行节点  
                }else{
                    $node_info['child_node_id'][] = $result['child_node_id']; //串行节点
                }

                $node_info['parent_node_id'] = $result['parent_node_id'];
            }

        }

        if (isset($this->request->get['flow_id'])) {
            $data['flow_id'] = $this->request->get['flow_id'];
        } elseif (!empty($node_info) && isset($node_info['flow_id'])) {
            $data['flow_id'] = $node_info['flow_id'];
        } else {
            $data['flow_id'] = 0;
        }

        if (isset($this->request->get['parent_node_id'])) {
            $data['parent_node_id'] = $this->request->get['parent_node_id'];
        } elseif (!empty($node_info) && isset($node_info['parent_node_id'])) {
            $data['parent_node_id'] = $node_info['parent_node_id'];
        } else {
            $data['parent_node_id'] = 0;
        }

        
        // 初始化branches变量，避免模板中in_array函数报错
        if (isset($this->request->post['branches'])) {
            $data['branches'] = $this->request->post['branches'];
        } elseif (!empty($node_info) && isset($node_info['branches'])) {
            $data['branches'] =  $node_info['branches'];
        } else {
            $data['branches'] = array();
        }

        // print_r( $node_info);die;



        // 初始化branches变量，避免模板中in_array函数报错
        if (isset($this->request->post['child_node_id'])) {
             $data['child_node_id'] = $this->request->post['child_node_id'];
        } elseif (!empty($node_info) && isset($node_info['child_node_id'])) {
            $data['child_node_id'] = $node_info['child_node_id'];
        } else {
            $data['child_node_id'] = array();
        }

        // 初始化parent_node_id变量
        if (isset($this->request->get['parent_node_id'])) {
            $data['parent_node_id'] = $this->request->get['parent_node_id'];
        } elseif (!empty($node_info) && isset($node_info['parent_node_id'])) {
            $data['parent_node_id'] = $node_info['parent_node_id'];
        } else {
            $data['parent_node_id'] = 0;
        }





        $data['flow_list'] = $this->model_admin_sheetnode->getFlowList();
        
        // 编辑和添加页面加载节点列表的逻辑修改
        if (isset($this->request->get['parent_node_id']) || isset($this->request->get['node_id'])) {
            // 如果是编辑页面，使用flow_id获取节点列表
            if ($data['flow_id'] > 0) {
                $data['node_list'] = $this->model_admin_sheetnode->getNodes(['filter_flow_id' => $data['flow_id']]);
            } else {
                $data['node_list'] = array();
            }
        } else {
            // 添加页面，节点列表为空
            $data['node_list'] = array();
        }
        
        $data['nodeFlow'] = $this->url->link('admin/sheetnodeact/getNodesByFlow', 'token=' . $this->session->data['token']);


    



   

        $data['node_id'] = isset($this->request->get['node_id']) ?? 0;


        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('sheetnodeact/sheetnodeact_form.tpl', $data));

    }

    public function getNodesByFlow() {
        $this->load->model('admin/sheetnode');
        
        $json = array();
        
        if (isset($this->request->get['flow_id'])) {
            $flow_id = $this->request->get['flow_id'];
            $node_list = $this->model_admin_sheetnode->getNodes(['filter_flow_id' => $flow_id]);
            $json = $node_list;
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getNodeList(){
        $this->load->model('admin/sheetnode');
        $flow_id = $this->request->get['flow_id'];
        $node_list = $this->model_admin_sheetnode->getNodes(['filter_flow_id'=>$flow_id]);
        return $node_list;
    }

 
    protected function validateFlowForm()
    {
        if (!$this->user->hasPermission('modify', 'admin/sheetnode')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (empty($this->request->post['flow_id'])) {
            $this->error['warning'] = '流程未选!';
            return false;
        }



        if (empty($this->request->post['child_node_id'])) {
            $this->error['warning'] = '下一环节必填!';
            return false;
        }

      

        return !$this->error;
    }


       // 流程图
       public function nodeActImage(){
        $this->load->model('admin/sheetnode');
        $this->load->model('admin/sheetnodeact');
        $nodeList = $this->model_admin_sheetnode->getNodes(['filter_flow_id'=>2]);
        $nodeIds = $nodeList ? implode(',',array_column($nodeList,'node_id')) : 0;
        $nodeListById = array_column($nodeList,null,'node_id');
        $actList = $this->model_admin_sheetnodeact->getActList($nodeIds);

        foreach($actList as $key=>$val){
            $actList[$key]['node_name'] = $nodeListById[$val['child_node_id']]['node_name'] ?? '未设置';
            $actList[$key]['union_id'] = $nodeListById[$val['child_node_id']]['_union_id'] ?? '';
            $actList[$key]['day'] = $nodeListById[$val['child_node_id']]['day'] ?? '';

            if(strpos($nodeListById[$val['child_node_id']]['_union_id'],',') !== false){
                $temp = explode(',',$nodeListById[$val['child_node_id']]['_union_id']);
                $temp_arr = [];
                foreach($temp as $v){
                    $temp_arr[] = $this->userList[$v]['real_name'];
                }
                $actList[$key]['union_id'] = implode(',',$temp_arr);
                unset($temp);unset($temp_arr);
            }else{
                $actList[$key]['union_id'] = $this->userList[$nodeListById[$val['child_node_id']]['_union_id']]['real_name'];
            }
         

        }

        $data['actList'] = $actList; 

        $this->response->setOutput($this->load->view('sheetnode/sheetnodeact.html',$data));
    }

}
