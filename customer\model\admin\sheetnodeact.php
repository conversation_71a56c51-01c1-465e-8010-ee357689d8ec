<?php
class ModelAdminSheetnodeact extends Model {
    public function addNode($data) {
        $union_id = implode(',',$data['union_id']);
        $this->db->query("INSERT INTO " . "rg_node SET flow_id = '" . $this->db->escape($data['flow_id']) . "', node_name = '" . $data['name'] .  "', sort = '" . (int)$data['sort'] . "', _union_id = '" . $union_id ."', create_time = '" . time() . "', day = '" . (int)$data['day'] . "', is_delete = '0'");
 
        return $this->db->getLastId();
    }

    public function editFlow($node_id, $data) {
        $this->db->query("UPDATE " . "rg_node SET node_name = '" . $this->db->escape($data['name']) . "', node_sort = '" . (int)$data['sort'] . "' WHERE node_id = '" . (int)$node_id . "'");
    }

    public function deleteNode($node_id) {
        $this->db->query("UPDATE " . "rg_node SET is_delete = 1 WHERE node_id = '" . (int)$node_id . "'");
    }

    public function getFlowInfo($node_id) {
        $query = $this->db->query("SELECT * FROM " . "rg_node WHERE node_id = '" . (int)$node_id . "'");
        return $query->row;
    }

    public function userList($union_id = 0) {
        $where = 'status = 1 ';
        if($union_id) $where .=  " and union_id in($union_id) ";
        $query = $this->db->query("SELECT * FROM " . "_union WHERE status = 1");
        return $query->rows;
    }

    public function getNodesAct($data = array(), $sheet_id = 0)
    {
        $sql = "SELECT * FROM rg_node WHERE is_delete = 0";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  node_name LIKE '%" . $this->db->escape(trim($data['filter_name'])) . "%'";
        }

        if (!empty($data['filter_flow_id'])) {
            $sql .= " AND flow_id = '" . $this->db->escape($data['filter_flow_id']) . "'";
        }

        $sort_data = array(
            'node_name',
            'node_sort',
            'create_time'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY sort";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }


        $query = $this->db->query($sql);

        $nodelist =  $query->rows;

        $nodeIds = $nodelist ? implode(',',array_column($nodelist,'node_id')) : 0;

        $sql = "SELECT * FROM " . "rg_node_relations WHERE is_delete = 0 and (child_node_id in($nodeIds) or parent_node_id in($nodeIds)) ";




        if (!empty($data['filter_date_start'])) {
            $sql .= " AND create_time >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND create_time <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }

        $query = $nodeIds ?  $this->db->query($sql) : [];




        if($query){
           return  $query->rows;
        }else{
            return  [];
        }

        return $query->rows;

    }



    public function getActList($node_ids = 0)
    {
        $sql = "SELECT * FROM rg_node_relations WHERE is_delete = 0 ";

        if($node_ids){
            $sql .= " AND child_node_id in($node_ids) ";
        }else{
            $sql .= " AND child_node_id = 0 ";
        } 
        
        $query = $this->db->query($sql);

        return $query->rows;
    }


    public function getFlowList() {
        $query = $this->db->query("SELECT * FROM rg_flow WHERE is_delete = 0");
        return $query->rows;
    }

    public function getTotalNodesAct($data)
    {
        $sql = "SELECT * FROM  rg_node WHERE is_delete = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  node_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }


        if (!empty($data['filter_date_start'])) {
            $sql .= " AND create_time >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND create_time <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }

        if (!empty($data['filter_flow_id'])) {
            $sql .= " AND flow_id = '" . $this->db->escape($data['filter_flow_id']) . "'";
        }



        $query = $this->db->query($sql);


        $nodelist =  $query->rows;

        $nodeIds = $nodelist ? implode(',',array_column($nodelist,'node_id')) : 0;

        $query =  $this->db->query("SELECT parent_node_id  FROM " . "rg_node_relations WHERE child_node_id in($nodeIds) and is_delete = 0 GROUP BY parent_node_id ");

        return count($query->rows);
    }


    public function getPareInfo($parent_node_id)  {
        $sql = "SELECT r.*, n.flow_id 
                FROM rg_node_relations r 
                LEFT JOIN rg_node n ON r.child_node_id = n.node_id 
                WHERE r.parent_node_id = $parent_node_id AND r.is_delete = 0";
        $query = $this->db->query($sql);
        return $query->rows;
    }


    public function addNodeAct($data=array()) {
        $child_node_id = $data['child_node_id'];
        $branches = $data['branches'] ?? [];
        $sj = time();

        if($child_node_id){
            foreach($child_node_id as $v){
                $this->db->query("INSERT INTO rg_node_relations SET create_time = '" . $sj . "', parent_node_id = '" . (int)$data['parent_node_id'] . "', child_node_id = '" . (int)$v . "', is_delete = '0'");
            }

        }

        if($branches){
            foreach($branches as $branch){
                $this->db->query("INSERT INTO rg_node_relations SET is_parallel = 1, create_time = '" . $sj . "', parent_node_id = '" . (int)$data['parent_node_id'] . "', child_node_id = '" . (int)$branch . "', is_delete = '0'");
            }
        }

      

    }

    public function editNodeAct($data=array()) {

        $child_node_id = $data['child_node_id'];
        $branches = isset($data['branches']) ? $data['branches'] : [];
        $sj = time();

        if($child_node_id){
            $this->db->query("update  rg_node_relations set is_delete = 1  WHERE is_parallel = 0 and parent_node_id = '" . (int)$data['parent_node_id'] . "'");
            foreach($child_node_id as $v){
                $this->db->query("INSERT INTO rg_node_relations SET create_time = '" . $sj . "', parent_node_id = '" . (int)$data['parent_node_id'] . "', child_node_id = '" . (int)$v . "', is_delete = '0'");
            }

        }

        if($branches){
            $this->db->query("update  rg_node_relations set is_delete = 1  WHERE is_parallel = 1 and parent_node_id = '" . (int)$data['parent_node_id'] . "'");
            foreach($branches as $branch){
                $this->db->query("INSERT INTO rg_node_relations SET is_parallel = 1,create_time = '" . $sj . "', parent_node_id = '" . (int)$data['parent_node_id'] . "', child_node_id = '" . (int)$branch . "', is_delete = '0'");
            }
        }



    }

    public function deleteNodeAct($parent_node_id){
        $this->db->query("update  rg_node_relations set is_delete = 1  WHERE is_parallel = 1 and parent_node_id = '" . (int)$parent_node_id . "'");
        $this->db->query("update  rg_node_relations set is_delete = 1  WHERE is_parallel = 0 and parent_node_id = '" . (int)$parent_node_id . "'");
    }

    




 
} 