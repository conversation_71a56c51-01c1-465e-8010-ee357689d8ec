<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        设置收费标准-<?php if(!empty($template['name'])){ ?><?php echo $template['name']; ?><?php } ?><br>日期：<?php if(!empty($expressFeeStandard['s_day'])){ ?><?php echo $expressFeeStandard['s_day']; ?><?php } ?>-<?php if(!empty($expressFeeStandard['e_day'])){ ?><?php echo $expressFeeStandard['e_day']; ?><?php } ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-mode"></label>
              <div class="box-tools" style="width: 15%;float: left;margin-left: 20px">
                <a class="btn btn-primary" href="javascript:addprovince();">添加省份</a>
              </div>
            </div>
            <div class="all-provinces-condition">
              <?php if(!empty($expressFeeStandard['provinces'])){ ?>
                <?php foreach ($expressFeeStandard['provinces'] as $tandard_province_key=>$tandard_province) { ?>
                  <div class="provinces-condition" style="padding-top: 20px;border-top: 1px solid #f39c12;position: relative">
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-mode">选择省份：</label>
                      <div class="col-sm-8">
                        <select id="w1-<?php echo $tandard_province_key; ?>" class="form-control select-province"  name="provinces[<?php echo $tandard_province_key; ?>][province][]" style="width: 80%;float: left " multiple data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">
                          <?php foreach ($provinces as $key=>$province) { ?>
                          <option value="<?php echo $key; ?>"><?php echo $province; ?></option>
                          <?php } ?>
                        </select>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-quan">使用重量区间：</label>
                      <div class="col-sm-8">
                        <div class="radio">
                          <label><input type="radio" name="provinces[<?php echo $tandard_province_key; ?>][weight_type]" <?php if(!empty($tandard_province['weight_type']) && $tandard_province['weight_type']==1){ ?>checked<?php } ?> onchange="weight_type($(this),<?php echo $tandard_province_key; ?>)" value="1" checked>是</label>
                          <label style="margin-left: 10px"><input type="radio" name="provinces[<?php echo $tandard_province_key; ?>][weight_type]" <?php if(!empty($tandard_province['weight_type']) && $tandard_province['weight_type']==2){ ?>checked<?php } ?> onchange="weight_type($(this),<?php echo $tandard_province_key; ?>)" value="2">否</label>
                        </div>
                      </div>
                    </div>
                    <div class="type-1-<?php echo $tandard_province_key; ?>" <?php if(!empty($tandard_province['weight_type']) && $tandard_province['weight_type']==2){ ?>style="display: none"<?php } ?>>
                      <?php foreach($expressFeeStandard['weight'] as $weight_key => $weight){ ?>
                        <div class="form-group">
                          <label class="col-sm-2 control-label" for="input-quan"><?php echo $weight[0]; ?>KG - <?php echo $weight[1]; ?>KG：</label>
                          <div class="col-sm-8 keyword">
                            <div>
                              <input type="text" name="provinces[<?php echo $tandard_province_key; ?>][price][<?php echo $weight_key; ?>]" value="<?php if(!empty( $tandard_province['price'][$weight_key])){ ?><?php echo $tandard_province['price'][$weight_key]; ?><?php } ?>" placeholder="请输入价格" class="form-control" style="width: 70%;float: left" />
                            </div>
                          </div>
                        </div>
                      <?php } ?>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-quan">首重计算方式：</label>
                      <div class="col-sm-8">
                        <div class="radio">
                          <label><input type="radio" name="provinces[<?php echo $tandard_province_key; ?>][starting_price_type]" <?php if(!empty($tandard_province['starting_price_type']) && $tandard_province['starting_price_type']==1){ ?>checked<?php } ?> onchange="starting_price_type($(this),<?php echo $tandard_province_key; ?>)" value="1" checked>默认</label>
                          <label style="margin-left: 10px"><input type="radio" name="provinces[<?php echo $tandard_province_key; ?>][starting_price_type]" <?php if(!empty($tandard_province['starting_price_type']) && $tandard_province['starting_price_type']==2){ ?>checked<?php } ?> onchange="starting_price_type($(this),<?php echo $tandard_province_key; ?>)" value="2">超过指定重量，首重价格按续重价格结算</label>
                        </div>
                      </div>
                    </div>
                    <div class="form-group first-follow-weight-<?php echo $tandard_province_key; ?>" <?php if(!empty($tandard_province['starting_price_type']) && $tandard_province['starting_price_type']==1){ ?>style="display: none"<?php } ?>>
                      <label class="col-sm-2 control-label" for="input-quan">首重续重同价重量：</label>
                      <div class="col-sm-8 keyword">
                        <div>
                          <input type="text" name="provinces[<?php echo $tandard_province_key; ?>][first_follow_weight]" value="<?php if(!empty($tandard_province['first_follow_weight'])){ ?><?php echo $tandard_province['first_follow_weight']; ?><?php } ?>" placeholder="请输入首重续重同价重量" class="form-control" style="width: 70%;float: left" />
                        </div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-quan">首重价格：</label>
                      <div class="col-sm-8 keyword">
                        <div>
                          <input type="text" name="provinces[<?php echo $tandard_province_key; ?>][starting_price][0]" value="<?php echo $tandard_province['starting_price'][0]; ?>" placeholder="请输入首重价格" class="form-control" style="width: 70%;float: left" />
                        </div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-quan">续重价格：</label>
                      <div class="col-sm-8 keyword">
                        <div>
                          <input type="text" name="provinces[<?php echo $tandard_province_key; ?>][starting_price][1]" value="<?php echo $tandard_province['starting_price'][1]; ?>" placeholder="请输入续重价格" class="form-control" style="width: 70%;float: left" />
                        </div>
                      </div>
                    </div>
                    <a class="btn btn-danger" href="javascript:;" onclick="delprovince($(this))" style="position: absolute;float: left;top: 25px;left: 50px">删除省份</a>
                  </div>
                <?php } ?>
              <?php } else { ?>
                <div class="provinces-condition" style="padding-top: 20px;border-top: 1px solid #f39c12;position: relative">
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-mode">选择省份：</label>
                    <div class="col-sm-8">
                      <select id="w1-0" class="form-control select-province"  name="provinces[0][province][]" style="width: 80%;float: left " multiple data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">
                        <?php foreach ($provinces as $key=>$province) { ?>
                        <option value="<?php echo $key; ?>"><?php echo $province; ?></option>
                        <?php } ?>
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-quan">使用重量区间：</label>
                    <div class="col-sm-8">
                      <div class="radio">
                        <label><input type="radio" name="provinces[0][weight_type]" onchange="weight_type($(this),0)" value="1" checked>是</label>
                        <label style="margin-left: 10px"><input type="radio" name="provinces[0][weight_type]" onchange="weight_type($(this),0)" value="2">否</label>
                      </div>
                    </div>
                  </div>
                  <div class="type-1-0">
                    <?php foreach($expressFeeStandard['weight'] as $weight_key => $weight){ ?>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-quan"><?php echo $weight[0]; ?>KG - <?php echo $weight[1]; ?>KG：</label>
                      <div class="col-sm-8 keyword">
                        <div>
                          <input type="text" name="provinces[0][price][<?php echo $weight_key; ?>]" value="" placeholder="请输入价格" class="form-control" style="width: 70%;float: left" />
                        </div>
                      </div>
                    </div>
                    <?php } ?>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-quan">首重计算方式：</label>
                    <div class="col-sm-8">
                      <div class="radio">
                        <label><input type="radio" name="provinces[0][starting_price_type]" onchange="starting_price_type($(this),0)" value="1" checked>默认</label>
                        <label style="margin-left: 10px"><input type="radio" name="provinces[0][starting_price_type]" onchange="starting_price_type($(this),0)" value="2">超过指定重量，首重价格按续重价格结算</label>
                      </div>
                    </div>
                  </div>
                  <div class="form-group first-follow-weight-0" style="display: none">
                    <label class="col-sm-2 control-label" for="input-quan">首重续重同价重量：</label>
                    <div class="col-sm-8 keyword">
                      <div>
                        <input type="text" name="provinces[0][first_follow_weight]" value="" placeholder="请输入首重续重同价重量" class="form-control" style="width: 70%;float: left" />
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-quan">首重价格：</label>
                    <div class="col-sm-8 keyword">
                      <div>
                        <input type="text" name="provinces[0][starting_price][0]" value="" placeholder="请输入首重价格" class="form-control" style="width: 70%;float: left" />
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-quan">续重价格：</label>
                    <div class="col-sm-8 keyword">
                      <div>
                        <input type="text" name="provinces[0][starting_price][1]" value="" placeholder="请输入续重价格" class="form-control" style="width: 70%;float: left" />
                      </div>
                    </div>
                  </div>

                </div>
              <?php } ?>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>

<script>
  var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
  window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","placeholder":"请选择省份","language":"zh-CN"};

  var mycars=new Array()
  var num = parseInt('<?php echo $num; ?>');
  var standard_provinces_json = '<?php echo $standard_provinces_json; ?>'
  for (i=0;i<=num;i++){
    if ($('#w1-'+i).data('select2')) { $('#w1-'+i).select2('destroy'); }
    $.when($('#w1-'+i).select2(select2_5eaa6d36)).done(initS2Loading('w1-'+i,'s2options_c4acac00'));
  }

  var standard_provinces_arr = $.parseJSON(standard_provinces_json)
  //默认选中省份
  if (standard_provinces_arr.length > 0) {
    $.each(standard_provinces_arr, function(index, value) {
      if (value['province']) {
        $("#w1-"+index).val(value['province']).trigger("change");
        $.merge(mycars, value['province']);
      }
    });

    $.each(mycars, function(id, value) {
      $('select option[value="'+value+'"]').attr('disabled', true);
      //监听选中事件
      $('#w1-'+id).on('select2:select', function (e) {
        mycars.push(e.params.data.id)
        refresh_select(e.params.data.id,1)
      });

      //监听删除选中事件
      $('#w1-'+id).on('select2:unselect', function (e) {
        $.each(mycars, function(index, value) {
          if (value === e.params.data.id) {
            mycars.splice(index, 1); // 删除指定的值
          }
        });
        refresh_select(e.params.data.id,2)
      });
    });
  } else {
    //监听选中事件
    $('#w1-0').on('select2:select', function (e) {
      mycars.push(e.params.data.id)
      refresh_select(e.params.data.id,1)
    });

    //监听删除选中事件
    $('#w1-0').on('select2:unselect', function (e) {
      $.each(mycars, function(index, value) {
        if (value === e.params.data.id) {
          mycars.splice(index, 1); // 删除指定的值
        }
      });
      refresh_select(e.params.data.id,2)
    });
  }

  var weight_json = '<?php echo $weight_json; ?>'
  var province_json = '<?php echo $province_json; ?>'
  function addprovince() {
    num += 1;
    var addHtml ='<div class="provinces-condition" style="padding-top: 20px;border-top: 1px solid #f39c12;position: relative">'+
            '<div class="form-group">'+
            '<label class="col-sm-2 control-label" for="input-mode">选择省份：</label>'+
            '<div class="col-sm-8">'+
            '<select id="w1-'+num+'" class="form-control select-province"  name="provinces['+num+'][province][]" style="width: 80%;float: left " multiple data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">'

            $.each($.parseJSON(province_json), function(i,val){
              addHtml += '<option value="'+i+'">'+val+'</option>'
            });

        addHtml +='</select>'+
            '</div>'+
            '</div>'+
            '<div class="form-group">'+
            '<label class="col-sm-2 control-label" for="input-quan">使用重量区间：</label>'+
            '<div class="col-sm-8">'+
            '<div class="radio">'+
            '<label><input type="radio" name="provinces['+num+'][weight_type]" onchange="weight_type($(this),'+num+')" value="1" checked>是</label>'+
            '<label style="margin-left: 10px"><input type="radio" name="provinces['+num+'][weight_type]" onchange="weight_type($(this),'+num+')" value="2">否</label>'+
            '</div>'+
            '</div>'+
            '</div>'+
            '<div class="type-1-'+num+'">'

            $.each($.parseJSON(weight_json), function(i,val){
              addHtml += '<div class="form-group">'+
                      '<label class="col-sm-2 control-label" for="input-quan">'+val[0]+'KG - '+val[1]+'KG：</label>'+
                      '<div class="col-sm-8 keyword">'+
                      '<div>'+
                      '<input type="text" name="provinces['+num+'][price]['+i+']" value="" placeholder="请输入价格" class="form-control" style="width: 70%;float: left" />'+
                      '</div>'+
                      '</div>'+
                      '</div>'
            });

        addHtml += '</div>'+
                    '<div class="form-group">'+
                    '<label class="col-sm-2 control-label" for="input-quan">首重计算方式：</label>'+
                    '<div class="col-sm-8">'+
                    '<div class="radio">'+
                    '<label><input type="radio" name="provinces['+num+'][starting_price_type]" onchange="starting_price_type($(this),'+num+')" value="1" checked>默认</label>'+
                    '<label style="margin-left: 10px"><input type="radio" name="provinces['+num+'][starting_price_type]" onchange="starting_price_type($(this),'+num+')" value="2">超过指定重量，首重价格按续重价格结算</label>'+
                    '</div>'+
                    '</div>'+
                    '</div>'+
                    '<div class="form-group first-follow-weight-'+num+'" style="display: none">'+
                    '<label class="col-sm-2 control-label" for="input-quan">首重续重同价重量：</label>'+
                    '<div class="col-sm-8 keyword">'+
                    '<div>'+
                    '<input type="text" name="provinces['+num+'][first_follow_weight]" value="" placeholder="请输入首重续重同价重量" class="form-control" style="width: 70%;float: left" />'+
                    '</div>'+
                    '</div>'+
                    '</div>'+
                    '<div class="form-group">'+
                    '<label class="col-sm-2 control-label" for="input-quan">首重价格：</label>'+
                    '<div class="col-sm-8 keyword">'+
                    '<div>'+
                    '<input type="text" name="provinces['+num+'][starting_price][0]" value="" placeholder="请输入首重价格" class="form-control" style="width: 70%;float: left" />'+
                    '</div>'+
                    '</div>'+
                    '</div>'+
                    '<div class="form-group">'+
                    '<label class="col-sm-2 control-label" for="input-quan">续重价格：</label>'+
                    '<div class="col-sm-8 keyword">'+
                    '<div>'+
                    '<input type="text" name="provinces['+num+'][starting_price][1]" value="" placeholder="请输入续重价格" class="form-control" style="width: 70%;float: left" />'+
                    '</div>'+
                    '</div>'+
                    '</div>'+
                    '<a class="btn btn-danger" href="javascript:;" onclick="delprovince($(this))" style="position: absolute;float: left;top: 25px;left: 50px">删除省份</a>'+
                    '</div>';
    $(".all-provinces-condition").prepend(addHtml);

    $.each(mycars, function(index, value) {
      $('#w1-'+num+' option[value="'+value+'"]').attr('disabled', true);
    });

    if ($('#w1-'+num).data('select2')) { $('#w1-'+num).select2('destroy'); }
    $.when($('#w1-'+num).select2(select2_5eaa6d36)).done(initS2Loading('w1-'+num,'s2options_c4acac00'));

    //监听选中事件
    $('#w1-'+num).on('select2:select', function (e) {
      mycars.push(e.params.data.id)
      refresh_select(e.params.data.id,1)
    });

    //监听删除选中事件
    $('#w1-'+num).on('select2:unselect', function (e) {
      $.each(mycars, function(index, value) {
        if (value === e.params.data.id) {
          mycars.splice(index, 1); // 删除指定的值
        }
      });
      refresh_select(e.params.data.id,2)
    });

  }

  function delprovince(obj) {
    $(obj).parent().remove();
  }

  function refresh_select(id,type) {
    if (type == 1) {
      $('select option[value="'+id+'"]').attr('disabled', true);
    } else if (type == 2) {
      $('select option[value="'+id+'"]').attr('disabled', false);
    }

    for (i=0;i<=num;i++){
      if ($('#w1-'+i).data('select2')) { $('#w1-'+i).select2('destroy'); }
      $.when($('#w1-'+i).select2(select2_5eaa6d36)).done(initS2Loading('w1-'+i,'s2options_c4acac00'));
    }
  }


  function toVaild() {
    //解除禁止选取,
    $.each(mycars, function(index, value) {
      $('select option[value="'+value+'"]').attr('disabled', false);
    });

    return true;
  }

  function weight_type(obj,thisnum) {
    if (obj.val() == 1) {
      $(".type-1-"+thisnum).show()
    } else {
      $(".type-1-"+thisnum).hide()
    }
  }

  function starting_price_type(obj,thisnum) {
    if (obj.val() == 1) {
      $(".first-follow-weight-"+thisnum).hide()
    } else {
      $(".first-follow-weight-"+thisnum).show()
    }
  }


</script>
<?php echo $footer; ?>