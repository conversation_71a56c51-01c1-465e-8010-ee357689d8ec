<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      详细商品成本
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <?php if (empty($products)) { ?>
    <div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 没有详细商品信息！ </div>
    <?php } ?>
    <div class="box box-primary">
      <!-- /.box-header -->
      <div class="box-body">
          <div class="table-responsive">
            <table id="plans" class="table table-striped table-bordered table-hover">
              <thead>
              <tr>
                <td class="text-left">图片</td>
                <td class="text-left">产品名称</td>
                <td class="text-left">商品编码</td>
                <td class="text-left">成本日期</td>
                <td class="text-left">商品数量</td>
                <td class="text-left">商品成本</td>
                <td class="text-left">成本类型</td>
             </tr>
             </thead>
             <tbody>
                <?php if (!empty($products)) { ?>
                <?php foreach ($products as $product) { ?>
                <tr>
                  <td class="text-left"><img width="100" src="<?php echo $product['img_url']; ?>" class="img-thumbnail"></td>
                  <td class="text-left"><?php echo $product['spec_name']; ?></td>
                  <td class="text-left"><?php echo $product['bsku']; ?></td>
                  <td class="text-left"><?php echo $product['month']; ?></td>
                  <td class="text-left"><?php echo $product['cost_quan']; ?></td>
                  <td class="text-left"><?php echo $product['cost_total']; ?></td>
                  <td class="text-left"><?php echo $types[$product['cost_type']] ?? ''; ?></td>
                </tr>
                <?php } ?>
                <?php } ?>
             </tbody>
           </table>
         </div>
      </div>
      <!-- /.box-body -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>