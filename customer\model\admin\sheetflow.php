<?php
class ModelAdminSheetflow extends Model {
    public function addFlow($data) {
        $this->db->query("INSERT INTO " . "rg_flow SET flow_name = '" . $this->db->escape($data['name']) . "', flow_sort = '" . (int)$data['sort'] . "', create_time = '" . time() . "', is_delete = '0'");
        return $this->db->getLastId();
    }

    public function editFlow($flow_id, $data) {
        $this->db->query("UPDATE " . "rg_flow SET flow_name = '" . $this->db->escape($data['name']) . "', flow_sort = '" . (int)$data['sort'] . "' WHERE flow_id = '" . (int)$flow_id . "'");
    }

    public function deleteFlow($flow_id) {
        $this->db->query("UPDATE " . "rg_flow SET is_delete = '1' WHERE flow_id = '" . (int)$flow_id . "'");
    }

    public function getFlowInfo($flow_id) {
        $query = $this->db->query("SELECT * FROM " . "rg_flow WHERE flow_id = '" . (int)$flow_id . "'");
        return $query->row;
    }

    public function getFlows($data = array(), $sheet_id = 0)
    {
        $sql = "SELECT * FROM rg_flow WHERE is_delete = 0";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  flow_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }


        if (!empty($data['filter_date_start'])) {
            $sql .= " AND create_time >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND create_time <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }


        $sort_data = array(
            'flow_name',
            'flow_sort',
            'create_time'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY flow_sort";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }


        $query = $this->db->query($sql);

        return $query->rows;
    }
    public function getTotalFlows($data, $sheet_id = 0)
    {
        $sql = "SELECT COUNT(*) AS total FROM  rg_flow WHERE is_delete = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  flow_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }


        if (!empty($data['filter_date_start'])) {
            $sql .= " AND create_time >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND create_time <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }


        $query = $this->db->query($sql);

        return $query->row['total'];
    }




 
} 