<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        图纸信息
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="名称/设计师/策划" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>所属分类：</label>
                  <select class="form-control" name="filter_category">
                    <option value="*">全部分类</option>
                    <?php foreach($categories as $category) { ?>
                    <?php if ($category == $filter_category) { ?>
                    <option value="<?php echo $category; ?>" selected="selected"><?php echo $category; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $category; ?>"><?php echo $category; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>添加时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <a class="btn btn-danger" href="<?php echo $export_url; ?>" title="" target="_blank">下载数据</a>
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">设计图列表</h3>
          <div class="box-tools">
            <a class="btn btn-sm btn-success" href="<?php echo $setType; ?>">设置类别</a>
            <a class="btn btn-sm btn-success" href="<?php echo $setStylist; ?>">设置设计师</a>
            <a class="btn btn-sm btn-success" href="<?php echo $setTexture; ?>">设置材质</a>
            <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">新增设计图</a>
          </div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>设计图</th>
              <th>类别</th>
              <th>系列名</th>
              <th>设计时间</th>
              <th>设计师</th>
              <th>材质</th>
              <th>投票情况</th>
              <th>是否翻单</th>
              <th>入仓时间</th>
              <th>翻单时间</th>
              <th>翻单天数</th>
              <th>备注</th>
              <th>分类</th>
              <th>售卖店铺</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($infos)) { ?>
            <?php foreach ($infos as $info) { ?>
            <tr data-id="<?php echo $info['info_id']; ?>">
              <td><img width="150" src="<?php echo HTTP_IMAGE . $info['image']; ?>" class="img-thumbnail"></td>
              <td><?php echo $info['type']; ?></td>
              <td><?php echo $info['name']; ?></td>
              <td><?php echo $info['design_date']; ?></td>
              <td><?php echo $info['designer']; ?></td>
              <td><?php echo $info['texture']; ?></td>
              <td><?php echo $info['vote']; ?></td>
              <td><?php echo $info['reorder'] == 1 ? '是' : ''; ?></td>
              <td><?php echo $info['warehousing_date']; ?></td>
              <td><?php echo $info['reorder_date']; ?></td>
              <td><?php echo $info['reorder_day']; ?></td>
              <td><?php echo $info['remark']; ?></td>
              <td><?php echo $info['category']; ?></td>
              <td><?php echo $info['storename']; ?></td>
              <td class="text-right">
                <a class="btn btn-success" href="<?php echo $edit; ?>&design_id=<?php echo $info['info_id']; ?>" title="">修改</a>
                <?php if ($info['delete']) { ?>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">删除</button>
                <?php } ?>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="15" align="center"> 暂无设计图数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>      
      <!-- 新增 -->
      <div class="modal modal-default fade" id="add-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $add; ?>" method="post" enctype="multipart/form-data" id="form-add" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">请填写设计信息</h4>
              </div>
              <div class="modal-body">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-store">设计图：</label>
                  <div class="col-sm-8">
                    <img width="180" src="<?php echo HTTP_IMAGE . 'no_image.png'; ?>" class="img-thumbnail" />
                    <input type="hidden" name="design_image" value="" />
                    <button type="button" class="btn-upload btn btn-success"><i class="fa fa-upload"></i> 上传</button>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-design-name">设计名称：</label>
                  <div class="col-sm-8">
                    <input type="text" name="design_name" value="" placeholder="请输入设计名称" id="input-design-name" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-designer">设计师：</label>
                  <div class="col-sm-8">
                    <input type="text" name="designer" value="<?php echo $customer_name; ?>" placeholder="请输入设计师" id="input-designer" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-planner">策划：</label>
                  <div class="col-sm-8">
                    <select name="planner" id="input-planner" class="form-control">
                      <option value="">无策划</option>
                      <option value="邓才炳">邓才炳</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-info pull-left" data-dismiss="modal">取消</button>
                <button id="add-yes" type="button" class="btn btn-danger">提交保存</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>

      <!-- 修改 -->
      <div class="modal modal-info fade" id="edit-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $edit; ?>" method="post" enctype="multipart/form-data" id="form-edit" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">修改分类</h4>
              </div>
              <div class="modal-body">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-design-name">分类：</label>
                  <div class="col-sm-8">
                    <select class="form-control" id="category" name="category">
                      <option value="">请选择分类</option>
                      <?php foreach($categories as $category) { ?>
                      <option value="<?php echo $category; ?>"><?php echo $category; ?></option>
                      <?php } ?>
                    </select>
                    
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-design-name">售卖店铺：</label>
                  <div class="col-sm-8">
                    <div class="well well-sm" style="height: 250px; color: black; overflow: auto;">
                    <?php foreach($stores as $store) { ?>
                    <?php if (!empty($store['class_name'])) { ?>
                    <div class="checkbox">
                      <label>
                        <input type="checkbox" name="store_ids[]" data-class="<?php echo $store['class_name']; ?>" value="<?php echo $store['store_id']; ?>">
                        <?php echo $store['name']; ?>
                      </label>
                    </div>
                    <?php } ?>
                    <?php } ?>
                    </div>
                  </div>
                </div>
                <input id="edit-id" name="design_id" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="edit-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>

      <!-- 删除 -->
      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-del">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除此记录吗？</p>
                <input id="del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
(function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_category = $('select[name=\'filter_category\']').val();

      if (filter_category != '*') {
        url += '&filter_category=' + encodeURIComponent(filter_category);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }
  
      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });

    $('#add-modal').on('show.bs.modal', function(event) {})
    $('#add-yes').on('click', () => {$('#form-add').submit()})

    $('#edit-modal').on('show.bs.modal', function(event) {
      $('#edit-id').val($(event.relatedTarget).parents('tr').data('id'))
      $('#category').val($(event.relatedTarget).data('category'))
      $('#category').trigger('change')
    })
    $('#edit-yes').on('click', () => {$('#form-edit').submit()})

    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
})()

$('.btn-upload').on('click', function() {
  $('#form-upload').remove();

  var target = $(this);

  $('body').prepend('<form enctype="multipart/form-data" id="form-upload" style="display: none;"><input type="file" name="file" value="" accept="image/*, video/*" /><input type="hidden" name="token" value="" /></form>');

  $('#form-upload input[name=\'file\']').trigger('click');

  if (typeof timer != 'undefined') {
      clearInterval(timer);
  }

  timer = setInterval(function() {
    if ($('#form-upload input[name=\'file\']').val() != '') {
      clearInterval(timer);

      $.ajax({
        url: '<?php echo $getToken; ?>',
        type: 'get',
        dataType: 'json',
        success: function(json) {
          $('#form-upload input[name=\'token\']').val(json.uploadToken);
          $.ajax({
            url: 'https://up-z2.qiniup.com',
            type: 'post',
            dataType: 'json',
            data: new FormData($('#form-upload')[0]),
            cache: false,
            contentType: false,
            processData: false,
            beforeSend: function() {
              target.find('i').replaceWith('<i class="fa fa-circle-o-notch fa-spin"></i>');
              target.prop('disabled', true);
            },
            complete: function() {
              target.find('i').replaceWith('<i class="fa fa-upload"></i>');
              target.prop('disabled', false);
            },
            success: function(res) {
              target.parent().find('input[type=\'hidden\']').val(res.key);
              target.parent().find('img').attr('src', json.httpHost + res.key);
            },
            error: function(xhr, ajaxOptions, thrownError) {
              alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
          });
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }
  }, 500);
});

$('#category').on('change', function () {
  var class_name = $(this).val();
  if (class_name != ''){
    $('input[type="checkbox"]').each(function() {
      if (($(this).data('class').indexOf('综合') >= 0) || ($(this).data('class').indexOf(class_name) >= 0)) {
        $(this).prop('checked', true);
      } else {
        $(this).prop('checked', false);
      }
    });
  } else {
    $('input[type="checkbox"]').prop('checked', false);
  }    
});
</script>
<?php echo $footer; ?>