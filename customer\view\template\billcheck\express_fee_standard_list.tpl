<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        快递公司管理
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">快递公司列表</h3>
          <div class="box-tools">
            <a class="btn btn-primary" href="<?php echo $add; ?>">添加</a>
          </div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>快递公司</th>
              <th>材积重量</th>
              <th>开始日期</th>
              <th>结束日期</th>
              <th>权重</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($standards)) { ?>
              <?php foreach ($standards as $standard) { ?>
                <tr data-id="<?php echo $standard['express_fee_standard_id']; ?>">
                  <td><?php echo $standard['name']; ?></td>
                  <td><?php if($standard['type'] == 1){ ?>是<?php } else { ?>否<?php } ?></td>
                  <td><?php echo $standard['s_day']; ?></td>
                  <td><?php echo $standard['e_day']; ?></td>
                  <td><?php echo $standard['sort']; ?></td>
                  <td class="text-right">
                    <a class="btn btn-success" href="<?php echo $edit_url; ?><?php echo $standard['express_fee_standard_id']; ?>" title="" target="_blank">编辑</a>
                    <a class="btn btn-info" href="<?php echo $detail_url; ?><?php echo $standard['express_fee_standard_id']; ?>" title="" target="_blank">查看详情</a>
                    <a class="btn btn-primary" href="<?php echo $add_province_url; ?><?php echo $standard['express_fee_standard_id']; ?>" title="" target="_blank">设置收费标准</a>
                    <a class="btn btn-warning" href="<?php echo $copy_province_url; ?><?php echo $standard['express_fee_standard_id']; ?>" title="" target="_blank">复制收费标准</a>
                    <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">删除</button>
                  </td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
      </div>

      <!-- 删除 -->
      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-del">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除此快递公司吗？该操作会删除对应的快递报价，此操作不可恢复！</p>
                <input id="del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
  })()
</script>
<?php echo $footer; ?>