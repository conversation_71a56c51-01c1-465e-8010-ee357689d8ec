<?php
// 引入配置文件
require_once('config/config.php');

// 引入启动文件
require_once(DIR_SYSTEM . 'startup.php');

// 路由
if (PHP_SAPI === 'cli') {//php_sapi_name() === 'cli'	
  // 如需接收参数使用固定变量$argv
  if (isset($argv[1])) {
    $route = $argv[1];
  } else {
    $route = 'task';
  }

  $controller = new Front();
  $controller->dispatch(new Action($route));
} else {
  // 预先加载类
  $controller = new Front();
  // 开始会话
  $session->start();

  load_class('document', 'Document');
  load_class('url', 'Url', HTTP_SERVER);
  load_class('user', 'Union');
  // 检查登录状态
  $controller->addPreAction(new Action('admin/common/checkLogin'));
  // 检查用户权限
  $controller->addPreAction(new Action('admin/common/checkPermission'));
  // 加载请求路径
  $controller->addPreAction(new Action('admin/common/router'));
  // 找不到请求路径
  $controller->dispatch(new Action('admin/common/notFound'));
  // 输出
  $response->output();
}