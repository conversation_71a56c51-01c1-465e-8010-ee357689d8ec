<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        店铺备货
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">      
      <div class="box box-warning">
        <!-- /.box-header -->
        <div class="box-body">
          <div class="form-horizontal">
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-store">备货店铺：</label>
              <div class="col-sm-8">
                <select name="store_id" id="input-store" class="form-control" onchange="javascript: location.href = '<?php echo $storePlan; ?>&store_id=' + this.value;">
                  <?php foreach($stores as $store) { ?>
                  <?php if ($store['store_id'] == $store_id) { ?>
                  <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-product">备货产品：</label>
              <div class="col-sm-8">
                <input type="text" value="" placeholder="输入产品编码或名称查找" id="input-product" class="form-control" />
              </div>
            </div>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-product">库存成本：</label>
              <div class="col-sm-8">
                <span class="form-control" style="border: none">成本：<?php echo $cost['member_cost']; ?>&nbsp;&nbsp;&nbsp;运营：<?php echo $cost['retail_cost']; ?></span>
              </div>
            </div>
          </div>
        </div>
        <!-- /.box-body -->
      </div>
      
      <div class="box box-primary">
        <div class="box-body">
          <table id="plans" class="table table-striped table-bordered table-hover">
              <thead>
                <tr>
                  <td class="text-left">图片</td>
                  <td class="text-left">产品名称编码</td>
                  <td class="text-left">剩余库存</td>
                  <td class="text-left">总备货</td>
                  <td class="text-left">总销量</td>
                  <td class="text-left">3天销量</td>
                  <td class="text-left">7天销量</td>
                  <td class="text-left">15天销量</td>
                  <td class="text-left">30天销量</td>
                  <td class="text-left">60天销量</td>
                  <td class="text-left">90天销量</td>
                  <td width="150" class="text-left">备货量</td>
                  <td></td>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($plans as $plan) { ?>
                <tr id="plan-<?php echo $plan['bsku']; ?>">
                  <td class="text-left"><img width="100" src="<?php echo $plan['img_url']; ?>" class="img-thumbnail"></td>
                  <td class="text-left"><?php echo $plan['spec_name']; ?><br><?php echo $plan['bsku']; ?></td>
                  <td class="text-left"><?php echo $plan['stock_quan']; ?></td>
                  <td class="text-left"><?php echo $plan['order_quan']; ?></td>
                  <td class="text-left"><?php echo $plan['sale_quan']; ?></td>
                  <td class="text-left"><?php echo $plan['3dsales']; ?></td>
                  <td class="text-left"><?php echo $plan['7dsales']; ?></td>
                  <td class="text-left"><?php echo $plan['15dsales']; ?></td>
                  <td class="text-left"><?php echo $plan['30dsales']; ?></td>
                  <td class="text-left"><?php echo $plan['60dsales']; ?></td>
                  <td class="text-left"><?php echo $plan['90dsales']; ?></td>
                  <td class="text-left">
                    <?php if (!empty($plan['stop'])) { ?>
                      不翻单
                    <?php } else { ?>
                    <input type="number" name="plan[]" value="<?php echo $plan['plan_quan']; ?>" data-sku="<?php echo $plan['bsku']; ?>" data-org="<?php echo $plan['plan_quan']; ?>" placeholder="备货量" class="input-plan form-control" />
                    <?php } ?>
                  </td>
                  <td class="text-left"><button type="button" data-sku="<?php echo $plan['bsku']; ?>" data-toggle="tooltip" title="删除" class="btn btn-danger btn-del"><i class="fa fa-minus-circle"></i></button></td>
                </tr>
                <?php } ?>
              </tbody>
          </table>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
// Autocomplete */
(function($) {
  $.fn.autocomplete = function(option) {
    return this.each(function() {
      var $this = $(this);
      var $dropdown = $('<ul class="dropdown-menu" />');

      this.timer = null;
      this.items = {};

      $.extend(this, option);

      $this.attr('autocomplete', 'off');

      // Focus
      $this.on('focus', function() {
        this.request();
      });

      // Blur
      $this.on('blur', function() {
        setTimeout(function(object) {
          object.hide();
        }, 200, this);
      });

      // Keydown
      $this.on('keydown', function(event) {
        switch(event.keyCode) {
          case 27: // escape
            this.hide();
            break;
          default:
            this.request();
            break;
        }
      });

      // Click
      this.click = function(event) {
        event.preventDefault();

        var value = $(event.target).parent().attr('data-value');

        if (value && this.items[value]) {
          this.select(this.items[value]);
        }
      }

      // Show
      this.show = function() {
        var pos = $this.position();

        $dropdown.css({
          'min-width': '50%',
          top: pos.top + $this.outerHeight(),
          left: pos.left
        });

        $dropdown.show();
      }

      // Hide
      this.hide = function() {
        $dropdown.hide();
      }

      // Request
      this.request = function() {
        clearTimeout(this.timer);

        this.timer = setTimeout(function(object) {
          object.source($(object).val(), $.proxy(object.response, object));
        }, 200, this);
      }

      // Response
      this.response = function(json) {
        var html = '';
        var category = {};
        var name;
        var i = 0, j = 0;

        if (json.length) {
          for (i = 0; i < json.length; i++) {
            // update element items
            this.items[json[i]['value']] = json[i];

            if (!json[i]['category']) {
              // ungrouped items
              html += '<li data-value="' + json[i]['value'] + '"><a href="#">' + json[i]['value'] + json[i]['label'] + '</a></li>';
            } else {
              // grouped items
              name = json[i]['category'];
              if (!category[name]) {
                category[name] = [];
              }

              category[name].push(json[i]);
            }
          }

          for (name in category) {
            html += '<li class="dropdown-header">' + name + '</li>';

            for (j = 0; j < category[name].length; j++) {
              html += '<li data-value="' + category[name][j]['value'] + '"><a href="#">&nbsp;&nbsp;&nbsp;' + category[name][j]['label'] + '</a></li>';
            }
          }
        }

        if (html) {
          this.show();
        } else {
          this.hide();
        }

        $dropdown.html(html);
      }

      $dropdown.on('click', '> li > a', $.proxy(this.click, this));
      $this.after($dropdown);
    });
  }
})(window.jQuery);

$(document).ready(function () {
  $('#input-product').autocomplete({
    'source': function(request, response) {
      $.ajax({
        url: '<?php echo $autoProduct;?>&filter_name=' + encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response(json);
          // response($.map(json, function(item) {
          //   return item
          // }));
        }
      });
    },
    'select': function(item) {
      // $('#input-product').val('');
      if ($('#plan-' + item['value']).length == 0) {
        addPlanRow(item);
      }
    }
  });
});
</script>
<script type="text/javascript"><!--
function addPlanRow(item) {
  html  = '<tr id="plan-' + item['value'] + '">';
  html += '  <td class="text-left"><img width="100" src="' + item['image'] + '" class="img-thumbnail"></td>';
  html += '  <td class="text-left">' + item['label'] + '<br>' + item['value'] + '</td>';
  html += '  <td class="text-left">' + item['stock_quan'] + '</td>';
  html += '  <td class="text-left">' + item['order_quan'] + '</td>';
  html += '  <td class="text-left">' + item['sale_quan'] + '</td>';
  html += '  <td class="text-left">' + item['3dsales'] + '</td>';
  html += '  <td class="text-left">' + item['7dsales'] + '</td>';
  html += '  <td class="text-left">' + item['15dsales'] + '</td>';
  html += '  <td class="text-left">' + item['30dsales'] + '</td>';
  html += '  <td class="text-left">' + item['60dsales'] + '</td>';
  html += '  <td class="text-left">' + item['90dsales'] + '</td>';
  html += '  <td class="text-left"><input type="number" name="plan[]" value="" data-sku="' + item['value'] + '" data-org="" placeholder="备货量" class="input-plan form-control" /></td>';
  html += '  <td class="text-left"><button type="button" data-sku="' + item['value'] + '" data-toggle="tooltip" title="删除" class="btn btn-danger btn-del"><i class="fa fa-minus-circle"></i></button></td>';
  html += '</tr>';
  
  $('#plans tbody').prepend(html);
}
$('#plans').on('blur', '.input-plan', function () {
  var obj = $(this);
  if (!obj.val() || (obj.val() == '0') || (obj.val() == obj.data('org'))) {
    obj.val(obj.data('org'));
    return;
  }
  $.ajax({
    url: '<?php echo $addPlan; ?>',
    type: 'post',
    data: {bsku: obj.data('sku'), plan_quan: obj.val()},
    dataType: 'json',
    success: function(json) {
      $('.alert-danger, .alert-success').remove();

      if (json['error']) {
        $('.box-primary').before('<div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 没有该店铺权限 </div>');

        obj.val(obj.data('org'));
      }

      if (json['success']) {
        $('.box-primary').before('<div class="alert alert-success alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> ' + obj.data('sku') + '备货成功 </div>');
        
        obj.data('org', obj.val());
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
});
$('#plans').on('click', '.btn-del', function () {
  var bsku = $(this).data('sku');
  $.ajax({
    url: '<?php echo $delPlan; ?>',
    type: 'post',
    data: {bsku: bsku},
    dataType: 'json',
    success: function(json) {
      $('.alert-danger, .alert-success').remove();

      if (json['error']) {
        $('.box-primary').before('<div class="alert alert-success alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 没有该店铺权限 </div>');
      }

      if (json['success']) {
        $('.box-primary').before('<div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> ' + bsku + '删除成功 </div>');

        $('#plan-' + bsku).remove();
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
});
//--></script>
<?php echo $footer; ?>