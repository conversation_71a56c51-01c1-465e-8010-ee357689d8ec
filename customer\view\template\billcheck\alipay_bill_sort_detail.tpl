<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      类别详情
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <?php if ($warning) { ?>
    <div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?> </div>
    <?php } ?>
    <div class="box box-primary">
      <div class="box-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label>账务流水号：</label>
              <div class="">
                <input type="text" name="filter_accounting_serial_number" value="<?php echo $filter_accounting_serial_number; ?>" placeholder="请输入账务流水号" id="filter_accounting_serial_number" class="form-control pull-right" />
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <label>业务流水号：</label>
              <div class="">
                <input type="text" name="filter_service_serial_number" value="<?php echo $filter_service_serial_number; ?>" placeholder="请输入业务流水号" id="filter_service_serial_number" class="form-control pull-right" />
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <label>发生时间：</label>
              <div class="input-group">
                <div class="input-group-addon">
                  <i class="glyphicon glyphicon-calendar"></i>
                </div>
                <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                <?php } else{ ?>
                <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                <?php } ?>
                <input type="text" class="hidden" name="filter_date_start" id="filter-start" placeholder="" value="<?php echo $filter_date_start; ?>">
                <input type="text" class="hidden" name="filter_date_end" id="filter-end" placeholder="" value="<?php echo $filter_date_end; ?>">
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- /.box-body -->
      <div class="box-footer">
        <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
      </div>
    </div>
    <div class="box box-primary box-primary2">
      <!-- /.box-header -->
      <div class="box-body">
          <div class="table-responsive">
            <table id="plans" class="table table-striped table-bordered table-hover" style="table-layout: fixed">
              <thead>
              <tr>
                <!--<th width="30"><input id="selectAll" class="flat" type="checkbox"></th>-->
                <td style="width: 60px" class="text-left">账务流水号</td>
                <td style="width: 60px" class="text-left">业务流水号</td>
                <td style="width: 150px" class="text-left">费用归属</td>
                <td style="width: 50px" class="text-left">不属于该类别</td>
                <td style="width: 50px" class="text-left">商户订单号</td>
                <td style="width: 50px" class="text-left">商品名称</td>
                <td style="width: 50px" class="text-left">发生时间</td>
                <td style="width: 50px" class="text-left">对方账号</td>
                <td style="width: 50px" class="text-left">收入金额（+元）</td>
                <td style="width: 50px" class="text-left">支出金额（-元）</td>
                <td style="width: 50px" class="text-left">账户余额（元）</td>
                <td style="width: 50px" class="text-left">交易渠道</td>
                <td style="width: 80px" class="text-left">业务类型</td>
                <td style="width: 200px" class="text-left">备注</td>
              </tr>
              </thead>
              <tbody>
              <?php if (!empty($details)) { ?>
                <?php foreach ($details as $detail) { ?>
                <tr id="detail-<?php echo $detail['accounting_serial_number']; ?>" style="word-wrap: break-word;">
                  <td class="text-left"><?php echo $detail['accounting_serial_number']; ?></td>
                  <td class="text-left"><?php echo $detail['service_serial_number']; ?></td>
                  <td class="text-left"><input style="width: 200px" type="text" name="detail[]" value="<?php echo $detail['cost_affiliation']; ?>" data-accounting-serial-number="<?php echo $detail['accounting_serial_number']; ?>" data-org="<?php echo $detail['cost_affiliation']; ?>" placeholder="请输入费用归属" class="input-detail form-control" /></td>
                  <td class="text-left"><button type="button" data-accounting-serial-number="<?php echo $detail['accounting_serial_number']; ?>" data-toggle="tooltip" title="删除" class="btn btn-danger btn-del"><i class="fa fa-minus-circle"></i></button></td>
                  <td class="text-left"><?php echo $detail['merchant_order_number']; ?></td>
                  <td class="text-left"><?php echo $detail['product_name']; ?></td>
                  <td class="text-left"><?php echo $detail['occurrence_time']; ?></td>
                  <td class="text-left"><?php echo $detail['reciprocal_account']; ?></td>
                  <td class="text-left"><?php echo $detail['income']; ?></td>
                  <td class="text-left"><?php echo $detail['disburse']; ?></td>
                  <td class="text-left"><?php echo $detail['balance']; ?></td>
                  <td class="text-left"><?php echo $detail['transaction_channel']; ?></td>
                  <td class="text-left"><?php echo $detail['business_type']; ?></td>
                  <td class="text-left"><?php echo $detail['remark']; ?></td>
                </tr>
                <?php } ?>
              <?php } ?>
              </tbody>
            </table>
          </div>
      </div>
      <div class="box-footer clearfix">
        <div class="flex ai__c jc__sb">
          <div><?php echo $results; ?></div>
          <?php echo $pagination; ?>
        </div>
      </div>
      <!-- /.box-body -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript"><!--
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })

    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_accounting_serial_number = $('input[name=\'filter_accounting_serial_number\']').val();

      url += '&filter_accounting_serial_number=' + encodeURIComponent(filter_accounting_serial_number);

      var filter_service_serial_number = $('input[name=\'filter_service_serial_number\']').val();

      url += '&filter_service_serial_number=' + encodeURIComponent(filter_service_serial_number);

      var filter_date_start = $('input[name=\'filter_date_start\']').val();

      url += '&filter_date_start=' + encodeURIComponent(filter_date_start);

      var filter_date_end = $('input[name=\'filter_date_end\']').val();

      url += '&filter_date_end=' + encodeURIComponent(filter_date_end);

      location.href = url;
    });

    $('#plans').on('blur', '.input-detail', function () {
      var obj = $(this);
      if (!obj.val() || (obj.val() == '0') || (obj.val() == obj.data('org'))) {
        obj.val(obj.data('org'));
        return;
      }
      $.ajax({
        url: '<?php echo $addCostAffiliation; ?>',
        type: 'post',
        data: {accounting_serial_number: obj.data('accounting-serial-number'), cost_affiliation: obj.val()},
        dataType: 'json',
        success: function(json) {
          $('.alert-danger, .alert-success').remove();

          if (json['error']) {
            $('.box-primary2').before('<div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 修改费用归属失败，请重试！ </div>');

            obj.val(obj.data('org'));
          }

          if (json['success']) {
            $('.box-primary2').before('<div class="alert alert-success alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> ' + obj.data('accounting-serial-number') + '修改费用归属成功 </div>');

            obj.data('org', obj.val());
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    });

    $('#plans').on('click', '.btn-del', function () {
      var accounting_serial_number = $(this).data('accounting-serial-number');
      if (confirm('确认从该类别删除'+accounting_serial_number+'吗？')) {
        $.ajax({
          url: '<?php echo $delSortGather; ?>',
          type: 'post',
          data: {accounting_serial_number: accounting_serial_number},
          dataType: 'json',
          success: function(json) {
            $('.alert-danger, .alert-success').remove();

            if (json['error']) {
              $('.box-primary').before('<div class="alert alert-success alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 删除失败，请重试！ </div>');
            }

            if (json['success']) {
              $('.box-primary').before('<div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> ' + accounting_serial_number + '删除成功 </div>');

              $('#detail-' + accounting_serial_number).remove();
            }
          },
          error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
          }
        });
      }

    });
  })()
  //--></script>
<?php echo $footer; ?>