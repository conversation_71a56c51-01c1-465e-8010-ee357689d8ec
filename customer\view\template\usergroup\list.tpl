<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      帐号分组
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <?php if ($success) { ?>
    <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
    <?php } ?>
    <?php if ($warning) { ?>
    <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
    <?php } ?>
    <div class="box">
      <div class="box-header">
        <h3 class="box-title">账号分组列表</h3>

        <div class="box-tools">
          <a class="btn btn-sm btn-success" href="<?php echo $setpermission; ?>">设置权限</a>
          <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
        </div>
      </div>
      <!-- /.box-header -->
      <div class="box-body table-responsive no-padding">
        <table class="table table-hover table-striped">
          <tbody><tr>
            <th>组别名称</th>
            <th>查看权限</th>
            <th>编辑权限</th>
            <th class="text-right">操作</th>
          </tr>
          <?php if (!empty($usergroups)) { ?>
          <?php foreach ($usergroups as $usergroup) { ?>
          <tr data-id="<?php echo $usergroup['user_group_id']; ?>">
            <td><?php echo $usergroup['name']; ?></td>
            <td>
              <?php if(!empty($usergroup['permission']['access'])) { ?>
                <?php foreach ($usergroup['permission']['access'] as $access) { ?>
                  <?php if(!empty($permissions[$access])) { ?>
                   <?php echo $permissions[$access]; ?> <br>
                  <?php } else { ?>
                   <?php echo $access; ?> <br>
                  <?php } ?>
                <?php } ?>
              <?php } ?>
            </td>
            <td>
              <?php if(!empty($usergroup['permission']['modify'])) { ?>
                <?php foreach ($usergroup['permission']['modify'] as $modify) { ?>
                  <?php if(!empty($permissions[$modify])) { ?>
                  <?php echo $permissions[$modify]; ?> <br>
                  <?php } else { ?>
                  <?php echo $modify; ?> <br>
                  <?php } ?>
                <?php } ?>
              <?php } ?>
            </td>
            <td class="text-right">
              <a class="btn btn-success" href="<?php echo $usergroup['edit']; ?>" title="">修改</a>
              <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#user-del-modal">删除</button>
            </td>
          </tr>
          <?php } ?>
          <?php } else{ ?>
          <td colspan="7" align="center"> 暂无账号分组 </td>
          <?php } ?>
          </tbody></table>
      </div>
      <div class="box-footer clearfix">
        <div class="flex ai__c jc__sb">
          <div><?php echo $results; ?></div>
          <?php echo $pagination; ?>
        </div>
      </div>
      <!-- /.box-body -->
    </div>
    <!-- 删除 -->
    <div class="modal modal-danger fade" id="user-del-modal">
      <div class="modal-dialog">
        <div class="modal-content">
          <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-user">
            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title">删除</h4>
            </div>
            <div class="modal-body">
              <p>确定删除此账号分组吗？</p>
              <input id="user-del-id" name="selected[]" type="hidden" value="">
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
              <button id="user-del-yes" type="button" class="btn btn-outline">是</button>
            </div>
          </form>
        </div>
        <!-- /.modal-content -->
      </div>
      <!-- /.modal-dialog -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function() {
    $('#user-del-modal').on('show.bs.modal', function(event) {
      $('#user-del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#user-del-yes').on('click', () => {$('#form-user').submit()})
  })()
</script>
<?php echo $footer; ?>