<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        岗位津贴
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>姓名：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="请输入姓名" value="<?php echo $filter_name; ?>">
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">列表</p>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'name') { ?>
                  <a href="<?php echo $sort_name; ?>" class="<?php echo strtolower($order); ?>">名称</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_name; ?>">名称</a>
                <?php } ?>
              </th>
              
              <th>
                <?php if ($sort == 'score') { ?>
                  <a href="<?php echo $sort_score; ?>" class="<?php echo strtolower($order); ?>">岗位津贴</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_score; ?>">岗位津贴</a>
                <?php } ?>
              </th>
                <th class="text-right">操作</th>
            </tr>
            <?php if (isset($sheet) && !empty($sheet)) { ?>
            <?php foreach ($sheet as $v) { ?>
            <tr data-id="<?php echo $v['union_id']; ?>" data-name="<?php echo $v['real_name']; ?>"  data-score="<?php echo $v['score']; ?>" >
              <td><?php echo $v['real_name']; ?></td>
              <td><?php echo $v['score'] ?? 0; ?></td>
              <td class="text-right">
              <button class="btn btn-success" type="button" data-toggle="modal" data-target="#add-modal">修改岗位津贴</button>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <tr>
                <td colspan="4" align="center">暂无数据</td>
            </tr>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

       <!-- 设置数量总价 -->
      <div class="modal modal-warning fade" id="add-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $update_import; ?>" method="post" enctype="multipart/form-data" id="form-add" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">请填写岗位津贴</h4>
              </div>
              <div class="modal-body">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">姓名：</label>
                  <div class="col-sm-8">
                    <input type="text"  style="pointer-events : none;" id="input-quantity" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">岗位津贴：</label>
                  <div class="col-sm-8">
                    <input type="number" name="score" value="" placeholder="请输入岗位津贴" id="input-total_prices" class="form-control" />
                  </div>
                </div>
                <input id="add-id" name="union_id" type="hidden" value="">
                <div class="modal-footer">
                  <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">取消</button>
                  <button id="add-yes" type="button" class="btn btn-outline">提交保存</button>
                </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after, .table a.undesc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after, .table a.unasc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">

  (function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }



      location.href = url;

    
    });

      $('#add-modal').on('show.bs.modal', function(event) {
        
      $('#add-id').val($(event.relatedTarget).parents('tr').data('id'))
      $('#input-quantity').val($(event.relatedTarget).parents('tr').data('name'))
      $('#input-total_prices').val($(event.relatedTarget).parents('tr').data('score'))
    })
    $('#add-yes').on('click', () => {$('#form-add').submit()})
  })()
</script>
<?php echo $footer; ?>