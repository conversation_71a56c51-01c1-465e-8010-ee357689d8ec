<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">快递公司：</label>
              <div class="col-sm-8">
                <input type="text" name="name" value="<?php if(!empty($expressFeeTemplate['name'])){ ?><?php echo $expressFeeTemplate['name']; ?><?php } ?>" placeholder="请输入快递公司" id="input-name" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-quan">是否核算加价：</label>
              <div class="col-sm-8">
                <div class="radio">
                  <label><input type="radio" name="is_surcharges" value="1"  <?php if(empty($expressFeeTemplate['is_surcharges']) || (!empty($expressFeeTemplate['is_surcharges']) && $expressFeeTemplate['is_surcharges'] == 1)){ ?>checked<?php } ?>>是</label>
                  <label style="margin-left: 10px"><input type="radio" name="is_surcharges" value="2" <?php if(!empty($expressFeeTemplate['is_surcharges']) && $expressFeeTemplate['is_surcharges'] == 2){ ?>checked<?php } ?>>否</label>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">快递单号：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[courier_number]" value="<?php if(!empty($expressFeeTemplate['setting']['courier_number'])){ ?><?php echo $expressFeeTemplate['setting']['courier_number']; ?><?php } ?>" placeholder="请输入快递单号取值列" id="input-courier_number" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">省份：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[province]" value="<?php if(!empty($expressFeeTemplate['setting']['province'])){ ?><?php echo $expressFeeTemplate['setting']['province']; ?><?php } ?>" placeholder="请输入省份取值列" id="input-province" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">市/区：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[city]" value="<?php if(!empty($expressFeeTemplate['setting']['city'])){ ?><?php echo $expressFeeTemplate['setting']['city']; ?><?php } ?>" placeholder="请输入市/区取值列" id="input-city" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">发货日期：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[delivery_date]" value="<?php if(!empty($expressFeeTemplate['setting']['delivery_date'])){ ?><?php echo $expressFeeTemplate['setting']['delivery_date']; ?><?php } ?>" placeholder="请输入发货日期取值列" id="input-delivery_date" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">长度：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[long]" value="<?php if(!empty($expressFeeTemplate['setting']['long'])){ ?><?php echo $expressFeeTemplate['setting']['long']; ?><?php } ?>" placeholder="请输入长度取值列" id="input-long" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">宽度：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[wide]" value="<?php if(!empty($expressFeeTemplate['setting']['wide'])){ ?><?php echo $expressFeeTemplate['setting']['wide']; ?><?php } ?>" placeholder="请输入宽度取值列" id="input-wide" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">高度：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[high]" value="<?php if(!empty($expressFeeTemplate['setting']['high'])){ ?><?php echo $expressFeeTemplate['setting']['high']; ?><?php } ?>" placeholder="请输入高度取值列" id="input-high" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">重量：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[weight]" value="<?php if(!empty($expressFeeTemplate['setting']['weight'])){ ?><?php echo $expressFeeTemplate['setting']['weight']; ?><?php } ?>" placeholder="请输入重量取值列" id="input-weight" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">快递费：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[price]" value="<?php if(!empty($expressFeeTemplate['setting']['price'])){ ?><?php echo $expressFeeTemplate['setting']['price']; ?><?php } ?>" placeholder="请输入快递费取值列" id="input-price" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">加价：</label>
              <div class="col-sm-8">
                <input type="text" name="setting[additional_charge]" value="<?php if(!empty($expressFeeTemplate['setting']['additional_charge'])){ ?><?php echo $expressFeeTemplate['setting']['additional_charge']; ?><?php } ?>" placeholder="请输入加价取值列" id="input-additional_charge" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">开始删除行数：</label>
              <div class="col-sm-8">
                <input type="number" name="setting[s_line]" value="<?php if(!empty($expressFeeTemplate['setting']['s_line'])){ ?><?php echo $expressFeeTemplate['setting']['s_line']; ?><?php }else{ ?>1<?php } ?>" placeholder="请输入开始行数" id="input-s_line" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">结尾删除行数：</label>
              <div class="col-sm-8">
                <input type="number" name="setting[e_line]" value="<?php if(!empty($expressFeeTemplate['setting']['e_line'])){ ?><?php echo $expressFeeTemplate['setting']['e_line']; ?><?php }else{ ?>0<?php } ?>" placeholder="请输入结尾删除行数" id="input-e_day" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script type="text/javascript">
  function toVaild() {
    // if ($('.glyphicon-info-sign').length) {
    //   confirm('数据不存在')
    //   return false;
    // }
    var name = document.getElementById("input-name").value;
    if (name == "") {
      confirm('请输入快递公司')
      return false;
    }
    return true;
  }


</script>
<?php echo $footer; ?>