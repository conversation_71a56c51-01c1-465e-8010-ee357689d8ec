<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-username">用户名：</label>
              <div class="col-sm-8">
                <input type="text" name="username" value="<?php echo $username; ?>" placeholder="用户名" id="input-username" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-user-group">所属角色：</label>
              <div class="col-sm-8">
                <select name="user_group_id" id="input-user-group" class="form-control">
                  <?php foreach ($user_groups as $user_group) { ?>
                  <?php if ($user_group['user_group_id'] == $user_group_id) { ?>
                  <option value="<?php echo $user_group['user_group_id']; ?>" selected="selected"><?php echo $user_group['name']; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $user_group['user_group_id']; ?>"><?php echo $user_group['name']; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>
            <?php if ($select_store) { ?>            
            <div class="form-group">
              <label class="col-sm-2 control-label">管理店铺：</label>
              <div class="col-sm-8">
                <div class="well well-sm" style="height: 150px; overflow: auto;">
                  <?php foreach ($stores as $store) { ?>
                  <?php if (in_array($store['store_id'], $store_ids)) { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="store_ids[]" value="<?php echo $store['store_id']; ?>" checked="checked"><?php echo $store['name']; ?></label>
                  </div>
                  <?php } else { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="store_ids[]" value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></label>
                  </div>
                  <?php } ?>
                  <?php } ?>
                </div>
              </div>
            </div>
            <?php } ?>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-real-name">真实姓名：</label>
              <div class="col-sm-8">
                <input type="text" name="real_name" value="<?php echo $real_name; ?>" placeholder="真实姓名：" id="input-real-name" class="form-control" />
              </div>
            </div>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-password">密码：</label>
              <div class="col-sm-8">
                <input type="password" name="password" value="<?php echo $password; ?>" placeholder="密码：" id="input-password" class="form-control" autocomplete="off" />
              </div>
            </div>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-confirm">确认密码：</label>
              <div class="col-sm-8">
                <input type="password" name="confirm" value="<?php echo $confirm; ?>" placeholder="确认密码：" id="input-confirm" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-status">状态：</label>
              <div class="col-sm-8">
                <select name="status" id="input-status" class="form-control">
                  <?php if ($status) { ?>
                  <option value="0"><?php echo $text_disabled; ?></option>
                  <option value="1" selected="selected"><?php echo $text_enabled; ?></option>
                  <?php } else { ?>
                  <option value="0" selected="selected"><?php echo $text_disabled; ?></option>
                  <option value="1"><?php echo $text_enabled; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>