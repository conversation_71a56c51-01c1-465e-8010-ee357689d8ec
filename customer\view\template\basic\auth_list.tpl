<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        货品权限
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="姓名/身份证/手机号搜索" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>添加时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">权限列表</h3>
          <div class="box-tools">
            <button class="btn btn-warning" type="button" data-toggle="modal" data-target="#add-modal">新增权限</button>
          </div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>所属店铺</th>
              <th>姓名</th>
              <th>身份证/账号</th>
              <th>手机号/密码</th>
              <th>价格权限</th>
              <th>添加时间</th>
            </tr>
            <?php if (!empty($auths)) { ?>
            <?php foreach ($auths as $auth) { ?>
            <tr>
              <td><?php echo $auth['storename']; ?></td>
              <td><?php echo $auth['realname']; ?></td>
              <td><?php echo $auth['cardid']; ?></td>
              <td><?php echo $auth['telephone']; ?></td>
              <td><?php if ($auth['wholesale'] == '1') { ?>批发一档价
                <?php } elseif ($auth['wholesale'] == '2') { ?>批发二档价
                <?php } elseif ($auth['wholesale'] == '3') { ?>批发三档价
                <?php } elseif ($auth['wholesale'] == '5') { ?>批发一二三档价
                <?php } elseif ($auth['wholesale'] == '6') { ?>分销价
                <?php } elseif ($auth['wholesale'] == '7') { ?>批发一二三档价+分销价
                <?php } else { ?>市场价
                <?php } ?></td>
              <td><?php echo $auth['date_added']; ?></td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="6" align="center"> 暂无退货数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>      
      <!-- 新增 -->
      <div class="modal modal-warning fade" id="add-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $add; ?>" method="post" enctype="multipart/form-data" id="form-add" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">请填写查看权限信息</h4>
              </div>
              <div class="modal-body">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-store">所属店铺：</label>
                  <div class="col-sm-8">
                    <select name="store_id" id="input-store" class="form-control">
                      <option value="">请选择所属店铺</option>
                      <?php foreach ($stores as $store) { ?>
                      <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-cardid">身份证/账号：</label>
                  <div class="col-sm-8">
                    <input type="text" name="cardid" value="" placeholder="请输入身份证/账号" id="input-cardid" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-realname">姓名：</label>
                  <div class="col-sm-8">
                    <input type="text" name="realname" value="" placeholder="请输入姓名" id="input-realname" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-telephone">手机号/密码：</label>
                  <div class="col-sm-8">
                    <input type="text" name="telephone" value="" placeholder="请输入手机号/密码" id="input-telephone" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-wholesale">价格权限：</label>
                  <div class="col-sm-8">
                    <select name="wholesale" id="input-wholesale" class="form-control">
                      <option value="">请选择价格权限</option>
                      <option value="4">市场价</option>
                      <option value="1">批发一档价</option>
                      <option value="2">批发二档价</option>
                      <option value="3">批发三档价</option>
                      <option value="5">批发一二三档价</option>
                      <option value="6">分销价</option>
                      <option value="7">批发一二三档价+分销价</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">取消</button>
                <button id="add-yes" type="button" class="btn btn-outline">提交保存</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }
  
      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });

    $('#add-modal').on('show.bs.modal', function(event) {
      $('#input-store').val('');
      $('#input-cardid').val('');
      $('#input-realname').val('');
      $('#input-telephone').val('');
      $('#input-wholesale').val('');
    })
    $('#add-yes').on('click', () => {$('#form-add').submit()})
  })()
</script>
<?php echo $footer; ?>