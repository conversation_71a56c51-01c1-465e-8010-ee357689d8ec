<?php
class ModelAdminFilemanagerpower extends Model {
    public function getList() {
        $query = $this->db->query("SELECT * FROM _union_file_power WHERE status = '1'");

        return $query->rows;
    }

    public function getPower($file_power_id) {
        $query = $this->db->query("SELECT * FROM _union_file_power WHERE file_power_id = '".$file_power_id."'");

        return $query->row;
    }

    public function addFilePower($data = array()) {
        if (!empty($data['department'])) {
            $department_ids = implode(',',$data['department']);
        } else {
            $department_ids = '';
        }
        if (!empty($data['role'])) {
            $role_ids = implode(',',$data['role']);
        } else {
            $role_ids = '';
        }
        $all_folder_ids = [];
        $folder_ids = explode(',',$data['folder_ids']);
        foreach ($folder_ids as $folder_id) {
            $parent_ids = $this->getParentFolder($folder_id);
            $all_folder_ids = array_merge($all_folder_ids,$parent_ids);
        }
        $this->db->query("INSERT INTO _union_file_power SET power_name = '" . $this->db->escape($data['power_name']) . "', folder_ids = '" . $this->db->escape($data['folder_ids']) . "', all_folder_ids = '" . implode(',',array_unique($all_folder_ids)) . "', department_ids = '" . $this->db->escape($department_ids) . "', role_ids = '" . $this->db->escape($role_ids) . "', date_added = NOW()");
    }

    public function editFilePower($data = array(),$file_power_id) {
        if (!empty($data['department'])) {
            $department_ids = implode(',',$data['department']);
        } else {
            $department_ids = '';
        }
        if (!empty($data['role'])) {
            $role_ids = implode(',',$data['role']);
        } else {
            $role_ids = '';
        }
        $all_folder_ids = [];
        $folder_ids = explode(',',$data['folder_ids']);
        foreach ($folder_ids as $folder_id) {
            $parent_ids = $this->getParentFolder($folder_id);
            $all_folder_ids = array_merge($all_folder_ids,$parent_ids);
        }
        $this->db->query("UPDATE _union_file_power SET power_name = '" . $this->db->escape($data['power_name']) . "', folder_ids = '" . $this->db->escape($data['folder_ids']) . "', all_folder_ids = '" . implode(',',array_unique($all_folder_ids)) . "', department_ids = '" . $this->db->escape($department_ids) . "', role_ids = '" . $this->db->escape($role_ids) . "', date_added = NOW() WHERE file_power_id=".$file_power_id);
    }

    public function getParentFolder($folder_id,$parent_ids = []) {
        $parent_ids[] = $folder_id;
        $query = $this->db->query("SELECT * FROM _union_file WHERE file_type = 'folder' AND file_id = '".$folder_id."'");
        if (!empty($query->row['folder_id']) && ($query->row['folder_id'] != 0)) {
            $aub_parent_ids = $this->getParentFolder($query->row['folder_id'],$parent_ids);
            $parent_ids = array_merge($parent_ids,$aub_parent_ids);
        }
        return $parent_ids;
    }

    public function getFolder($folder_id) {
        $query = $this->db->query("SELECT * FROM _union_file WHERE file_type = 'folder' AND folder_id = '".$folder_id."'");

        return $query->rows;
    }

    public function getAllFolder() {
//        $top_query = $this->db->query("SELECT file_id FROM _union_file WHERE file_type = 'folder' AND folder_id = '0'");
        $top_query = $this->db->query("SELECT file_id FROM _union_file WHERE folder_id = '0' AND status >= '0'");

        $top_ids = [];
        if (!empty($top_query->rows)) {
            foreach ($top_query->rows as $v) {
                $top_ids[] = $v['file_id'];
            }
        }

//        $query = $this->db->query("SELECT * FROM _union_file WHERE file_type = 'folder' AND (folder_id = '0' OR FIND_IN_SET(folder_id, '" . implode(',',$top_ids) . "'))");
        $query = $this->db->query("SELECT * FROM _union_file WHERE (folder_id = '0' OR FIND_IN_SET(folder_id, '" . implode(',',$top_ids) . "')) AND status >= '0'");

        return $query->rows;
    }

    public function getDepartments() {
        $query = $this->db->query("SELECT * FROM _union_department WHERE status = '1'");

        return $query->rows;
    }

    public function getRoles() {
        $query = $this->db->query("SELECT * FROM _union_role WHERE status = '1'");

        return $query->rows;
    }

    public function deletePower($power_id) {
        $this->db->query("UPDATE _union_file_power SET status = '0' WHERE file_power_id=".$power_id);
    }

}