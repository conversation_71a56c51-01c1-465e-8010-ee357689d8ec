<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-warning">
        <div class="row">
          <div class="col-xs-12 pdh10"></div>
          <div class="col-xs-12 text-center">
            <a class="btn btn-info" id="pickfiles" href="javascript:;" title="">选择压缩包</a>
            <input id="originaltarfile" type="hidden" name="original_name" value="">
            <input id="tarfile" type="hidden" name="bill_file" value="">
            <input id="tarfilehash" type="hidden" name="bill_file_hash" value="">
            <span class="text-success pdw10" id="updone" style="display:none;">
          <span class="glyphicon glyphicon-ok"></span>
          上传完成
        </span>
          </div>
          <div class="col-xs-12 pdh10"></div>
        </div>
        <div class="row">
          <div class="col-xs-12">
            <div class="progress" id="tar-progress" style="display:none;">
              <div class="progress-bar progress-bar-success progress-bar-striped active" role="progressbar" aria-valuenow="2" aria-valuemin="0" aria-valuemax="100">
                <p class="text">已上传 <span id="percent"></span>%&nbsp;&nbsp;&nbsp;速度 <span id="speed"></span>/s</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="form-group">
        <div class="col-sm-offset-2 col-sm-8">
          <button class="btn btn-primary" id="btn-sub" type="button">确认</button>
          <a href="<?php echo $action; ?>" class="btn btn-default">重置数据</a>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script src="<?php echo HTTP_SERVER; ?>static/js/plupload.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/qiniu.min.js"></script>
<script type="text/javascript">
  new Qiniu.uploader({
    browse_button: 'pickfiles',
    runtimes: 'html5,html4',
    uptoken: '<?php echo $gettoken; ?>',
    get_new_uptoken: false,
    unique_names: true,
    save_key: true,
    max_file_size: '1000mb',
    chunk_size: '4mb',
    max_retries: 3,
    dragdrop: false,
    multi_selection: false,
    domain: 'http://public.roogo.youranjian.cn/',
    auto_start: true,
    init: {
      'BeforeChunkUpload': function(up, file) {
        $('#tar-progress').show('fast')
        $("#updone").hide()
      },
      'FilesAdded': function () {},
      'BeforeUpload': function (up, file) {
        $('#originaltarfile').val(file.name)
      },
      'UploadProgress': function (up, file) {
        $('#tar-progress .progress-bar').width(file.percent + '%')
        $('#percent').text(file.percent)
        $('#speed').text(plupload.formatSize(file.speed).toUpperCase())
      },
      'UploadComplete': function (up, file) {},
      'FileUploaded': function (up, file, info) {
        $("#updone").show()
        var res = JSON.parse(info.response);
        $('#tarfile').val(res.key)
        $('#tarfilehash').val(res.hash)
        $('#tar-progress').hide('fast')
        $('#tar-progress .progress-bar').width('0%')
      },
      'Error': function (up, err, errTip) {
        console.log(up)
        console.log(err)
        console.log(errTip)
        alert(errTip)
        $('#tar-progress').hide('fast')
        $('#tar-progress .progress-bar').width('0%')
      }
    }
  })
</script>
<script>
  $('#btn-sub').on('click', function() {
    var bill_file = $('input[name=\'bill_file\']').val();
    var bill_file_hash = $('input[name=\'bill_file_hash\']').val();
    var original_name = $('input[name=\'original_name\']').val();
    if (bill_file == '' || bill_file_hash == '' || original_name == '') {
      confirm("请选择压缩包")
      return false
    }
    bill_file = 'http://public.roogo.youranjian.cn/'+bill_file
    $.ajax({
      url: '<?php echo $action; ?>',
      type: 'post',
      dataType: 'json',
      data: {bill_file,bill_file_hash,original_name},
      success: function(json) {
        if (json['status'] == 1) {
          confirm("提交成功")
        } else if (json['status'] == 2){
          confirm("重复上传")
        } else {
          confirm("未知错误")
        }

      },
      error: function(xhr, ajaxOptions, thrownError) {
        // alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        confirm("上传失败，请刷新页面重试")
      }
    });
  })
</script>
<?php echo $footer; ?>