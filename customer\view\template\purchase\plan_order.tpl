<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        备货清单
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?> </div>
      <?php } ?>
      <div class="box box-primary">
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-order" class="form-horizontal">
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-store">采购合同号：</label>
              <div class="col-sm-8">
                <input type="text" name="order_no" value="<?php echo (!empty($order_no) ? $order_no : '采购下单' . date('YmdH')); ?>" placeholder="输入采购合同号" id="input-product" class="form-control" />
              </div>
            </div>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-product">合同到货日期：</label>
              <div class="col-sm-8">
                <div class="input-group">
                  <div class="input-group-addon">
                    <i class="glyphicon glyphicon-calendar"></i>
                  </div>
                  <input type="text" name="complete_date" class="form-control" id="reservation" placeholder="请选择合同到货日期" value="<?php echo $complete_date; ?>" readonly="readonly">
                </div>
              </div>
            </div>
            <div class="table-responsive">
              <table id="plans" class="table table-striped table-bordered table-hover">
                <thead>
                  <tr>
                    <th width="30"><input id="selectAll" class="flat" type="checkbox"></th>
                    <td class="text-left">图片</td>
                    <td class="text-left">产品名称编码</td>
                    <td class="text-left">备货数量</td>
                    <td class="text-left">可发库存</td>
                    <td class="text-left">待审核量</td>
                    <td class="text-left">采购在途</td>
                    <td width="150" class="text-left">采购数量</td>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($plans as $plan) { ?>
                  <tr>
                    <td><input class="flat" type="checkbox" name="selected[<?php echo $plan['bsku']; ?>]" value="<?php echo implode(',', $plan['plan_ids']); ?>"></td>
                    <td class="text-left"><img width="100" src="<?php echo $plan['img_url']; ?>" class="img-thumbnail"></td>
                    <td class="text-left"><?php echo $plan['spec_name']; ?><br><?php echo $plan['bsku']; ?></td>
                    <td class="text-left"><div class="dropdown">
                      <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                        <?php echo $plan['plan_quan']; ?>
                        <span class="caret"></span>
                      </a>
                      <ul class="dropdown-menu">
                        <?php foreach ($plan['plan_list'] as $list) { ?>
                        <li><a href="javascript:void(0);"><?php echo $list['storename']; ?>：<?php echo $list['plan_quan']; ?></a></li>
                        <?php } ?>
                      </ul>
                    </div></td>
                    <td class="text-left"><?php echo (int)$plan['avaliable_num']; ?></td>
                    <td class="text-left"><?php echo (int)$plan['order_num']; ?></td>
                    <td class="text-left"><?php echo (int)$plan['purchase_num']; ?></td>
                    <td class="text-left"><input type="number" name="quantity[<?php echo $plan['bsku']; ?>]" value="<?php echo $plan['plan_quan']; ?>" placeholder="采购数量" class="form-control" /></td>
                  </tr>
                  <?php } ?>
                </tbody>
              </table>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交下单</button>
                <a href="<?php echo $action; ?>" class="btn btn-default">重置数据</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript"><!--
(function () {
    // 全选操作
    $('#selectAll').on('ifChecked', function() {
      $('input.flat').iCheck('check')
    })
    $('#selectAll').on('ifUnchecked', function() {
      $('input.flat').iCheck('uncheck')
    })
})()

// 日期显示
$('#reservation').daterangepicker({
  autoApply: true,
  autoUpdateInput: false,
  singleDatePicker: true,
  timePicker: false,
  timePicker24Hour: true,
  locale: {
    format: 'YYYY-MM-DD',
    applyLabel: '确定',
    cancelLabel: '清除'
  }
})
$('#reservation').on('apply.daterangepicker', function(ev, picker) {
  $(this).val(picker.startDate.format('YYYY-MM-DD'))
})
$('#reservation').on('cancel.daterangepicker', function(ev, picker) {
  $(this).val('')
})
//--></script>
<?php echo $footer; ?>