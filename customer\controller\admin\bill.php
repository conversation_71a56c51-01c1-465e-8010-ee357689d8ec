<?php
class ControllerAdminBill extends Controller {
	private $error = array();

	public function add() {
		$this->load->model('admin/bill');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$this->model_admin_bill->addBill($this->request->post);

			$this->session->data['success'] = $this->language->get('text_add_success');

			$url = '';

			if (isset($this->request->get['filter_store'])) {
				$url .= '&filter_store=' . $this->request->get['filter_store'];
			}

	        if (isset($this->request->get['filter_state'])) {
	            $url .= '&filter_state=' . $this->request->get['filter_state'];
	        }

	        if (isset($this->request->get['filter_date_start'])) {
	            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
	        }

	        if (isset($this->request->get['filter_date_end'])) {
	            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
	        }

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->getForm();
	}

	public function edit() {
		$this->load->model('admin/bill');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$this->model_admin_bill->editBill($this->request->get['bill_id'], $this->request->post);

			$this->session->data['success'] = $this->language->get('text_edit_success');

			$url = '';

			if (isset($this->request->get['filter_store'])) {
				$url .= '&filter_store=' . $this->request->get['filter_store'];
			}

	        if (isset($this->request->get['filter_state'])) {
	            $url .= '&filter_state=' . $this->request->get['filter_state'];
	        }

	        if (isset($this->request->get['filter_date_start'])) {
	            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
	        }

	        if (isset($this->request->get['filter_date_end'])) {
	            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
	        }

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->getForm();
	}

	public function getList() {
		if (isset($this->request->get['filter_store'])) {
			$filter_store = $this->request->get['filter_store'];
		} else {
			$filter_store = '';
		}

		if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = 'bill_date';  
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = 'DESC';
		}

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_store'])) {
			$url .= '&filter_store=' . $this->request->get['filter_store'];
		}

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$data['add'] = $this->url->link('admin/bill/add', 'token=' . $this->session->data['token'] . $url);
		$data['delete'] = $this->url->link('admin/bill/delete', 'token=' . $this->session->data['token'] . $url);

		$data['bills'] = array();

		$filter_data = array(
			'filter_store'		=> $filter_store,
			'filter_state'		=> $filter_state,
			'filter_date_start'	=> $filter_date_start,
			'filter_date_end'	=> $filter_date_end,
			'sort'			=> $sort,
			'order'			=> $order,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);
		
		$this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

		$this->load->model('admin/bill');
		$results = $this->model_admin_bill->getBills($filter_data);

		foreach ($results as $result) {
			if (!empty($result['state'])) {
				$monthly = $this->model_admin_bill->getMonthly($result['store_id'], $result['bill_date']);
			} else {
				$monthly = array();
			}

			$data['bills'][] = array(
				'bill_id' => $result['bill_id'],
				'store' => $stores[$result['store_id']] ?? '',
				'sales'	=> $result['sales'],
				'advert'=> $result['advert'],
				'cost'	=> $result['cost'],
				'fee'	=> $result['fee'],
				'detail'=> json_decode($result['fee_detail'], true),
				'total'	=> $result['bill_total'],
				'month' => $result['monthly_total'],
				'date'	=> $result['bill_date'],
				'state'	=> $result['state'],
                'msales' 	  => $monthly['sales'] ?? '0.00',
				'madvert'	  => $monthly['advert'] ?? '0.00',
                'mcost' 	  => $monthly['cost'] ?? '0.00',
				'mfee'		  => $monthly['fee'] ?? '0.00',
                'mtotal' 	  => $monthly['total'] ?? '0.00',
                'dtotal_ratio'=> (isset($result['sales']) && (float)$result['sales'] > 0) ? priceformat($result['bill_total'] * 100 / $result['sales']) : '0.00',
                'dcost_ratio' => (isset($result['sales']) && (float)$result['sales'] > 0) ? priceformat($result['cost'] * 100 / $result['sales']) : '0.00',
                'mtotal_ratio'=> (isset($monthly['sales']) && (float)$monthly['sales'] > 0) ? priceformat($monthly['total'] * 100 / $monthly['sales']) : '0.00',
                'mcost_ratio' => (isset($monthly['sales']) && (float)$monthly['sales'] > 0) ? priceformat($monthly['cost'] * 100 / $monthly['sales']) : '0.00',
				'edit'  => $this->url->link('admin/bill/edit', 'token=' . $this->session->data['token'] . '&bill_id=' . $result['bill_id'] . $url)
			);
		}

		$total = $this->model_admin_bill->getTotalBills($filter_data);

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_store'])) {
			$url .= '&filter_store=' . $this->request->get['filter_store'];
		}

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if ($order == 'ASC') {
			$url .= '&order=DESC';
		} else {
			$url .= '&order=ASC';
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['sort_store'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
		$data['sort_sales'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . '&sort=sales' . $url);
		$data['sort_advert'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . '&sort=advert' . $url);
		$data['sort_cost'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . '&sort=cost' . $url);
		$data['sort_fee'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . '&sort=fee' . $url);
		$data['sort_state'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . '&sort=state' . $url);
		$data['sort_total'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . '&sort=bill_total' . $url);
		$data['sort_monthly'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . '&sort=monthly_total' . $url);
		$data['sort_date'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . '&sort=bill_date' . $url);

		$url = '';

        if (isset($this->request->get['filter_store'])) {
			$url .= '&filter_store=' . $this->request->get['filter_store'];
		}

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

		$data['filter_store'] = $filter_store;
		$data['filter_state'] = $filter_state;
		$data['filter_date_start'] = $filter_date_start;
		$data['filter_date_end'] = $filter_date_end;

		$data['nofilter'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token']);

		$data['sort'] = $sort;
		$data['order'] = $order;

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('bill/list.tpl', $data));
	}

	protected function getForm() {
		$data['text_form'] = !isset($this->request->get['bill_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_store'])) {
			$url .= '&filter_store=' . $this->request->get['filter_store'];
		}

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		if (!isset($this->request->get['bill_id'])) {
			$data['action'] = $this->url->link('admin/bill/add', 'token=' . $this->session->data['token'] . $url);
		} else {
			$data['action'] = $this->url->link('admin/bill/edit', 'token=' . $this->session->data['token'] . '&bill_id=' . $this->request->get['bill_id'] . $url);
		}

		$data['cancel'] = $this->url->link('admin/bill/getList', 'token=' . $this->session->data['token'] . $url);

		if (isset($this->request->get['bill_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
			$bill_info = $this->model_admin_bill->getBill($this->request->get['bill_id']);
		}

		$this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();
        $store_ids = array();

        foreach ($data['stores'] as $store) {
            $store_ids[] = $store['store_id'];
        }

        if (isset($this->request->get['store_id'])) {
			$data['store_id'] = $this->request->get['store_id'];
		} elseif (!empty($bill_info)) {
			$data['store_id'] = $bill_info['store_id'];
		} else {
			$data['store_id'] = '';
		}

        if (!in_array($data['store_id'], $store_ids)) {
            $data['store_id'] = array_shift($store_ids);
        }

		if (!empty($bill_info)) {
			$data['bill_date'] = $bill_info['bill_date'];
		} else {
			$data['bill_date'] = date('Y-m-d', strtotime('-1 day'));
		}

		$summary = $this->model_admin_bill->getSalesSummary($data['store_id'], $data['bill_date']);

		if (isset($this->request->post['sales'])) {
			$data['sales'] = $this->request->post['sales'];
		} elseif (!empty($bill_info)) {
			$data['sales'] = $bill_info['sales'];
		}

		if (empty((float)$data['sales'])) {
			$data['sales'] = moneyformat($summary['sales']);
		}

		if (isset($this->request->post['cost'])) {
			$data['cost'] = $this->request->post['cost'];
		} elseif (!empty($bill_info)) {
			$data['cost'] = $bill_info['cost'];
		}

		if (empty((float)$data['cost'])) {
			$data['cost'] = moneyformat($summary['cost']);
		}

		if (isset($this->request->post['advert'])) {
			$data['advert'] = $this->request->post['advert'];
		} elseif (!empty($bill_info)) {
			$data['advert'] = $bill_info['advert'];
		} else {
			$data['advert'] = '0';
		}

		if (isset($this->request->post['fee_detail'])) {
			$data['fee_detail'] = $this->request->post['fee_detail'];
		} elseif (!empty($bill_info)) {
			$data['fee_detail'] = json_decode($bill_info['fee_detail'], true);
		}

		if (empty($data['fee_detail'])) {
			$data['fee_detail'] = $this->model_admin_bill->getFeeDetail($data['store_id']);
		}
		
		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('bill/form.tpl', $data));
	}

	protected function validateForm() {
		if (!$this->user->hasPermission('modify', 'admin/bill')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if (empty($this->request->post['fee_detail'])) {
            $this->request->post['fee_detail'] = array();
        }

		if (empty($this->request->post['store_id'])) {
            $this->error['warning'] = '所属店铺不能为空！';
            return false;
        }

		return !$this->error;
	}

	protected function validateDelete() {
		if (!$this->user->hasPermission('modify', 'admin/bill')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		return !$this->error;
	}

	public function addFee() {
		$this->load->model('admin/bill');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateFeeForm()) {
			$this->model_admin_bill->addFee($this->request->post);

			$this->session->data['success'] = $this->language->get('text_add_success');

			$url = '';

			if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_store'])) {
				$url .= '&filter_store=' . $this->request->get['filter_store'];
			}

	        if (isset($this->request->get['filter_calculate'])) {
	            $url .= '&filter_calculate=' . $this->request->get['filter_calculate'];
	        }

	        if (isset($this->request->get['filter_method'])) {
	            $url .= '&filter_method=' . $this->request->get['filter_method'];
	        }

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/bill/getFeeList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->getFeeForm();
	}

	public function editFee() {
		$this->load->model('admin/bill');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateFeeForm()) {
			$this->model_admin_bill->editFee($this->request->get['fee_id'], $this->request->post);

			$this->session->data['success'] = $this->language->get('text_edit_success');

			$url = '';

			if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_store'])) {
				$url .= '&filter_store=' . $this->request->get['filter_store'];
			}

	        if (isset($this->request->get['filter_calculate'])) {
	            $url .= '&filter_calculate=' . $this->request->get['filter_calculate'];
	        }

	        if (isset($this->request->get['filter_method'])) {
	            $url .= '&filter_method=' . $this->request->get['filter_method'];
	        }

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/bill/getFeeList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->getFeeForm();
	}

	public function deleteFee() {
		$this->load->model('admin/bill');

		if (isset($this->request->post['selected']) && $this->validateFeeDelete()) {
			foreach ($this->request->post['selected'] as $fee_id) {
				$this->model_admin_bill->deleteFee($fee_id);
			}

			$this->session->data['success'] = $this->language->get('text_delete_success');

			$url = '';

			if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_store'])) {
				$url .= '&filter_store=' . $this->request->get['filter_store'];
			}

	        if (isset($this->request->get['filter_calculate'])) {
	            $url .= '&filter_calculate=' . $this->request->get['filter_calculate'];
	        }

	        if (isset($this->request->get['filter_method'])) {
	            $url .= '&filter_method=' . $this->request->get['filter_method'];
	        }

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/bill/getFeeList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->getFeeList();
	}

	public function getFeeList() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_store'])) {
			$filter_store = $this->request->get['filter_store'];
		} else {
			$filter_store = '';
		}

		if (isset($this->request->get['filter_calculate'])) {
            $filter_calculate = $this->request->get['filter_calculate'];
        } else {
            $filter_calculate = '';
        }

        if (isset($this->request->get['filter_method'])) {
            $filter_method = $this->request->get['filter_method'];
        } else {
            $filter_method = '';
        }

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_store'])) {
			$url .= '&filter_store=' . $this->request->get['filter_store'];
		}

        if (isset($this->request->get['filter_calculate'])) {
            $url .= '&filter_calculate=' . $this->request->get['filter_calculate'];
        }

        if (isset($this->request->get['filter_method'])) {
            $url .= '&filter_method=' . $this->request->get['filter_method'];
        }

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$data['add'] = $this->url->link('admin/bill/addFee', 'token=' . $this->session->data['token'] . $url);
		$data['delete'] = $this->url->link('admin/bill/deleteFee', 'token=' . $this->session->data['token'] . $url);

		$data['fees'] = array();

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_store'		=> $filter_store,
			'filter_calculate'	=> $filter_calculate,
			'filter_method'		=> $filter_method,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);

		$this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores(array('all' => 1));

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

		$this->load->model('admin/bill');
		$results = $this->model_admin_bill->getFees($filter_data);

		foreach ($results as $result) {
			$store_names = array();

			foreach ((array)explode(',', $result['store_ids']) as $store_id) {
				$store_names[] = $stores[$store_id] ?? '';
			}

			$data['fees'][] = array(
				'fee_id'	=> $result['fee_id'],
				'name'		=> $result['fee_name'],
				'total'		=> $result['fee_total'],
				'percent'	=> $result['fee_percent'],
				'calculate'	=> $result['calculate'],
				'stores'	=> $store_names,
				'method'	=> $result['method'],
				'order'		=> $result['sort_order'],
				'date_added'=> $result['date_added'],
				'edit'  => $this->url->link('admin/bill/editFee', 'token=' . $this->session->data['token'] . '&fee_id=' . $result['fee_id'] . $url)
			);
		}

		$total = $this->model_admin_bill->getTotalFees($filter_data);

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_store'])) {
			$url .= '&filter_store=' . $this->request->get['filter_store'];
		}

        if (isset($this->request->get['filter_calculate'])) {
            $url .= '&filter_calculate=' . $this->request->get['filter_calculate'];
        }

        if (isset($this->request->get['filter_method'])) {
            $url .= '&filter_method=' . $this->request->get['filter_method'];
        }

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/bill/getFeeList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
		$data['filter_store'] = $filter_store;
		$data['filter_calculate'] = $filter_calculate;
		$data['filter_method'] = $filter_method;

		$data['nofilter'] = $this->url->link('admin/bill/getFeeList', 'token=' . $this->session->data['token']);

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('bill/fee_list.tpl', $data));
	}

	protected function getFeeForm() {
		$data['text_form'] = !isset($this->request->get['fee_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_store'])) {
			$url .= '&filter_store=' . $this->request->get['filter_store'];
		}

        if (isset($this->request->get['filter_calculate'])) {
            $url .= '&filter_calculate=' . $this->request->get['filter_calculate'];
        }

        if (isset($this->request->get['filter_method'])) {
            $url .= '&filter_method=' . $this->request->get['filter_method'];
        }

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		if (!isset($this->request->get['fee_id'])) {
			$data['action'] = $this->url->link('admin/bill/addFee', 'token=' . $this->session->data['token'] . $url);
		} else {
			$data['action'] = $this->url->link('admin/bill/editFee', 'token=' . $this->session->data['token'] . '&fee_id=' . $this->request->get['fee_id'] . $url);
		}

		$data['cancel'] = $this->url->link('admin/bill/getFeeList', 'token=' . $this->session->data['token'] . $url);

		if (isset($this->request->get['fee_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
			$fee_info = $this->model_admin_bill->getFee($this->request->get['fee_id']);
		}

		$fields = array('fee_name', 'fee_total', 'fee_percent', 'calculate', 'method', 'sort_order');

		foreach ($fields as $field) {
			if (isset($this->request->post[$field])) {
				$data[$field] = $this->request->post[$field];
			} elseif (!empty($fee_info)) {
				$data[$field] = $fee_info[$field];
			} else {
				$data[$field] = '';
			}
		}

		$this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        if (isset($this->request->post['store_ids'])) {
            $data['store_ids'] = $this->request->post['store_ids'];
        } elseif (!empty($fee_info)) {
            $data['store_ids'] = explode(',', $fee_info['store_ids']);
        } else {
            $data['store_ids'] = array();
        }
		
		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('bill/fee_form.tpl', $data));
	}

	protected function validateFeeForm() {
		if (!$this->user->hasPermission('modify', 'admin/bill/fee')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if (empty($this->request->post['fee_name'])) {
            $this->error['warning'] = '费用名称不能为空！';
            return false;
        }

		if (empty($this->request->post['calculate'])) {
            $this->error['warning'] = '计算方式不能为空！';
            return false;
        }

		if (empty($this->request->post['fee_total']) && empty($this->request->post['fee_percent'])) {
            $this->error['warning'] = '金额或百分比不能为空！';
            return false;
        }

		if (empty($this->request->post['store_ids'])) {
            $this->error['warning'] = '分摊店铺不能为空！';
            return false;
        }

		if (empty($this->request->post['method'])) {
            $this->error['warning'] = '分摊方式不能为空！';
            return false;
        }

		return !$this->error;
	}

	protected function validateFeeDelete() {
		if (!$this->user->hasPermission('modify', 'admin/bill/fee')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		return !$this->error;
	}

	public function getCost() {

	}

	public function getShipCost() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'month';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $data['stockouts'] = array();

        $filter_data = array(
            'filter_name'       => $filter_name,
            'filter_store'      => $filter_store,
            'filter_date_start' => $filter_date_start,
            'filter_date_end'   => $filter_date_end,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit')
        );

        $this->load->model('admin/bill');
        $data['months'] = $this->model_admin_bill->getMonths();

        $results = $this->model_admin_bill->getShipList($filter_data);

        foreach ($results as $result) {
            $data['stockouts'][] = array(
                'store' => $stores[$result['store_id']] ?? '',
                'month' => $result['month'],
                'count' => $result['count'],
                'quan'  => $result['quan'],
                'total'	=> $result['total'],
                'detail'=> $this->url->link('admin/bill/getShipCostDetail', 'token=' . $this->session->data['token'] . '&store_id=' . $result['store_id'] . '&month=' . $result['month'])
            );
        }

        $total = $this->model_admin_bill->getShipTotal($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_store'] = $this->url->link('admin/bill/getShipCost', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
        $data['sort_month'] = $this->url->link('admin/bill/getShipCost', 'token=' . $this->session->data['token'] . '&sort=month' . $url);
        $data['sort_count'] = $this->url->link('admin/bill/getShipCost', 'token=' . $this->session->data['token'] . '&sort=count' . $url);
        $data['sort_quan'] = $this->url->link('admin/bill/getShipCost', 'token=' . $this->session->data['token'] . '&sort=quan' . $url);
        $data['sort_total'] = $this->url->link('admin/bill/getShipCost', 'token=' . $this->session->data['token'] . '&sort=total' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/bill/getShipCost', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/bill/getShipCost', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('bill/ship_list.tpl', $data));
    }

    public function getShipCostDetail(){
        if (isset($this->request->get['store_id'])) {
            $store_id = $this->request->get['store_id'];
        } else {
            $store_id = '';
        }

        if (isset($this->request->get['month'])) {
            $month = $this->request->get['month'];
        } else {
            $month = '';
        }

        $data['list'] = $this->url->link('admin/bill/getShipCost', 'token=' . $this->session->data['token']);

        $this->load->model('admin/bill');
        $data['products'] = $this->model_admin_bill->getShipDetail($store_id, $month);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('bill/ship_detail.tpl', $data));
    }

    public function getReturnCost() {
    	$this->request->get[''] = 'return';
    	
    	$this->getShipCost();
    }

    public function grossProfitRate() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
            $data_filter_store = explode(',',$this->request->get['filter_store']);
        } else {
            $filter_store = '';
            $data_filter_store = [];
        }

        if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '0';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'gross_margin';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . $this->request->get['filter_name'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (isset($this->request->get['search'])) {
            $url .= '&search=1';
        }

        $data['sort_total_sales'] = $this->url->link('admin/bill/grossProfitRate', 'token=' . $this->session->data['token'] . '&sort=total_sales' . $url);
        $data['sort_total_quantity'] = $this->url->link('admin/bill/grossProfitRate', 'token=' . $this->session->data['token'] . '&sort=total_quantity' . $url);
        $data['sort_total_cost'] = $this->url->link('admin/bill/grossProfitRate', 'token=' . $this->session->data['token'] . '&sort=total_cost' . $url);
        $data['sort_gross_profit'] = $this->url->link('admin/bill/grossProfitRate', 'token=' . $this->session->data['token'] . '&sort=gross_profit' . $url);
        $data['sort_gross_margin'] = $this->url->link('admin/bill/grossProfitRate', 'token=' . $this->session->data['token'] . '&sort=gross_margin' . $url);

        $data['lists'] = array();

        $filter_data = array(
            'filter_user'		=> [$this->user->union_id],
        );
        $this->load->model('admin/bill');
        $user_store_povers = $this->model_admin_bill->getUserStorePovers($filter_data);

        if (!in_array($this->user->user_group_id,[1,6,9])) {
            $user_store = explode(',',$this->user->store_ids);
            foreach ($user_store_povers as $v) {
                $user_store = array_merge($user_store,explode(',',$v['stores']));
            }
            if (!empty($user_store)) {
                $user_store = array_unique($user_store);
            }
            $data['stores'] = $this->model_admin_bill->getStores(implode(',',$user_store));
        } else {
            $this->load->model('admin/setting');
            $data['stores'] = $this->model_admin_setting->getStores();
        }

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $data['stores_k'] = $stores;

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_store'		=> $filter_store,
            'filter_state'		=> $filter_state,
            'filter_date_start'	=> $filter_date_start,
            'filter_date_end'	=> $filter_date_end,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit'),
            'all_store_id'  => implode(',',array_column($data['stores'],'store_id'))
        );

        $data['lists'] = [];
        $total = 0;
        $data['search'] = 0;
        if (isset($this->request->get['search'])) {
            $data['lists'] = $this->model_admin_bill->getGrossProfitRate($filter_data);

            $total = $this->model_admin_bill->getTotalGrossProfitRate($filter_data);
            $data['search'] = 1;
        }

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . $this->request->get['filter_name'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['search'])) {
            $url .= '&search=1';
        }

        $data['export'] = $this->url->link('admin/bill/exportGrossProfitRate', 'token=' . $this->session->data['token'] . $url);
        if (in_array($this->user->user_group_id,[1,6,9])) {
            $data['userStore'] = $this->url->link('admin/bill/grossProfitRateUserStore', 'token=' . $this->session->data['token']);
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/bill/grossProfitRate', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_store_json'] = json_encode($data_filter_store);
        $data['filter_state'] = $filter_state;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['nofilter'] = $this->url->link('admin/bill/grossProfitRate', 'token=' . $this->session->data['token']);


        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('bill/gross_profit_rate.tpl', $data));
    }

    public function exportGrossProfitRate() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '0';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'gross_margin';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_store'		=> $filter_store,
            'filter_state'		=> $filter_state,
            'filter_date_start'	=> $filter_date_start,
            'filter_date_end'	=> $filter_date_end,
            'sort'			=> $sort,
            'order'			=> $order,
            'all_store_id'  => implode(',',array_column($data['stores'],'store_id'))
        );

        $data['search'] = 0;
        $this->load->model('admin/bill');
        $lists = $this->model_admin_bill->getGrossProfitRate($filter_data);

        $export_data = array();
        if ($filter_state == 0) {
            $export_data[] = array('店铺', '总销售额', '销售数量', '总成本', '毛利率(%)');
            foreach ($lists as $v) {
                $export_data[] = array(
                    $stores[$v['store_id']],
                    $v['total_sales'] ?? '',
                    $v['total_quantity'] ?? '',
                    $v['total_cost'] ?? '',
                    $v['gross_margin'] ? $v['gross_margin'].'%' : '--',
                );
            }
        } else {
            $export_data[] = array('产品名称', '编码', '总销售额', '销售数量', '总成本', '毛利率(%)');
            foreach ($lists as $v) {
                $export_data[] = array(
                    $v['spec_name'] ?? '',
                    $v['product_code'] ?? '',
                    $v['total_sales'] ?? '',
                    $v['total_quantity'] ?? '',
                    $v['total_cost'] ?? '',
                    $v['gross_margin'] ? $v['gross_margin'].'%' : '--',
                );
            }
        }

        if (!empty($export_data)) {
            $this->load->model('admin/excel');
            $this->model_admin_excel->export('毛利率' . date('Y-m-d'), $export_data, array(2 => 'numbric', 3 => 'numbric', 4 => 'numbric', 5 => 'numbric'), '.xlsx');
        }

        $this->grossProfitRate();
    }

    public function grossProfitRateUserStore() {
        if (isset($this->request->get['filter_user'])) {
            $filter_user = $this->request->get['filter_user'];
            $data_filter_user = explode(',',$this->request->get['filter_user']);
        } else {
            $filter_user = '';
            $data_filter_user = [];
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
            $data_filter_store = explode(',',$this->request->get['filter_store']);
        } else {
            $filter_store = '';
            $data_filter_store = [];
        }

        $data['add'] = $this->url->link('admin/bill/grossProfitRateUserStoreSet', 'token=' . $this->session->data['token']);
        $data['delete'] = $this->url->link('admin/bill/grossProfitRateUserStoreDelete', 'token=' . $this->session->data['token']);

        $filter_data = array(
            'filter_user'		=> $data_filter_user,
            'filter_store'		=> $data_filter_store,
        );

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $this->load->model('admin/department');
        $data['users'] = $this->model_admin_department->getUser();

        $all_users = array();

        foreach ($data['users'] as $all_user) {
            $all_users[$all_user['union_id']] = $all_user['real_name'];
        }

        $this->load->model('admin/bill');
        $results = $this->model_admin_bill->getUserStorePovers($filter_data);

        foreach ($results as $k => $v) {
            $data['userStores'][$k]['user_store_pover_id'] = $v['user_store_pover_id'];
            $data['userStores'][$k]['name'] = $v['name'];

            $users = explode(',',$v['users']);
            foreach ($users as $user_k => $user_v) {
                $users[$user_k] = $all_users[$user_v];
            }
            $data['userStores'][$k]['users'] = implode(',',$users);

            $in_stores = explode(',',$v['stores']);
            foreach ($in_stores as $store_k => $store_v) {
                $in_stores[$store_k] = $stores[$store_v];
            }
            $data['userStores'][$k]['stores'] = implode(',',$in_stores);
        }

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['filter_user'] = $filter_user;
        $data['filter_store'] = $filter_store;
        $data['filter_user_json'] = json_encode($data_filter_user);
        $data['filter_store_json'] = json_encode($data_filter_store);

        $data['nofilter'] = $this->url->link('admin/bill/grossProfitRateUserStore', 'token=' . $this->session->data['token']);
        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('bill/user_store_list.tpl', $data));
    }

    public function grossProfitRateUserStoreSet() {
        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->load->model('admin/bill');

            if (isset($this->request->get['user_store_pover_id'])) {
                $this->model_admin_bill->editUserStorePover($this->request->get['user_store_pover_id'],$this->request->post);
            } else {
                $this->model_admin_bill->addUserStorePover($this->request->post);
            }

            $this->grossProfitRateUserStore();
        } else {
            $this->load->model('admin/setting');
            $data['stores'] = $this->model_admin_setting->getStores();
            $this->load->model('admin/department');
            $data['users'] = $this->model_admin_department->getUser();

            $data['user_store_name'] = '';
            $data['filter_user_json'] = '{}';
            $data['filter_store_json'] = '{}';

            $url = '';
            if (isset($this->request->get['user_store_pover_id'])) {
                $data['user_store_pover_id'] = $this->request->get['user_store_pover_id'];

                $url .= '&user_store_pover_id='.$this->request->get['user_store_pover_id'];

                $this->load->model('admin/bill');
                $userStorePover = $this->model_admin_bill->getUserStorePover($this->request->get['user_store_pover_id']);
                if (!empty($userStorePover)) {
                    $data['user_store_name'] = $userStorePover['name'];
                    $data['filter_user_json'] = json_encode(explode(',',$userStorePover['users']));
                    $data['filter_store_json'] = json_encode(explode(',',$userStorePover['stores']));
                }
            }

            if (isset($this->error['warning'])) {
                $data['warning'] = $this->error['warning'];
            } else {
                $data['warning'] = '';
            }

            $data['text_form'] = !isset($this->request->get['user_store_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
            $data['action'] = $this->url->link('admin/bill/grossProfitRateUserStoreSet', 'token=' . $this->session->data['token'] . $url);
            $data['cancel'] = $this->url->link('admin/bill/grossProfitRateUserStore', 'token=' . $this->session->data['token']);

            $data['header'] = $this->load->controller('admin/template/header');
            $data['content_top'] = $this->load->controller('admin/template/top');
            $data['content_bottom'] = $this->load->controller('admin/template/bottom');
            $data['footer'] = $this->load->controller('admin/template/footer');
            $this->response->setOutput($this->load->view('bill/user_store_form.tpl', $data));
        }

    }

    public function grossProfitRateUserStoreDelete() {
        $this->load->model('admin/bill');

        if (isset($this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $user_store_pover_id) {
                $this->model_admin_bill->deleteUserStorePover($user_store_pover_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['filter_user'])) {
                $url .= '&filter_user=' . $this->request->get['filter_user'];
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }


            $this->response->redirect($this->url->link('admin/bill/grossProfitRateUserStore', 'token=' . $this->session->data['token'] . $url));
        }

        $this->grossProfitRateUserStore();
    }

    public function performance(){

        $this->load->model('admin/bill');
 
         if (isset($this->request->get['filter_name'])) {
             $filter_name = $this->request->get['filter_name'];
         } else {
             $filter_name = '';
         }
 
 
 
         if (isset($this->request->get['sort'])) {
             $sort = $this->request->get['sort'];
         } else {
             $sort = 'score asc';
         }
 
        
 
         if (isset($this->request->get['order'])) {
             $order = $this->request->get['order'];
         } else {
             $order = 'ASC';
         }
 
 
         if (isset($this->request->get['page'])) {
             $page = $this->request->get['page'];
         } else {
             $page = 1;
         }
 
         $url = '';
 
         if (isset($this->request->get['filter_name'])) {
             $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
         }
 

 
 
         if (isset($this->request->get['sort'])) {
             $url .= '&sort=' . $this->request->get['sort'];
         }
 
         if (isset($this->request->get['order'])) {
             $url .= '&order=' . $this->request->get['order'];
         }
 
 
         if ($order == 'ASC') {
             $url .= '&order=DESC';
         } else {
             $url .= '&order=ASC';
         }
 
         if (isset($this->request->get['page'])) {
             $url .= '&page=' . $this->request->get['page'];
         }
 

         $data['update_import'] = $this->url->link('admin/bill/performanceUpdateImport', 'token=' . $this->session->data['token']);
 
 

 
         $filter_data = array(
             'filter_name' => $filter_name,
             'sort' => $sort,
             'order'=> $order,
             'start' => ($page - 1) * $this->config->get('config_limit'),
             'limit' => $this->config->get('config_limit'),
         );
 
 
         $data['list'] = array();
 
         $results = $this->model_admin_bill->getPerformance($filter_data);
         
 
         foreach ($results as $k => $result) {
             $data['sheet'][$k] = array(
                 'union_id' => $result['union_id'],
                 'real_name' => $result['real_name'],
                 'score' => $result['extra_info'],
                 'detail' => $this->url->link('admin/bill/setPerformance', 'token=' . $this->session->data['token'] . '&union_id=' . $result['union_id'])
             );
         }
         $total = $this->model_admin_bill->getPerformanceTotal($filter_data);
 
         if (isset($this->error['warning'])) {
             $data['warning'] = $this->error['warning'];
         } else {
             $data['warning'] = '';
         }
 
         if (isset($this->session->data['success'])) {
             $data['success'] = $this->session->data['success'];
             unset($this->session->data['success']);
         } else {
             $data['success'] = '';
         }
 
         $url = '';
 
         if ($order == 'ASC') {
             $url .= '&order=DESC';
         } else {
             $url .= '&order=ASC';
         }
 
         if (isset($this->request->get['page'])) {
             $url .= '&page=' . $this->request->get['page'];
         }
 
       
         
         $data['sort_name'] = $this->url->link('admin/bill/performance', 'token=' . $this->session->data['token'] . '&sort=name' . $url);
         $data['sort_score'] = $this->url->link('admin/bill/performance', 'token=' . $this->session->data['token'] . '&sort=score' . $url);
 
 
         $url = '';
 
         if (isset($this->request->get['filter_name'])) {
             $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
         }
 
         if (isset($this->request->get['sort'])) {
             $url .= '&sort=' . $this->request->get['sort'];
         }
 
         if (isset($this->request->get['order'])) {
             $url .= '&order=' . $this->request->get['order'];
         }
 
         if (isset($this->request->get['page'])) {
             $url .= '&page=' . $this->request->get['page'];
         }
 
         $pagination = new Pagination();
         $pagination->total = $total;
         $pagination->page = $page;
         $pagination->limit = $this->config->get('config_limit');
         $pagination->url = $this->url->link('admin/bill/performance', 'token=' . $this->session->data['token'] . $url . '&page={page}');
         
         $pagination_template = $this->load->view('common/pagination.tpl');
         $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));
         $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

         $data['filter_name'] = $filter_name;
 
         $data['nofilter'] = $this->url->link('admin/bill/performance', 'token=' . $this->session->data['token']);
 
         $data['sort'] = $sort;
         $data['order'] = $order;
 
         $data['header'] = $this->load->controller('admin/template/header');
         $data['content_top'] = $this->load->controller('admin/template/top');
         $data['content_bottom'] = $this->load->controller('admin/template/bottom');
         $data['footer'] = $this->load->controller('admin/template/footer');
 
         $this->response->setOutput($this->load->view('bill/performance.tpl', $data));
     }

     public function performanceUpdateImport(){
        if (isset($this->request->post['score']) && isset($this->request->post['union_id'])) {
            $this->load->model('admin/bill');
            $this->model_admin_bill->editPerformance($this->request->post['score'],$this->request->post['union_id']);
        }
        $this->performance();
     }
}
