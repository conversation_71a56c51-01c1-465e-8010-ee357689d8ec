<?php

class ControllerAdminRole extends Controller
{
    private $error = array();

    public function add()
    {
        $this->load->model('admin/role');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_role->addRole($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/role/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function edit()
    {
        $this->load->model('admin/role');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_role->editRole($this->request->get['role_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/role/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function delete()
    {
        $this->load->model('admin/role');

        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $id) {
                $this->model_admin_role->deleteRole($id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/role/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getList();
    }

    public function getList()
    {
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }
        $data['add'] = $this->url->link('admin/role/add', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/role/delete', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['roles'] = array();

        $filter_data = array(
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );

        $this->load->model('admin/role');
        $results = $this->model_admin_role->getRoles($filter_data);

        foreach ($results as $result) {
            $data['roles'][] = array(
                'role_id' => $result['role_id'],
                'role_name' => $result['role_name'],
                'date_added' => $result['date_added'],
                'status'     => ($result['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled')),
                'edit' => $this->url->link('admin/role/edit', 'token=' . $this->session->data['token'] . '&role_id=' . $result['role_id'] . $url)
            );
        }

        $total = $this->model_admin_role->getTotalRoles();

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/role/getList', 'token=' . $this->session->data['token'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('role/role_list.tpl', $data));
    }

    protected function getForm()
    {
        $data['text_form'] = !isset($this->request->get['role_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['role_id'])) {
            $data['action'] = $this->url->link('admin/role/add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/role/edit', 'token=' . $this->session->data['token'] . '&role_id=' . $this->request->get['role_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/role/getList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['role_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $role_info = $this->model_admin_role->getRole($this->request->get['role_id']);
        }

        if (isset($this->request->post['role_name'])) {
            $data['role_name'] = $this->request->post['role_name'];
        } elseif (!empty($role_info)) {
            $data['role_name'] = $role_info['role_name'];
        } else {
            $data['role_name'] = '';
        }

        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($role_info)) {
            $data['status'] = $role_info['status'];
        } else {
            $data['status'] = 1;
        }
        $data['users'] = $this->model_admin_role->getUser();
        if (isset($this->request->post['union_id'])) {
            $data['union_id'] = $this->request->post['union_id'];
        } elseif (!empty($role_info)) {
            $data['union_id'] = explode(',', $role_info['union_id']);
        } else {
            $data['union_id'] = array();
        }
        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('role/role_form.tpl', $data));
    }

    protected function validateForm()
    {
        if (!$this->user->hasPermission('modify', 'admin/role')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['role_name']) < 1) || (utf8_strlen($this->request->post['role_name']) > 32)) {
            $this->error['warning'] = $this->language->get('error_account_length');
        }

        $role_info = $this->model_admin_role->getRoleByRoleName($this->request->post['role_name']);

        if (!isset($this->request->get['role_id'])) {
            if (!empty($role_info['role_name'])) {
                $this->error['warning'] = $this->language->get('error_role_name_exists');
            }
        } else {
            if ($role_info && ($this->request->get['role_id'] != $role_info['role_id'])) {
                $this->error['warning'] = $this->language->get('error_role_name_exists');
            }
        }


        return !$this->error;
    }

    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'admin/role')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }
}