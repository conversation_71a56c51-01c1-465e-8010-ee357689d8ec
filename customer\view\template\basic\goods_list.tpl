<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        货品信息
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="输入编码或名称查找" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>创建时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">货品列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <table id="stock" class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>图片</th>
              <th>名称</th>
              <th>编码</th>
              <th>尺寸</th>
              <th>装箱</th>
              <th>净重</th>
              <th>毛重</th>
              <th>批发1档</th>
              <th>批发2档</th>
              <th>批发3档</th>
              <th>材质</th>
            </tr>
            <?php if (!empty($goods)) { ?>
            <?php foreach ($goods as $good) { ?>
            <tr>
              <td><img width="100" src="<?php echo $good['img_url']; ?>" data-toggle="popover" data-content="<img src='<?php echo $good['img_url']; ?>' width='480'>" class="img-thumbnail"></td>
              <td><?php echo $good['spec_name']; ?></td>
              <td><?php echo $good['spec_no']; ?></td>
              <td>
                <?php if(!empty($good['bat_size'])){echo '外箱：'.$good['bat_size'].'<br>';} ?>
                <?php if(!empty($good['box_size'])){echo '内盒：'.$good['box_size'].'<br>';} ?>
                <?php if(!empty($good['spec_size'])){echo '产品：'.$good['spec_size'].'<br>';} ?>
              </td>
              <td><?php echo $good['bat_quan']; ?></td>
              <td><?php echo $good['weight']; ?></td>
              <td></td>
              <td><?php echo $good['wholesale1']; ?></td>
              <td><?php echo $good['wholesale2']; ?></td>
              <td><?php echo $good['wholesale3']; ?></td>
              <td></td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="11" align="center"> 暂无货品数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    $('[data-toggle="popover"]').popover({html:true, placement:'auto right', trigger: 'hover', template: '<div class="popover" style="max-width: 640px;"><div class="popover-content"></div></div>'});
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }
  
      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>