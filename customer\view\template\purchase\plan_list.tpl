<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        店铺采购
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="产品名称编码/采购合同号" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>备货店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>备货时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">采购列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">备货店铺</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">备货店铺</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'bsku') { ?>
                  <a href="<?php echo $sort_bsku; ?>" class="<?php echo strtolower($order); ?>">商品编码</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_bsku; ?>">商品编码</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'spec_name') { ?>
                  <a href="<?php echo $sort_spec; ?>" class="<?php echo strtolower($order); ?>">产品名称</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_spec; ?>">产品名称</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'order_no') { ?>
                  <a href="<?php echo $sort_orderno; ?>" class="<?php echo strtolower($order); ?>">采购合同号</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_orderno; ?>">采购合同号</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'complete_date') { ?>
                  <a href="<?php echo $sort_complete; ?>" class="<?php echo strtolower($order); ?>">合同到货日期</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_complete; ?>">合同到货日期</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'plan_quan') { ?>
                  <a href="<?php echo $sort_plan; ?>" class="<?php echo strtolower($order); ?>">备货数量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_plan; ?>">备货数量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'plan_id') { ?>
                  <a href="<?php echo $sort_added; ?>" class="<?php echo strtolower($order); ?>">备货时间</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_added; ?>">备货时间</a>
                <?php } ?>
              </th>
            </tr>
            <?php if (!empty($plans)) { ?>
            <?php foreach ($plans as $plan) { ?>
            <tr>
              <td><?php echo $plan['storename']; ?></td>
              <td><?php echo $plan['bsku']; ?></td>
              <td><?php echo $plan['spec_name']; ?></td>
              <td><?php echo $plan['order_no']; ?></td>
              <td><?php echo $plan['complete']; ?></td>
              <td><?php echo $plan['plan_quan']; ?></td>
              <td><?php echo $plan['date_added']; ?></td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="7" align="center"> 暂无备货数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }
  
      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>