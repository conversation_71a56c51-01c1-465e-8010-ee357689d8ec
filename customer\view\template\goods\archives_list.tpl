<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <h1>
            档案管理
            <small></small>
        </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
        <?php if ($success) { ?>
        <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
        <?php } ?>
        <?php if ($warning) { ?>
        <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
        <?php } ?>
        <div class="box box-primary">
            <div class="box-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>关键字：</label>
                            <input type="text" class="form-control" name="filter_name" placeholder="搜索编码/名称" value="<?php echo $filter_name; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>供应商：</label>
                            <select class="form-control" name="filter_provider">
                                <option value="*">全部供应商</option>
                                <?php foreach ($providers as $provider) { ?>
                                <?php if ($provider['provider_no'] == $filter_provider) { ?>
                                <option value="<?php echo $provider['provider_no']; ?>" selected="selected"><?php echo $provider['provider_name']; ?></option>
                                <?php } else { ?>
                                <option value="<?php echo $provider['provider_no']; ?>"><?php echo $provider['provider_name']; ?></option>
                                <?php } ?>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.box-body -->
            <div class="box-footer">
                <div class="pull-right">
                    <button type="button" id="button-filter" class="btn bg-purple"><i class="glyphicon glyphicon-search"></i> 筛选</button>
                </div>
            </div>
        </div>
        <div class="box box-success">
            <div class="box-header with-border">
                <p class="box-title">货品列表</p>
                <div class="box-tools"></div>
            </div>
            <div class="box-body table-responsive no-padding">
                <table class="table text-middle table-bordered table-hover table-striped">
                    <tbody><tr>
                        <th>货品编号</th>
                        <th style="width: 200px">货品名称</th>
                        <th>规格数</th>
                        <th>品牌</th>
                        <th>分类</th>
                        <th>是否已删除</th>
                        <th class="text-right">操作</th>
                    </tr>
                    <?php if (!empty($goods)) { ?>
                    <?php foreach ($goods as $good) { ?>
                    <tr>
                        <td><?php echo $good['goods_no']; ?></td>
                        <td><?php echo $good['goods_name']; ?></td>
                        <td><?php echo $good['spec_count']; ?></td>
                        <td><?php echo $good['brand_name']; ?></td>
                        <td><?php echo $good['class_name']; ?></td>
                        <td><?php if($good['deleted'] == 0) { ?>否<?php } else { ?>是<?php } ?></td>
                        <td class="text-right">
                            <a class="btn btn-success" href="<?php echo $detail; ?><?php echo $good['goods_id']; ?>" title="" target="_blank">查看详情</a>
                        </td>
                    </tr>
                    <?php } ?>
                    <?php } else{ ?>
                    <td colspan="10" align="center"> 暂无库存数据 </td>
                    <?php } ?>
                    </tbody></table>
            </div>
            <div class="box-footer clearfix">
                <div class="flex ai__c jc__sb">
                    <div><?php echo $results; ?></div>
                    <?php echo $pagination; ?>
                </div>
            </div>
        </div>

    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
    .table a.asc:after {
        content: " \f106";
        font-family: FontAwesome;
    }
    .table a.desc:after {
        content: " \f107";
        font-family: FontAwesome;
    }
</style>
<script type="text/javascript">
    (function () {
        // 筛选
        $('#button-filter').on('click', function() {
            url = '<?php echo $nofilter; ?>';

            var filter_name = $('input[name=\'filter_name\']').val();

            if (filter_name) {
                url += '&filter_name=' + encodeURIComponent(filter_name);
            }

            var filter_provider = $('select[name=\'filter_provider\']').val();

            if (filter_provider != '*') {
                url += '&filter_provider=' + encodeURIComponent(filter_provider);
            }
            location.href = url;
        });
    })()
</script>
<?php echo $footer; ?>