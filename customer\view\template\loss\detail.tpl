<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        破损信息
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title">查看详情</h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <div class="nav-tabs-custom">
            <table class="table table-bordered table-hover"><tbody>
              <tr>
                <td width="180">记录时间：</td>
                <td><?php echo $loss_info['date_added']; ?></td>
              </tr>
              <tr>
                <td width="180">破损产品：</td>
                <td><?php echo $loss_info['spec_name']; ?><?php echo $loss_info['bsku']; ?></td>
              </tr>
              <tr>
                <td width="180">破损数量：</td>
                <td><?php echo $loss_info['quantity']; ?></td>
              </tr>
              <tr>
                <td width="180">产品总成本：</td>
                <td><?php echo $loss_info['allcost']; ?></td>
              </tr>
              <tr>
                <td width="180">所属供应商：</td>
                <td><?php echo $loss_info['supplier']; ?></td>
              </tr>
              <tr>
                <td width="180">破损图片：</td>
                <td><ul class="list-inline">
                <?php if(!empty($loss_info['images'])){ ?>
                <?php foreach(explode(',', $loss_info['images']) as $img) { ?>
                <li><a href="<?php echo HTTP_IMAGE . $img; ?>" target="_blank"><img width="50" src="<?php echo HTTP_IMAGE . $img; ?>?imageView2/1/w/100" class="img-thumbnail"></a></li>
                <?php } ?>
                <?php } ?>
              </ul></td>
              </tr>
              <tr>
                <td width="180">责任原因：</td>
                <td><?php echo $loss_info['responsible']; ?>-<?php echo $loss_info['reason']; ?></td>
              </tr>
              <tr>
                <td width="180">处理方案：</td>
                <td><?php echo $loss_info['solution']; ?></td>
              </tr>
              <tr>
                <td width="180">所属店铺：</td>
                <td><?php echo $store['name'] ?? ''; ?></td>
              </tr>
              <tr>
                <td width="180">所在地：</td>
                <td><?php echo $loss_info['buyer_location']; ?></td>
              </tr>
              <?php if (!empty($loss_info['buyer_id'])) { ?>
              <tr>
                <td width="180">原始单号：</td>
                <td><?php echo $loss_info['buyer_id']; ?></td>
              </tr>
              <?php } ?>
              <?php if (!empty($loss_info['order_no'])) { ?>
              <tr>
                <td width="180">订单编号：</td>
                <td><?php echo $loss_info['order_no']; ?></td>
              </tr>
              <?php } ?>
              <?php if ($loss_info['order_fee'] != '0.00') { ?>
              <tr>
                <td width="180">订单金额：</td>
                <td><?php echo $loss_info['order_fee']; ?></td>
              </tr>
              <?php } ?>
              <?php if (!empty($loss_info['order_img'])) { ?>
              <tr>
                <td width="180">订单图片：</td>
                <td><ul class="list-inline">
                <?php foreach(explode(',', $loss_info['order_img']) as $img) { ?>
                <li><a href="<?php echo HTTP_IMAGE . $img; ?>" target="_blank"><img width="50" src="<?php echo HTTP_IMAGE . $img; ?>?imageView2/1/w/100" class="img-thumbnail"></a></li>
                <?php } ?>
              </ul></td>
              </tr>
              <?php } ?>
              <?php if (!empty($loss_info['handler'])) { ?>
              <tr>
                <td width="180">责任人：</td>
                <td><?php echo $loss_info['handler']; ?></td>
              </tr>
              <?php } ?>
              <?php if ($loss_info['refund_fee'] != '0.00') { ?>
              <tr>
                <td width="180">退款金额：</td>
                <td><?php echo $loss_info['refund_fee']; ?></td>
              </tr>
              <?php } ?>
              <?php if ($loss_info['loss_fee'] != '0.00') { ?>
              <tr>
                <td width="180">补偿金额：</td>
                <td><?php echo $loss_info['loss_fee']; ?></td>
              </tr>
              <?php } ?>
              <tr>
                <td width="180">快递公司：</td>
                <td><?php echo $loss_info['ship_name']; ?></td>
              </tr>
              <tr>
                <td width="180">快递单号：</td>
                <td><?php echo $loss_info['ship_no']; ?></td>
              </tr>
              <tr>
                <td width="180">快递运费：</td>
                <td><?php echo $loss_info['ship_fee']; ?></td>
              </tr>
              <?php if (!empty($loss_info['reship_name'])) { ?>
              <tr>
                <td width="180">补发快递：</td>
                <td><?php echo $loss_info['reship_name']; ?></td>
              </tr>
              <?php } ?>
              <?php if (!empty($loss_info['reship_no'])) { ?>
              <tr>
                <td width="180">补发快递单号：</td>
                <td><?php echo $loss_info['reship_no']; ?></td>
              </tr>
              <?php } ?>
              <?php if ($loss_info['reship_fee'] != '0.00') { ?>
              <tr>
                <td width="180">补发运费：</td>
                <td><?php echo $loss_info['reship_fee']; ?></td>
              </tr>
              <?php } ?>
            </tbody></table>
          </div>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>