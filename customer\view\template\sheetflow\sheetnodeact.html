<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>流程图</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsPlumb/2.15.6/js/jsplumb.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f0f2f5;
        }
        
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        #canvas {
            position: relative;
            width: 100%;
            height: 2000px;
            min-height: calc(100vh - 50px);
            background: #f0f2f5;
            overflow: auto;
            margin: 0 auto;
        }
        
        .node {
            position: absolute;
            width: 220px;
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.09);
            z-index: 10;
            transition: all 0.3s;
            cursor: default;
        }
        
        .node-header {
            padding: 12px;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            border-radius: 6px 6px 0 0;
            font-weight: bold;
            color: #333;
            font-size: 15px;
            text-align: center;
        }
        
        .node-content {
            padding: 12px;
            line-height: 1.5;
            color: #666;
        }
        
        .is-parallel {
            border-left: 4px solid #1890ff;
        }
        
        .is-audit {
            border-right: 4px solid #f5222d;
        }
        
        .is-over {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        
        .is-over .node-header {
            background: #f6ffed;
            border-bottom: 1px solid #b7eb8f;
        }
        
        .legend {
            padding: 15px;
            margin-bottom: 25px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background: white;
            display: flex;
            gap: 30px;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }
        
        .parallel-color {
            border-left: 4px solid #1890ff;
            background: white;
            border: 1px solid #e8e8e8;
        }
        
        .audit-color {
            border-right: 4px solid #f5222d;
            background: white;
            border: 1px solid #e8e8e8;
        }
        
        .over-color {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        
        .node-id, .node-permissions {
            text-align: center;
            padding: 3px 0;
        }
        
        .title {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
            font-size: 24px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">流程图</h1>
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color parallel-color"></div>
                <span>并行节点</span>
            </div>

        </div>
        <div id="canvas"></div>
    </div>

    <script>
        var actListData = '<?php echo isset($actList) ? addslashes(json_encode($actList)) : "[]"; ?>';
        let instance;
        let nodesData = JSON.parse(actListData || '[]');
        
        let nodePositions = {};
        let levelCounts = {};
        let levelHeight = 160;
        let horizontalSpacing = 300;
        
        // 初始化jsPlumb
        document.addEventListener('DOMContentLoaded', function() {
            instance = jsPlumb.getInstance({
                ConnectionOverlays: [
                    ['Arrow', { location: 1, width: 10, length: 10, foldback: 0.8 }]
                ],
                PaintStyle: { 
                    strokeWidth: 2,
                    stroke: '#aaa',
                    outlineWidth: 1,
                    outlineStroke: '#fff'
                },
                Connector: ['Flowchart', { cornerRadius: 5, stub: 30, gap: 10 }],
                Endpoint: ['Dot', { radius: 0 }],
                EndpointStyle: { visible: false },
                Anchors: ['Bottom', 'Top']
            });

            instance.setContainer(document.getElementById('canvas'));
            
            // 分析流程层级
            analyzeFlowLevels();
            
            // 渲染流程图
            renderFlowchart();
        });
        
        // 分析流程层级
        function analyzeFlowLevels() {
            // 创建节点层级映射
            let nodeLevels = {};
            let rootNodes = nodesData.filter(node => node.parent_node_id == 0);
            
            // 设置根节点为第0级
            rootNodes.forEach(node => {
                nodeLevels[node.child_node_id] = 0;
                if (!levelCounts[0]) levelCounts[0] = 0;
                levelCounts[0]++;
            });
            
            // 通过BFS计算每个节点的层级
            let queue = [...rootNodes];
            while (queue.length > 0) {
                let current = queue.shift();
                let parentLevel = nodeLevels[current.child_node_id];
                let childLevel = parentLevel + 1;
                
                // 找到当前节点的所有子节点
                let children = nodesData.filter(node => node.parent_node_id == current.child_node_id);
                children.forEach(child => {
                    // 确保子节点的层级至少是父节点+1
                    if (!nodeLevels[child.child_node_id] || nodeLevels[child.child_node_id] < childLevel) {
                        nodeLevels[child.child_node_id] = childLevel;
                        if (!levelCounts[childLevel]) levelCounts[childLevel] = 0;
                        levelCounts[childLevel]++;
                    }
                    queue.push(child);
                });
            }
            
            // 计算每个节点在其层级中的水平位置
            let levelPositions = {};
            for (let level in levelCounts) {
                levelPositions[level] = 0;
            }
            
            // 按层级排序节点
            let sortedNodes = [...nodesData].sort((a, b) => {
                return (nodeLevels[a.child_node_id] || 0) - (nodeLevels[b.child_node_id] || 0);
            });
            
            // 计算每个节点的位置
            sortedNodes.forEach(node => {
                let level = nodeLevels[node.child_node_id] || 0;
                let position = levelPositions[level] || 0;
                
                // 计算水平位置，确保居中
                const levelWidth = levelCounts[level] * horizontalSpacing;
                const startX = (document.getElementById('canvas').offsetWidth - levelWidth) / 2;
                
                nodePositions[node.child_node_id] = {
                    x: startX + position * horizontalSpacing,
                    y: 50 + level * levelHeight,
                    level: level
                };
                
                levelPositions[level] = position + 1;
            });
            
            // 处理并行节点的位置
            handleParallelNodes();
        }
        
        // 处理并行节点的位置调整
        function handleParallelNodes() {
            let parallelGroups = {};
            
            // 找出所有具有相同父节点的并行节点
            nodesData.forEach(node => {
                if (node.is_parallel == 1) {
                    if (!parallelGroups[node.parent_node_id]) {
                        parallelGroups[node.parent_node_id] = [];
                    }
                    parallelGroups[node.parent_node_id].push(node.child_node_id);
                }
            });
            
            // 调整并行节点的位置
            for (let parentId in parallelGroups) {
                let children = parallelGroups[parentId];
                if (children.length > 1) {
                    // 找到父节点的位置
                    let parentPos = nodePositions[parentId];
                    if (!parentPos) continue;
                    
                    // 计算并行节点的新位置
                    let totalWidth = (children.length - 1) * horizontalSpacing;
                    let startX = parentPos.x - totalWidth / 2;
                    
                    children.forEach((childId, index) => {
                        if (nodePositions[childId]) {
                            nodePositions[childId].x = startX + index * horizontalSpacing;
                        }
                    });
                }
            }
        }
        
        // 渲染流程图
        function renderFlowchart() {
            let canvas = document.getElementById('canvas');
            
            // 创建所有节点
            nodesData.forEach(node => {
                let position = nodePositions[node.child_node_id];
                if (!position) return;
                
                createNode(node, position.x, position.y);
            });
            
            // 水平居中整个流程图
            setTimeout(() => {
                centerFlowchart();
                
                // 创建所有连接
                nodesData.forEach(node => {
                    if (node.parent_node_id != 0) {
                        instance.connect({
                            source: 'node_' + node.parent_node_id,
                            target: 'node_' + node.child_node_id,
                            anchors: ['Bottom', 'Top'],
                            paintStyle: { stroke: '#aaa', strokeWidth: 2 }
                        });
                    }
                });
            }, 100);
        }
        
        // 添加新函数 - 水平居中整个流程图
        function centerFlowchart() {
            // 获取所有节点的边界
            let minX = Infinity, maxX = -Infinity;
            let nodes = document.querySelectorAll('.node');
            
            if (nodes.length === 0) return;
            
            nodes.forEach(node => {
                const x = parseInt(node.style.left);
                const width = node.offsetWidth;
                minX = Math.min(minX, x);
                maxX = Math.max(maxX, x + width);
            });
            
            // 计算整个流程图的宽度和偏移量
            const flowchartWidth = maxX - minX;
            const canvasWidth = document.getElementById('canvas').offsetWidth;
            const offset = (canvasWidth - flowchartWidth) / 2 - minX;
            
            // 应用偏移量
            nodes.forEach(node => {
                const currentX = parseInt(node.style.left);
                node.style.left = (currentX + offset) + 'px';
            });
        }
        
        // 修改createNode函数，移除拖拽功能
        function createNode(node, x, y) {
            const nodeId = 'node_' + node.child_node_id;
            
            const nodeEl = document.createElement('div');
            nodeEl.id = nodeId;
            nodeEl.className = 'node';
            nodeEl.style.left = x + 'px';
            nodeEl.style.top = y + 'px';
            
            // 添加特殊类名
            if (node.is_parallel == 1) nodeEl.classList.add('is-parallel');
            if (node.is_audit == 1) nodeEl.classList.add('is-audit');
            if (node.is_over == 1) nodeEl.classList.add('is-over');
            
            nodeEl.innerHTML = `
                <div class="node-header">${node.node_name || '未命名节点'}</div>
                <div class="node-content">
                    <div class="node-id">工时天数: ${node.day || 0}</div>
                    ${node.union_id ? `<div class="node-permissions">参与人员: ${node.union_id}</div>` : ''}
                </div>
            `;
            
            document.getElementById('canvas').appendChild(nodeEl);
            
            // 添加端点 - 隐藏样式但保持功能
            instance.addEndpoint(nodeId, {
                anchor: 'Top',
                isTarget: true,
                maxConnections: -1,
                endpoint: ['Dot', { radius: 0 }],
                visible: false
            });
            
            instance.addEndpoint(nodeId, {
                anchor: 'Bottom',
                isSource: true,
                maxConnections: -1,
                endpoint: ['Dot', { radius: 0 }],
                visible: false
            });
            
            return nodeEl;
        }
    </script>
</body>
</html>