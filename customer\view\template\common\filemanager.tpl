<div id="modal-file" class="modal fade">
  <div id="filemanager" class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
        <h4 class="modal-title">云盘文件</h4>
      </div>
      <div class="modal-body">
        <!-- Nav tabs -->
        <div class="nav-tabs-custom" style="margin-bottom: 10px">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#filelist" data-toggle="tab">文件列表</a></li>
            <li><a href="#uploadlist" data-toggle="tab">上传任务</a></li>
            <li><a href="#downlist" data-toggle="tab">下载任务</a></li>
          </ul>
          <!-- Tab panes -->
          <div class="tab-content">
            <div class="tab-pane active" id="filelist"></div>
            <div class="tab-pane" id="uploadlist">
              <h3 class="text-center">暂无上传任务</h3>
              <ul class="list-group"></ul>
            </div>
            <div class="tab-pane" id="downlist">
              <h3 class="text-center">暂无下载任务</h3>
              <ul class="list-group"></ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <style type="text/css">
  .mb-3 {
    margin-bottom: 1rem!important;
  }
  .badge {
    padding:5px 8px;
    background-color: #337ab7;
    color: #fff;
  }
  #parent-folders a:hover {
    color: red;
  }
  </style>
  <script type="text/javascript"><!--
  $('#modal-file #filelist').load('<?php echo $list; ?>');

  $('#modal-file').on('click', '#button-parent', function (e) {
      e.preventDefault();

      $('#modal-file #filelist').load($(this).attr('href'));
  });

  $('#modal-file').on('click', '#button-refresh', function (e) {
      e.preventDefault();

      $('#modal-file #filelist').load($(this).attr('href'));
  });

  $('#modal-file').on('keydown', '#input-search', function (e) {
      if (e.which == 13) {
          $('#modal-file #button-search').trigger('click');
      }
  });

  $('#modal-file').on('click', '#button-search', function (e) {
      var url = '<?php echo $list; ?>';

      var filter_name = $('#input-search').val();

      if (filter_name) {
          url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      $('#modal-file #filelist').load(url);
  });

  $('#modal-file').on('click', '#button-upload', function () {
    $.ajax({
      url: '<?php echo $checkAddPower; ?>&folder=' + encodeURIComponent($('#input-folder').val()),
      type: 'post',
      dataType: 'json',
      success: function (json) {
        if (json['error']) {
          alert(json['error']);
        } else {
          $('#file-upload').remove();

          $('body').prepend('<form enctype="multipart/form-data" id="file-upload" style="display: none;"><input type="file" name="files" value="" multiple="multiple"/></form>');

          $('#file-upload input[name=\'files\']').trigger('click');

          $('#file-upload input[name=\'files\']').on('change', function () {
            if ($(this).val() !== '') {
              let rand = randomString(8);

              for (i = 0; i < this.files.length; i++) {
                uploadlist.push({id:rand + i.toString(), file:this.files[i], queue:1});

                $('#uploadlist .list-group').prepend('<li class="list-group-item" id="ulist' + rand + i.toString() + '"><span class="badge">等待上传</span>' + this.files[i].name + '</li>');
              }

              $('#modal-file a[href="#uploadlist"]').tab('show');

              startUpload();
            }
          });
        }
      },
    });
  });

  $('#modal-file').on('click', '#button-download', function (e) {
      if ($('input[name^=\'path\']:checked').length > 0) {
          let rand = randomString(8);
          $('input[name^=\'path\']:checked').each(function (i, v) {
              downlist.push({id:rand + i.toString(), fileid:$(this).val(), filename: $(this).parent().text().trim(), queue:1});
              $('#downlist .list-group').prepend('<li class="list-group-item" id="dlist' + rand + i.toString() + '"><span class="badge">等待下载</span>' + $(this).parent().text().trim() + '</li>');
          });

          $('#modal-file a[href="#downlist"]').tab('show');

          startDown();
      }
  });

  $('#modal-file').on('click', '#button-folder', function () {
    $.ajax({
      url: '<?php echo $checkAddPower; ?>&folder=' + encodeURIComponent($('#input-folder').val()),
      type: 'post',
      dataType: 'json',
      success: function (json) {
        if (json['error']) {
          alert(json['error']);
        } else {
          $('#modal-tags').hide();
          $('#modal-folder').slideToggle();
        }
      },
    });
  });

  $('#modal-file').on('click', '#button-create', function () {
      var url = '<?php echo $addFolder; ?>';

      var folder = $('#input-folder').val();

      if (folder) {
          url += '&folder=' + encodeURIComponent(folder);
      }

      $.ajax({
          url: url,
          type: 'post',
          dataType: 'json',
          data: 'filename=' + encodeURIComponent($('#input-filename').val()),
          beforeSend: function () {
              $('#button-create').button('loading');
          },
          complete: function () {
              $('#button-create').button('reset');
          },
          success: function (json) {
              if (json['error']) {
                  alert(json['error']);
              }

              if (json['success']) {
                  // alert(json['success']);

                $('.prompt-dialog-box').html('<p style="line-height:50px">'+json['success']+'</p>');

                setTimeout(function (){
                  $('#button-refresh').trigger('click');
                },2000);
              }
          },
          error: function (xhr, ajaxOptions, thrownError) {
              console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
          }
      });
  });

    $('#modal-file').on('click', '#button-tags', function () {
      $('#modal-folder').hide()
      $('#modal-tags').slideToggle();
    });

    $('#modal-file').on('click', '#button-tags-create', function () {
      var url = '<?php echo $addTags; ?>';

      var tags = $('#tags_name').val();

      if (tags.length == 0) {
        confirm("请输入标签内容")
        return;
      }

      if ($('input[name^=\'path\']:checked').length == 0) {
        confirm("请选择文件")
        return;
      }

      var files = [];
      $('input[name^=\'path\']:checked').each(function(){
        files.push($(this).val());
      });


      $.ajax({
        url: url,
        type: 'post',
        dataType: 'json',
        data:  {tags,files},
        beforeSend: function () {
          $('#button-create').button('loading');
        },
        complete: function () {
          $('#button-create').button('reset');
        },
        success: function (json) {
          if (json['error']) {
            alert(json['error']);
          }

          if (json['success']) {
            $('.prompt-dialog-box').html('<p style="line-height:50px">'+json['success']+'</p>');

            setTimeout(function (){
              $('#button-refresh').trigger('click');
            },2000);
          }
        },
        error: function (xhr, ajaxOptions, thrownError) {
          console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    });

  $('#modal-file').on('click', '#button-delete', function (e) {
      if ($('input[name^=\'path\']:checked').length == 0) {
          return;
      }
      if (confirm('确认删除所选文件？')) {
          $.ajax({
              url: '<?php echo $delete; ?>',
              type: 'post',
              dataType: 'json',
              data: $('input[name^=\'path\']:checked'),
              beforeSend: function () {
                  $('#button-delete').button('loading');
              },
              complete: function () {
                  $('#button-delete').button('reset');
              },
              success: function (json) {
                  if (json['error']) {
                      alert(json['error']);
                  }

                  if (json['success']) {
                      // alert(json['success']);

                      $('#button-refresh').trigger('click');
                  }
              },
              error: function (xhr, ajaxOptions, thrownError) {
                  console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
              }
          });
      }
  });

  $('#modal-file').on('click', '#button-checkall', function () {
      $('#filelist input[name^=\'path\']').iCheck('check');
  });

  $('#modal-file').on('click', 'a.directory', function (e) {
      e.preventDefault();

      $('#modal-file #filelist').load($(this).attr('href'));
  });

  $('#modal-file').on('click', '.pagination a', function (e) {
      e.preventDefault();

      $('#modal-file #filelist').load($(this).attr('href'));
  });

    $('#modal-file').on('click', 'a.parent-folde', function (e) {
      e.preventDefault();

      $('#modal-file #filelist').load($(this).attr('href'));
    });

  var uploadlist = [], downlist = [];
  var uploading = 0, downing = 0;

  function startUpload() {
      $('#uploadlist h3').text('正在上传文件，请勿关闭本窗口');

      if (typeof utimer != 'undefined') {
          clearInterval(utimer);
      }

      utimer = setInterval(function () {
          uploadlist.forEach(function(value, index) {
              if ((uploading <= 5) && (value.queue == 1)) {
                  upFile(index);
                  uploading++;
              }
          });

          if (uploading == 0) {
              clearInterval(utimer);
              
              $('#uploadlist h3').text('全部上传完成');
              $('#button-refresh').trigger('click');
          }
      }, 500);
  }

  function startDown() {
      $('#downlist h3').text('正在下载文件');

      if (typeof dtimer != 'undefined') {
          clearInterval(dtimer);
      }

      dtimer = setInterval(function () {
          downlist.forEach(function(value, index) {
              if ((downing <= 5) && (value.queue == 1)) {
                  downFile(index);
                  downing++;
              }
          });

          if (downing == 0) {
              clearInterval(dtimer);
              
              $('#downlist h3').text('全部下载完成');
          }
      }, 500);
  }

  function upFile(index) {
      if (uploadlist[index].queue == 1) {
          uploadlist[index].queue = 2;
          var cur = uploadlist[index];
          var formData = new FormData();
          formData.append('token', '<?php echo $uploadToken; ?>');
          formData.append('file', cur.file);
          $.ajax({
              url: 'http://up-z2.qiniu.com',
              type: 'post',
              dataType: 'json',
              data: formData,
              cache: false,
              contentType: false,
              processData: false,
              xhr: function() {
                  x = $.ajaxSettings.xhr();
                  if(x.upload){
                      x.upload.addEventListener('progress', function (e) {
                          if (e.lengthComputable) {
                              $('#ulist' + cur.id + ' .progress').show('fast', function() {
                                  $('#ulist' + cur.id + ' .progress-bar').css('width', (e.loaded / e.total) * 100 + '%');
                              });
                          }
                      }, false);
                  }
                  x.addEventListener('loadend', function () {
                      $('#ulist' + cur.id + ' .progress').hide('fast');
                  }, false);
                  return x;
              },
              beforeSend: function () {
                  $('#ulist' + cur.id + ' .badge').remove();
                  $('#ulist' + cur.id).append('<div class="progress" style="display: none;"><div class="progress-bar progress-bar-info progress-bar-striped" style="width: 0%"></div></div>');
              },
              complete: function () {
                  $('#ulist' + cur.id + ' .progress').remove();
                  uploading--;
              },
              success: function (json) {
                  if (json['key']) {
                      $.ajax({
                          url: '<?php echo $addFile; ?>&folder=' + encodeURIComponent($('#input-folder').val()),
                          type: 'post',
                          dataType: 'json',
                          data: {
                              filename: cur.file.name,
                              filesize: cur.file.size,
                              filetype: cur.file.type,
                              filekey: json['key'],
                              filehash: json['hash']
                          },
                          success: function (json) {
                              if (json['error']) {
                                  alert(json['error']);
                              }

                              if (json['success']) {
                                  // alert(json['success']);
                                  $('#ulist' + cur.id).prepend('<span class="badge">上传完成</span>');
                                  uploadlist[index].queue = 0;
                              }
                          },
                          error: function () {
                              uploadlist[index].queue = 1;
                          }
                      });
                  } else {
                      uploadlist[index].queue = 1;
                  }
              },
              error: function () {
                  uploadlist[index].queue = 1;
              }
          });
      }
  }

  function downFile(index) {
      if (downlist[index].queue == 1) {
          downlist[index].queue = 2;
          var cur = downlist[index];
          $.ajax({
              url: '<?php echo $download; ?>',
              type: 'get',
              dataType: 'binary',
              data: 'file_id=' + encodeURIComponent(cur.fileid),
              xhrFields: {responseType: 'blob'},
              xhr: function() {
                  x = $.ajaxSettings.xhr();
                  x.addEventListener('progress', function (e) {
                      if (e.lengthComputable) {
                          $('#dlist' + cur.id + ' .progress').show('fast', function() {
                              $('#dlist' + cur.id + ' .progress-bar').css('width', (e.loaded / e.total) * 100 + '%');
                          });
                      }
                  }, false);
                  x.addEventListener('loadend', function () {
                      $('#dlist' + cur.id + ' .progress').hide('fast');
                  }, false);
                  return x;
              },
              beforeSend: function () {
                  $('#dlist' + cur.id + ' .badge').remove();
                  $('#dlist' + cur.id).append('<div class="progress" style="display: none;"><div class="progress-bar progress-bar-info progress-bar-striped" style="width: 0%"></div></div>');
              },
              complete: function () {
                  $('#dlist' + cur.id + ' .progress').remove();
                  downing--;
              },
              success: function (data) {
                  if (data.size > 0) {
                      var blob = new Blob([data], {type:'application/octet-stream'});
                      var url = window.URL.createObjectURL(blob);
                      var alink = document.createElement('a');
                      alink.href = url;
                      alink.download = cur.filename;
                      alink.click();
                      window.URL.revokeObjectURL(url);
                      alink.remove();
                      $('#dlist' + cur.id).prepend('<span class="badge">下载完成</span>');
                  } else {
                      $('#dlist' + cur.id).prepend('<span class="badge">暂不支持下载</span>');
                  }
                  downlist[index].queue = 0;
              },
              error: function (xhr, ajaxOptions, thrownError) {
                  downlist[index].queue = 1;
              }
          });
      }
  }

  function downFileBak(fileid, filename) {
      var xhr = new XMLHttpRequest();
      xhr.open('GET', '<?php echo $download; ?>&file_id=' + encodeURIComponent(fileid), true);
      xhr.responseType = 'blob';
      xhr.onload = function () {
          if (this.status == 200) {
              var blob = this.response;
              var url = window.URL.createObjectURL(blob);
              var alink = document.createElement('a');
              alink.href = url;
              alink.download = filename;
              alink.click();
              window.URL.revokeObjectURL(url);
              alink.remove();
          }
      };
      xhr.send();
  }

  function randomString(length) {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      return Array(length).fill().map(() => characters[Math.floor(Math.random() * characters.length)]).join('');
  }

  function getFileType(filename) {
      return filename.substring(filename.lastIndexOf("."), filename.length);
  }
  //--></script>
</div>