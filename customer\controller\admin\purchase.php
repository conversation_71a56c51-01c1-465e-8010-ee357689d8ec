<?php
class ControllerAdminPurchase extends Controller {
    private $error = array();

    public function storePlan() {
        if (isset($this->request->get['store_id'])) {
            $data['store_id'] = $this->request->get['store_id'];
        } else {
            $data['store_id'] = '';
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();
        $store_ids = array();

        foreach ($data['stores'] as $store) {
            $store_ids[] = $store['store_id'];
        }

        if (!in_array($data['store_id'], $store_ids)) {
            $data['store_id'] = array_shift($store_ids);
        }

        $this->load->model('admin/purchase');
        $data['plans'] = array();
        $results = $this->model_admin_purchase->getStorePlans($data['store_id']);

        foreach ($results as $result) {
            $data['plans'][$result['bsku']] = array(
                'plan_id'   => $result['plan_id'],
                'bsku'      => $result['bsku'],
                'plan_quan' => $result['plan_quan'],
                'stock_quan'=> 0,
                'order_quan'=> 0,
                'sale_quan' => 0,
                '1dsales'   => 0,
                '3dsales'   => 0,
                '7dsales'   => 0,
                '15dsales'  => 0,
                '30dsales'  => 0,
                '60dsales'  => 0,
                '90dsales'  => 0,
                'stop'      => '0',
                'spec_name' => '',
                'img_url'   => ''
            );
        }

        if (!empty($data['plans'])) {
            $results = $this->model_admin_purchase->getStoreStock(array('filter_store' => $data['store_id'], 'filter_bsku_list' => array_keys($data['plans'])));

            foreach ($results as $result) {
                if (isset($data['plans'][$result['bsku']])) {
                    $data['plans'][$result['bsku']] = array_merge($data['plans'][$result['bsku']], $result);
                }
            }
        }

        $data['cost'] = $this->model_admin_purchase->getStoreCost($data['store_id']);

        $data['autoProduct'] = $this->url->link('admin/purchase/autoProduct', 'store_id=' . $data['store_id'] . '&token=' . $this->session->data['token']);
        $data['addPlan'] = $this->url->link('admin/purchase/addPlan', 'store_id=' . $data['store_id'] . '&token=' . $this->session->data['token']);
        $data['delPlan'] = $this->url->link('admin/purchase/deletePlan', 'store_id=' . $data['store_id'] . '&token=' . $this->session->data['token']);
        $data['storePlan'] = $this->url->link('admin/purchase/storePlan', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/store_plan.tpl', $data));
    }

    public function newPlan() {
        if (isset($this->request->get['goods_id'])) {
            $data['goods_id'] = $this->request->get['goods_id'];
        } else {
            $data['goods_id'] = '';
        }

        $this->load->model('admin/purchase');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateNewForm()) {
            $this->model_admin_purchase->addNewOrder($this->request->post);

            $this->response->redirect($this->url->link('admin/purchase/getList', 'token=' . $this->session->data['token']));
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $data['classes'] = $this->model_admin_purchase->getStoreClass();
        $data['goods'] = $this->model_admin_purchase->getWdtGoods(array('filter_new' => true));
        $data['specs'] = $this->model_admin_purchase->getWdtSpecs(array('filter_goods' => $data['goods_id']));

        $data['action'] = $this->url->link('admin/purchase/newPlan', 'token=' . $this->session->data['token'] . '&goods_id=' . $data['goods_id']);
        $data['getNewGoods'] = $this->url->link('admin/purchase/getNewGoods', 'token=' . $this->session->data['token']);

        $data['complete_date'] = date($this->language->get('date_format'), strtotime("+25 days"));

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/new_plan.tpl', $data));
    }

    public function addPlan() {
        $json = array();

        if (isset($this->request->get['store_id'])) {
            $store_id = $this->request->get['store_id'];
        } else {
            $store_id = '';
        }

        if ($this->user->store_ids && !in_array($store_id, explode(',', $this->user->store_ids))) {
            $json['error'] = true;
        } elseif (($this->request->server['REQUEST_METHOD'] == 'POST') && !empty($this->request->post['bsku'])) {
            $this->load->model('admin/purchase');
            $plan_info = $this->model_admin_purchase->getStorePlan($store_id, $this->request->post['bsku']);

            if ($plan_info) {
                $this->model_admin_purchase->editPlan($plan_info['plan_id'], $this->request->post);
            } else {
                $this->request->post['store_id'] = $store_id;
                $this->model_admin_purchase->addPlan($this->request->post);
            }

            $json['success'] = true;
        }

        $this->response->setOutJson($json);
    }

    public function deletePlan() {
        $json = array();

        if (isset($this->request->get['store_id'])) {
            $store_id = $this->request->get['store_id'];
        } else {
            $store_id = '';
        }

        if ($this->user->store_ids && !in_array($store_id, explode(',', $this->user->store_ids))) {
            $json['error'] = true;
        } elseif (($this->request->server['REQUEST_METHOD'] == 'POST') && !empty($this->request->post['bsku'])) {
            $this->load->model('admin/purchase');
            $plan_info = $this->model_admin_purchase->getStorePlan($store_id, $this->request->post['bsku']);

            if ($plan_info) {
                $this->model_admin_purchase->deletePlan($plan_info['plan_id']);
            }

            $json['success'] = true;
        }

        $this->response->setOutJson($json);
    }

    public function storeProduct() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
            $data_filter_store = explode(',',$this->request->get['filter_store']);
        } else {
            $filter_store = '';
            $data_filter_store = [];
        }

        if (isset($this->request->get['filter_rule'])) {
            $filter_rule = $this->request->get['filter_rule'];
        } else {
            $filter_rule = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = '7dsales';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . $this->request->get['filter_rule'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['addPlan'] = $this->url->link('admin/purchase/addPlan', 'token=' . $this->session->data['token']);
        $data['delPlan'] = $this->url->link('admin/purchase/deletePlan', 'token=' . $this->session->data['token']);

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores(['all' => true]);

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $filter_data = array(
            'filter_store'  => $filter_store,
            'filter_name'   => $filter_name,
            'filter_rule'   => $filter_rule,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $data['stocks'] = $plans = $allplan = array();

        $this->load->model('admin/purchase');
        $data['rules'] = $this->model_admin_purchase->getStoreRules();
        $results = $this->model_admin_purchase->getStorePlans();
        foreach ($results as $result) {
            $plans[$result['store_id'] . $result['bsku']] = $result['plan_quan'];

            if (isset($allplan[$result['bsku']])) {
                $allplan[$result['bsku']] += $result['plan_quan'];
            } else {
                $allplan[$result['bsku']] = $result['plan_quan'];
            }
        }

        $data['store_ids'] = explode(',', $this->user->store_ids);

        $data['sumsales'] = array(
            'sale_quan' => 0,
            '1dsales'   => 0,
            '3dsales'   => 0,
            '7dsales'   => 0,
            '15dsales'  => 0,
            '30dsales'  => 0,
            '60dsales'  => 0,
            '90dsales'  => 0
        );

        $results = $this->model_admin_purchase->getStoreStock($filter_data);

        foreach ($results as $result) {
            $plan_quan = $plans[$result['store_id'] . $result['bsku']] ?? 0;
            $all_quan = $allplan[$result['bsku']] ?? 0;

            $data['stocks'][] = array(
                'store_id'  => $result['store_id'],
                'bsku'      => $result['bsku'],
                'storename' => $stores[$result['store_id']] ?? '',
                'stock_quan'=> $result['stock_quan'],
                'order_quan'=> $result['order_quan'],
                'sale_quan' => $result['sale_quan'],
                '1dsales'   => $result['1dsales'],
                '3dsales'   => $result['3dsales'],
                '7dsales'   => $result['7dsales'],
                '15dsales'  => $result['15dsales'],
                '30dsales'  => $result['30dsales'],
                '60dsales'  => $result['60dsales'],
                '90dsales'  => $result['90dsales'],
                'stop'      => $result['stop'],
                'other_quan'=> $all_quan - $plan_quan,
                'plan_quan' => $plan_quan,
                'spec_name' => $result['spec_name'],
                'img_url'   => $result['img_url'],
                'bat_quan'  => $result['prop2']
            );

            $data['sumsales']['sale_quan'] += $result['sale_quan'];
            $data['sumsales']['1dsales'] += $result['1dsales'];
            $data['sumsales']['3dsales'] += $result['3dsales'];
            $data['sumsales']['7dsales'] += $result['7dsales'];
            $data['sumsales']['15dsales'] += $result['15dsales'];
            $data['sumsales']['30dsales'] += $result['30dsales'];
            $data['sumsales']['60dsales'] += $result['60dsales'];
            $data['sumsales']['90dsales'] += $result['90dsales'];
        }

        $total = $this->model_admin_purchase->getTotalStoreStocks($filter_data);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . $this->request->get['filter_rule'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_bsku'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=bsku' . $url);
        $data['sort_store'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
        $data['sort_stock'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=stock_quan' . $url);
        $data['sort_order'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=order_quan' . $url);
        $data['sort_sale'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=sale_quan' . $url);
        $data['sort_1d'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=1dsales' . $url);
        $data['sort_3d'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=3dsales' . $url);
        $data['sort_7d'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=7dsales' . $url);
        $data['sort_15d'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=15dsales' . $url);
        $data['sort_30d'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=30dsales' . $url);
        $data['sort_60d'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=60dsales' . $url);
        $data['sort_90d'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . '&sort=90dsales' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . $this->request->get['filter_rule'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store_json'] = json_encode($data_filter_store);
        $data['filter_rule'] = $filter_rule;

        $data['nofilter'] = $this->url->link('admin/purchase/storeProduct', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/store_product.tpl', $data));
    }

    public function getPlan() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'plan_id';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $data['plans'] = $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_store'  => $filter_store,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $this->load->model('admin/purchase');        
        $results = $this->model_admin_purchase->getPlans($filter_data);

        foreach ($results as $result) {
            $data['plans'][] = array(
                'bsku'      => $result['bsku'],
                'spec_name' => $result['spec_name'],
                'storename' => $stores[$result['store_id']] ?? '',
                'plan_quan' => $result['plan_quan'],
                'order_no'  => $result['order_no'],
                'complete'  => $result['complete_date'],
                'date_added'=> $result['date_added']
            );
        }

        $total = $this->model_admin_purchase->getTotalPlans($filter_data);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_store'] = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
        $data['sort_bsku'] = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token'] . '&sort=bsku' . $url);
        $data['sort_spec'] = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token'] . '&sort=spec_name' . $url);
        $data['sort_plan'] = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token'] . '&sort=plan_quan' . $url);       
        $data['sort_orderno'] = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token'] . '&sort=order_no' . $url);
        $data['sort_complete'] = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token'] . '&sort=complete_date' . $url);
        $data['sort_added'] = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token'] . '&sort=plan_id' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/purchase/getPlan', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/plan_list.tpl', $data));
    }

    public function subtract() {
        if (isset($this->request->get['purchase_id'])) {
            $purchase_id = $this->request->get['purchase_id'];
        } else {
            $purchase_id = '';
        }

        $this->load->model('admin/purchase');
        $this->model_admin_purchase->subtractOrder($purchase_id);
        exit;

        $this->response->redirect($this->url->link('admin/purchase/getList', 'token=' . $this->session->data['token']));
    }

    public function order() {
        $this->load->model('admin/purchase');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_purchase->addOrder($this->request->post);

            $this->response->redirect($this->url->link('admin/purchase/getList', 'token=' . $this->session->data['token']));
        }

        $this->load->model('admin/setting');
        $results = $this->model_admin_setting->getStores();
        $stores = array();

        foreach ($results as $result) {
            $stores[$result['store_id']] = $result['name'];
        }

        $data['plans'] = array();
        $this->model_admin_purchase->deleteInvalidPlan();
        $results = $this->model_admin_purchase->getStorePlans();

        foreach ($results as $result) {
            if (!isset($data['plans'][$result['bsku']])) {
                $data['plans'][$result['bsku']] = array(
                    'plan_ids'  => array(),
                    'bsku'      => $result['bsku'],
                    'plan_quan' => 0,
                    'plan_list' => array(),
                    'spec_name' => '',
                    'img_url'   => '',
                    'order_num' => 0,
                    'avaliable_num' => 0,
                    'purchase_num'  => 0
                );
            }

            $data['plans'][$result['bsku']]['plan_ids'][] = $result['plan_id'];
            $data['plans'][$result['bsku']]['plan_quan'] += (int)$result['plan_quan'];
            $data['plans'][$result['bsku']]['plan_list'][] = array(
                'storename' => $stores[$result['store_id']] ?? '',
                'plan_quan' => $result['plan_quan']
            );
        }

        $sort_order = array();

        foreach ($data['plans'] as $key => $row) {
            $sort_order[$key] = $row['plan_quan'];
        }

        array_multisort($sort_order, SORT_DESC, SORT_NUMERIC, $data['plans']);

        if (!empty($data['plans'])) {
            $results = $this->model_admin_purchase->getWdtStocks(array('spec_in_list'=> array_keys($data['plans'])));

            foreach ($results as $result) {
                if (isset($data['plans'][$result['spec_no']])) {
                    $data['plans'][$result['spec_no']] = array_merge($data['plans'][$result['spec_no']], $result);
                }
            }
        }

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->request->post['order_no'])) {
            $data['order_no'] = $this->request->post['order_no'];
        } else {
            $data['order_no'] = '';
        }
        
        if (isset($this->request->post['complete_date'])) {
            $data['complete_date'] = $this->request->post['complete_date'];
        } else {
            $data['complete_date'] = date($this->language->get('date_format'), strtotime("+20 days"));
        }

        $data['action'] = $this->url->link('admin/purchase/order', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/plan_order.tpl', $data));
    }

    public function getList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_bsku'])) {
            $filter_bsku = $this->request->get['filter_bsku'];
        } else {
            $filter_bsku = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_bsku'])) {
            $url .= '&filter_bsku=' . $this->request->get['filter_bsku'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['edit'] = $this->url->link('admin/purchase/getList', 'token=' . $this->session->data['token'] . $url);

        $this->load->model('admin/purchase');  

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $this->model_admin_purchase->editOrderNo($this->request->post['purchase_id'], $this->request->post['order_no']);
        }

        $data['orders'] = array();

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_bsku'   => $filter_bsku,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $results = $this->model_admin_purchase->getOrders($filter_data);

        foreach ($results as $result) {
            $bsku_list = $this->model_admin_purchase->getOrderDetail($result['purchase_id']);

            $data['orders'][] = array(
                'purchase_id'  => $result['purchase_id'],
                'complete_date'=> $result['complete_date'],
                'order_no'  => $result['order_no'],
                'bsku_list' => $bsku_list,
                'date_added'=> $result['date_added'],
                'view'      => $this->url->link('admin/purchase/getListDetail', 'token=' . $this->session->data['token'] . $url . '&purchase_id=' . $result['purchase_id']),
                'export'    => $this->url->link('admin/purchase/getListExport', 'token=' . $this->session->data['token'] . $url . '&purchase_id=' . $result['purchase_id']),
            );
        }

        $total = $this->model_admin_purchase->getTotalOrders($filter_data);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_bsku'])) {
            $url .= '&filter_bsku=' . $this->request->get['filter_bsku'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/getList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_bsku'] = $filter_bsku;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/purchase/getList', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/list.tpl', $data));
    }

    public function getListDetail() {
        if (isset($this->request->get['purchase_id'])) {
            $purchase_id = $this->request->get['purchase_id'];
        } else {
            $purchase_id = '';
        }

        $this->load->model('admin/purchase');
        $data['purchase_info'] = $this->model_admin_purchase->getOrder($purchase_id);

        if (empty($data['purchase_info'])) {
            $this->response->redirect($this->url->link('admin/purchase/getList', 'token=' . $this->session->data['token']));
        }

        $this->load->model('admin/setting');
        $results = $this->model_admin_setting->getStores();
        $stores = array();

        foreach ($results as $result) {
            $stores[$result['store_id']] = $result['name'];
        }

        $data['bsku_list'] = array();
        $results = $this->model_admin_purchase->getOrderDetail($purchase_id);

        foreach ($results as $result) {
            $data['bsku_list'][$result['bsku']] = array(
                'bsku'      => $result['bsku'],
                'order_quan'=> $result['order_quan'],
                'plans'     => array(),
                'spec_name' => $result['spec_name'],
                'img_url'   => $result['img_url']
            );
        }

        $results = $this->model_admin_purchase->getOrderPlans($purchase_id);

        foreach ($results as $result) {
            $data['bsku_list'][$result['bsku']]['plans'][] = array(
                'storename' => $stores[$result['store_id']] ?? '',
                'plan_quan' => $result['plan_quan']
            );
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/detail.tpl', $data));
    }

    public function getListExport() {
        if (isset($this->request->get['purchase_id'])) {
            $purchase_id = $this->request->get['purchase_id'];
        } else {
            $purchase_id = '';
        }

        $this->load->model('admin/purchase');
        $purchase_info = $this->model_admin_purchase->getOrder($purchase_id);

        if (empty($purchase_info)) {
            $this->response->redirect($this->url->link('admin/purchase/getList', 'token=' . $this->session->data['token']));
        }

        $bsku_list = array();
        $results = $this->model_admin_purchase->getOrderDetail($purchase_id);

        foreach ($results as $result) {
            $bsku_list[$result['bsku']] = array(
                'bsku'      => $result['bsku'],
                'spec_name' => $result['spec_name'],
                'order_quan'=> $result['order_quan']
            );
        }

        $export_head = array();
        $export_head[] = '商家编码';
        $export_head[] = '名称';
        $export_head[] = '采购量';

        $export_data = array();
        $export_data[] = $export_head;

        foreach ($bsku_list as $list) {
            $export_data[] = $list;
        }

        if (!empty($export_data)) {
            $this->load->model('admin/excel');
            $this->model_admin_excel->export($purchase_info['order_no'] . '(' . $purchase_info['complete_date'] . ')', $export_data, array(), '.xlsx');
        }

        $this->getList();
    }

    public function getStock() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'stock_quan';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $this->load->model('admin/setting');
        $results = $this->model_admin_setting->getStores();

        $stores = array();

        foreach ($results as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $data['stocks'] = array();

        $this->load->model('admin/purchase');
        $results = $this->model_admin_purchase->getStocks($filter_data);

        foreach ($results as $result) {
            $data['stocks'][$result['bsku']] = array(
                'bsku'      => $result['bsku'],
                'stock_quan'=> $result['stock_quan'],
                'order_quan'=> $result['order_quan'],
                'sale_quan' => $result['sale_quan'],
                'stores'    => array(),
                'spec_name' => $result['spec_name'],
                'img_url'   => $result['img_url'],
                'order_num' => $result['order_num'],
                'purchase_num'  => $result['purchase_num'],
                'avaliable_num' => $result['avaliable_num']
            );
        }

        if (!empty($data['stocks'])) {
            $results = $this->model_admin_purchase->getStoreStock(array('filter_bsku_list' => array_keys($data['stocks']), 'sort' => 'stock_quan', 'order' => 'DESC'));

            foreach ($results as $result) {
                if (isset($data['stocks'][$result['bsku']]) && (int)$result['stock_quan'] != 0) {
                    $data['stocks'][$result['bsku']]['stores'][] = array(
                        'store'  => $stores[$result['store_id']] ?? '',
                        'stock'  => $result['stock_quan'],
                    );
                }
            }
        }

        $total = $this->model_admin_purchase->getTotalStocks($filter_data);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_bsku'] = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token'] . '&sort=bsku' . $url);
        $data['sort_stock'] = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token'] . '&sort=stock_quan' . $url);
        $data['sort_order'] = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token'] . '&sort=order_quan' . $url);
        $data['sort_sale'] = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token'] . '&sort=sale_quan' . $url);
        $data['sort_ordered'] = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token'] . '&sort=order_num' . $url);
        $data['sort_purchase'] = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token'] . '&sort=purchase_num' . $url);
        $data['sort_avaliable'] = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token'] . '&sort=avaliable_num' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;

        $data['nofilter'] = $this->url->link('admin/purchase/getStock', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/stock_list.tpl', $data));
    }

    public function uploadSales() {
        $data['action'] = $this->url->link('admin/purchase/importSales', 'token=' . $this->session->data['token']);
        $data['template'] = $this->url->link('admin/purchase/template', 'token=' . $this->session->data['token']);

        $data['sale_date'] = date($this->language->get('date_format'), strtotime("-1 days"));

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/upload_sales.tpl', $data));
    }

    public function importSales() {
        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->load->language('purchase');
            $this->load->model('admin/purchase');
            $this->load->model('admin/excel');
            $this->load->model('admin/setting');

            $upload_info = $this->model_admin_excel->upload('storesale');

            if (isset($upload_info['name'])) {
                $sale_datas = array();
                $upload_info['error'] = '';
                $sale_date = $this->request->post['sale_date'];

                $import_total = 0;
                $import_total_success = 0;

                $excel_datas = $this->model_admin_excel->import($upload_info['name']);
                $titles = array_shift($excel_datas);

                if (implode(',', $titles) == $this->language->get('text_template_title')) {
                    $sale_datas = array();
                    $stores = $this->model_admin_setting->getStores();

                    foreach ($excel_datas as $excel_data) {
                        if (isset($excel_data['A']) && trim($excel_data['A'])) {
                            $import_total++;
                            $store_id = '0';

                            if ((int)$excel_data['C'] == 0) {
                                continue;
                            }
                            
                            foreach ($stores as $store) {
                                if ($store['name'] == trim($excel_data['E'])) {
                                    $store_id = $store['store_id'];
                                    break;
                                }
                            }

                            $sale_datas[] = array(
                                'store_id'  => $store_id,
                                'store_name'=> $excel_data['E'],
                                'bsku'      => $excel_data['A'],
                                'gsku'      => $excel_data['B'],
                                'quantity'  => $excel_data['C'],
                                'total'     => $excel_data['D'],
                                'sale_date' => $sale_date
                            );
                        }
                    }
                } else {
                    $upload_info['error'] = $this->language->get('error_template');
                }

                @unlink($upload_info['name']);

                $exist = $this->model_admin_purchase->getSalesExist($sale_date);
                
                if ($exist) {
                    $upload_info['error'] = $this->language->get('error_exist');
                }
            }

            if (isset($upload_info['error']) && $upload_info['error']) {
                $json['error'] = $upload_info['error'] . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
            } else {
                if (!empty($sale_datas)) {
                    foreach ($sale_datas as $sale_data) {
                        $this->model_admin_purchase->addStoreSales($sale_data);
                        $import_total_success++;
                    }

                    $this->log->write($sale_date);

                    $json['success'] = $this->language->get('text_upload_success') . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
                } else {
                    $json['error'] = $this->language->get('error_upload');
                }
            }
        }

        $this->response->setOutJson($json);
    }

    public function template() {
        $file = DIR_DOWNLOAD . '店铺销售导入模板.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }

    public function getNewGoods() {
        $base = new Weixin\Base(null, null);
        $res = $base->getRemoteInfo('https://flow.youranjian.cn/wdt/getGoodsList?now=1');

        $this->response->redirect($this->url->link('admin/purchase/newPlan', 'token=' . $this->session->data['token']));
    }

    public function addReturn() {
        $this->load->model('admin/purchase');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateReturnForm()) {
            $this->model_admin_purchase->addReturn($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/purchase/getReturn', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getReturn();
    }

    public function getReturn() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'return_id';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/purchase/addReturn', 'token=' . $this->session->data['token'] . $url);
        $data['autoStock'] = $this->url->link('admin/purchase/autoStock', '&token=' . $this->session->data['token'] . '&hide=1');

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $data['return'] = $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_store'  => $filter_store,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $this->load->model('admin/purchase');        
        $results = $this->model_admin_purchase->getReturn($filter_data);

        foreach ($results as $result) {
            $data['return'][] = array(
                'bsku'      => $result['bsku'],
                'spec_name' => $result['spec_name'],
                'storename' => $stores[$result['store_id']] ?? '',
                'quantity'  => $result['quantity'],
                'date_added'=> $result['date_added']
            );
        }

        $total = $this->model_admin_purchase->getTotalReturn($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_store'] = $this->url->link('admin/purchase/getReturn', 'token=' . $this->session->data['token'] . '&sort=store_id' . $url);
        $data['sort_bsku'] = $this->url->link('admin/purchase/getReturn', 'token=' . $this->session->data['token'] . '&sort=bsku' . $url);
        $data['sort_spec'] = $this->url->link('admin/purchase/getReturn', 'token=' . $this->session->data['token'] . '&sort=spec_name' . $url);
        $data['sort_plan'] = $this->url->link('admin/purchase/getReturn', 'token=' . $this->session->data['token'] . '&sort=quantity' . $url);
        $data['sort_added'] = $this->url->link('admin/purchase/getReturn', 'token=' . $this->session->data['token'] . '&sort=return_id' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/getReturn', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/purchase/getReturn', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/return_list.tpl', $data));
    }

    public function addTransfer() {
        $this->load->model('admin/purchase');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateTransferForm()) {
            $this->model_admin_purchase->addTransfer($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_mode'])) {
                $url .= '&filter_mode=' . $this->request->get['filter_mode'];
            }

            if (isset($this->request->get['filter_state'])) {
                $url .= '&filter_state=' . $this->request->get['filter_state'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/purchase/getTransfers', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getTransferForm();
    }

    public function editTransfer() {
        $this->load->model('admin/purchase');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateTransferForm()) {
            $this->model_admin_purchase->editTransfer($this->request->get['transfer_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_mode'])) {
                $url .= '&filter_mode=' . $this->request->get['filter_mode'];
            }

            if (isset($this->request->get['filter_state'])) {
                $url .= '&filter_state=' . $this->request->get['filter_state'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/purchase/getTransfers', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getTransferForm();
    }

    public function deleteTransfer() {
        $this->load->model('admin/purchase');

        if (isset($this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $transfer_id) {
                $this->model_admin_purchase->deleteTransfer($transfer_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_mode'])) {
                $url .= '&filter_mode=' . $this->request->get['filter_mode'];
            }

            if (isset($this->request->get['filter_state'])) {
                $url .= '&filter_state=' . $this->request->get['filter_state'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/purchase/getTransfers', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getTransfers();
    }

    public function actionTransfer() {
        $this->load->model('admin/purchase');

        if (isset($this->request->post['selected']) && isset($this->request->post['state'])) {
            foreach ($this->request->post['selected'] as $transfer_id) {
                $this->model_admin_purchase->actionTransfer($transfer_id, $this->request->post['state']);
            }

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_mode'])) {
                $url .= '&filter_mode=' . $this->request->get['filter_mode'];
            }

            if (isset($this->request->get['filter_state'])) {
                $url .= '&filter_state=' . $this->request->get['filter_state'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/purchase/getTransfers', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getTransfers();
    }

    public function getTransfers() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_mode'])) {
            $filter_mode = $this->request->get['filter_mode'];
        } else {
            $filter_mode = '';
        }

        if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_mode'])) {
            $url .= '&filter_mode=' . $this->request->get['filter_mode'];
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/purchase/addTransfer', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/purchase/deleteTransfer', 'token=' . $this->session->data['token'] . $url);
        $data['action'] = $this->url->link('admin/purchase/actionTransfer', 'token=' . $this->session->data['token'] . $url);

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores(array('all' => true));

        $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $data['transfers'] = array();

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_mode'   => $filter_mode,
            'filter_state'  => $filter_state,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $this->load->model('admin/purchase');
        $results = $this->model_admin_purchase->getTransfers($filter_data);

        foreach ($results as $result) {
            $data['transfers'][] = array(
                'transfer_id' => $result['transfer_id'],
                'fromstore' => $stores[$result['from_store_id']] ?? '',
                'tostore'   => $stores[$result['to_store_id']] ?? '',
                'mode'      => $result['mode'],
                'bsku'      => $result['bsku'],
                'spec_name' => $result['spec_name'],
                'quan'      => $result['transfer_quan'],
                'state'     => $result['state'],
                'date_added'=> $result['date_added'],
                'creater'   => ($result['from_user_id'] == $this->user->user_id) ? true : false,
                'operator'  => (in_array($result['to_store_id'], explode(',', $this->user->store_ids))) ? true : false,
                'edit'      => $this->url->link('admin/purchase/editTransfer', 'token=' . $this->session->data['token'] . '&transfer_id=' . $result['transfer_id'] . $url)
            );
        }

        $total = $this->model_admin_purchase->getTotalTransfers($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_mode'])) {
            $url .= '&filter_mode=' . $this->request->get['filter_mode'];
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/getTransfers', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_mode'] = $filter_mode;
        $data['filter_state'] = $filter_state;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/purchase/getTransfers', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/transfer_list.tpl', $data));
    }

    protected function getTransferForm() {
        $data['text_form'] = !isset($this->request->get['transfer_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        
        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_mode'])) {
            $url .= '&filter_mode=' . $this->request->get['filter_mode'];
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['transfer_id'])) {
            $data['action'] = $this->url->link('admin/purchase/addTransfer', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/purchase/editTransfer', 'token=' . $this->session->data['token'] . '&transfer_id=' . $this->request->get['transfer_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/purchase/getTransfers', 'token=' . $this->session->data['token'] . $url);
        $data['autoStock'] = $this->url->link('admin/purchase/autoStock', '&token=' . $this->session->data['token']);

        if (isset($this->request->get['transfer_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $transfer_info = $this->model_admin_purchase->getTransfer($this->request->get['transfer_id']);
        }

        if (isset($this->request->post['mode'])) {
            $data['mode'] = $this->request->post['mode'];
        } elseif (!empty($transfer_info)) {
            $data['mode'] = $transfer_info['mode'];
        } else {
            $data['mode'] = 'in';
        }

        if (isset($this->request->post['bsku'])) {
            $data['bsku'] = $this->request->post['bsku'];
        } elseif (!empty($transfer_info)) {
            $data['bsku'] = $transfer_info['bsku'];
        } else {
            $data['bsku'] = '';
        }

        if (isset($this->request->post['transfer_quan'])) {
            $data['transfer_quan'] = $this->request->post['transfer_quan'];
        } elseif (!empty($transfer_info)) {
            $data['transfer_quan'] = $transfer_info['transfer_quan'];
        } else {
            $data['transfer_quan'] = '';
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        if (isset($this->request->post['from_store_id'])) {
            $data['from_store_id'] = $this->request->post['from_store_id'];
        } elseif (!empty($transfer_info)) {
            $data['from_store_id'] = $transfer_info['from_store_id'];
        } else {
            $data['from_store_id'] = '';
        }

        if (isset($this->request->post['to_store_id'])) {
            $data['to_store_id'] = $this->request->post['to_store_id'];
        } elseif (!empty($transfer_info)) {
            $data['to_store_id'] = $transfer_info['to_store_id'];
        } else {
            $data['to_store_id'] = '';
        }

        if (isset($this->request->post['state'])) {
            $data['state'] = $this->request->post['state'];
        } elseif (!empty($transfer_info)) {
            $data['state'] = $transfer_info['state'];
        } else {
            $data['state'] = '0';
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/transfer_form.tpl', $data));
    }

    protected function validateTransferForm() {
        if (!$this->user->hasPermission('modify', 'admin/purchase')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['bsku']) || empty($this->request->post['transfer_quan'])) {
            $this->error['warning'] = '借调产品和数量不能为空！';
            return false;
        }

        if (empty($this->request->post['from_store_id']) || empty($this->request->post['to_store_id'])) {
            $this->error['warning'] = '申请和目标店铺不能为空！';
            return false;
        }

        if (!empty($this->request->post['state'])) {
            $this->error['warning'] = '当前状态不能修改！';
            return false;
        }
        
        return !$this->error;
    }

    protected function validateNewForm() {
        if (!$this->user->hasPermission('modify', 'admin/purchase')) {
            $this->error['warning'] = $this->language->get('error_permission');
            return false;
        }

        if (empty($this->request->post['order_quan'])) {
            $this->error['warning'] = '下单数量不能为空！';
            return false;
        }

        if (empty($this->request->post['plan_quan'])) {
            $this->error['warning'] = '备货数量不能为空！';
            return false;
        }

        return !$this->error;
    }

    protected function validateReturnForm() {
        if (!$this->user->hasPermission('modify', 'admin/purchase')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['bsku']) || empty($this->request->post['quantity'])) {
            $this->error['warning'] = '退货产品和数量不能为空！';
            return false;
        }

        if (empty($this->request->post['store_id'])) {
            $this->error['warning'] = '退货店铺不能为空！';
            return false;
        }
        
        return !$this->error;
    }

    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'admin/purchase')) {
            $this->error['warning'] = $this->language->get('error_permission');
            return false;
        }

        if (empty($this->request->post['order_no'])) {
            $this->error['warning'] = '采购合同号不能为空！';
            return false;
        }

        if (empty($this->request->post['complete_date'])) {
            $this->error['warning'] = '合同到货日期不能为空！';
            return false;
        }

        if (empty($this->request->post['selected'])) {
            $this->error['warning'] = '采购产品不能为空！';
            return false;
        }

        return !$this->error;
    }

    public function autoProduct() {
        $json = array();

        if (isset($this->request->get['filter_name'])) {
            if (isset($this->request->get['filter_name'])) {
                $filter_name = $this->request->get['filter_name'];
            } else {
                $filter_name = '';
            }

            if (isset($this->request->get['store_id'])) {
                $store_id = $this->request->get['store_id'];
            } else {
                $store_id = '';
            }

            $this->load->model('admin/purchase');
            $bsku_list = array();
            $results = $this->model_admin_purchase->getStorePlans($store_id);

            foreach ($results as $result) {
                $bsku_list[] = $result['bsku'];
            }

            if (isset($this->request->get['limit'])) {
                $limit = $this->request->get['limit'];
            } else {
                $limit = 5;
            }

            $filter_data = array(
                'filter_name'   => $filter_name,
                'filter_store'  => $store_id,
                'filter_bsku_notlist'=> $bsku_list,
                'start'         => 0,
                'limit'         => $limit
            );

            $results = $this->model_admin_purchase->getStoreStock($filter_data);

            foreach ($results as $result) {
                $bsku_list[] = $result['bsku'];

                if ($result['stop'] == '0') {
                    $json[] = array(
                        'value'     => $result['bsku'],
                        'label'     => strip_tags(html_entity_decode($result['spec_name'], ENT_QUOTES, 'UTF-8')),
                        'image'     => $result['img_url'],
                        'stock_quan'=> $result['stock_quan'],
                        'order_quan'=> $result['order_quan'],
                        'sale_quan' => $result['sale_quan'],
                        '1dsales'   => $result['1dsales'],
                        '3dsales'   => $result['3dsales'],
                        '7dsales'   => $result['7dsales'],
                        '15dsales'  => $result['15dsales'],
                        '30dsales'  => $result['30dsales'],
                        '60dsales'  => $result['60dsales'],
                        '90dsales'  => $result['90dsales']
                    );
                }
            }

            $filter_data = array(
                'filter_name'  => $filter_name,
                'spec_not_list'=> $bsku_list,
                'start'        => 0,
                'limit'        => $limit
            );

            $results = $this->model_admin_purchase->getWdtSpecs($filter_data);

            foreach ($results as $result) {
                $json[] = array(
                    'value'     => $result['spec_no'],
                    'label'     => strip_tags(html_entity_decode($result['spec_name'], ENT_QUOTES, 'UTF-8')),
                    'image'     => $result['img_url'],
                    'stock_quan'=> 0,
                    'order_quan'=> 0,
                    'sale_quan' => 0,
                    '1dsales'   => 0,
                    '3dsales'   => 0,
                    '7dsales'   => 0,
                    '15dsales'  => 0,
                    '30dsales'  => 0,
                    '60dsales'  => 0,
                    '90dsales'  => 0
                );
            }
        }

        $this->response->setOutJson($json);
    }

    public function autoStock() {
        $json = array();

        if (isset($this->request->get['filter_name'])) {
            if (isset($this->request->get['filter_name'])) {
                $filter_name = $this->request->get['filter_name'];
            } else {
                $filter_name = '';
            }

            if (isset($this->request->get['hide'])) {
                $hide = true;
            } else {
                $hide = false;
            }
            
            if (isset($this->request->get['limit'])) {
                $limit = $this->request->get['limit'];
            } else {
                $limit = 5;
            }

            $bsku_list = array();

            $filter_data = array(
                'filter_name'   => $filter_name,
                'start'         => 0,
                'limit'         => $limit
            );

            $this->load->model('admin/purchase');
            $results = $this->model_admin_purchase->getWdtSpecs($filter_data);

            foreach ($results as $result) {
                $bsku_list[$result['spec_no']] = array(
                    'value' => $result['spec_no'],
                    'label' => strip_tags(html_entity_decode($result['spec_name'], ENT_QUOTES, 'UTF-8')),
                    'stocks'=> array()
                );
            }

            if (!empty($bsku_list)) {
                if (!$hide) {
                    $this->load->model('admin/setting');
                    $stores = array();
                    $results = $this->model_admin_setting->getStores(array('all' => true));

                    foreach ($results as $store) {
                        $stores[$store['store_id']] = $store['name'];
                    }

                    $results = $this->model_admin_purchase->getTransferStock(array('filter_bsku_list' => array_keys($bsku_list)));

                    foreach ($results as $result) {
                        $bsku_list[$result['bsku']]['stocks'][] = array(
                            'store'  => $result['store_id'],
                            'stock'  => ($stores[$result['store_id']] ?? '') . '库存：' .  $result['stock_quan']
                        );
                    }
                }  

                foreach ($bsku_list as $list) {
                    $json[] = $list;
                }
            }
        }

        $this->response->setOutJson($json);
    }

    public function contract() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'purchase_contract_id';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_date_start'  => $filter_start,
            'filter_date_end'    => $filter_end,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $this->load->model('admin/purchase');

        $total = $this->model_admin_purchase->getPurchaseContractTotal($filter_data);
        $data['purchase_contracts'] = $this->model_admin_purchase->getPurchaseContract($filter_data);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/contract', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['nofilter'] = $this->url->link('admin/purchase/contract', 'token=' . $this->session->data['token']);
        $data['add'] = $this->url->link('admin/purchase/contractAdd', 'token=' . $this->session->data['token']);
        $data['crowd'] = $this->url->link('admin/purchase/contractCrowd', 'token=' . $this->session->data['token']);
        $data['edit_url'] = $this->url->link('admin/purchase/contractAdd', 'token=' . $this->session->data['token'].'&purchase_contract_id=');
        $data['export_url'] = $this->url->link('admin/purchase/contractExport', 'token=' . $this->session->data['token'].'&purchase_contract_id=');
        $data['documentary_export_url'] = $this->url->link('admin/purchase/contractDocumentaryExport', 'token=' . $this->session->data['token'].'&purchase_contract_id=');

        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;
        $data['filter_name'] = $filter_name;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/contract_list.tpl', $data));
    }

    public function contractAdd() {
        $this->load->model('admin/purchase');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm2()) {
            if (!empty($this->request->post['manner_of_packing'])) {
                foreach ($this->request->post['manner_of_packing'] as $key => $value) {
                    $text = str_replace("\r\n", "<br>", $value[0]);
                    $this->request->post['manner_of_packing'][$key][0] =  htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
                }
            }

            if (!empty($this->request->post['specs'])) {
                foreach ($this->request->post['specs'] as $spec_key => $spec) {
                    if (!empty($spec['spec_no'])) {
                        $goods_no = $this->model_admin_purchase->getGoodsNo($spec['spec_no']);
                        if (!empty($goods_no) && !empty($spec['packaging'])) {
                            $this->model_admin_purchase->setPurchaseContractContent($goods_no['goods_no'],1,$spec['packaging']);
                        }
                        if (!empty($goods_no) && !empty($spec['accessories'])) {
                            $this->model_admin_purchase->setPurchaseContractContent($goods_no['goods_no'],3,$spec['accessories']);
                        }
                        if (!empty($goods_no) && !empty($spec['attention'])) {
                            $this->model_admin_purchase->setPurchaseContractContent($goods_no['goods_no'],4,$spec['attention']);
                        }
                        if (!empty($goods_no) && !empty($spec['weight'])) {
                            $this->model_admin_purchase->setPurchaseContractContent($goods_no['goods_no'],5,$spec['weight']);
                        }
                        if (!empty($spec['new_img'])) {
                            $this->model_admin_purchase->setPurchaseContractContent($spec['spec_no'],6,$spec['new_img']);
                            $this->request->post['specs'][$spec_key]['img_url'] = $spec['new_img'];
                    }
                }
            }
            }

            !isset($this->request->get['purchase_contract_id']) ? $this->model_admin_purchase->addPurchaseContract($this->request->post) : $this->model_admin_purchase->editPurchaseContract($this->request->post,$this->request->get['purchase_contract_id']);

            if (!empty($this->request->post['carton_mark']) && !empty($this->request->post['provider_no'])) {
                $this->model_admin_purchase->setPurchaseContractContent($this->request->post['provider_no'],2,$this->request->post['carton_mark']);
            }
            $this->response->redirect($this->url->link('admin/purchase/contract', 'token=' . $this->session->data['token']));
        }

        $data['text_form'] = !isset($this->request->get['purchase_contract_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        $count = 0;
        $count_manner_of_packing = 0;
        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        if (isset($this->request->get['purchase_contract_id'])) {
            $purchaseContract = $this->model_admin_purchase->getOnePurchaseContract($this->request->get['purchase_contract_id']);
            if (empty($purchaseContract['purchase_contract_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                if (!empty($purchaseContract['specs'])) {
                    $purchaseContract['specs'] = json_decode($purchaseContract['specs'],true);
                    $count = count($purchaseContract['specs']);
                }
                if (!empty($purchaseContract['manner_of_packing'])) {
                    $manner_of_packing = json_decode($purchaseContract['manner_of_packing'],true);
                    foreach ($manner_of_packing as $key => $value) {
                        $manner_of_packing[$key][0] = str_replace("<br>", "\r\n", htmlspecialchars_decode($value[0]));
                    }
                    $count_manner_of_packing = count($manner_of_packing);
                    $purchaseContract['manner_of_packing'] = $manner_of_packing;
                }
                if (!empty($purchaseContract['inner_box'])) {
                    $purchaseContract['inner_box'] = json_decode($purchaseContract['inner_box'],true);
                }
                $data['purchase_contract'] = $purchaseContract;
            }
        }
        $data['packaging_list'] =  $this->model_admin_purchase->getPackagingList();
        $data['packaging_list_json'] = json_encode($data['packaging_list']);
        $data['accessories_list'] =  $this->model_admin_purchase->getAccessoriesList();
        $data['accessories_list_json'] = json_encode($data['accessories_list']);
        $data['attention_list'] =  $this->model_admin_purchase->getAttentionList();
        $data['attention_list_json'] = json_encode($data['attention_list']);

        if (!isset($this->request->get['purchase_contract_id'])) {
            $data['action'] = $this->url->link('admin/purchase/contractAdd', 'token=' . $this->session->data['token']);
        } else {
            $data['action'] = $this->url->link('admin/purchase/contractAdd', 'token=' . $this->session->data['token'] . '&purchase_contract_id=' . $this->request->get['purchase_contract_id']);
        }
        $data['get_specs'] = $this->url->link('admin/purchase/getSpecs', '&token=' . $this->session->data['token']);
        $data['cancel'] = $this->url->link('admin/purchase/contract', 'token=' . $this->session->data['token']);
        $data['count'] = $count;
        $data['count_manner_of_packing'] = $count_manner_of_packing;
        $data['providers'] = $this->model_admin_purchase->getPurchaseProviders();
        $data['getToken'] = $this->url->link('admin/common/getUploadToken', 'token=' . $this->session->data['token']);
        $data['get_provider_carton_mark'] = $this->url->link('admin/purchase/providerCartonMark', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('purchase/contract_form.tpl', $data));
    }

    public function contractCrowd() {
        if (isset($this->request->get['contract_ids'])) {
            $contract_ids = $this->request->get['contract_ids'];
        } else {
            $contract_ids = '';
        }

        $url = '';

        if (isset($this->request->get['contract_ids'])) {
            $url .= '&contract_ids=' . $this->request->get['contract_ids'];
        }

        $data['action'] = $this->url->link('admin/purchase/contractCrowd', 'token=' . $this->session->data['token'] . $url);

        $this->load->model('admin/purchase');
        $data['products'] = $this->model_admin_purchase->getCrowdProduct($contract_ids);

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            if (!empty($data['products']) && !empty($this->request->post['quantity'])) {
                $products = $data['products'];
                
                foreach ($this->request->post['quantity'] as $product_id => $product) {
                    foreach ($product as $option_id => $quan) {
                        if (isset($products[$product_id][$option_id])) {
                            $products[$product_id][$option_id]['quan'] = $quan;
                            unset($products[$product_id][$option_id]['name']);
                            unset($products[$product_id][$option_id]['img']);
                        }
                    }
                }

                $this->model_admin_purchase->updateCrowdProduct($contract_ids, $products);
            }

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $this->response->redirect($this->url->link('admin/purchase/contract', 'token=' . $this->session->data['token']));
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/contract_crowd.tpl', $data));
    }

    public function providerCartonMark() {
        $json = array();
        $results = '';

        if (!empty($this->request->get['provider_no'])) {
            $this->load->model('admin/purchase');
            $results = $this->model_admin_purchase->getPurchaseContractContent($this->request->get['provider_no'],2);
        }
        $this->response->setOutJson($results);
    }

    public function getSpecs() {
        $json = array();
        $results = '';

        if (isset($this->request->get['filter_name'])) {
            if (isset($this->request->get['filter_name'])) {
                $filter_name = $this->request->get['filter_name'];
            } else {
                $filter_name = '';
            }

            if (isset($this->request->get['provider_no'])) {
                $provider_no = $this->request->get['provider_no'];
            } else {
                $provider_no = '';
            }

            if (isset($this->request->get['limit'])) {
                $limit = $this->request->get['limit'];
            } else {
                $limit = $this->config->get('config_limit');
            }

            $filter_data = array(
                'filter_name'   => $filter_name,
                'provider_no'   => $provider_no,
                'start'         => 0,
                'limit'         => $limit
            );

            $this->load->model('admin/purchase');
            $results = $this->model_admin_purchase->getWdtSpecsTwo($filter_data);
        }

        $this->response->setOutJson($results);
    }

    public function contractExport() {
        if (isset($this->request->get['purchase_contract_id'])) {
            $this->load->model('admin/purchase');
            $purchaseContract = $this->model_admin_purchase->getOnePurchaseContract($this->request->get['purchase_contract_id']);

            if (empty($purchaseContract['purchase_contract_id'])) {
                echo '数据不存在';exit();
            } else {
                $purchaseContract['specs'] = json_decode($purchaseContract['specs'],true);

                $this->load->helper('office/autoload');

                $styleArray_header = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  10,
                        'bold' => true, // 设置字体加粗
                    ]
                ];

                $styleArray_default_bold = [
                    'alignment' =>  [
                        'horizontal' =>  'left',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  10,
                        'bold' => true, // 设置字体加粗
                    ]
                ];

                $styleArray_default = [
                    'alignment' =>  [
                        'horizontal' =>  'left',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  10,
                    ]
                ];

                $styleArray_table_left_default = [
                    'alignment' =>  [
                        'horizontal' =>  'left',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                        'wrapText' => true, // 设置自动换行
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  12,
                    ]
                ];

                $styleArray_table_default = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                        'wrapText' => true, // 设置自动换行
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  12,
                    ]
                ];

                $styleArray_default2 = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  10,
                    ]
                ];

                $styleArray_border = [
                    'borders' => [
                        'outline' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ];

                $styleArray_arial_default = [
                    'alignment' =>  [
                        'horizontal' =>  'left',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  'Arial',
                        'size' =>  10,
                        'bold' => true, // 设置字体加粗
                    ]
                ];

                $default_line_height = 14.25;

                $phpexcel = new PhpOffice\PhpSpreadsheet\Spreadsheet();// 生成新的excel对象
                $phpexcel->getDefaultStyle()->getFont()->setName('宋体');    //设置默认字体
                $phpexcel->getDefaultStyle()->getFont()->setSize(11);        //设置默认字体大小
                $phpexcel->getActiveSheet()->getDefaultRowDimension()->setRowHeight(14.25); // 设置默认行高

                $n = 0;
                $n2 = 1;
                $phpexcel->createSheet();//创建sheet
                $phpexcel->setActiveSheetIndex($n);//设置当前的活动sheet
                $phpexcel->getActiveSheet($n)->setTitle($purchaseContract['contract_number']);//设置sheet的名称

                //设置列宽
                $phpexcel->getActiveSheet($n)->getColumnDimension('A')->setWidth(18);
                $phpexcel->getActiveSheet($n)->getColumnDimension('B')->setWidth(11);
                $phpexcel->getActiveSheet($n)->getColumnDimension('C')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('D')->setWidth(19);
                $phpexcel->getActiveSheet($n)->getColumnDimension('E')->setWidth(9.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('F')->setWidth(11);
                $phpexcel->getActiveSheet($n)->getColumnDimension('G')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('H')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('I')->setWidth(13);
                $phpexcel->getActiveSheet($n)->getColumnDimension('J')->setWidth(12);
                $phpexcel->getActiveSheet($n)->getColumnDimension('K')->setWidth(15);
                $phpexcel->getActiveSheet($n)->getColumnDimension('L')->setWidth(12);
                $phpexcel->getActiveSheet($n)->getColumnDimension('M')->setWidth(26);

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_header);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('产品购销合同');
                //边框
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':E'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('合同号：'.$purchaseContract['contract_number']);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->mergeCells('F'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('签订时间：'.$purchaseContract['sign_date']);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_default_bold);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':E'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue(!empty($purchaseContract['warehouse_date']) ?$purchaseContract['warehouse_date']:'货物送至南安17:00前入仓');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->mergeCells('F'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('交货时间：'.$purchaseContract['delivery_date']);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_default_bold);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':E'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('需  方：泉州市盈扩电子商务有限公司');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('联 系 人：');
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->mergeCells('G'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue('董丽影');
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_default);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':E'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('电 话：');
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->mergeCells('G'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue('13559024905');
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_default);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':E'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue($purchaseContract['provider_name']?'供  方：'.$purchaseContract['provider_name']:'');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('联 系 人：');
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->mergeCells('G'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue($purchaseContract['contact']?$purchaseContract['contact']:'');
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_default);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':E'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue($purchaseContract['address']?$purchaseContract['address']:'');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('电 话：');
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->mergeCells('G'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue($purchaseContract['mobile']?$purchaseContract['mobile']:'');
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_default);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':J'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('本合同由买卖双方订交，根据本合同条款规定，买方同意购买，卖方同意出售下列商品：');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':J'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_default);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('图片');
                $phpexcel->getActiveSheet($n)->getCell('B'.$n2)->setValue('品名');
                $phpexcel->getActiveSheet($n)->getCell('C'.$n2)->setValue('条码');
                $phpexcel->getActiveSheet($n)->getCell('D'.$n2)->setValue('尺寸');
                $phpexcel->getActiveSheet($n)->getCell('E'.$n2)->setValue('数量（套）');
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('件/箱');
                $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue('箱数');
                $phpexcel->getActiveSheet($n)->getCell('H'.$n2)->setValue('单价/套');
                $phpexcel->getActiveSheet($n)->getCell('I'.$n2)->setValue('重量（净重）');
                $phpexcel->getActiveSheet($n)->getCell('J'.$n2)->setValue('总金额（人民币）');
                $phpexcel->getActiveSheet($n)->getCell('K'.$n2)->setValue('包装方式');
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_default2);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
                $n2 += 1;

                $count_spec = 0;
                $folderPath = DIR_DOWNLOAD.'/purchase_contract_image/'.$purchaseContract['purchase_contract_id'];
                if (file_exists($folderPath)) {
                    $this->deleteDir($folderPath );
                }
                mkdir($folderPath,0777,true);

                $packaging_like_list =  $this->model_admin_purchase->getPackagingLikeList();
                $packaging_list =  $this->model_admin_purchase->getPackagingList();

                $in_packaging = [];

                foreach ($purchaseContract['specs'] as $spec_key => $spec) {
                    if (isset($spec['carton_num']) && !empty($spec['num']) && !empty($spec['price'])) {
                        foreach ($packaging_like_list as $packaging_like_key => $packaging_like) {
                            $packaging_like = explode(',',$packaging_like);
                            foreach ($packaging_like as $like) {
                                if (strpos($spec['packaging'],$like)  !== false) {
                                    $in_packaging[] = $packaging_like_key;
                                }
                            }
                        }
                        $destination = '';
                        if (!empty($spec['img_url'])  && $spec['img_url'] != 'null') {
                            $headers = @get_headers($spec['img_url'], 1); // 获取文件的HTTP头信息
                            if ($headers && $headers[0] == 'HTTP/1.1 200 OK') {
                                $content_type = $headers['Content-Type']; // 从HTTP头中获取Content-Type字段
                                $file_type = explode('/', $content_type)[1]; // 获取文件类型部分

                                $destination = $folderPath.'/'.$spec['spec_no'].'.'.$file_type;
                                $file_content = file_get_contents($spec['img_url']);
                                file_put_contents($destination, $file_content);
                            }
                        }

                        if ($spec['carton_num'] == 0) {
                            $count_carton_num = 0;
                            $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->getFont()->getColor()->setRGB('FF0000');
                        } else {
                            if ((int)$spec['num'] % (int)$spec['carton_num'] == 0) {
                                $count_carton_num = (int)$spec['num'] / (int)$spec['carton_num'];
                            } else {
                                $count_carton_num = number_format((int)$spec['num'] / (int)$spec['carton_num'],2);
//                                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_00);
                                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->getFont()->getColor()->setRGB('FF0000');
                            }
                        }

                        $count_price = (int)$spec['num'] * (int)$spec['price'];
                        $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(84);

                        if (!empty($spec['img_url'])  && $spec['img_url'] != 'null' && $headers && $headers[0] == 'HTTP/1.1 200 OK') {
                            $drawing[$spec_key] = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                            $drawing[$spec_key]->setName($spec['spec_no']);
                            $drawing[$spec_key]->setDescription($spec['spec_name']);
                            $drawing[$spec_key]->setPath($destination);
//                        $drawing[$spec_key]->setWidth(80);
                            $drawing[$spec_key]->setHeight(100);
                            $drawing[$spec_key]->setCoordinates('A'.$n2);
                            $drawing[$spec_key]->setOffsetX(25);
                            $drawing[$spec_key]->setOffsetY(6);
                            $drawing[$spec_key]->setWorksheet($phpexcel->getActiveSheet());
                        }

                        $phpexcel->getActiveSheet($n)->getCell('B'.$n2)->setValue($spec['spec_name']);
                        $phpexcel->getActiveSheet($n)->getCell('C'.$n2)->setValue($spec['spec_no']);
                        $phpexcel->getActiveSheet($n)->getCell('D'.$n2)->setValue($spec['size']);
                        $phpexcel->getActiveSheet($n)->getCell('E'.$n2)->setValue(trim($spec['num']));
                        $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue(trim($spec['carton_num']));
//                        $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue($count_carton_num);
                        $phpexcel->getActiveSheet($n)->setCellValue('G'.$n2,'=ROUNDUP(E'.$n2.'/F'.$n2.',0)');
                        $phpexcel->getActiveSheet($n)->getCell('H'.$n2)->setValue(trim($spec['price']));
                        $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->getNumberFormat()->setFormatCode('¥#,##0.00;¥-#,##0.00');;
                        $phpexcel->getActiveSheet($n)->getCell('I'.$n2)->setValue('不低于'.$spec['weight'].'克');
//                        $phpexcel->getActiveSheet($n)->getCell('J'.$n2)->setValue($count_price);
                        $phpexcel->getActiveSheet($n)->setCellValue('J'.$n2,'=E'.$n2.'*H'.$n2);
                        $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->getNumberFormat()->setFormatCode('¥#,##0.00;¥-#,##0.00');;
                        $phpexcel->getActiveSheet($n)->getCell('K'.$n2)->setValue($spec['packaging']);

                        $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_table_left_default);
                        $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_table_left_default);
                        $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_table_left_default);
                        $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->getFont()->getColor()->setRGB('FF0000');

                        $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_border);

                        if ($spec['accessories']) {
                            $phpexcel->getActiveSheet($n)->getCell('L'.$n2)->setValue($spec['accessories']);
                            $phpexcel->getActiveSheet($n)->getStyle('L'.$n2)->applyFromArray($styleArray_table_default);
                            $phpexcel->getActiveSheet($n)->getStyle('L'.$n2)->getFont()->getColor()->setRGB('FF0000');
                            $phpexcel->getActiveSheet($n)->getStyle('L'.$n2)->getFont()->setBold(true);
                            $phpexcel->getActiveSheet($n)->getStyle('L'.$n2)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFF00');
                        }
                        if ($spec['attention']) {
                            $phpexcel->getActiveSheet($n)->getCell('M'.$n2)->setValue($spec['attention']);
                            $phpexcel->getActiveSheet($n)->getStyle('M'.$n2)->applyFromArray($styleArray_table_default);
                            $phpexcel->getActiveSheet($n)->getStyle('M'.$n2)->getFont()->getColor()->setRGB('FF0000');
                        }

                        $n2 += 1;
                        $count_spec += 1;
                    }
                }
                $in_packaging = array_values(array_unique($in_packaging));

                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(19.5);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':B'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('D'.$n2)->setValue('数量合计');
                $phpexcel->getActiveSheet($n)->setCellValue('E'.$n2,'=SUM(E'.($n2-$count_spec).':E'.($n2-1).')');
                $phpexcel->getActiveSheet($n)->setCellValue('G'.$n2,'=SUM(G'.($n2-$count_spec).':G'.($n2-1).')');
//                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_00);
                $phpexcel->getActiveSheet($n)->getCell('I'.$n2)->setValue('金额合计');
                $phpexcel->getActiveSheet($n)->setCellValue('J'.$n2,'=SUM(J'.($n2-$count_spec).':J'.($n2-1).')');
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_table_default);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->getFont()->getColor()->setRGB('FF0000');

                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':B'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;

                $count_in_packaging = count($in_packaging);

                if ($count_in_packaging > 0) {
                    $in_packaging_line = ceil($count_in_packaging / 2);
                    for ($i=0;$i<$in_packaging_line;$i++) {
                        for ($j=0;$j<=7;$j++) {
                            $phpexcel->getActiveSheet($n)->getRowDimension(($n2+($i*8)+$j))->setRowHeight(19.5);
                        }
                    }
                    $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':A'.($n2+7+($in_packaging_line-1)*8));
                    $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('内部包装形式'.PHP_EOL.'（示例图）');
                    $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_table_default);
                    $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':A'.($n2+7+($in_packaging_line-1)*8))->applyFromArray($styleArray_border);

                    $packaging_list_img =  $this->model_admin_purchase->getPackagingListImg();

                    $pattern = '/\((.*?)\)/';
                    foreach ($in_packaging as $k => $v) {
                        if ($k > 0 && $k % 2 == 0) {
                            $n2 += 8;
                        }
                        if (($k+1) % 2 != 0) {
                            $phpexcel->getActiveSheet($n)->mergeCells('B'.$n2.':E'.($n2+1));
                            $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_table_default);
                            $phpexcel->getActiveSheet($n)->getStyle('B'.$n2.':E'.($n2+1))->applyFromArray($styleArray_border);

                            $matches = [];
                            preg_match($pattern, $packaging_list[$v], $matches);
                            if (!empty($matches)) {
                                $matches_one = str_replace($matches[0], "", $packaging_list[$v]);
                                $richText = new \PhpOffice\PhpSpreadsheet\RichText\RichText();
                                $richText->createText('');
                                $objPayable = $richText->createTextRun($matches_one);
                                $objPayable->getFont()->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color('FFFF0000'));
                                $objPayable = $richText->createTextRun($matches[0]);

                                $phpexcel->getActiveSheet($n)->getCell('B'.$n2)->setValue($richText);
                            } else {
                                $phpexcel->getActiveSheet($n)->getCell('B'.$n2)->setValue($packaging_list[$v]);
                            }
                            $phpexcel->getActiveSheet($n)->mergeCells('B'.($n2+2).':E'.($n2+7));
                            foreach ($packaging_list_img[$v] as $img_key => $img) {
                                $interior_drawing_one[$img_key] = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                                $interior_drawing_one[$img_key]->setName($img);
                                $interior_drawing_one[$img_key]->setDescription($img);
                                $interior_drawing_one[$img_key]->setPath(DIR_DOWNLOAD.'/purchase_contract_image/'.$img);
                                $interior_drawing_one[$img_key]->setWidth(100);
                                $interior_drawing_one[$img_key]->setHeight(100);
                                if ($img_key == 0) {
                                    $interior_drawing_one[$img_key]->setCoordinates('B'.($n2+2));
                                    $interior_drawing_one[$img_key]->setOffsetX(10);
                                } else if ($img_key == 1) {
                                    $interior_drawing_one[$img_key]->setCoordinates('C'.($n2+2));
                                    $interior_drawing_one[$img_key]->setOffsetX(50);
                                } else if ($img_key == 2) {
                                    $interior_drawing_one[$img_key]->setCoordinates('D'.($n2+2));
                                    $interior_drawing_one[$img_key]->setOffsetX(80);
                                }
                                $interior_drawing_one[$img_key]->setOffsetY(20);
                                $interior_drawing_one[$img_key]->setWorksheet($phpexcel->getActiveSheet());
                            }
                            $phpexcel->getActiveSheet($n)->getStyle('B'.($n2+2).':E'.($n2+7))->applyFromArray($styleArray_border);
                        } else {
                            $phpexcel->getActiveSheet($n)->mergeCells('F'.$n2.':K'.($n2+1));
                            $matches = [];
                            preg_match($pattern, $packaging_list[$v], $matches);
                            if (!empty($matches)) {
                                $matches_one = str_replace($matches[0], "", $packaging_list[$v]);
                                $richText = new \PhpOffice\PhpSpreadsheet\RichText\RichText();
                                $richText->createText('');
                                $objPayable = $richText->createTextRun($matches_one);
                                $objPayable->getFont()->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color('FFFF0000'));
                                $objPayable = $richText->createTextRun($matches[0]);

                                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue($richText);
                            } else {
                            $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue($packaging_list[$v]);
                            }

                            $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_table_default);
                            $phpexcel->getActiveSheet($n)->getStyle('F'.$n2.':K'.($n2+1))->applyFromArray($styleArray_border);
                            $phpexcel->getActiveSheet($n)->mergeCells('F'.($n2+2).':K'.($n2+7));
                            if (!empty($packaging_list_img[$v])) {
                            foreach ($packaging_list_img[$v] as $img) {
                                foreach ($packaging_list_img[$v] as $img_key => $img) {
                                    $interior_drawing_one[$img_key] = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                                    $interior_drawing_one[$img_key]->setName($img);
                                    $interior_drawing_one[$img_key]->setDescription($img);
                                    $interior_drawing_one[$img_key]->setPath(DIR_DOWNLOAD.'/purchase_contract_image/'.$img);
                                    $interior_drawing_one[$img_key]->setWidth(100);
                                    $interior_drawing_one[$img_key]->setHeight(100);
                                    if ($img_key == 0) {
                                        $interior_drawing_one[$img_key]->setCoordinates('F'.($n2+2));
                                        $interior_drawing_one[$img_key]->setOffsetX(30);
                                    } else if ($img_key == 1) {
                                        $interior_drawing_one[$img_key]->setCoordinates('H'.($n2+2));
                                        $interior_drawing_one[$img_key]->setOffsetX(10);
                                    } else if ($img_key == 2) {
                                        $interior_drawing_one[$img_key]->setCoordinates('I'.($n2+2));
                                        $interior_drawing_one[$img_key]->setOffsetX(60);
                                    }
                                    $interior_drawing_one[$img_key]->setOffsetY(20);
                                    $interior_drawing_one[$img_key]->setWorksheet($phpexcel->getActiveSheet());
                                    }
                                }
                            }
                            $phpexcel->getActiveSheet($n)->getStyle('F'.($n2+2).':K'.($n2+7))->applyFromArray($styleArray_border);
                        }
                    }
                }

                if ($count_in_packaging % 2 == 0) {
                    if ($count_spec > 0 && $count_in_packaging > 0) {
                        $n2 += 8;
                    }
                    if (!empty($purchaseContract['carton_mark'])) {
                        $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(106);
                        $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('外箱唛'.PHP_EOL.'（外箱规格要求）');
                        $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->mergeCells('B'.$n2.':E'.$n2);
                        $headers = get_headers($purchaseContract['carton_mark'], 1); // 获取文件的HTTP头信息
                        $content_type = $headers['Content-Type']; // 从HTTP头中获取Content-Type字段
                        $file_type = explode('/', $content_type)[1]; // 获取文件类型部分
                        $carton_mark_destination = $folderPath.'/carton_mark.'.$file_type;
                        $file_content = file_get_contents($purchaseContract['carton_mark']);
                        file_put_contents($carton_mark_destination, $file_content);
                        $interior_drawing_two = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                        $interior_drawing_two->setName('carton_mark');
                        $interior_drawing_two->setDescription('carton_mark');
                        $interior_drawing_two->setPath($carton_mark_destination);
                        $interior_drawing_two->setHeight(100);
                        $interior_drawing_two->setCoordinates('B'.$n2);
                        $interior_drawing_two->setOffsetX(30);
                        $interior_drawing_two->setOffsetY(20);
                        $interior_drawing_two->setWorksheet($phpexcel->getActiveSheet());
                        $phpexcel->getActiveSheet($n)->getStyle('B'.$n2.':E'.$n2)->applyFromArray($styleArray_border);

                        $phpexcel->getActiveSheet($n)->mergeCells('F'.$n2.':K'.$n2);
                        $interior_drawing_three = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                        $interior_drawing_three->setName('interior_manner_of_packing_three');
                        $interior_drawing_three->setDescription('interior_manner_of_packing_three');
                        $interior_drawing_three->setPath(DIR_DOWNLOAD.'/purchase_contract_image/interior_manner_of_packing_three.png');
                        $interior_drawing_three->setHeight(120);
                        $interior_drawing_three->setCoordinates('G'.$n2);
                        $interior_drawing_three->setOffsetX(10);
                        $interior_drawing_three->setOffsetY(10);
                        $interior_drawing_three->setWorksheet($phpexcel->getActiveSheet());
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                        $n2 += 1;
                    }
                } else {
                    if (!empty($purchaseContract['carton_mark'])) {
                        $phpexcel->getActiveSheet($n)->mergeCells('F'.$n2.':I'.($n2+1));
                        $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('外箱唛');
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2.':I'.($n2+1))->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->mergeCells('F'.($n2+2).':I'.($n2+7));
                        $headers = get_headers($purchaseContract['carton_mark'], 1); // 获取文件的HTTP头信息
                        $content_type = $headers['Content-Type']; // 从HTTP头中获取Content-Type字段
                        $file_type = explode('/', $content_type)[1]; // 获取文件类型部分
                        $carton_mark_destination = $folderPath.'/carton_mark.'.$file_type;
                        $file_content = file_get_contents($purchaseContract['carton_mark']);
                        file_put_contents($carton_mark_destination, $file_content);
                        $interior_drawing_two = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                        $interior_drawing_two->setName('carton_mark');
                        $interior_drawing_two->setDescription('carton_mark');
                        $interior_drawing_two->setPath($carton_mark_destination);
                        $interior_drawing_two->setHeight(100);
                        $interior_drawing_two->setCoordinates('F'.($n2+2));
                        $interior_drawing_two->setOffsetX(30);
                        $interior_drawing_two->setOffsetY(20);
                        $interior_drawing_two->setWorksheet($phpexcel->getActiveSheet());
                        $phpexcel->getActiveSheet($n)->getStyle('F'.($n2+2).':I'.($n2+7))->applyFromArray($styleArray_border);

                        $phpexcel->getActiveSheet($n)->mergeCells('J'.$n2.':K'.($n2+1));
                        $phpexcel->getActiveSheet($n)->getCell('J'.$n2)->setValue('外箱规格要求');
                        $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('J'.$n2.':K'.($n2+1))->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->mergeCells('J'.($n2+2).':K'.($n2+7));
                        $interior_drawing_three = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                        $interior_drawing_three->setName('interior_manner_of_packing_three');
                        $interior_drawing_three->setDescription('interior_manner_of_packing_three');
                        $interior_drawing_three->setPath(DIR_DOWNLOAD.'/purchase_contract_image/interior_manner_of_packing_three.png');
                        $interior_drawing_three->setHeight(130);
                        $interior_drawing_three->setCoordinates('J'.($n2+2));
                        $interior_drawing_three->setOffsetX(10);
                        $interior_drawing_three->setOffsetY(10);
                        $interior_drawing_three->setWorksheet($phpexcel->getActiveSheet());
                        $phpexcel->getActiveSheet($n)->getStyle('J'.($n2+2).':K'.($n2+7))->applyFromArray($styleArray_border);
                    } else {
                        $phpexcel->getActiveSheet($n)->mergeCells('F'.$n2.':K'.($n2+7));
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2.':K'.($n2+7))->applyFromArray($styleArray_border);
                    }
                    if ($count_in_packaging > 0) {
                        $n2 += 8;
                    }
                }

                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(19.5);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('1. 货物的质量要与最终样品一致。若因质量/包装而遭受客户退货或索赔时,供方应负所有责任。');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_arial_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(19.5);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('2.请严格注意包装方式');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_arial_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(19.5);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('3. 所有货物必须按时交货 因逾期而产生的所有费用将由供方支付。');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_arial_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(19.5);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('4. 付款方式：月结。生效日：'.$purchaseContract['sign_date']);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_arial_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(19.5);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('5. 本合同一式两份, 签字即日起生效(传真有效)。');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_arial_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(19.5);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('6.定单确认后需出两套出货样，大货生产以确认的出货样为准。');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_arial_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(19.5);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('7.版权已登记备案，公司不得泄露产品给第三方，一经查实，按订单的实际金额10倍赔偿给甲方，另外追究乙方给甲方造成的其他一切损失及责任。');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_arial_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(19.5);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_arial_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(20);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('需方代表:');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_table_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->getFont()->setBold(true);
                $phpexcel->getActiveSheet($n)->mergeCells('B'.$n2.':E'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('B'.$n2)->setValue('董丽影');
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_table_left_default);
                $phpexcel->getActiveSheet($n)->mergeCells('F'.$n2.':G'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('供方代表:');
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_table_default);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->getFont()->setBold(true);
                $phpexcel->getActiveSheet($n)->mergeCells('H'.$n2.':K'.$n2);

                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2.':E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2.':G'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('H'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(25);
                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':E'.$n2);
                $phpexcel->getActiveSheet($n)->mergeCells('F'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2.':K'.$n2)->applyFromArray($styleArray_border);

                $title = $purchaseContract['contract_number']."产品购销合同";
                header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');//表明当前文件是.xlsx
                header('Content-Disposition: attachment;filename="'.$title.'.xlsx"');//文件名称
                header('Cache-Control: max-age=0'); //禁用缓存
                header('Cache-Control: max-age=1'); //通知浏览器：1 秒之内不要烦我，自己从缓冲区中刷新。
                header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // 缓存过期时间(禁用缓存)
                header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // 上一次修改时间
                header('Cache-Control: cache, must-revalidate'); // HTTP/1.1 缓存控制：必须重新验证
                header('Pragma: public'); // 可被任何缓存所缓存 http1.0协议
                $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($phpexcel, 'Xlsx');
                $objWriter->save('php://output');       //保存地址
                $this->deleteDir($folderPath);
                exit;
            }
        }
    }

    public function contractDocumentaryExport() {
        if (isset($this->request->get['purchase_contract_id'])) {
            $this->load->model('admin/purchase');
            $purchaseContract = $this->model_admin_purchase->getOnePurchaseContract($this->request->get['purchase_contract_id']);

            if (empty($purchaseContract['purchase_contract_id'])) {
                echo '数据不存在';exit();
            } else {
                $purchaseContract['specs'] = json_decode($purchaseContract['specs'],true);

                $this->load->helper('office/autoload');

                $styleArray_header = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  20,
                        'bold' => true, // 设置字体加粗
                    ]
                ];

                $styleArray_table_left_default = [
                    'alignment' =>  [
                        'horizontal' =>  'left',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                        'wrapText' => true, // 设置自动换行
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  12,
                    ]
                ];

                $styleArray_table_default = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                        'wrapText' => true, // 设置自动换行
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  12,
                    ]
                ];

                $styleArray_default2 = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  10,
                    ]
                ];

                $styleArray_default3 = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  14,
                    ]
                ];

                $styleArray_border = [
                    'borders' => [
                        'outline' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ];


                $default_line_height = 60;

                $phpexcel = new PhpOffice\PhpSpreadsheet\Spreadsheet();// 生成新的excel对象
                $phpexcel->getDefaultStyle()->getFont()->setName('宋体');    //设置默认字体
                $phpexcel->getDefaultStyle()->getFont()->setSize(11);        //设置默认字体大小
                $phpexcel->getActiveSheet()->getDefaultRowDimension()->setRowHeight(14.25); // 设置默认行高

                $n = 0;
                $n2 = 1;
                $phpexcel->createSheet();//创建sheet
                $phpexcel->setActiveSheetIndex($n);//设置当前的活动sheet
                $phpexcel->getActiveSheet($n)->setTitle($purchaseContract['contract_number']);//设置sheet的名称

                //设置列宽
                $phpexcel->getActiveSheet($n)->getColumnDimension('A')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('B')->setWidth(16);
                $phpexcel->getActiveSheet($n)->getColumnDimension('C')->setWidth(14);
                $phpexcel->getActiveSheet($n)->getColumnDimension('D')->setWidth(14);
                $phpexcel->getActiveSheet($n)->getColumnDimension('E')->setWidth(14);
                $phpexcel->getActiveSheet($n)->getColumnDimension('F')->setWidth(14);
                $phpexcel->getActiveSheet($n)->getColumnDimension('G')->setWidth(14);
                $phpexcel->getActiveSheet($n)->getColumnDimension('H')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('I')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('J')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('K')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('L')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('M')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('N')->setWidth(16);

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':N'.$n2);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_header);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue($purchaseContract['contract_number'].' '.date("m.d",strtotime($purchaseContract['delivery_date'])).'交货');
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(33.75);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('序号');
                $phpexcel->getActiveSheet($n)->getCell('B'.$n2)->setValue('图片');
                $phpexcel->getActiveSheet($n)->getCell('C'.$n2)->setValue('品名');
                $phpexcel->getActiveSheet($n)->getCell('D'.$n2)->setValue('条码');
                $phpexcel->getActiveSheet($n)->getCell('E'.$n2)->setValue('数量');
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('装箱数');
                $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue('重量');
                $phpexcel->getActiveSheet($n)->getCell('H'.$n2)->setValue('开模');
                $phpexcel->getActiveSheet($n)->getCell('I'.$n2)->setValue('灌浆');
                $phpexcel->getActiveSheet($n)->getCell('J'.$n2)->setValue('白胚');
                $phpexcel->getActiveSheet($n)->getCell('K'.$n2)->setValue('彩绘');
                $phpexcel->getActiveSheet($n)->getCell('L'.$n2)->setValue('包材');
                $phpexcel->getActiveSheet($n)->getCell('M'.$n2)->setValue('包装时间');
                $phpexcel->getActiveSheet($n)->getCell('N'.$n2)->setValue('包装');
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_table_default);
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('N'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('L'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('M'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('N'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(14.25);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('备注');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_default2);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->mergeCells('B'.$n2.':N'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('B'.$n2)->setValue('外箱规格不超过53cm*45cm*50cm,毛重不得超过15KG。');
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_default3);
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFF00');
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2.':N'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;

                $folderPath = DIR_DOWNLOAD.'/purchase_contract_image/'.$purchaseContract['purchase_contract_id'];
                if (file_exists($folderPath)) {
                    $this->deleteDir($folderPath );
                }
                mkdir($folderPath,0777,true);

                $no = 1;
                foreach ($purchaseContract['specs'] as $spec_key => $spec) {
                    if (isset($spec['carton_num']) && !empty($spec['num']) && !empty($spec['price'])) {

                        $destination = '';
                        if (!empty($spec['img_url'])  && $spec['img_url'] != 'null') {
                            $headers = @get_headers($spec['img_url'], 1); // 获取文件的HTTP头信息
                            if ($headers && $headers[0] == 'HTTP/1.1 200 OK') {
                                $content_type = $headers['Content-Type']; // 从HTTP头中获取Content-Type字段
                                $file_type = explode('/', $content_type)[1]; // 获取文件类型部分

                                $destination = $folderPath.'/'.$spec['spec_no'].'.'.$file_type;
                                $file_content = file_get_contents($spec['img_url']);
                                file_put_contents($destination, $file_content);
                            }
                        }

                        $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(84);

                        $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue($no);
                        if (!empty($spec['img_url'])  && $spec['img_url'] != 'null' && $headers && $headers[0] == 'HTTP/1.1 200 OK') {
                            $drawing[$spec_key] = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                            $drawing[$spec_key]->setName($spec['spec_no']);
                            $drawing[$spec_key]->setDescription($spec['spec_name']);
                            $drawing[$spec_key]->setPath($destination);
//                        $drawing[$spec_key]->setWidth(80);
                            $drawing[$spec_key]->setHeight(100);
                            $drawing[$spec_key]->setCoordinates('B'.$n2);
                            $drawing[$spec_key]->setOffsetX(6);
                            $drawing[$spec_key]->setOffsetY(6);
                            $drawing[$spec_key]->setWorksheet($phpexcel->getActiveSheet());
                        }

                        $phpexcel->getActiveSheet($n)->getCell('C'.$n2)->setValue($spec['spec_name']);
                        $phpexcel->getActiveSheet($n)->getCell('D'.$n2)->setValue($spec['spec_no']);
                        $phpexcel->getActiveSheet($n)->getCell('E'.$n2)->setValue($spec['num']);
                        $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue($spec['carton_num']);
                        $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue('不低于'.$spec['weight'].'克');
                        $phpexcel->getActiveSheet($n)->getCell('N'.$n2)->setValue($spec['packaging']);

                        $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('N'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('N'.$n2)->getFont()->getColor()->setRGB('FF0000');

                        $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('L'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('M'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('N'.$n2)->applyFromArray($styleArray_border);

                        if ($spec['accessories']) {
                            $phpexcel->getActiveSheet($n)->getCell('O'.$n2)->setValue($spec['accessories']);
                            $phpexcel->getActiveSheet($n)->getStyle('O'.$n2)->applyFromArray($styleArray_table_default);
                            $phpexcel->getActiveSheet($n)->getStyle('O'.$n2)->getFont()->getColor()->setRGB('FF0000');
                            $phpexcel->getActiveSheet($n)->getStyle('O'.$n2)->getFont()->setBold(true);
                            $phpexcel->getActiveSheet($n)->getStyle('O'.$n2)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFF00');
                        }
                        if ($spec['attention']) {
                            $phpexcel->getActiveSheet($n)->getCell('P'.$n2)->setValue($spec['attention']);
                            $phpexcel->getActiveSheet($n)->getStyle('P'.$n2)->applyFromArray($styleArray_table_default);
                            $phpexcel->getActiveSheet($n)->getStyle('P'.$n2)->getFont()->getColor()->setRGB('FF0000');
                        }

                        $n2 += 1;
                        $no += 1;
                    }
                }


                $title = $purchaseContract['contract_number']."跟单表";
                header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');//表明当前文件是.xlsx
                header('Content-Disposition: attachment;filename="'.$title.'.xlsx"');//文件名称
                header('Cache-Control: max-age=0'); //禁用缓存
                header('Cache-Control: max-age=1'); //通知浏览器：1 秒之内不要烦我，自己从缓冲区中刷新。
                header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // 缓存过期时间(禁用缓存)
                header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // 上一次修改时间
                header('Cache-Control: cache, must-revalidate'); // HTTP/1.1 缓存控制：必须重新验证
                header('Pragma: public'); // 可被任何缓存所缓存 http1.0协议
                $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($phpexcel, 'Xlsx');
                $objWriter->save('php://output');       //保存地址
                $this->deleteDir($folderPath);
                exit;
            }
        }
    }

    public function contractDocumentaryExportCopy() {
        if (isset($this->request->get['purchase_contract_id'])) {
            $this->load->model('admin/purchase');
            $purchaseContract = $this->model_admin_purchase->getOnePurchaseContract($this->request->get['purchase_contract_id']);

            if (empty($purchaseContract['purchase_contract_id'])) {
                echo '数据不存在';exit();
            } else {
                $purchaseContract['specs'] = json_decode($purchaseContract['specs'],true);

                $this->load->helper('office/autoload');

                $styleArray_header = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  24,
                        'bold' => true, // 设置字体加粗
                    ]
                ];

                $styleArray_table_left_default = [
                    'alignment' =>  [
                        'horizontal' =>  'left',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                        'wrapText' => true, // 设置自动换行
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  20,
                        'bold' => true, // 设置字体加粗
                    ]
                ];

                $styleArray_table_right_default = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                        'wrapText' => true, // 设置自动换行
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  20,
                        'bold' => true, // 设置字体加粗
                    ]
                ];

                $styleArray_table_default = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                        'wrapText' => true, // 设置自动换行
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  12,
                    ]
                ];

                $styleArray_default2 = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  10,
                    ]
                ];

                $styleArray_default3 = [
                    'alignment' =>  [
                        'horizontal' =>  'center',  // 水平居中
                        'vertical' =>  'center',    // 垂直居中
                    ],
                    'font' =>  [
                        'name' =>  '宋体',
                        'size' =>  14,
                    ]
                ];

                $styleArray_border = [
                    'borders' => [
                        'outline' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ];


                $default_line_height = 60;

                $phpexcel = new PhpOffice\PhpSpreadsheet\Spreadsheet();// 生成新的excel对象
                $phpexcel->getDefaultStyle()->getFont()->setName('宋体');    //设置默认字体
                $phpexcel->getDefaultStyle()->getFont()->setSize(11);        //设置默认字体大小
                $phpexcel->getActiveSheet()->getDefaultRowDimension()->setRowHeight(14.25); // 设置默认行高

                $n = 0;
                $n2 = 1;
                $phpexcel->createSheet();//创建sheet
                $phpexcel->setActiveSheetIndex($n);//设置当前的活动sheet
                $phpexcel->getActiveSheet($n)->setTitle($purchaseContract['contract_number']);//设置sheet的名称

                //设置列宽
                $phpexcel->getActiveSheet($n)->getColumnDimension('A')->setWidth(10);
                $phpexcel->getActiveSheet($n)->getColumnDimension('B')->setWidth(14.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('C')->setWidth(14.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('D')->setWidth(14.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('E')->setWidth(14.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('F')->setWidth(14.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('G')->setWidth(14.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('H')->setWidth(14.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('I')->setWidth(23.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('J')->setWidth(23.5);
                $phpexcel->getActiveSheet($n)->getColumnDimension('K')->setWidth(23.5);

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_header);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('合同号：'.$purchaseContract['contract_number']);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(33.75);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':H'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('工厂：'.$purchaseContract['provider_name']);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_table_left_default);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':H'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->mergeCells('I'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('I'.$n2)->setValue('交货时间：'.$purchaseContract['delivery_date']);
                $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_table_right_default);
                $phpexcel->getActiveSheet($n)->getStyle('I'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(33.75);

                $n2 += 1;

                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('序号');
                $phpexcel->getActiveSheet($n)->getCell('B'.$n2)->setValue('图片');
                $phpexcel->getActiveSheet($n)->getCell('C'.$n2)->setValue('条码');
                $phpexcel->getActiveSheet($n)->getCell('D'.$n2)->setValue('品名');
                $phpexcel->getActiveSheet($n)->getCell('E'.$n2)->setValue('尺寸');
                $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue('数量');
                $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue('装箱数');
                $phpexcel->getActiveSheet($n)->getCell('H'.$n2)->setValue('重量');
                $phpexcel->getActiveSheet($n)->getCell('I'.$n2)->setValue('包装');
                $phpexcel->getActiveSheet($n)->getCell('J'.$n2)->setValue('配件');
                $phpexcel->getActiveSheet($n)->getCell('K'.$n2)->setValue('注意事项');
                $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_table_default);
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(14.25);
                $n2 += 1;

                $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('备注');
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_default2);
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                $phpexcel->getActiveSheet($n)->mergeCells('B'.$n2.':K'.$n2);
                $phpexcel->getActiveSheet($n)->getCell('B'.$n2)->setValue('外箱规格不超过53cm*45cm*50cm,毛重不得超过15KG。');
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_default3);
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->getFont()->getColor()->setRGB('FF0000');
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFF00');
                $phpexcel->getActiveSheet($n)->getStyle('B'.$n2.':K'.$n2)->applyFromArray($styleArray_border);
                $n2 += 1;

                $folderPath = DIR_DOWNLOAD.'/purchase_contract_image/'.$purchaseContract['purchase_contract_id'];
                if (file_exists($folderPath)) {
                    $this->deleteDir($folderPath );
                }
                mkdir($folderPath,0777,true);

                $no = 1;
                foreach ($purchaseContract['specs'] as $spec_key => $spec) {
                    if (isset($spec['carton_num']) && !empty($spec['num']) && !empty($spec['price'])) {

                        $destination = '';
                        if (!empty($spec['img_url'])  && $spec['img_url'] != 'null') {
                            $headers = get_headers($spec['img_url'], 1); // 获取文件的HTTP头信息
                            if ($headers && $headers[0] == 'HTTP/1.1 200 OK') {
                                $content_type = $headers['Content-Type']; // 从HTTP头中获取Content-Type字段
                                $file_type = explode('/', $content_type)[1]; // 获取文件类型部分

                                $destination = $folderPath.'/'.$spec['spec_no'].'.'.$file_type;
                                $file_content = file_get_contents($spec['img_url']);
                                file_put_contents($destination, $file_content);
                            }
                        }

                        $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(84);

                        $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue($no);
                        if (!empty($spec['img_url'])  && $spec['img_url'] != 'null' && $headers && $headers[0] == 'HTTP/1.1 200 OK') {
                            $drawing[$spec_key] = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                            $drawing[$spec_key]->setName($spec['spec_no']);
                            $drawing[$spec_key]->setDescription($spec['spec_name']);
                            $drawing[$spec_key]->setPath($destination);
//                        $drawing[$spec_key]->setWidth(80);
                            $drawing[$spec_key]->setHeight(100);
                            $drawing[$spec_key]->setCoordinates('B'.$n2);
                            $drawing[$spec_key]->setOffsetX(10);
                            $drawing[$spec_key]->setOffsetY(6);
                            $drawing[$spec_key]->setWorksheet($phpexcel->getActiveSheet());
                        }

                        $phpexcel->getActiveSheet($n)->getCell('C'.$n2)->setValue($spec['spec_no']);
                        $phpexcel->getActiveSheet($n)->getCell('D'.$n2)->setValue($spec['spec_name']);
                        $phpexcel->getActiveSheet($n)->getCell('E'.$n2)->setValue($spec['size']);
                        $phpexcel->getActiveSheet($n)->getCell('F'.$n2)->setValue($spec['num']);
                        $phpexcel->getActiveSheet($n)->getCell('G'.$n2)->setValue($spec['carton_num']);
                        $phpexcel->getActiveSheet($n)->getCell('H'.$n2)->setValue('不低于'.$spec['weight'].'克');
                        $phpexcel->getActiveSheet($n)->getCell('I'.$n2)->setValue($spec['packaging']);

                        $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_table_default);
                        $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->getFont()->getColor()->setRGB('FF0000');

                        $phpexcel->getActiveSheet($n)->getStyle('A'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('B'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('C'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('D'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('E'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('F'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('G'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('H'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('I'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_border);
                        $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_border);

                        if ($spec['accessories']) {
                            $phpexcel->getActiveSheet($n)->getCell('J'.$n2)->setValue($spec['accessories']);
                            $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->applyFromArray($styleArray_table_default);
                            $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->getFont()->getColor()->setRGB('FF0000');
                            $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->getFont()->setBold(true);
                            $phpexcel->getActiveSheet($n)->getStyle('J'.$n2)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFF00');
                        }
                        if ($spec['attention']) {
                            $phpexcel->getActiveSheet($n)->getCell('K'.$n2)->setValue($spec['attention']);
                            $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->applyFromArray($styleArray_table_default);
                            $phpexcel->getActiveSheet($n)->getStyle('K'.$n2)->getFont()->getColor()->setRGB('FF0000');
                        }

                        $n2 += 1;
                        $no += 1;
                    }
                }

                $title = $purchaseContract['contract_number']."跟单表";
                header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');//表明当前文件是.xlsx
                header('Content-Disposition: attachment;filename="'.$title.'.xlsx"');//文件名称
                header('Cache-Control: max-age=0'); //禁用缓存
                header('Cache-Control: max-age=1'); //通知浏览器：1 秒之内不要烦我，自己从缓冲区中刷新。
                header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // 缓存过期时间(禁用缓存)
                header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // 上一次修改时间
                header('Cache-Control: cache, must-revalidate'); // HTTP/1.1 缓存控制：必须重新验证
                header('Pragma: public'); // 可被任何缓存所缓存 http1.0协议
                $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($phpexcel, 'Xlsx');
                $objWriter->save('php://output');       //保存地址
                exit;
            }
        }
    }

    public function pushList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_state'])) {
            $filter_state = $this->request->get['filter_state'];
        } else {
            $filter_state = '-1';
        }

        if (isset($this->request->get['filter_warehouse'])) {
            $filter_warehouse = $this->request->get['filter_warehouse'];
        } else {
            $filter_warehouse = '';
        }

        if (isset($this->request->get['filter_provider'])) {
            $filter_provider = $this->request->get['filter_provider'];
        } else {
            $filter_provider = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->load->model('admin/purchase');

        $data['warehouses'] = [];
        $warehouses = $this->model_admin_purchase->getPushWarehouseList();
        foreach ($warehouses as $v) {
            $data['warehouses'][$v['warehouse_no']] = $v['name'];
        }

        $data['providers'] = [];
        $providers = $this->model_admin_purchase->getPurchaseProviders();
        foreach ($providers as $v) {
            $data['providers'][$v['provider_no']] = $v['provider_name'];
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_warehouse' => $filter_warehouse,
            'filter_provider'  => $filter_provider,
            'filter_state'  => $filter_state,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $data['pushs'] = $this->model_admin_purchase->getPushs($filter_data);
        foreach ($data['pushs'] as $k => $v) {
            if (!empty($v['push_data'])) {
                $data['pushs'][$k]['push_data'] = json_decode($v['push_data'],true);
            }
        }

        $total = $this->model_admin_purchase->getTotalPushs($filter_data);

        $data['execute_detail'] = $this->model_admin_purchase->getCron();

        $data['push_insufficients'] = $this->model_admin_purchase->getPushInsufficients();

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_warehouse'])) {
            $url .= '&filter_warehouse=' . $this->request->get['filter_warehouse'];
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . $this->request->get['filter_provider'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/pushList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_warehouse'] = $filter_warehouse;
        $data['filter_provider'] = $filter_provider;
        $data['filter_state'] = $filter_state;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/purchase/pushList', 'token=' . $this->session->data['token']);
        $data['import'] = $this->url->link('admin/purchase/pushListImport', 'token=' . $this->session->data['token']);
        $data['execute'] = $this->url->link('admin/purchase/pushListExecute', 'token=' . $this->session->data['token']);
        $data['delPush'] = $this->url->link('admin/purchase/deletePush', 'token=' . $this->session->data['token']);
        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_warehouse'])) {
            $url .= '&filter_warehouse=' . $this->request->get['filter_warehouse'];
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . $this->request->get['filter_provider'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }
        $data['editPush'] = $this->url->link('admin/purchase/pushListEdit', 'token=' . $this->session->data['token'].$url);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/push_list.tpl', $data));
    }

    public function pushListEdit() {
        $this->load->model('admin/purchase');

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_state'])) {
            $url .= '&filter_state=' . $this->request->get['filter_state'];
        }

        if (isset($this->request->get['filter_warehouse'])) {
            $url .= '&filter_warehouse=' . $this->request->get['filter_warehouse'];
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . $this->request->get['filter_provider'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            if (!empty($this->request->post['bsku']) && !empty($this->request->post['quantity']) && !empty($this->request->post['push_date']) && !empty($this->request->post['warehouse_no']) && !empty($this->request->post['provider_no'])) {
                $this->model_admin_purchase->editPush($this->request->get['push_id'], $this->request->post);
            }
            $this->session->data['success'] = $this->language->get('text_edit_success');

            $this->response->redirect($this->url->link('admin/purchase/pushList', 'token=' . $this->session->data['token'] . $url));
        }

        $data['push'] = [];
        if (isset($this->request->get['push_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $data['push'] = $this->model_admin_purchase->getPush($this->request->get['push_id']);
        }

        if (empty($data['push'])) {
            $this->response->redirect($this->url->link('admin/purchase/pushList', 'token=' . $this->session->data['token'] . $url));
        }

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $data['warehouses'] = $this->model_admin_purchase->getPushWarehouseList();
        $data['providers'] = $this->model_admin_purchase->getPurchaseProviders();

        $data['action'] = $this->url->link('admin/purchase/pushListEdit', 'token=' . $this->session->data['token'] . '&push_id=' . $this->request->get['push_id'] . $url);
        $data['cancel'] = $this->url->link('admin/purchase/pushList', 'token=' . $this->session->data['token'] . $url);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/push_form.tpl', $data));
    }

    public function deletePush() {
        $this->load->model('admin/purchase');

        if (isset($this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $push_id) {
                $this->model_admin_purchase->deletePush($push_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/purchase/pushList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->pushList();
    }

    public function pushListExecute() {
        $this->load->model('admin/purchase');
        $this->model_admin_purchase->setCron();

        $json['status'] = 1;
        $this->response->setOutJson($json);
    }

    public function pushListImport() {
        $data['action'] = $this->url->link('admin/purchase/pushImport', 'token=' . $this->session->data['token']);
        $data['template'] = $this->url->link('admin/purchase/pushTemplate', 'token=' . $this->session->data['token']);
        $data['date'] = date('Y-m-d',time());
        $this->load->model('admin/purchase');
        $data['warehouses'] = $this->model_admin_purchase->getPushWarehouseList();
        $data['providers'] = $this->model_admin_purchase->getPurchaseProviders();

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/push_import.tpl', $data));
    }

    public function pushImport() {
        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            if (isset($_FILES['file']) && $_FILES['file']['error'] == 0 && !empty($this->request->post['push_date']) && !empty($this->request->post['warehouse_no']) && !empty($this->request->post['provider_no'])) {
                $this->load->helper('office/autoload');

                $excelPath = $_FILES['file']['tmp_name']; // 获取上传文件的临时路径

                try {
                    //识别文件类型
                    $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($excelPath);
                    //创建读取器
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
                    $reader->setReadDataOnly(true); // 只读数据，不读样式
                    //加载文件
                    $spreadsheet = $reader->load($excelPath);
                    //获取第一个工作表
                    $objWorksheet = $spreadsheet->getSheet(0);
                    //获取所有数据
                    $data = $objWorksheet->toArray();
                } catch (\PhpOffice\PhpSpreadsheet\Reader\Exception $e) {
                    die('读取文件出错: '.$e->getMessage());
                } catch (Exception $e) {
                    die('发生错误: '.$e->getMessage());
                }

                array_shift($data);
                array_shift($data);

                $this->load->model('admin/purchase');
                $datetime = date('Y-m-d H:i:s',time());
                $add_num = 0;
                foreach ($data as $v) {
                    if (!empty($v[1]) && !empty($v[6])) {
                        $add_data = [
                            'user_id' => (int)$this->user->user_id,
                            'push_date' => $this->request->post['push_date'],
                            'warehouse_no' => $this->request->post['warehouse_no'],
                            'provider_no' => $this->request->post['provider_no'],
                            'bsku' => $v[1],
                            'spec_name' => !empty($v[2]) ? $v[2] : '',
                            'PSC' => !empty($v[3]) ? $v[3] : '',
                            'carton_numbers' => !empty($v[4]) ? $v[4] : '',
                            'mantissa' => !empty($v[5]) ? $v[5] : '',
                            'quantity' => $v[6],
                            'remark' => !empty($v[0]) ? $v[0] : '',
                            'status' => 0,
                            'push_data' => '',
                            'date_added' => $datetime,
                            'date_modified' => $datetime,
                        ];
                        $push_id = $this->model_admin_purchase->addPush($add_data);
                        if (!empty($push_id)) $add_num += 1;
                    }
                }
                $json['success'] = '成功导入'.$add_num.'条数据';
            } else {
                $json['error'] = '导入失败';
            }
        }
        $this->response->setOutJson($json);
    }

    public function pushTemplate() {
        $file = DIR_DOWNLOAD . '导入采购入库模板.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }


    public function antifakeList() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_provider'])) {
            $filter_provider = $this->request->get['filter_provider'];
        } else {
            $filter_provider = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->load->model('admin/purchase');

        $data['providers'] = [];
        $providers = $this->model_admin_purchase->getPurchaseProviders();
        foreach ($providers as $v) {
            $data['providers'][$v['provider_no']] = $v['provider_name'];
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_provider'  => $filter_provider,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $data['antifakes'] = $this->model_admin_purchase->getAntifakes($filter_data);

        $total = $this->model_admin_purchase->getTotalAntifakes($filter_data);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . $this->request->get['filter_provider'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/antifakeList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_provider'] = $filter_provider;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/purchase/antifakeList', 'token=' . $this->session->data['token']);
        $data['allocation'] = $this->url->link('admin/purchase/antifakeAllocation', 'token=' . $this->session->data['token']);
        $data['delAntifake'] = $this->url->link('admin/purchase/deleteAntifake', 'token=' . $this->session->data['token']);

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . $this->request->get['filter_provider'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        $data['add'] = $this->url->link('admin/purchase/antifakeAdd', 'token=' . $this->session->data['token'].$url);
        $data['edit'] = $this->url->link('admin/purchase/antifakeEdit', 'token=' . $this->session->data['token'].$url);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/antifake_list.tpl', $data));
    }

    public function antifakeAllocation() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_provider'])) {
            $filter_provider = $this->request->get['filter_provider'];
        } else {
            $filter_provider = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->load->model('admin/purchase');

        $data['providers'] = [];
        $providers = $this->model_admin_purchase->getPurchaseProviders();
        foreach ($providers as $v) {
            $data['providers'][$v['provider_no']] = $v['provider_name'];
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_provider'  => $filter_provider,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $data['antifakes'] = $this->model_admin_purchase->getAntifakeAllocation($filter_data);

        $total = $this->model_admin_purchase->getTotalAntifakeAllocation($filter_data);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . $this->request->get['filter_provider'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/purchase/antifakeAllocation', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_provider'] = $filter_provider;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/purchase/antifakeAllocation', 'token=' . $this->session->data['token']);

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . $this->request->get['filter_provider'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/antifake_allocation_list.tpl', $data));
    }

    public function antifakeAdd() {
        $this->load->model('admin/purchase');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateAntifakeForm()) {
            $this->model_admin_purchase->addAntifake($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_provider'])) {
                $url .= '&filter_provider=' . $this->request->get['filter_provider'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            $this->response->redirect($this->url->link('admin/purchase/antifakeList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getAntifakeForm();
    }

    public function antifakeEdit() {
        $this->load->model('admin/purchase');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateAntifakeForm()) {
            $this->model_admin_purchase->editAntifake($this->request->get['antifake_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_provider'])) {
                $url .= '&filter_provider=' . $this->request->get['filter_provider'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            $this->response->redirect($this->url->link('admin/purchase/antifakeList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getAntifakeForm();
    }

    protected function getAntifakeForm() {
        $data['text_form'] = !isset($this->request->get['antifake_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . $this->request->get['filter_provider'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (!isset($this->request->get['antifake_id'])) {
            $data['action'] = $this->url->link('admin/purchase/antifakeAdd', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/purchase/antifakeEdit', 'token=' . $this->session->data['token'] . '&antifake_id=' . $this->request->get['antifake_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/purchase/antifakeList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['antifake_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $antifake_info = $this->model_admin_purchase->getAntifake($this->request->get['antifake_id']);
        }

        if (isset($this->request->post['provider_no'])) {
            $data['provider_no'] = $this->request->post['provider_no'];
        } elseif (!empty($antifake_info)) {
            $data['provider_no'] = $antifake_info['provider_no'];
        } else {
            $data['provider_no'] = '';
        }

        if (isset($this->request->post['prefix'])) {
            $data['prefix'] = $this->request->post['prefix'];
        } elseif (!empty($antifake_info)) {
            $data['prefix'] = $antifake_info['prefix'];
        } else {
            $data['prefix'] = 'RG';
        }

        if (isset($this->request->post['digit'])) {
            $data['digit'] = $this->request->post['digit'];
        } elseif (!empty($antifake_info)) {
            $data['digit'] = $antifake_info['digit'];
        } else {
            $data['digit'] = '8';
        }

        if (isset($this->request->post['start_num'])) {
            $data['start_num'] = $this->request->post['start_num'];
        } elseif (!empty($antifake_info)) {
            $data['start_num'] = $antifake_info['start_num'];
        } else {
            $data['start_num'] = '';
        }

        if (isset($this->request->post['end_num'])) {
            $data['end_num'] = $this->request->post['end_num'];
        } elseif (!empty($antifake_info)) {
            $data['end_num'] = $antifake_info['end_num'];
        } else {
            $data['end_num'] = '';
        }

        $data['providers'] = $this->model_admin_purchase->getPurchaseProviders();

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('purchase/antifake_form.tpl', $data));
    }

    public function deleteAntifake() {
        $this->load->model('admin/purchase');

        if (isset($this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $antifake_id) {
                $this->model_admin_purchase->deleteAntifake($antifake_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/purchase/antifakeList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getList();
    }

    protected function validateAntifakeForm() {
        if (!$this->user->hasPermission('modify', 'admin/purchase')) {
            $this->error['warning'] = $this->language->get('error_permission');
            return false;
        }

        if (empty($this->request->post['prefix'])) {
            $this->error['warning'] = '前缀不能为空！';
            return false;
        }

        if (!preg_match('/^[a-zA-Z]+$/', $this->request->post['prefix'])) {
            $this->error['warning'] = '前缀格式错误！';
            return false;
        }

        if (empty($this->request->post['digit'])) {
            $this->error['warning'] = '位数不能为空！';
            return false;
        }

        if (empty($this->request->post['start_num'])) {
            $this->error['warning'] = '开始值不能为空！';
            return false;
        }

        if (empty($this->request->post['end_num'])) {
            $this->error['warning'] = '结束值不能为空！';
            return false;
        }

        if ($this->request->post['start_num'] > $this->request->post['end_num']) {
            $this->error['warning'] = '结束值不能小于开始值！';
            return false;
        }

        if (strlen($this->request->post['prefix'].$this->request->post['end_num']) > $this->request->post['digit']) {
            $this->error['warning'] = '位数错误！';
            return false;
        }

        $filter_data = array(
            'filter_prefix'    => $this->request->post['prefix'],
            'filter_digit'     => $this->request->post['digit'],
            'filter_start_num' => $this->request->post['start_num'],
            'filter_end_num'   => $this->request->post['end_num'],
        );
        if (!empty($this->request->get['antifake_id'])) {
            $filter_data['filter_antifake_id'] = $this->request->get['antifake_id'];
        }
        $antifake =  $this->model_admin_purchase->getAntifakes($filter_data);
        if (!empty($antifake)) {
            $this->error['warning'] = '范围重复！';
            return false;
        }

        return !$this->error;
    }

    protected function validateForm2() {
        if (!$this->user->hasPermission('modify', 'admin/purchase')) {
            $this->error['warning'] = $this->language->get('error_permission');
            return false;
        }

        if (empty($this->request->post['contract_number'])) {
            $this->error['warning'] = '合同号不能为空！';
            return false;
        }

//        if (empty($this->request->post['warehouse_date'])) {
//            $this->error['warning'] = '入仓时间不能为空！';
//            return false;
//        }

        if (empty($this->request->post['sign_date'])) {
            $this->error['warning'] = '签订时间不能为空！';
            return false;
        }

        if (empty($this->request->post['delivery_date'])) {
            $this->error['warning'] = '交货时间不能为空！';
            return false;
        }

        if (empty($this->request->post['specs'])) {
            $this->error['warning'] = '产品不能为空！';
            return false;
        }

        if (empty($this->request->post['provider_no'])) {
            $this->error['warning'] = '供应商不能为空！';
            return false;
        }

//        if (empty($this->request->post['carton_mark'])) {
//            $this->error['warning'] = '外箱唛不能为空！';
//            return false;
//        }

        return !$this->error;
    }

    function deleteDir($dirPath) {
        if (!is_dir($dirPath)) {
            throw new InvalidArgumentException("$dirPath must be a directory");
        }
        if (substr($dirPath, strlen($dirPath) - 1, 1) != '/') {
            $dirPath .= '/';
        }
        $files = glob($dirPath . '*', GLOB_MARK);
        foreach ($files as $file) {
            if (is_dir($file)) {
                $this->deleteDir($file);
            } else {
                unlink($file);
            }
        }
        rmdir($dirPath);
    }

}