<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        新品备货
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?> </div>
      <?php } ?>    
      <div class="box box-primary">
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-order" class="form-horizontal">
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-goods">下单新品：</label>
              <div class="col-sm-8">
                <input type="hidden" name="order_no" value="新品下单<?php echo date('YmdH'); ?>">
                <input type="hidden" name="complete_date" value="<?php echo $complete_date; ?>">
                <select name="goods_id" id="input-goods" class="form-control" onchange="javascript: location.href = '<?php echo $action; ?>&goods_id=' + this.value;">
                  <option value="" selected="selected">请选择新品</option>
                  <?php foreach($goods as $goods) { ?>
                  <?php if ($goods['goods_id'] == $goods_id) { ?>
                  <option value="<?php echo $goods['goods_id']; ?>" selected="selected"><?php echo $goods['goods_no'] . $goods['goods_name']; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $goods['goods_id']; ?>"><?php echo $goods['goods_no'] . $goods['goods_name']; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>
            <?php if (!empty($specs)) { ?>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-quan">下单数量：</label>
              <div class="col-sm-8">
                <?php foreach($specs as $spec) { ?>
                <div class="input-group">
                  <span class="input-group-addon"><?php echo $spec['spec_no']; ?><?php echo $spec['spec_name']; ?></span>
                  <input type="number" name="order_quan[<?php echo $spec['spec_no']; ?>]" data-sku="<?php echo $spec['spec_no']; ?>" value="" placeholder="请输入下单数量" class="form-control" />
                </div>
                <?php } ?>
              </div>
            </div>
            <div class="form-group required">
              <label class="col-sm-2 control-label">店铺类目：</label>
              <div class="col-sm-4">
                <div class="well well-sm" style="height: 150px; overflow: auto;">
                  <?php foreach($classes as $class) { ?>
                  <div class="radio">
                    <label>
                      <input type="radio" name="classes" value="<?php echo $class; ?>">
                      <?php echo $class; ?>
                    </label>
                  </div>
                  <?php } ?>
                  <div class="radio">
                    <label><input type="radio" name="classes" value="专款"> 专款产品</label>
                  </div>
                </div>
              </div>
              <div class="col-sm-4">
                <div class="well well-sm" style="height: 150px; overflow: auto;">
                <?php foreach($stores as $store) { ?>
                <?php if (!empty($store['class_name']) && strpos($store['class_name'], '不备新品') === false) { ?>
                <div class="checkbox">
                  <label>
                    <input type="checkbox" name="stores" data-name="<?php echo $store['name']; ?>" data-class="<?php echo $store['class_name']; ?>" value="<?php echo $store['store_id']; ?>">
                    <?php echo $store['name']; ?>
                  </label>
                </div>
                <?php } ?>
                <?php } ?>
                </div>
              </div>
            </div>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-store">新品列表：</label>
              <div class="col-sm-8">
                <table id="newplan" class="table table-striped table-bordered table-hover">
                  <thead>
                    <tr>
                      <th class="text-left">备货店铺</td>
                      <th class="text-right">备货数量</td>
                    </tr>
                  </thead>
                  <tbody>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交下单</button>
                <a href="<?php echo $getNewGoods; ?>" class="btn btn-danger">更新旺店通新品</a>
              </div>
            </div>
            <?php } else { ?>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <a href="<?php echo $getNewGoods; ?>" class="btn btn-danger">更新旺店通新品</a>
              </div>
            </div>
            <?php } ?>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript"><!--
var specs = {};
<?php foreach($specs as $spec) { ?>
specs.<?php echo $spec['spec_no']; ?> = {name: '<?php echo $spec['spec_name']; ?>', quan: 0};
<?php } ?>
$('input[name^="order_quan"]').on('change', function () {
  var sku = $(this).data('sku');
  if (specs[sku]) {
    if (Number.isNaN(parseInt($(this).val()))) {
      specs[sku].quan = 0;
    } else {
      specs[sku].quan = parseInt($(this).val());
    }
  }
  // $('input[name="stores"]').trigger('change');
});
$('input[name="classes"]').on('change', function () {
  var class_name = $(this).val();
  $('input[name="stores"]').each(function() {
    if (($(this).data('class') == '综合' && class_name != '专款') || ($(this).data('class').indexOf(class_name) >= 0)) {
      $(this).prop('checked', true);
    } else {
      $(this).prop('checked', false);
    }
  });
  $('input[name="stores"]').trigger('change');
});
$('input[name="stores"]').on('change', function () {
  var html = '', length = $('input[name="stores"]:checked').length;
  $('input[name="stores"]:checked').each(function() {
    // $(this).is(":checked")
    var store_id = $(this).val();
    html += '<tr>';
    html += '  <td class="text-left">' + $(this).data('name') + '</td><td class="text-right">';
    $.each(specs, function(i,v) {
      html += '<div class="input-group"><span class="input-group-addon">' + i + v.name + '</span><input type="number" name="plan_quan[' + i + '][' + store_id + ']" value="' + Math.floor(v.quan / length) + '" placeholder="请输入下单数量" class="form-control" /></div>';
    });
    html += '</td></tr>';
  });

  $('#newplan tbody').html(html);
});
//--></script>
<?php echo $footer; ?>