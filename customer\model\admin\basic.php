<?php
class ModelAdminBasic extends Model {
    public function getWdtSpecs($data) {
        $sql = "SELECT spec_no, spec_name, img_url, wholesale_price, custom_price1, custom_price2, market_price, weight, prop1, prop2, prop3, prop5 FROM wdt_spec_list WHERE deleted = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(created) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(created) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " ORDER BY goods_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalSpecs($data) {
        $sql = "SELECT COUNT(*) AS total FROM wdt_spec_list WHERE deleted = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(spec_no, spec_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(created) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(created) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function addAuth($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "customer_auth SET store_id = '" . (int)$data['store_id'] . "', user_id = '" . (int)$this->user->user_id . "', cardid = '" . $this->db->escape($data['cardid']) . "', realname = '" . $this->db->escape($data['realname']) . "', telephone = '" . $this->db->escape($data['telephone']) . "', wholesale = '" . (int)$data['wholesale'] . "', date_added = NOW()");
    }

    public function getAuths($data) {
        $sql = "SELECT * FROM " . DB_PREFIX . "customer_auth WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(cardid, realname, telephone) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " ORDER BY customer_auth_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalAuths($data) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_auth WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(cardid, realname, telephone) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function addInfo($data) {
        if (!empty($data['category'])) {
            $data['category'] = implode(',',$data['category']);
        } else {
            $data['category'] = '';
        }
        if (!empty($data['store_ids'])) {
            $data['store_ids'] = implode(',',$data['store_ids']);
        } else {
            $data['store_ids'] = '';
        }
        $this->db->query("INSERT INTO " . DB_PREFIX . "design_info SET user_id = '" . (int)$this->user->user_id . "', design_name = '" . $this->db->escape($data['design_name']) . "', design_image = '" . $this->db->escape($data['design_image']) . "', designer = '" . $this->db->escape($data['designer']) . "', planner = '', category = '" . $this->db->escape($data['category']) . "', store_ids = '" . $this->db->escape($data['store_ids']) . "', type = '" . $this->db->escape($data['type']) . "', design_date = '" . $this->db->escape($data['design_date']) . "', goods_no = '" . $this->db->escape($data['goods_no']) . "', vote = '" . (int)$data['vote'] . "', texture = '" . (int)$data['texture'] . "', remark = '" . $this->db->escape($data['remark']) . "', status = '1', date_added = NOW(), date_modified = NOW()");
    }

    public function editInfo($design_id, $data) {
        if (!empty($data['category'])) {
            $data['category'] = implode(',',$data['category']);
        } else {
            $data['category'] = '';
        }
        if (!empty($data['store_ids'])) {
            $store_ids = implode(',', $data['store_ids']);
        } else {
            $store_ids = '';
        }

        $info = $this->getInfo($design_id);
        $upstr = "";
        if ($info['goods_no'] != $data['goods_no']) {
            $data['warehousing_date'] = '';
            $data['reorder_date'] = '';
            $data['reorder_day'] = null;

            $upstr = ", warehousing_date = '" . $this->db->escape($data['warehousing_date']) . "', reorder_date = '" . $this->db->escape($data['reorder_date']) . "', reorder_day = '" . $this->db->escape($data['reorder_day']) . "'";
        }

        $this->db->query("UPDATE " . DB_PREFIX . "design_info SET design_name = '" . $this->db->escape($data['design_name']) . "', design_image = '" . $this->db->escape($data['design_image']) . "', designer = '" . $this->db->escape($data['designer']) . "', category = '" . $this->db->escape($data['category']) . "', store_ids = '" . $this->db->escape($store_ids) . "', type = '" . $this->db->escape($data['type']) . "', design_date = '" . $this->db->escape($data['design_date']) . "', goods_no = '" . $this->db->escape($data['goods_no']) . "', vote = '" . (int)$data['vote'] . "', texture = '" . (int)$data['texture'] . "', remark = '" . $this->db->escape($data['remark']) . "'" . $upstr . ", date_modified = NOW() WHERE design_id = '" . (int)$design_id . "'");
    }

    public function deleteInfo($design_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "design_info SET status = '-1' WHERE design_id = '" . (int)$design_id . "' AND user_id = '" . (int)$this->user->user_id . "'");
    }

    public function getInfo($design_id) {
        $sql = "SELECT * FROM " . DB_PREFIX . "design_info WHERE status = '1' AND design_id = ' " . (int)$design_id . " '";

        $query = $this->db->query($sql);

        return $query->row;
    }

    public function getInfos($data) {
        $sql = "SELECT * FROM " . DB_PREFIX . "design_info WHERE status = '1'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(design_name, designer, planner) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_category'])) {
            $sql .= " AND category = '" . $this->db->escape($data['filter_category']) . "'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND FIND_IN_SET('" . (int)$data['filter_store'] . "', store_ids)";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " ORDER BY design_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalInfos($data) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "design_info WHERE status = '1'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(design_name, designer, planner) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_category'])) {
            $sql .= " AND category = '" . $this->db->escape($data['filter_category']) . "'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND FIND_IN_SET('" . (int)$data['filter_store'] . "', store_ids)";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    //查询快递模板
    public function getExpressFeeTemplate() {
        $query = $this->db->query("SELECT * FROM cr_express_fee_template WHERE name like '%24部%'");

        return $query->rows;
    }

    //查询符合条件的快递报价
    public function getExpressFeeStandard($express_fee_template_id,$s_day,$e_day) {
        $query = $this->db->query("SELECT * FROM cr_express_fee_standard WHERE name=".$express_fee_template_id." AND s_day<=".$s_day." AND e_day>=".$e_day);

        return $query->row;
    }

    //保存快递试算
    public function addFreight($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "freight SET order_no = '" . $this->db->escape($data['order_no']) . "',order_address = '" . $this->db->escape($data['order_address']) . "', status = '" . (int)$data['status'] . "', province_id = '" . (int)$data['province_id'] . "', template = '" . $this->db->escape(json_encode($data['template'],320)) . "', res = '" . $this->db->escape(json_encode($data['res'],320)) . "', res_sum = '" . $this->db->escape(json_encode($data['res_sum'],320)) . "', date_added = NOW()");
        $freight_id = $this->db->getLastId();

        return $freight_id;
    }

    //修改快递试算
    public function setFreight($data,$freight_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "freight SET order_no = '" . $this->db->escape($data['order_no']) . "',order_address = '" . $this->db->escape($data['order_address']) . "', status = '" . (int)$data['status'] . "', province_id = '" . (int)$data['province_id'] . "', template = '" . $this->db->escape(json_encode($data['template'],320)) . "', res = '" . $this->db->escape(json_encode($data['res'],320)) . "', res_sum = '" . $this->db->escape(json_encode($data['res_sum'],320)) . "' WHERE freight_id = '" . (int)$freight_id . "'");
        $freight_id = $this->db->getLastId();

        return $freight_id;
    }

    public function getFreights($data) {
        $sql = "SELECT * FROM " . DB_PREFIX . "freight WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND order_no LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_status']) && $data['filter_status'] != '') {
            $sql .= " AND status = '" . $this->db->escape($data['filter_status']) . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND date_added >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND date_added <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $sql .= " ORDER BY freight_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalFreights($data) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "freight WHERE 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND order_no LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_status']) && $data['filter_status'] != '') {
            $sql .= " AND status = '" . $this->db->escape($data['filter_status']) . "'";
        }

        if (!empty($data['filter_start'])) {
            $sql .= " AND date_added >= '" . $this->db->escape($data['filter_start']) . "'";
        }

        if (!empty($data['filter_end'])) {
            $sql .= " AND date_added <= '" . $this->db->escape($data['filter_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getFreight($freight_id) {
        $sql = "SELECT * FROM " . DB_PREFIX . "freight WHERE freight_id=".(int)$freight_id;

        $query = $this->db->query($sql);

        return $query->row;
    }

    //确认快递试算
    public function setFreightAffirm($freight_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "freight SET status = '2' WHERE freight_id = '" . (int)$freight_id . "' AND status = '1'");
    }

    public function getTypes() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "setting WHERE `key` = 'design_type'");

        return $query->row;
    }

    public function setTypes($permissions) {
        $this->db->query("UPDATE " . DB_PREFIX . "setting SET `value` = '" . $this->db->escape(json_encode($permissions,320)) . "' WHERE `key` = 'design_type'");
    }

    public function getStylists() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "setting WHERE `key` = 'design_stylist'");

        return $query->row;
    }

    public function setStylists($permissions) {
        $this->db->query("UPDATE " . DB_PREFIX . "setting SET `value` = '" . $this->db->escape(json_encode($permissions,320)) . "' WHERE `key` = 'design_stylist'");
    }

    public function getTextures() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "setting WHERE `key` = 'design_texture'");

        return $query->row;
    }

    public function setTextures($permissions) {
        $this->db->query("UPDATE " . DB_PREFIX . "setting SET `value` = '" . $this->db->escape(json_encode($permissions,320)) . "' WHERE `key` = 'design_texture'");
    }

    public function votes() {
        return [
            1 => '通过',
            2 => '不通过',
            3 => '复活',
            4 => '定制',
        ];
    }

    public function getWarehousing($goods_no,$design_id) {
        $sql = "SELECT 
                  MIN(push_date) AS first_date,
                  (SELECT push_date 
                   FROM cr_purchase_push 
                   WHERE bsku LIKE CONVERT('".$this->db->escape($goods_no)."%' USING utf8mb4) COLLATE utf8mb4_0900_ai_ci AND status = 1
                   ORDER BY push_date 
                   LIMIT 1 OFFSET 1) AS second_date,
                  DATEDIFF(
                    (SELECT push_date 
                     FROM cr_purchase_push 
                     WHERE bsku LIKE CONVERT('".$this->db->escape($goods_no)."%' USING utf8mb4) COLLATE utf8mb4_0900_ai_ci AND status = 1
                     ORDER BY push_date 
                     LIMIT 1 OFFSET 1),
                    MIN(push_date)
                  ) AS date_diff
                FROM cr_purchase_push
                WHERE bsku LIKE CONVERT('".$this->db->escape($goods_no)."%' USING utf8mb4) COLLATE utf8mb4_0900_ai_ci AND status = 1";

        $query = $this->db->query($sql);
        $up_sql = '';
        if (!empty($query->row['first_date'])) {
            $up_sql .= " `warehousing_date` = '" . $this->db->escape($query->row['first_date']) . "'";
        }
        if (!empty($query->row['second_date'])) {
            $up_sql .= " ,`reorder_date` = '" . $this->db->escape($query->row['second_date']) . "'";
        }
        if (!empty($query->row['date_diff'])) {
            $up_sql .= " ,`reorder_day` = '" . $this->db->escape($query->row['date_diff']) . "'";
        }

        if (!empty($up_sql)) {
            $this->db->query("UPDATE " . DB_PREFIX . "design_info SET ".$up_sql." WHERE `design_id` = '" . (int)$design_id . "'");

            return $query->row;
        }
    }

}