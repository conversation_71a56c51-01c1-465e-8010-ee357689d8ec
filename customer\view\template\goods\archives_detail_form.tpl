<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        详情
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } else { ?>

      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $record_action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
          <div class="nav-tabs-custom">
            <ul class="nav nav-tabs">
              <li><a href="#tab-general" data-toggle="tab">基本信息</a></li>
              <li><a href="#tab-specs" data-toggle="tab">规格明细</a></li>
              <li><a href="#tab-specs-copyright" data-toggle="tab">版权证书</a></li>
              <li><a href="#tab-specs-custom" data-toggle="tab">自定义内容</a></li>
              <li><a href="#tab-specs-tag" data-toggle="tab">标签</a></li>
            </ul>
            <div class="tab-content">
              <div class="tab-pane" id="tab-general">
                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">货品ID：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['goods_id']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">货品编号：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['goods_no']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">货品名称：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['goods_name']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">简称：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['short_name']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">货品别名：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['alias']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">货品类别：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label">
                      <?php if($goods['goods_type']==0){ ?>其它<?php } ?>
                      <?php if($goods['goods_type']==1){ ?>销售货品<?php } ?>
                      <?php if($goods['goods_type']==2){ ?>原材料<?php } ?>
                      <?php if($goods['goods_type']==3){ ?>包装物<?php } ?>
                      <?php if($goods['goods_type']==4){ ?>周转材料<?php } ?>
                      <?php if($goods['goods_type']==5){ ?>虚拟商品<?php } ?>
                      <?php if($goods['goods_type']==6){ ?>固定资产<?php } ?>
                      <?php if($goods['goods_type']==7){ ?>保修配件<?php } ?>
                    </label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">规格数：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['spec_count']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">拼音：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['pinyin']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">品牌编号：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['brand_no']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">品牌：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['brand_name']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">备注：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['remark']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">自定义属性1：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['prop1']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">自定义属性2：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['prop2']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">自定义属性3：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['prop3']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">自定义属性4：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['prop4']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">自定义属性5：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['prop5']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">自定义属性6：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['prop6']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">产地：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['origin']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">分类id：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['class_id']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">分类：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['class_name']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">品牌id：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['brand_id']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">基本单位id：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['unit']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">辅助单位id：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['aux_unit']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">标记：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['flag_id']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">属性：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['properties']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">版本号，用来检查同时修改的：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['version_id']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">最后修改时间：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['modified']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">创建时间：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['created']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">基本单位：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['unit_name']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">辅助单位：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['aux_unit_name']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">标记名称：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['flag_name']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">创建时间：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['goods_created']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">最后修改时间：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label"><?php echo $goods['goods_modified']; ?></label>
                  </div>
                </div>

                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-nickname">是否已删除：</label>
                  <div class="col-sm-8">
                    <label style="text-align:left;width:100%" class="col-sm-2 control-label">
                      <?php if($goods['deleted']==0){ ?>未删除<?php }else{ ?>已删除<?php } ?>
                    </label>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-packaging">包装方式：</label>
                  <div class="col-sm-8">
                    <input type="text" name="packaging" list="packaginglist" value="<?php if(!empty($packaging)){ ?><?php echo $packaging['value']; ?><?php } ?>" placeholder="请输入包装方式" id="input-packaging" class="form-control" />
                    <datalist id="packaginglist">
                      <?php foreach($packaging_list as $packaging){ ?>
                      <option><?php echo $packaging; ?></option>
                      <?php } ?>
                    </datalist>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-accessories">配件：</label>
                  <div class="col-sm-8">
                    <input type="text" name="accessories" list="accessorieslist" value="<?php if(!empty($accessories)){ ?><?php echo $accessories['value']; ?><?php } ?>" placeholder="请输入配件" id="input-accessories" class="form-control" />
                    <datalist id="accessorieslist">
                      <?php foreach($accessories_list as $accessories){ ?>
                      <option><?php echo $accessories; ?></option>
                      <?php } ?>
                    </datalist>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-attention">注意事项：</label>
                  <div class="col-sm-8">
                    <input type="text" name="attention" list="attentionlist" value="<?php if(!empty($attention)){ ?><?php echo $attention['value']; ?><?php } ?>" placeholder="请输入注意事项" id="input-attention" class="form-control" />
                    <datalist id="attentionlist">
                      <?php foreach($attention_list as $attention){ ?>
                      <option><?php echo $attention; ?></option>
                      <?php } ?>
                    </datalist>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-weight">重量（净重）：</label>
                  <div class="col-sm-8">
                    <input type="text" name="weight"  value="<?php if(!empty($weight)){ ?><?php echo $weight['value']; ?><?php } ?>" placeholder="请输入重量（净重）" id="input-weight" class="form-control" />
                  </div>
                </div>

                <div class="form-group">
                  <div class="col-sm-offset-2 col-sm-8">
                    <button class="btn btn-primary" type="submit">提交保存</button>
                    <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
                  </div>
                </div>


              </div>

              <div class="tab-pane" id="tab-specs">
                <div id="specs"></div>
                <br />
              </div>

              <div class="tab-pane" id="tab-specs-copyright">
                <div id="specs-copyright"></div>
                <br />
              </div>

              <div class="tab-pane" id="tab-specs-custom">
                <div id="specs-custom"></div>
                <br />
              </div>

              <div class="tab-pane" id="tab-specs-tag">
                <div id="specs-tag"></div>
                <br />
              </div>

            </div>
          </div>

          </form>
        </div>
        <!-- /.box-body -->
      </div>
      <?php } ?>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script src="<?php echo HTTP_SERVER; ?>static/js/echarts.min.js"></script>
<script type="text/javascript">
$('.nav-tabs li:first a').tab('show');


$('#specs').delegate('.pagination a', 'click', function(e) {
  e.preventDefault();

  $('#specs').load(this.href);
});

$('#specs').load('<?php echo $specs; ?>');

$('#specs-copyright').delegate('.pagination a', 'click', function(e) {
  e.preventDefault();

  $('#specs-copyright').load(this.href);
});

$('#specs-copyright').load('<?php echo $specs_copyright; ?>');

$('#specs-custom').delegate('.pagination a', 'click', function(e) {
  e.preventDefault();

  $('#specs-custom').load(this.href);
});

$('#specs-custom').load('<?php echo $specs_custom; ?>');

$('#specs-tag').delegate('.pagination a', 'click', function(e) {
  e.preventDefault();

  $('#specs-tag').load(this.href);
});

$('#specs-tag').load('<?php echo $specs_tag; ?>');

// document.getElementById('multiFiles').addEventListener('change', function(e) {
//   e.preventDefault();
//   var formData = new FormData();
//   var files = $('#multiFiles')[0].files;
//   for (var i = 0; i < files.length; i++) {
//     formData.append('file-' + i, files[i]);
//   }
//   console.log(formData)
// });

$('.content').on('click', '.btn-upload', function() {
  $('#form-upload').remove();

  var target = $(this);

  $('body').prepend('<form enctype="multipart/form-data" id="form-upload" style="display: none;"><input type="file" name="file" value="" accept="image/*" /><input type="hidden" name="token" value="" /></form>');

  $('#form-upload input[name=\'file\']').trigger('click');

  if (typeof timer != 'undefined') {
    clearInterval(timer);
  }

  timer = setInterval(function() {
    if ($('#form-upload input[name=\'file\']').val() != '') {
      clearInterval(timer);

      $.ajax({
        url: '<?php echo $getToken; ?>',
        type: 'get',
        dataType: 'json',
        success: function(json) {
          $('#form-upload input[name=\'token\']').val(json.uploadToken);
          $.ajax({
            url: 'https://up-z2.qiniup.com',
            type: 'post',
            dataType: 'json',
            data: new FormData($('#form-upload')[0]),
            cache: false,
            contentType: false,
            processData: false,
            beforeSend: function() {
              target.find('i').replaceWith('<i class="fa fa-circle-o-notch fa-spin"></i>');
              target.prop('disabled', true);
            },
            complete: function() {
              target.find('i').replaceWith('<i class="fa fa-upload"></i>');
              target.prop('disabled', false);
            },
            success: function(res) {
              var spec_id = target.parents('tr').data('id')
              var copyright_image = json.httpHost + res.key
              $.ajax({
                url: '<?php echo $addcopyright; ?>',
                type: 'post',
                dataType: 'json',
                data:  {spec_id:spec_id,copyright_image:copyright_image},
                success: function(json) {
                  $('.alert').remove();

                  if (json['error']) {

                  }

                  if (json['success']) {
                    target.parent().prev().append('<div style="width: 25%;float: left"><div class="thumbnail"><img src="' + copyright_image + '?imageView2/1/w/100" onclick="openImage(\''+copyright_image+'\')"><input type="hidden" name="' + target.data('field') + '[]" value="' + json.httpHost + res.key + '"><div class="caption text-center"><button type="button" onclick="$(this).parent().parent().parent().remove()" class="btn btn-default">删除</button></div></div></div>');
                  }
                }
              });
            },
            error: function(xhr, ajaxOptions, thrownError) {
              alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText)
            }
          });
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }
  }, 500);
});

function openImage(url) {
  window.open(url)
}

function del_copyright_image(t,img) {
  if (confirm('确认删除吗？')) {
    var spec_id = t.parents('tr').data('id')
    $.ajax({
      url: '<?php echo $addcopyright; ?>',
      type: 'post',
      dataType: 'json',
      data:  {spec_id:spec_id,copyright_image:img,del:1},
      success: function(json) {
        $('.alert').remove();

        if (json['error']) {

        }

        if (json['success']) {
          t.parent().parent().parent().remove()
        }
      }
    });

  }

}

</script>
<?php echo $footer; ?>