<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        员工信息
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"><?php echo $text_form; ?></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
          <div class="nav-tabs-custom">
            <ul class="nav nav-tabs">
              <li><a href="#tab-base" data-toggle="tab">基础信息</a></li>
              <li><a href="#tab-card" data-toggle="tab">身份信息</a></li>
              <li><a href="#tab-job" data-toggle="tab">任职情况</a></li>
              <li><a href="#tab-edu" data-toggle="tab">学历信息</a></li>
              <li><a href="#tab-contract" data-toggle="tab">劳动合同</a></li>
              <li><a href="#tab-insurance" data-toggle="tab">保险缴纳</a></li>
            </ul>
            <div class="tab-content">
              <div class="tab-pane" id="tab-base">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-name">姓名*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="fullname" value="<?php echo $fullname; ?>" placeholder="请输入姓名" id="input-name" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-phone">手机号*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="telephone" value="<?php echo $telephone; ?>" placeholder="请输入手机号" id="input-phone" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-address">常住地址：</label>
                  <div class="col-sm-8">
                    <input type="text" name="address" value="<?php echo $address; ?>" placeholder="常住地址" id="input-address" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-contact">紧急联系人*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="contact_name" value="<?php echo $contact_name; ?>" placeholder="紧急联系人" id="input-contact" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-relation">紧急联系人关系：</label>
                  <div class="col-sm-8">
                    <select name="contact_relation" id="input-relation" class="form-control">
                      <?php foreach($relations as $row) { ?>
                      <option value="<?php echo $row; ?>"<?php if ($contact_relation == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-ctelephone">紧急联系人手机号*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="contact_telephone" value="<?php echo $contact_telephone; ?>" placeholder="紧急联系人手机号" id="input-ctelephone" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-from">入职来源：</label>
                  <div class="col-sm-8">
                    <select name="channel_from" id="input-from" class="form-control">
                      <?php foreach($channels as $row) { ?>
                      <option value="<?php echo $row; ?>"<?php if ($channel_from == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
              </div>
              <div class="tab-pane" id="tab-card">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-sex">性别：</label>
                  <div class="col-sm-8">
                    <select name="sex" id="input-sex" class="form-control">
                      <option value="男"<?php if ($sex == '男') { ?> selected="selected"<?php } ?>>男</option>
                      <option value="女"<?php if ($sex == '女') { ?> selected="selected"<?php } ?>>女</option>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-birth">出生日期*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="birth_date" value="<?php echo ($birth_date == '0000-00-00') ? '' : $birth_date; ?>" placeholder="出生日期" id="input-birth" class="input-date form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-nation">民族：</label>
                  <div class="col-sm-8">
                    <input type="text" name="nation" value="<?php echo $nation; ?>" placeholder="民族" id="input-nation" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-marriage">婚姻状况：</label>
                  <div class="col-sm-8">
                    <select name="marriage" id="input-marriage" class="form-control">
                      <?php foreach($marriages as $row) { ?>
                      <option value="<?php echo $row; ?>"<?php if ($marriage == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-politics">政治面貌：</label>
                  <div class="col-sm-8">
                    <select name="politics" id="input-politics" class="form-control">
                      <?php foreach($politicses as $row) { ?>
                      <option value="<?php echo $row; ?>"<?php if ($politics == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label">身份证图片：</label>
                  <div class="col-sm-8">
                    <img width="180" src="<?php echo HTTP_IMAGE . (!empty($card_img) ? $card_img : 'no_image.png'); ?>" class="img-thumbnail" />
                    <div class="input-group pdh10">
                      <input type="text" name="card_img" value="<?php echo $card_img; ?>" placeholder="请点击上传按钮" class="form-control" />
                      <div class="input-group-btn">
                        <button type="button" class="btn-upload btn btn-success"><i class="fa fa-upload"></i> 上传</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-cardno">身份证件号码*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="card_no" value="<?php echo $card_no; ?>" placeholder="身份证件号码" id="input-cardno" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-cardname">身份证姓名：</label>
                  <div class="col-sm-8">
                    <input type="text" name="card_name" value="<?php echo $card_name; ?>" placeholder="身份证姓名" id="input-cardname" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-cardaddress">身份证地址：</label>
                  <div class="col-sm-8">
                    <input type="text" name="card_address" value="<?php echo $card_address; ?>" placeholder="身份证地址" id="input-cardaddress" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-cardexpire">证件有效期：</label>
                  <div class="col-sm-8">
                    <input type="text" name="card_expire" value="<?php echo ($card_expire == '0000-00-00') ? '' : $card_expire; ?>" placeholder="证件有效期" id="input-cardexpire" class="input-date form-control" />
                  </div>
                </div>
              </div>
              <div class="tab-pane" id="tab-job">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-department">所属部门*：</label>
                  <div class="col-sm-8">
                    <select name="department" id="input-department" class="form-control">
                      <option value="">选择部门</option>
                      <?php foreach($departments as $row) { ?>
                        <?php foreach($row['subs'] as $sub) { ?>
                        <?php $subname = $row['name'] . '-' . $sub; ?>
                        <?php if ($department == $subname) { ?>
                        <option value="<?php echo $subname; ?>" selected="selected"><?php echo $subname; ?></option>
                        <?php } else{ ?>
                        <option value="<?php echo $subname; ?>"><?php echo $subname; ?></option>
                        <?php } ?>
                        <?php } ?>
                      <?php } ?>
                    </select>
                    <input type="hidden" name="job_centre" value="<?php echo $job_centre; ?>">
                    <input type="hidden" name="job_department" value="<?php echo $job_department; ?>">
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-jobpost">职位：</label>
                  <div class="col-sm-8">
                    <input type="text" name="job_post" value="<?php echo $job_post; ?>" placeholder="职位" id="input-jobpost" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-joboffice">办公地址：</label>
                  <div class="col-sm-8">
                    <select name="job_office" id="input-joboffice" class="form-control">
                      <?php foreach($offices as $row) { ?>
                      <option value="<?php echo $row; ?>"<?php if ($job_office == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-jobnumber">工号：</label>
                  <div class="col-sm-8">
                    <input type="text" name="job_number" value="<?php echo $job_number; ?>" placeholder="工号" id="input-jobnumber" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-jobworkdate">入职时间*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="job_work_date" value="<?php echo ($job_work_date == '0000-00-00') ? '' : $job_work_date; ?>" placeholder="入职时间" id="input-jobworkdate" class="input-date form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-jobestimatedate">预计转正时间*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="job_estimate_date" value="<?php echo ($job_estimate_date == '0000-00-00') ? '' : $job_estimate_date; ?>" placeholder="预计转正时间" id="input-jobestimatedate" class="input-date form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-jobregulardate">实际转正时间：</label>
                  <div class="col-sm-8">
                    <input type="text" name="job_regular_date" value="<?php echo ($job_regular_date == '0000-00-00') ? '' : $job_regular_date; ?>" placeholder="实际转正时间" id="input-jobregulardate" class="input-date form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-jobleavedate">离职时间：</label>
                  <div class="col-sm-8">
                    <input type="text" name="job_leave_date" value="<?php echo ($job_leave_date == '0000-00-00') ? '' : $job_leave_date; ?>" placeholder="离职时间" id="input-jobleavedate" class="input-date form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-jobstatus">任职状态：</label>
                  <div class="col-sm-8">
                    <select name="job_status" id="input-jobstatus" class="form-control">
                      <?php foreach($statuses as $status) { ?>
                      <?php if ($status == $job_status) { ?>
                      <option value="<?php echo $status; ?>" selected="selected"><?php echo $status; ?></option>
                      <?php } else{ ?>
                      <option value="<?php echo $status; ?>"><?php echo $status; ?></option>
                      <?php } ?>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-jobconfident">保密协议：</label>
                  <div class="col-sm-8">
                    <input type="text" name="job_confidentiality" value="<?php echo $job_confidentiality; ?>" placeholder="保密协议签订情况" id="input-jobconfident" class="form-control" />
                  </div>
                </div>
              </div>
              <div class="tab-pane" id="tab-edu">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-edudegree">最高学历：</label>
                  <div class="col-sm-8">
                    <select name="edu_degree" id="input-edudegree" class="form-control">
                      <?php foreach($degrees as $row) { ?>
                      <option value="<?php echo $row; ?>"<?php if ($edu_degree == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-eduschool">毕业院校：</label>
                  <div class="col-sm-8">
                    <input type="text" name="edu_school" value="<?php echo $edu_school; ?>" placeholder="毕业院校" id="input-eduschool" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-eduspeciality">所学专业：</label>
                  <div class="col-sm-8">
                    <input type="text" name="edu_speciality" value="<?php echo $edu_speciality; ?>" placeholder="所学专业" id="input-eduspeciality" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-edugraduatedate">毕业时间：</label>
                  <div class="col-sm-8">
                    <input type="text" name="edu_graduate_date" value="<?php echo ($edu_graduate_date == '0000-00-00') ? '' : $edu_graduate_date; ?>" placeholder="毕业时间" id="input-edugraduatedate" class="input-date form-control" />
                  </div>
                </div>
              </div>
              <div class="tab-pane" id="tab-contract">
                <div class="table-responsive">
                  <table id="contracts" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <td class="text-left">单位名称</td>
                        <td class="text-left">签订时间</td>
                        <td class="text-left">起始时间</td>
                        <td class="text-left">到期时间</td>
                        <td></td>
                      </tr>
                    </thead>
                    <tbody>
                      <?php $contract_row = 0; ?>
                      <?php foreach ($contracts as $contract) { ?>
                      <tr id="contract-row<?php echo $contract_row; ?>">
                        <td class="text-left"><select name="contracts[<?php echo $contract_row; ?>][company]" class="form-control">
                          <?php foreach($companys as $row) { ?>
                          <option value="<?php echo $row; ?>"<?php if ($contract['company'] == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                          <?php } ?>
                        </select></td>
                        <td class="text-left"><input type="text" name="contracts[<?php echo $contract_row; ?>][sign_date]" value="<?php echo ($contract['sign_date'] == '0000-00-00') ? '' : $contract['sign_date']; ?>" placeholder="签订时间" class="input-date form-control" /></td>
                        <td class="text-left"><input type="text" name="contracts[<?php echo $contract_row; ?>][start_date]" value="<?php echo ($contract['start_date'] == '0000-00-00') ? '' : $contract['start_date']; ?>" placeholder="起始时间" class="input-date form-control" /></td>
                        <td class="text-left"><input type="text" name="contracts[<?php echo $contract_row; ?>][end_date]" value="<?php echo ($contract['end_date'] == '0000-00-00') ? '' : $contract['end_date']; ?>" placeholder="到期时间" class="input-date form-control" /></td>
                        <td class="text-left"><button type="button" onclick="$('#contract-row<?php echo $contract_row; ?>').remove();" data-toggle="tooltip" title="删除" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
                      </tr>
                      <?php $contract_row++; ?>
                      <?php } ?>
                    </tbody>
                    <tfoot>
                      <tr>
                        <td colspan="4"></td>
                        <td class="text-left"><button type="button" onclick="addContract();" data-toggle="tooltip" title="增加" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
              <div class="tab-pane" id="tab-insurance">
                <div class="table-responsive">
                  <table id="insurances" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <td class="text-left">保险类型</td>
                        <td class="text-left">缴纳单位</td>
                        <td class="text-left">增员时间</td>
                        <td class="text-left">减员时间</td>
                        <td></td>
                      </tr>
                    </thead>
                    <tbody>
                      <?php $insurance_row = 0; ?>
                      <?php foreach ($insurances as $insurance) { ?>
                      <tr id="insurance-row<?php echo $insurance_row; ?>">
                        <td class="text-left"><select name="insurances[<?php echo $insurance_row; ?>][insurance_name]" class="form-control">
                            <?php foreach ($innames as $inname) { ?>
                              <?php if ($insurance['insurance_name'] == $inname) { ?>
                              <option value="<?php echo $inname; ?>" selected="selected"><?php echo $inname; ?></option>
                              <?php } else{ ?>
                              <option value="<?php echo $inname; ?>"><?php echo $inname; ?></option>
                              <?php } ?>
                            <?php } ?> 
                          </select></td>
                        <td class="text-left"><select name="insurances[<?php echo $insurance_row; ?>][company]" class="form-control">
                          <?php foreach($companys as $row) { ?>
                          <option value="<?php echo $row; ?>"<?php if ($insurance['company'] == $row) { ?> selected="selected"<?php } ?>><?php echo $row; ?></option>
                          <?php } ?>
                        </select></td>
                        <td class="text-left"><input type="text" name="insurances[<?php echo $insurance_row; ?>][add_date]" value="<?php echo ($insurance['add_date'] == '0000-00-00') ? '' : $insurance['add_date']; ?>" placeholder="增员时间" class="input-date form-control" /></td>
                        <td class="text-left"><input type="text" name="insurances[<?php echo $insurance_row; ?>][del_date]" value="<?php echo ($insurance['del_date'] == '0000-00-00') ? '' : $insurance['del_date']; ?>" placeholder="减员时间" class="input-date form-control" /></td>
                        <td class="text-left"><button type="button" onclick="$('#insurance-row<?php echo $insurance_row; ?>').remove();" data-toggle="tooltip" title="删除" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
                      </tr>
                      <?php $insurance_row++; ?>
                      <?php } ?>
                    </tbody>
                    <tfoot>
                      <tr>
                        <td colspan="4"></td>
                        <td class="text-left"><button type="button" onclick="addInsurance();" data-toggle="tooltip" title="增加" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" type="submit">提交保存</button>
              <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
            </div>
          </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
$('.nav-tabs li:first a').tab('show');

// 日期显示
$('.input-date').daterangepicker({
  autoApply: true,
  autoUpdateInput: false,
  singleDatePicker: true,
  timePicker: false,
  timePicker24Hour: false,
  locale: {
    format: 'YYYY-MM-DD',
    applyLabel: '确定',
    cancelLabel: '清除'
  }
})
$('.tab-content').on('apply.daterangepicker', '.input-date', function(ev, picker) {
  $(this).val(picker.startDate.format('YYYY-MM-DD'))
})
$('.tab-content').on('cancel.daterangepicker', '.input-date', function(ev, picker) {
  $(this).val('')
})

$('.tab-content').on('click', '.btn-upload', function() {
  $('#form-upload').remove();

  var target = $(this);

  $('body').prepend('<form enctype="multipart/form-data" id="form-upload" style="display: none;"><input type="file" name="file" value="" accept="image/*, video/*" /><input type="hidden" name="token" value="" /></form>');

  $('#form-upload input[name=\'file\']').trigger('click');

  if (typeof timer != 'undefined') {
      clearInterval(timer);
  }

  timer = setInterval(function() {
    if ($('#form-upload input[name=\'file\']').val() != '') {
      clearInterval(timer);

      $.ajax({
        url: '<?php echo $getToken; ?>',
        type: 'get',
        dataType: 'json',
        success: function(json) {
          $('#form-upload input[name=\'token\']').val(json.uploadToken);
          $.ajax({
            url: 'https://up-z2.qiniup.com',
            type: 'post',
            dataType: 'json',
            data: new FormData($('#form-upload')[0]),
            cache: false,
            contentType: false,
            processData: false,
            beforeSend: function() {
              target.find('i').replaceWith('<i class="fa fa-circle-o-notch fa-spin"></i>');
              target.prop('disabled', true);
            },
            complete: function() {
              target.find('i').replaceWith('<i class="fa fa-upload"></i>');
              target.prop('disabled', false);
            },
            success: function(res) {
              target.parent().parent().find('input[type=\'text\']').val(res.key);
              target.parent().parent().parent().parent().find('img').attr('src', json.httpHost + res.key);
            },
            error: function(xhr, ajaxOptions, thrownError) {
              alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
          });
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }
  }, 500);
});
</script>
<script type="text/javascript"><!--
var contract_row = <?php echo $contract_row; ?>;
var insurance_row = <?php echo $insurance_row; ?>;

function addContract() {
  html  = '<tr id="contract-row' + contract_row + '">';
  html += '  <td class="text-left"><select name="contracts[' + contract_row + '][company]" value="" class="form-control">';
  <?php foreach($companys as $company) { ?>
  html += '<option value="<?php echo $company; ?>"><?php echo $company; ?></option>';
  <?php } ?>
  html += '</select></td>';
  html += '  <td class="text-left"><input type="text" name="contracts[' + contract_row + '][sign_date]" value="" placeholder="签订时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><input type="text" name="contracts[' + contract_row + '][start_date]" value="" placeholder="起始时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><input type="text" name="contracts[' + contract_row + '][end_date]" value="" placeholder="到期时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><button type="button" onclick="$(\'#contract-row' + contract_row  + '\').remove();" data-toggle="tooltip" title="删除" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
  html += '</tr>';

  $('#contracts tbody').append(html);

  addDatePicker('contract-row' + contract_row);

  contract_row++;
}

function addInsurance() {
  html  = '<tr id="insurance-row' + insurance_row + '">';
  html += '  <td class="text-left"><select name="insurances[' + insurance_row + '][insurance_name]" class="form-control">';
  <?php foreach ($innames as $inname) { ?>
  html += '<option value="<?php echo $inname; ?>"><?php echo $inname; ?></option>';
  <?php } ?>   
  html += '</select></td>';
  html += '  <td class="text-left"><select name="insurances[' + insurance_row + '][company]" value="" class="form-control">';
  <?php foreach($companys as $company) { ?>
  html += '<option value="<?php echo $company; ?>"><?php echo $company; ?></option>';
  <?php } ?>
  html += '</select></td>';
  html += '  <td class="text-left"><input type="text" name="insurances[' + insurance_row + '][add_date]" value="" placeholder="增员时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><input type="text" name="insurances[' + insurance_row + '][del_date]" value="" placeholder="减员时间" class="input-date form-control" /></td>';
  html += '  <td class="text-left"><button type="button" onclick="$(\'#insurance-row' + insurance_row + '\').remove();" data-toggle="tooltip" title="删除" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
  html += '</tr>';
  
  $('#insurances tbody').append(html);

  addDatePicker('insurance-row' + insurance_row);
  
  insurance_row++;
}

function addDatePicker(rowid) {
  $('#' + rowid + ' .input-date').daterangepicker({
    autoApply: true,
    autoUpdateInput: false,
    singleDatePicker: true,
    timePicker: false,
    timePicker24Hour: false,
    locale: {
      format: 'YYYY-MM-DD',
      applyLabel: '确定',
      cancelLabel: '清除'
    }
  })
}
//--></script>
<?php echo $footer; ?>