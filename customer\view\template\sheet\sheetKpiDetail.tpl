<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        多维表绩效
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>发布时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right" id="reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right" id="reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start-time" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end-time" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
              
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">列表</p>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'name') { ?>
                  <a href="<?php echo $sort_name; ?>" class="<?php echo strtolower($order); ?>">名称</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_name; ?>">名称</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'months') { ?>
                  <a href="<?php echo $sort_months; ?>" class="<?php echo strtolower($order); ?>">截止时间</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_months; ?>">截止时间</a>
                <?php } ?>
              </th>
                <th>
                <?php if ($sort == 'create_time') { ?>
                  <a href="<?php echo $sort_create_time; ?>" class="<?php echo strtolower($order); ?>">执行时间</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_create_time; ?>">执行时间</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'score') { ?>
                  <a href="<?php echo $sort_score; ?>" class="<?php echo strtolower($order); ?>">说明</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_score; ?>">说明</a>
                <?php } ?>
              </th>

              <th>
                是否补分
              </th>
               
            </tr>
            <?php if (isset($sheet) && !empty($sheet)) { ?>
            <?php foreach ($sheet as $v) { ?>
            <tr data-id="<?php echo $v['id']; ?>">
              <td><?php echo $v['real_name']; ?></td>
              <td><?php echo $v['deadline']; ?></td>
              <td><?php echo $v['create_time']; ?></td>
              <td><?php echo $v['remark']; ?></td>
              <td>
                <?php if (isset($v['is_compensation']) && $v['is_compensation'] == 1) { ?>
                  <button type="button" class="btn btn-danger btn-xs" disabled>已补分</button>
                <?php } else { ?>
                  <button type="button" class="btn btn-success btn-xs btn-compensation" data-id="<?php echo $v['id']; ?>">补分</button>
                <?php } ?>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <tr>
                <td colspan="5" align="center">暂无数据</td>
            </tr>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after, .table a.undesc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after, .table a.unasc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {

        // 日期筛选
    $('#reservation').daterangepicker({
      autoUpdateInput: false,
      daterangepicker: true,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('#reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start-time').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end-time').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('#reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start-time').val('')
      $('#filter-end-time').val('')
    })

    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }
      
      location.href = url;
    });

    // 补分按钮点击事件
    $('.btn-compensation').on('click', function() {
      var id = $(this).data('id');
      var $btn = $(this);
      
      if (confirm('确定要为该任务进行补分操作吗？')) {
        $.ajax({
          url: '<?php echo $action_compensation; ?>',
          type: 'POST',
          data: { id: id },
          dataType: 'json',
          beforeSend: function() {
            $btn.prop('disabled', true);
          },
          complete: function() {
            $btn.prop('disabled', false);
          },
          success: function(json) {
            if (json.success) {
              $btn.removeClass('btn-success').addClass('btn-danger');
              $btn.text('已补分');
              $btn.prop('disabled', true);
              
              // 更新备注文本，添加[扣分已失效]
              
              var $row = $btn.closest('tr');
              var $remarkCell = $row.find('td:eq(3)');
              var currentRemark = $remarkCell.text();
              $remarkCell.text(currentRemark + '[扣分已失效]');
              
              // 显示成功消息
              alert(json.success);
            }
            
            if (json.error) {
              alert(json.error);
            }
          },
          error: function(xhr, ajaxOptions, thrownError) {
            alert('操作失败，请稍后重试！');
          }
        });
      }
    });
  })()
</script>
<?php echo $footer; ?>