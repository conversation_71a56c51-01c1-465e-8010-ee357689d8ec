<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="select-calculate">选择店铺：</label>
              <div class="col-sm-8">
                <select name="store_id" id="select-store_id" class="form-control">
                  <?php foreach($stores as $store){ ?>
                  <option value="<?php echo $store['store_id']; ?>" <?php if(!empty($store_id) && ($store_id == $store['store_id'])){ ?>selected="selected"<?php } ?>><?php echo $store['name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-platform_id">平台链接id：</label>
              <div class="col-sm-8">
                <input type="text" name="platform_id" value="<?php echo $platform_id; ?>" placeholder="平台链接id" id="input-platform_id" class="form-control" />
                <div class="text-danger" style="display: none">请输入平台链接id</div>
              </div>
            </div>

            <?php if ($user_group_id == 1) { ?>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-custodian">管理人：</label>
              <div class="col-sm-8">
                <select id="w1" class="form-control select-custodian"  name="custodian" style="width: 80%;float: left " data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">
                  <?php foreach ($users as $user) { ?>
                  <option value="<?php echo $user['user_id']; ?>"><?php echo $user['real_name']; ?></option>
                  <?php } ?>
                </select>
                <div class="text-danger" style="display: none">请选择管理人</div>
              </div>
            </div>
            <?php } ?>

            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>

<style>
  .select2-container .select2-selection--single {
    height: 35px;
  }

  .select2-container .select2-selection--single .select2-selection__rendered {
    line-height: 35px;
  }
</style>
<script>
  var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
  window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"50px","placeholder":"请选择管理人","language":"zh-CN"};

  if (jQuery('#w1').data('select2')) { jQuery('#w1').select2('destroy'); }
  jQuery.when(jQuery('#w1').select2(select2_5eaa6d36)).done(initS2Loading('w1','s2options_c4acac00'));

  var user = '<?php echo $user_json; ?>'
  $("#w1").val($.parseJSON(user)).trigger("change");
</script>

<script type="text/javascript">
  function toVaild() {
    var isValid = true;
    var inputName = $("#input-name").val()
    if (inputName == '') {
      isValid = false;
      $("#input-name").siblings('.text-danger').show();
    } else {
      $("#input-name").siblings('.text-danger').hide();
    }

    var selectedValues = $("#w1").select2('data');
    if (selectedValues.length > 0) {
      $("#w1").siblings('.text-danger').hide();
    } else {
      isValid = false;
      $("#w1").siblings('.text-danger').show();
    }

    var selectedValues2 = $("#w2").select2('data');
    if (selectedValues2.length > 0) {
      $("#w2").siblings('.text-danger').hide();
    } else {
      isValid = false;
      $("#w2").siblings('.text-danger').show();
    }

    var inputRoute = $("#input-route").val()
    if (inputRoute == '') {
      isValid = false;
      $("#input-route").siblings('.text-danger').show();
    } else {
      $("#input-route").siblings('.text-danger').hide();
    }

    if (isValid) {
      return true;
    } else {
      return false;
    }
  }
</script>
<?php echo $footer; ?>