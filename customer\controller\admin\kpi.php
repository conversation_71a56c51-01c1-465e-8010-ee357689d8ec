<?php
class ControllerAdmin<PERSON><PERSON> extends Controller {
	private $error = array();

	public function itemAdd() {
		$this->load->model('admin/kpi');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateItemForm()) {
			$this->model_admin_kpi->addItem($this->request->post);

			$this->session->data['success'] = $this->language->get('text_add_success');

			$url = '';

			if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_status'])) {
	            $url .= '&filter_status=' . $this->request->get['filter_status'];
	        }

	        if (isset($this->request->get['filter_date_start'])) {
	            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
	        }

	        if (isset($this->request->get['filter_date_end'])) {
	            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
	        }

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->itemForm();
	}

	public function itemEdit() {
		$this->load->model('admin/kpi');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateItemForm()) {
			$this->model_admin_kpi->editItem($this->request->get['item_id'], $this->request->post);

			$this->session->data['success'] = $this->language->get('text_edit_success');

			$url = '';

			if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_status'])) {
	            $url .= '&filter_status=' . $this->request->get['filter_status'];
	        }

	        if (isset($this->request->get['filter_date_start'])) {
	            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
	        }

	        if (isset($this->request->get['filter_date_end'])) {
	            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
	        }

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->itemForm();
	}

	public function itemDelete() {
		$this->load->model('admin/kpi');

		if (isset($this->request->post['selected']) && $this->validateDelete()) {
			foreach ($this->request->post['selected'] as $item_id) {
				$this->model_admin_kpi->deleteItem($item_id);
			}

			$this->session->data['success'] = $this->language->get('text_delete_success');

			$url = '';

			if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_status'])) {
	            $url .= '&filter_status=' . $this->request->get['filter_status'];
	        }

	        if (isset($this->request->get['filter_date_start'])) {
	            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
	        }

	        if (isset($this->request->get['filter_date_end'])) {
	            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
	        }

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->itemList();
	}

	public function itemList() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = 'date_added';
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = 'DESC';
		}

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$data['add'] = $this->url->link('admin/kpi/itemAdd', 'token=' . $this->session->data['token'] . $url);
		$data['delete'] = $this->url->link('admin/kpi/itemDelete', 'token=' . $this->session->data['token'] . $url);

		$this->load->model('admin/user');
		$results = $this->model_admin_user->getUsers();
		$users = array();

        foreach ($results as $result) {
            $users[$result['user_id']] = $result['real_name'];
        }

		$data['items'] = array();

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_status'		=> $filter_status,
			'filter_date_start'	=> $filter_date_start,
			'filter_date_end'	=> $filter_date_end,
			'sort'			=> $sort,
			'order'			=> $order,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);

		$this->load->model('admin/kpi');
		$results = $this->model_admin_kpi->getItems($filter_data);

		foreach ($results as $result) {
			$rater = array();

			foreach ((array)explode(',', $result['rater_ids']) as $rater_id) {
				$rater[] = $users[$rater_id] ?? '';
			}
			
			$data['items'][] = array(
				'item_id'	=> $result['item_id'],
				'name'		=> $result['item_name'],
				'score'		=> $result['item_score'],
				'rater'		=> $rater,
				'status'    => ($result['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled')),
				'date_added'=> $result['date_added'],
				'edit'  => $this->url->link('admin/kpi/itemEdit', 'token=' . $this->session->data['token'] . '&item_id=' . $result['item_id'] . $url)
			);
		}

		$total = $this->model_admin_kpi->getTotalItems($filter_data);

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if ($order == 'ASC') {
			$url .= '&order=DESC';
		} else {
			$url .= '&order=ASC';
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['sort_name'] = $this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . '&sort=item_name' . $url);
		$data['sort_score'] = $this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . '&sort=item_score' . $url);
		$data['sort_rater'] = $this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . '&sort=rater_ids' . $url);
		$data['sort_status'] = $this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . '&sort=status' . $url);
		$data['sort_date'] = $this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . '&sort=date_added' . $url);

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
		$data['filter_status'] = $filter_status;
		$data['filter_date_start'] = $filter_date_start;
		$data['filter_date_end'] = $filter_date_end;

		$data['sort'] = $sort;
		$data['order'] = $order;

		$data['nofilter'] = $this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token']);

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('kpi/item_list.tpl', $data));
	}

	protected function itemForm() {
		$data['text_form'] = !isset($this->request->get['item_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		if (!isset($this->request->get['item_id'])) {
			$data['action'] = $this->url->link('admin/kpi/itemAdd', 'token=' . $this->session->data['token'] . $url);
		} else {
			$data['action'] = $this->url->link('admin/kpi/itemEdit', 'token=' . $this->session->data['token'] . '&item_id=' . $this->request->get['item_id'] . $url);
		}

		$data['cancel'] = $this->url->link('admin/kpi/itemList', 'token=' . $this->session->data['token'] . $url);

		if (isset($this->request->get['item_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
			$item_info = $this->model_admin_kpi->getItem($this->request->get['item_id']);
		}

		$fields = array('item_name', 'item_score', 'item_desc', 'status');

		foreach ($fields as $field) {
			if (isset($this->request->post[$field])) {
				$data[$field] = $this->request->post[$field];
			} elseif (!empty($item_info)) {
				$data[$field] = $item_info[$field];
			} else {
				$data[$field] = '';
			}
		}

		$this->load->model('admin/user');
        $data['users'] = $this->model_admin_user->getUsers();

        if (isset($this->request->post['rater_ids'])) {
            $data['rater_ids'] = $this->request->post['rater_ids'];
        } elseif (!empty($item_info)) {
            $data['rater_ids'] = explode(',', $item_info['rater_ids']);
        } else {
            $data['rater_ids'] = array();
        }
		
		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('kpi/item_form.tpl', $data));
	}

	public function userMonthScore() {
        $this->load->model('admin/kpi');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateDelete()) {
            $this->model_admin_kpi->addUserMonthScore();

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/kpi/userList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->userList();
    }

	public function userAdd() {
        $this->load->model('admin/kpi');

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->model_admin_kpi->addUser($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/kpi/userList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->userForm();
    }

    public function userEdit() {
        $this->load->model('admin/kpi');

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->model_admin_kpi->editUser($this->request->get['user_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/kpi/userList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->userForm();
    }

    public function userDelete() {
        $this->load->model('admin/kpi');

        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $user_id) {
                $this->model_admin_user->deleteUser($user_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/kpi/userList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->userList();
    }

    public function userList() {
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['month'] = $this->url->link('admin/kpi/userMonthScore', 'token=' . $this->session->data['token'] . $url);
        $data['add'] = $this->url->link('admin/kpi/userAdd', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/kpi/userDelete', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['users'] = $items = array();

        $this->load->model('admin/kpi');
        $results = $this->model_admin_kpi->getItems();

        foreach ($results as $result) {
        	$items[$result['item_id']] = $result['item_name'];
        }

        $filter_data = array(
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );

        $results = $this->model_admin_kpi->getUsers($filter_data);

        foreach ($results as $result) {
        	$useritems = array();

        	foreach (explode(',', $result['kpi_item_ids']) as $item_id) {
        		if (isset($items[$item_id])) {
        			$useritems[] = $items[$item_id];
        		}
        	}

            $data['users'][] = array(
                'user_id'    => $result['user_id'],
                'username'   => $result['username'],
                'realname' 	 => $result['real_name'],
                'useritems'  => $useritems,
                'edit'       => $this->url->link('admin/kpi/userEdit', 'token=' . $this->session->data['token'] . '&user_id=' . $result['user_id'] . $url)
            );
        }

        $total = $this->model_admin_kpi->getTotalUsers();

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/kpi/userList', 'token=' . $this->session->data['token'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('kpi/user_list.tpl', $data));
    }

    protected function userForm() {
        $data['text_form'] = !isset($this->request->get['user_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['user_id'])) {
            $data['action'] = $this->url->link('admin/kpi/userAdd', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/kpi/userEdit', 'token=' . $this->session->data['token'] . '&user_id=' . $this->request->get['user_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/kpi/userList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['user_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $user_info = $this->model_admin_kpi->getUser($this->request->get['user_id']);
        }

        $this->load->model('admin/user');
        $data['users'] = $this->model_admin_user->getUsers();

        $data['items'] = $this->model_admin_kpi->getItems();

        if (isset($this->request->post['kpi_item_ids'])) {
            $data['kpi_item_ids'] = $this->request->post['kpi_item_ids'];
        } elseif (!empty($user_info)) {
            $data['kpi_item_ids'] = explode(',', $user_info['kpi_item_ids']);
        } else {
            $data['kpi_item_ids'] = array();
        }

        if (isset($this->request->post['user_id'])) {
            $data['user_id'] = $this->request->post['user_id'];
        } elseif (!empty($user_info)) {
            $data['user_id'] = $user_info['user_id'];
        } else {
            $data['user_id'] = '';
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('kpi/user_form.tpl', $data));
    }

	public function rateAdd() {
        $this->load->model('admin/kpi');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateRate()) {
            $this->model_admin_kpi->addRate($this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_item'])) {
	            $url .= '&filter_item=' . $this->request->get['filter_item'];
	        }

	        if (isset($this->request->get['filter_date_start'])) {
	            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
	        }

	        if (isset($this->request->get['filter_date_end'])) {
	            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
	        }

			if (isset($this->request->get['filter_status'])) {
	            $url .= '&filter_status=' . $this->request->get['filter_status'];
	        }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/kpi/rateList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->rateList();
    }

    public function rateList() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_item'])) {
            $filter_item = $this->request->get['filter_item'];
        } else {
            $filter_item = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

		if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_item'])) {
            $url .= '&filter_item=' . $this->request->get['filter_item'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$data['action'] = $this->url->link('admin/kpi/rateAdd', 'token=' . $this->session->data['token'] . $url);

		$this->load->model('admin/user');
		$results = $this->model_admin_user->getUsers();
		$users = array();

        foreach ($results as $result) {
            $users[$result['user_id']] = $result['real_name'];
        }

		$this->load->model('admin/kpi');
		$data['months'] = $this->model_admin_kpi->getMonths();
		$data['items'] = $this->model_admin_kpi->getItems();

        $items = array();

        foreach ($data['items'] as $item) {
            $items[$item['item_id']] = $item['item_name'];
        }

		$data['rates'] = array();

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_item'		=> $filter_item,
			'filter_date_start'	=> $filter_date_start,
			'filter_date_end'	=> $filter_date_end,
			'filter_status'		=> $filter_status,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);
		
		$results = $this->model_admin_kpi->getRates($filter_data);

		foreach ($results as $result) {
			$data['rates'][] = array(
				'score_id'	=> $result['score_id'],
				'item'		=> $items[$result['item_id']] ?? '',
				'user'		=> $users[$result['user_id']] ?? '',
				'rater'		=> $users[$result['rater_id']] ?? '',
				'israter'	=> ($this->user->user_id == $result['rater_id']) ? true : false,
				'score'		=> $result['score'],
				'month'		=> $result['month'],
				'remark'	=> $result['remark'],
				'status'	=> $result['status'],
				'date_added'=> $result['date_added']
			);
		}

		$total = $this->model_admin_kpi->getTotalRates($filter_data);

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_item'])) {
            $url .= '&filter_item=' . $this->request->get['filter_item'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/kpi/rateList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
		$data['filter_item'] = $filter_item;
		$data['filter_date_start'] = $filter_date_start;
		$data['filter_date_end'] = $filter_date_end;
		$data['filter_status'] = $filter_status;

		$data['nofilter'] = $this->url->link('admin/kpi/rateList', 'token=' . $this->session->data['token']);

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('kpi/rate_list.tpl', $data));
	}

    public function scoreList() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_item'])) {
            $filter_item = $this->request->get['filter_item'];
        } else {
            $filter_item = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$this->load->model('admin/user');
		$results = $this->model_admin_user->getUsers();
		$users = array();

        foreach ($results as $result) {
            $users[$result['user_id']] = $result['real_name'];
        }

		$this->load->model('admin/kpi');
		$data['months'] = $this->model_admin_kpi->getMonths();
		$data['items'] = $this->model_admin_kpi->getItems();

        $items = array();

        foreach ($data['items'] as $item) {
            $items[$item['item_id']] = $item['item_name'];
        }

		$data['scores'] = array();

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_item'		=> $filter_item,
			'filter_date_start'	=> $filter_date_start,
			'filter_date_end'	=> $filter_date_end,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);
		
		$results = $this->model_admin_kpi->getScores($filter_data);

		foreach ($results as $result) {
			$data['scores'][] = array(
				'item'		=> $items[$result['item_id']] ?? '',
				'user'		=> $users[$result['user_id']] ?? '',
				'score'		=> $result['score'],
				'kpi'		=> $result['kpi'],
				'month'		=> $result['month'],
				'modified'	=> $result['date_modified']
			);
		}

		$total = $this->model_admin_kpi->getTotalScores($filter_data);

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_item'])) {
            $url .= '&filter_item=' . $this->request->get['filter_item'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/kpi/scoreList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
		$data['filter_item'] = $filter_item;
		$data['filter_date_start'] = $filter_date_start;
		$data['filter_date_end'] = $filter_date_end;

		$data['nofilter'] = $this->url->link('admin/kpi/scoreList', 'token=' . $this->session->data['token']);

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('kpi/score_list.tpl', $data));
	}

	public function myItem() {
		$data['items'] = array();

		if ($this->user->kpi_item_ids) {
			$this->load->model('admin/user');
			$results = $this->model_admin_user->getUsers();
			$users = array();

	        foreach ($results as $result) {
	            $users[$result['user_id']] = $result['real_name'];
	        }

	        $this->load->model('admin/kpi');
			$results = $this->model_admin_kpi->getMyItems($this->user->kpi_item_ids);

			foreach ($results as $result) {
				$rater = array();

				foreach ((array)explode(',', $result['rater_ids']) as $rater_id) {
					$rater[] = $users[$rater_id] ?? '';
				}
				
				$data['items'][] = array(
					'name'		=> $result['item_name'],
					'desc'		=> $result['item_desc'],
					'score'		=> $result['item_score'],
					'rater'		=> $rater
				);
			}
		}

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('kpi/myitem.tpl', $data));
	}

	protected function validateItemForm() {
		if (!$this->user->hasPermission('modify', 'admin/kpi')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if (empty($this->request->post['item_name'])) {
            $this->error['warning'] = '名称不能为空！';
            return false;
        }

		if (empty($this->request->post['rater_ids'])) {
            $this->error['warning'] = '评分人不能为空！';
            return false;
        }

		if (empty($this->request->post['item_score'])) {
            $this->error['warning'] = '分值不能为空！';
            return false;
        }

		return !$this->error;
	}

	protected function validateRate() {
		if (empty($this->request->post['score_id'])) {
            $this->error['warning'] = '参数错误请刷新页面！';
            return false;
        }

		if (!isset($this->request->post['score']) || ($this->request->post['score'] === '') || ((int)$this->request->post['score'] > 100) || ((int)$this->request->post['score'] < 0)) {
            $this->error['warning'] = '分数必须0-100！';
            return false;
        }

		/*if (empty($this->request->post['remark'])) {
            $this->error['warning'] = '说明不能为空！';
            return false;
        }*/

		return !$this->error;
	}

	protected function validateDelete() {
		if (!$this->user->hasPermission('modify', 'admin/kpi')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		return !$this->error;
	}
}
