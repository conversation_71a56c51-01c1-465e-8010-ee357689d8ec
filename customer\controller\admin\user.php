<?php
class ControllerAd<PERSON><PERSON>ser extends Controller {
    private $error = array();

    public function add() {
        $this->load->model('admin/user');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_user->addUser($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/user/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function edit() {
        $this->load->model('admin/user');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_user->editUser($this->request->get['user_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/user/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function delete() {
        $this->load->model('admin/user');

        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $user_id) {
                $this->model_admin_user->deleteUser($user_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/user/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getList();
    }

    public function getList() {
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/user/add', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/user/delete', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['users'] = array();

        $filter_data = array(
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );

        $this->load->model('admin/user');
        $results = $this->model_admin_user->getUsers($filter_data);

        foreach ($results as $result) {
            $data['users'][] = array(
                'user_id'    => $result['user_id'],
                'username'   => $result['username'],
                'real_name'  => $result['real_name'],
                'user_group' => $result['user_group'],
                'status'     => ($result['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled')),
                'date_added' => date($this->language->get('date_format'), strtotime($result['date_added'])),
                'last_ip'    => $result['ip'],                
                'last_date'  => $result['date_modified'],
                'edit'       => $this->url->link('admin/user/edit', 'token=' . $this->session->data['token'] . '&user_id=' . $result['user_id'] . $url)
            );
        }

        $total = $this->model_admin_user->getTotalUsers();

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/user/getList', 'token=' . $this->session->data['token'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('user/user_list.tpl', $data));
    }

    protected function getForm() {
        $data['text_form'] = !isset($this->request->get['user_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['user_id'])) {
            $data['action'] = $this->url->link('admin/user/add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/user/edit', 'token=' . $this->session->data['token'] . '&user_id=' . $this->request->get['user_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/user/getList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['user_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $user_info = $this->model_admin_user->getUser($this->request->get['user_id']);
        }

        if (isset($this->request->post['username'])) {
            $data['username'] = $this->request->post['username'];
        } elseif (!empty($user_info)) {
            $data['username'] = $user_info['username'];
        } else {
            $data['username'] = '';
        }

        $data['user_groups'] = $this->model_admin_user->getUserGroups();

        if (isset($this->request->post['user_group_id'])) {
            $data['user_group_id'] = $this->request->post['user_group_id'];
        } elseif (!empty($user_info)) {
            $data['user_group_id'] = $user_info['user_group_id'];
        } else {
            $data['user_group_id'] = 0;
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        if (isset($this->request->post['store_ids'])) {
            $data['store_ids'] = $this->request->post['store_ids'];
        } elseif (!empty($user_info)) {
            $data['store_ids'] = explode(',', $user_info['store_ids']);
        } else {
            $data['store_ids'] = array();
        }

        if (isset($this->request->post['password'])) {
            $data['password'] = $this->request->post['password'];
        } else {
            $data['password'] = '';
        }

        if (isset($this->request->post['confirm'])) {
            $data['confirm'] = $this->request->post['confirm'];
        } else {
            $data['confirm'] = '';
        }

        if (isset($this->request->post['real_name'])) {
            $data['real_name'] = $this->request->post['real_name'];
        } elseif (!empty($user_info)) {
            $data['real_name'] = $user_info['real_name'];
        } else {
            $data['real_name'] = '';
        }
        
        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($user_info)) {
            $data['status'] = $user_info['status'];
        } else {
            $data['status'] = 1;
        }

        if ($this->user->store_ids) {
            $data['select_store'] = false;
        } else {
            $data['select_store'] = true;
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('user/user_form.tpl', $data));
    }

    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'admin/user')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['username']) < 1) || (utf8_strlen($this->request->post['username']) > 32)) {
            $this->error['warning'] = $this->language->get('error_account_length');
        }

        $user_info = $this->model_admin_user->getUserByUsername($this->request->post['username']);

        if ($user_info) {
            $this->request->post['union_id'] = $user_info['union_id'];
        }

        if (!isset($this->request->get['user_id'])) {
            if (!empty($user_info['user_id'])) {
                $this->error['warning'] = $this->language->get('error_account_exists');
            }
        } else {
            if ($user_info && ($this->request->get['user_id'] != $user_info['user_id'])) {
                $this->error['warning'] = $this->language->get('error_account_exists');
            }
        }

        if ($this->request->post['password'] || (!isset($this->request->get['user_id']))) {
            if ((utf8_strlen($this->request->post['password']) < 4) || (utf8_strlen($this->request->post['password']) > 20)) {
                $this->error['warning'] = $this->language->get('error_password_length');
            }

            if ($this->request->post['password'] != $this->request->post['confirm']) {
                $this->error['warning'] = $this->language->get('error_password_confirm');
            }
        }

        return !$this->error;
    }

    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'admin/user')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        foreach ($this->request->post['selected'] as $user_id) {
            if ($this->user->user_id == $user_id) {
                $this->error['warning'] = $this->language->get('error_delete_yourself');
            }
        }

        return !$this->error;
    }
}