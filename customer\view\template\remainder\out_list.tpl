<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        库存销售数据
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索商家编码" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach ($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>起始年月：</label>
                  <select class="form-control" name="filter_date_start">
                    <option value="*">不限年月</option>
                    <?php foreach($months as $month) { ?>
                    <?php if ($month == $filter_date_start) { ?>
                    <option value="<?php echo $month; ?>" selected="selected"><?php echo $month; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $month; ?>"><?php echo $month; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>截止年月：</label>
                  <select class="form-control" name="filter_date_end">
                    <option value="*">不限年月</option>
                    <?php foreach($months as $month) { ?>
                    <?php if ($month == $filter_date_end) { ?>
                    <option value="<?php echo $month; ?>" selected="selected"><?php echo $month; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $month; ?>"><?php echo $month; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">销售列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">店铺</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">店铺</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'stockout_month') { ?>
                <a href="<?php echo $sort_month; ?>" class="<?php echo strtolower($order); ?>">销售日期</a>
                <?php } else { ?>
                <a href="<?php echo $sort_month; ?>">销售日期</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'stockout_count') { ?>
                <a href="<?php echo $sort_count; ?>" class="<?php echo strtolower($order); ?>">商品种类</a>
                <?php } else { ?>
                <a href="<?php echo $sort_count; ?>">商品种类</a>
                <?php } ?>
              </th><th>
                <?php if ($sort == 'stockout_quan') { ?>
                <a href="<?php echo $sort_quan; ?>" class="<?php echo strtolower($order); ?>">销售数量</a>
                <?php } else { ?>
                <a href="<?php echo $sort_quan; ?>">销售数量</a>
                <?php } ?>
              </th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($stockouts)) { ?>
              <?php foreach ($stockouts as $stockout) { ?>
                <tr>
                  <td><?php echo $stockout['out_store']; ?></td>
                  <td><?php echo $stockout['out_month']; ?></td>
                  <td><?php echo $stockout['out_count']; ?></td>
                  <td><?php echo $stockout['out_quan']; ?></td>
                  <td class="text-right">
                    <?php if ($stockout['status'] == '0') { ?>
                    <a class="btn btn-danger" href="<?php echo $stockout['detail']; ?>">核对销售</a>
                    <?php } else { ?>
                    <a class="btn btn-success" href="<?php echo $stockout['detail']; ?>">查看销售</a>
                    <?php } ?>
                  </td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="5" align="center"> 暂无销售数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_date_start = $('select[name=\'filter_date_start\']').val();
  
      if (filter_date_start != '*') {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('select[name=\'filter_date_end\']').val();
  
      if (filter_date_end != '*') {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>