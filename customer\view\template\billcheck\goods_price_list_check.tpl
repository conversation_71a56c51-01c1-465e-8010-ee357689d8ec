<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        货款核对-<?php echo $goodsPriceList['name']; ?>-<?php echo $goodsPriceList['check_date']; ?>-<?php echo $goodsPriceList['bsku']; ?><br>数量：<?php echo $goodsPriceList['quantity']; ?>-金额：<?php echo $goodsPriceList['total_prices']; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>

      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">货款核对</h3>
        </div>
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th width="30"><input id="selectAll" class="flat" type="checkbox"></th>
              <th>入库单号</th>
              <th>数量</th>
              <th>单价</th>
              <th>总金额</th>
              <th>备注</th>
            </tr>
            <?php if (!empty($goodsPriceListStockin)) { ?>
              <?php foreach ($goodsPriceListStockin as $stockin) { ?>
                <tr>
                  <td><input class="flat" type="checkbox" name="selected[]" value="<?php echo $stockin['stockin_id']; ?>"></td>
                  <td><?php echo $stockin['stockin_id']; ?></td>
                  <td><?php echo $stockin['quantity']; ?></td>
                  <td><?php echo $stockin['price']; ?></td>
                  <td><?php echo $stockin['total_prices']; ?></td>
                  <td><?php echo $stockin['remark']; ?></td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" type="submit">确认</button>
              <a href="<?php echo $action; ?>" class="btn btn-default">重置数据</a>
            </div>
          </div>
          </form>
        </div>

      </div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 全选操作
    $('#selectAll').on('ifChecked', function() {
      $('input.flat').iCheck('check')
    })
    $('#selectAll').on('ifUnchecked', function() {
      $('input.flat').iCheck('uncheck')
    })
  })()
</script>
<?php echo $footer; ?>