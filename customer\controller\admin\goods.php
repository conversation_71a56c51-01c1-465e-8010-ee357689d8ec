<?php
class ControllerAdminGoods extends Controller {
	private $error = array();

	public function exportStocks() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_provider'])) {
			$filter_provider = $this->request->get['filter_provider'];
		} else {
			$filter_provider = '';
		}

		if (isset($this->request->get['filter_rule'])) {
            $filter_rule = $this->request->get['filter_rule'];
        } else {
            $filter_rule = 'outstock';
        }

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = 'last_sales';
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = 'DESC';
		}

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_provider'	=> $filter_provider,
			'filter_rule'		=> $filter_rule,
			'sort'			=> $sort,
			'order'			=> $order
		);

		$bsku_list = array();

		$export_head = array('商家编码', '产品名称', '实际库存', '待审核', '采购在途', '缺货数量', '零售缺货', '批发缺货', '最后销售日期', '供应商', '到货时间', '到货数量');		
		$export_data = array();
        $export_data[] = $export_head;

		$this->load->model('admin/goods');
		$results = $this->model_admin_goods->getStocks($filter_data);

		foreach ($results as $result) {
			$bsku_list[] = $result['spec_no'];
		}

		$providers = $this->model_admin_goods->getPurchaseProviders($bsku_list);
		$arrivals = $this->model_admin_goods->getPurchaseArrivals($bsku_list);
		$quans = $this->model_admin_goods->getStockOutQuan($bsku_list);

		foreach ($results as $result) {
			$provider = $providers[$result['spec_no']] ?? [];
			$arrival = $arrivals[$result['spec_no']] ?? [];
			$quan = $quans[$result['spec_no']] ?? [];

			$export_data[] = array(
				$result['spec_no'],
				$result['spec_name'],
				(int)$result['stock_num'],
				(int)$result['order_num'],
				(int)$result['purchase_num'],
				((int)$result['out_num'] > 0) ? (int)$result['out_num'] : 0,
				$quan['retail'] ?? '',
				$quan['wholesale'] ?? '',
				$result['last_sales'],
				implode(',', $provider),
				$arrival['date'] ?? '',
				$arrival['quan'] ?? ''
			);
		}

        if (!empty($export_data)) {
            $this->load->model('admin/excel');
            $this->model_admin_excel->export('库存表' . date('Y-m-d'), $export_data, array(2 => 'numbric', 3 => 'numbric', 4 => 'numbric', 5 => 'numbric'), '.xlsx');
        }

        $this->getStocks();
	}

	public function getStocks() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_provider'])) {
			$filter_provider = $this->request->get['filter_provider'];
		} else {
			$filter_provider = '';
		}

		if (isset($this->request->get['filter_rule'])) {
            $filter_rule = $this->request->get['filter_rule'];
        } else {
            $filter_rule = 'outstock';
        }

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = 'last_sales';
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = 'DESC';
		}

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$data['export'] = $this->url->link('admin/goods/exportStocks', 'token=' . $this->session->data['token'] . $url);
		$data['addArrival'] = $this->url->link('admin/goods/addPurchaseArrival', 'token=' . $this->session->data['token'] . $url);
		$data['addOutQuan'] = $this->url->link('admin/goods/addStockOutQuan', 'token=' . $this->session->data['token'] . $url);

		$bsku_list = array();
		$data['stocks'] = array();

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_provider'	=> $filter_provider,
			'filter_rule'		=> $filter_rule,
			'sort'			=> $sort,
			'order'			=> $order,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);

		$this->load->model('admin/goods');
		$data['providers'] = $this->model_admin_goods->getProviders();
		$data['rules'] = $this->model_admin_goods->getStockRules();

		$results = $this->model_admin_goods->getStocks($filter_data);

		foreach ($results as $result) {
			$bsku_list[] = $result['spec_no'];
		}

		$providers = $this->model_admin_goods->getPurchaseProviders($bsku_list);
		$arrivals = $this->model_admin_goods->getPurchaseArrivals($bsku_list);
		$quans = $this->model_admin_goods->getStockOutQuan($bsku_list);

		foreach ($results as $result) {
			$provider = $providers[$result['spec_no']] ?? [];
			$arrival = $arrivals[$result['spec_no']] ?? [];
			$quan = $quans[$result['spec_no']] ?? [];

			$data['stocks'][] = array(
				'img_url'	=> $result['img_url'],
				'bsku'		=> $result['spec_no'],
				'spec_name' => $result['spec_name'],
				'providers'	=> implode('<br>', $provider),
				'order'		=> (int)$result['order_num'],
				'purchase'	=> (int)$result['purchase_num'],
				'stock'		=> (int)$result['stock_num'],
				'out'		=> ((int)$result['out_num'] > 0) ? (int)$result['out_num'] : 0,
				'last'		=> $result['last_sales'],
				'quan'		=> $quan,
				'arrival'	=> $arrival
			);
		}

		$total = $this->model_admin_goods->getTotalStocks($filter_data);

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
        }

		if ($order == 'ASC') {
			$url .= '&order=DESC';
		} else {
			$url .= '&order=ASC';
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['sort_bsku'] = $this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token'] . '&sort=spec_no' . $url);
		$data['sort_order'] = $this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token'] . '&sort=order_num' . $url);
		$data['sort_purchase'] = $this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token'] . '&sort=purchase_num' . $url);		
		$data['sort_stock'] = $this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token'] . '&sort=stock_num' . $url);
		$data['sort_out'] = $this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token'] . '&sort=out_num' . $url);
		$data['sort_last'] = $this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token'] . '&sort=last_sales' . $url);

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

		$data['filter_name'] = $filter_name;
		$data['filter_provider'] = $filter_provider;
		$data['filter_rule'] = $filter_rule;

		$data['nofilter'] = $this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token']);

		$data['sort'] = $sort;
		$data['order'] = $order;

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('goods/stock_list.tpl', $data));
	}

	public function addPurchaseArrival() {
		$this->load->model('admin/goods');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && isset($this->request->post['bsku'])) {
        	if (!empty($this->request->post['arrival_quan']) && !empty($this->request->post['arrival_date'])) {
        		$this->model_admin_goods->addPurchaseArrival($this->request->post);
        	}

            $url = '';

            if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_provider'])) {
				$url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_rule'])) {
	            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
	        }

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

            $this->response->redirect($this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getStocks();
	}

	public function addStockOutQuan() {
		$this->load->model('admin/goods');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && isset($this->request->post['bsku'])) {
        	if (isset($this->request->post['retail_quan']) && isset($this->request->post['wholesale_quan']) && ((int)$this->request->post['retail_quan'] + (int)$this->request->post['wholesale_quan'] > 0)) {
        		$this->model_admin_goods->addStockOutQuan($this->request->post);
        	}

            $url = '';

            if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_provider'])) {
				$url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_rule'])) {
	            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
	        }

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

            $this->response->redirect($this->url->link('admin/goods/getStocks', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getStocks();
	}

    //物品档案管理
    public function getArchives(){
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_provider'])) {
            $filter_provider = $this->request->get['filter_provider'];
        } else {
            $filter_provider = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'goods_modified';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }
        
        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }
        $url = '';

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
        }

        $this->load->model('admin/goods');
        $data['providers'] = $this->model_admin_goods->getProviders();

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_provider'	=> $filter_provider,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $data['goods'] = $this->model_admin_goods->getGoods($filter_data);

        $total = $this->model_admin_goods->getGoodsTotal($filter_data);;

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/goods/getArchives', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_provider'] = $filter_provider;

        $data['nofilter'] = $this->url->link('admin/goods/getArchives', 'token=' . $this->session->data['token']);

        $data['detail'] = $this->url->link('admin/goods/getArchivesDetail', 'token=' . $this->session->data['token']. '&goods_id=');

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('goods/archives_list.tpl', $data));
    }

    public function getArchivesDetail() {
        $this->load->model('admin/goods');
        $data['warning'] = '';
        if (!empty($this->request->get['goods_id'])) {
            $data['goods'] = $this->model_admin_goods->getOneGoods($this->request->get['goods_id']);
            if (empty($data['goods'])) {
                $data['warning'] = '参数错误';
            } else {
                $data['specs'] = $this->url->link('admin/goods/specs', 'token=' . $this->session->data['token']. '&goods_id=' . $this->request->get['goods_id']);
                $data['specs_copyright'] = $this->url->link('admin/goods/specsCopyright', 'token=' . $this->session->data['token']. '&goods_id=' . $this->request->get['goods_id']);
                $data['specs_custom'] = $this->url->link('admin/goods/specsCustom', 'token=' . $this->session->data['token']. '&goods_id=' . $this->request->get['goods_id']);
                $data['specs_tag'] = $this->url->link('admin/goods/specsTag', 'token=' . $this->session->data['token']. '&goods_id=' . $this->request->get['goods_id']);
                $data['getToken'] = $this->url->link('admin/common/getUploadToken', 'token=' . $this->session->data['token']);
                $data['addcopyright'] = $this->url->link('admin/goods/addCopyright', 'token=' . $this->session->data['token'] . '&goods_id=' . $this->request->get['goods_id']);
                $data['record_action'] = $this->url->link('admin/goods/addRecord', 'token=' . $this->session->data['token'] . '&goods_id=' . $this->request->get['goods_id']);
                $data['cancel'] = $this->url->link('admin/goods/getArchivesDetail', 'token=' . $this->session->data['token'] . '&goods_id=' . $this->request->get['goods_id']);
                $this->load->model('admin/purchase');
                $data['packaging'] = $this->model_admin_purchase->getPurchaseContractContent($data['goods']['goods_no'],1);
                $data['accessories'] = $this->model_admin_purchase->getPurchaseContractContent($data['goods']['goods_no'],3);
                $data['attention'] = $this->model_admin_purchase->getPurchaseContractContent($data['goods']['goods_no'],4);
                $data['weight'] = $this->model_admin_purchase->getPurchaseContractContent($data['goods']['goods_no'],5);
                $data['packaging_list'] =  $this->model_admin_purchase->getPackagingList();
                $data['accessories_list'] =  $this->model_admin_purchase->getAccessoriesList();
                $data['attention_list'] =  $this->model_admin_purchase->getAttentionList();
            }
        } else {
            $data['warning'] = '参数错误';
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('goods/archives_detail_form.tpl', $data));
    }

    public function specs() {
        if (!isset($this->request->get['goods_id'])) {
            $this->request->get['goods_id'] = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->config->set('config_limit', 10);

        $filter_data = array(
            'start'   => ($page - 1) * $this->config->get('config_limit'),
            'limit'   => $this->config->get('config_limit')
        );

        $this->load->model('admin/goods');
        $data['specs'] = $this->model_admin_goods->getSpecs($this->request->get['goods_id'], $filter_data);
        foreach ($data['specs'] as $spec_key => $spec) {
            if (!empty($spec['copyright_images'])) {
                $data['specs'][$spec_key]['copyright_images'] = json_decode($spec['copyright_images'],true);
            }
        }
        $total = $this->model_admin_goods->getSpecsTotal($this->request->get['goods_id']);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/goods/specs', 'token=' . $this->session->data['token'] . '&goods_id=' . $this->request->get['goods_id'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $this->response->setOutput($this->load->view('goods/archives_specs.tpl', $data));
    }

    public function specsCopyright() {
        if (!isset($this->request->get['goods_id'])) {
            $this->request->get['goods_id'] = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->config->set('config_limit', 10);

        $filter_data = array(
            'start'   => ($page - 1) * $this->config->get('config_limit'),
            'limit'   => $this->config->get('config_limit')
        );

        $this->load->model('admin/goods');
        $data['specs'] = $this->model_admin_goods->getSpecs($this->request->get['goods_id'], $filter_data);
        foreach ($data['specs'] as $spec_key => $spec) {
            if (!empty($spec['copyright_images'])) {
                $data['specs'][$spec_key]['copyright_images'] = json_decode($spec['copyright_images'],true);
            }
        }
        $total = $this->model_admin_goods->getSpecsTotal($this->request->get['goods_id']);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/goods/specs', 'token=' . $this->session->data['token'] . '&goods_id=' . $this->request->get['goods_id'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $this->response->setOutput($this->load->view('goods/archives_specs_copyright.tpl', $data));
    }

    public function addCopyright() {
        $json = array();

        if (empty($this->request->get['goods_id']) || empty($this->request->post['copyright_image']) || empty($this->request->post['spec_id'])) {
            $json['error'] = $this->language->get('error_input_empty');
        }

        if (!isset($json['error'])) {
            $del = !empty($this->request->post['del']) ? 1 : 0;
            $this->load->model('admin/goods');
            $this->model_admin_goods->setGoodDetailCopyright($this->request->post['spec_id'],$this->request->post['copyright_image'],$del);

            $json['success'] = $this->language->get('text_add_success');
        }

        $this->response->setOutJson($json);
    }

    public function specsCustom() {
        if (!isset($this->request->get['goods_id'])) {
            $this->request->get['goods_id'] = '';
        }

        $this->load->model('admin/goods');
        $data['specs'] = $this->model_admin_goods->getSpecs($this->request->get['goods_id']);

        $data['getSpecCustom'] = $this->url->link('admin/goods/getSpecCustom', 'token=' . $this->session->data['token']);
        $data['addSpecCustom'] = $this->url->link('admin/goods/addSpecCustom', 'token=' . $this->session->data['token']);

        $data['this_spec_id'] = 0;
        $data['this_spec_name'] = '';
        $data['this_spec_img'] = '';
        $data['this_spec_key_num'] = 0;
        $data['this_spec_custom'] = [];
        if (!empty($data['specs'])) {
            $data['this_spec_id'] = $data['specs'][0]['spec_id'];
            $data['this_spec_name'] = $data['specs'][0]['spec_name'];
            $data['this_spec_img'] = $data['specs'][0]['img_url'];
            $custom = $this->model_admin_goods->getSpecsCustom($data['specs'][0]['spec_id']);
            if (!empty($custom['custom_value'])) {
                $data['this_spec_custom'] = json_decode($custom['custom_value'],true);
                $data['this_spec_key_num'] = count($data['this_spec_custom']);
            }
        }
        $this->response->setOutput($this->load->view('goods/archives_specs_customt.tpl', $data));
    }

    public function getSpecCustom() {
        $json = [];
        $json['this_spec_name'] = '';
        $json['this_spec_img'] = '';
        $json['this_spec_custom'] = [];
        $json['this_spec_key_num'] = 0;
        if (!empty($this->request->post['spec_id'])) {
            $json['this_spec_id'] = $this->request->post['spec_id'];
            $this->load->model('admin/goods');
            $custom = $this->model_admin_goods->getSpecsCustom($this->request->post['spec_id']);
            $json['this_spec_name'] = $custom['spec_name'];
            $json['this_spec_img'] = $custom['img_url'];

            if (!empty($custom['custom_value'])) {
                $json['this_spec_custom'] = json_decode($custom['custom_value'],true);
                $json['this_spec_key_num'] = count($json['this_spec_custom']);
            }
        }

        $this->response->setOutJson($json);
    }

    public function addSpecCustom() {
        $json = [];

        if (!empty($this->request->post['spec_id'])) {
            $update_data['spec_id'] = $this->request->post['spec_id'];
            $update_data['custom_value'] = '';
            if (!empty($this->request->post['spec'][$update_data['spec_id']])) {
                $update_data['custom_value'] = json_encode(array_values($this->request->post['spec'][$update_data['spec_id']]),320);
            }
            $this->load->model('admin/goods');
            $this->model_admin_goods->setSpecsCustom($update_data);
            $json['success'] = $this->language->get('text_add_success');
        }

        $this->response->setOutJson($json);
    }

    public function addRecord() {
        if (!empty($this->request->get['goods_id'])) {
            $this->load->model('admin/goods');
            $this->load->model('admin/purchase');
            $goods = $this->model_admin_goods->getOneGoods($this->request->get['goods_id']);
            if (!empty($this->request->post['packaging']) && !empty($goods['goods_no'])) {
                $this->model_admin_purchase->setPurchaseContractContent($goods['goods_no'],1,$this->request->post['packaging']);
            }
            if (!empty($this->request->post['accessories']) && !empty($goods['goods_no'])) {
                $this->model_admin_purchase->setPurchaseContractContent($goods['goods_no'],3,$this->request->post['accessories']);
            }
            if (!empty($this->request->post['attention']) && !empty($goods['goods_no'])) {
                $this->model_admin_purchase->setPurchaseContractContent($goods['goods_no'],4,$this->request->post['attention']);
            }
            if (!empty($this->request->post['weight']) && !empty($goods['goods_no'])) {
                $this->model_admin_purchase->setPurchaseContractContent($goods['goods_no'],5,$this->request->post['weight']);
            }
            $this->response->redirect($this->url->link('admin/goods/getArchivesDetail', 'token=' . $this->session->data['token'] . '&goods_id=' . $this->request->get['goods_id']));
        }
    }

    public function addPackage() {
        $this->load->model('admin/goods');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validatePackageForm()) {
        	if (!empty($this->request->post['in_quan'])) {
        		$this->request->post['action'] = 'in' . $this->request->post['in_type'];
        		$this->request->post['quantity'] = $this->request->post['in_quan'];

        		$this->model_admin_goods->addPackage($this->request->post);
        	} elseif (!empty($this->request->post['out_quan'])) {
        		$this->request->post['action'] = 'out';
        		$this->request->post['quantity'] = $this->request->post['out_quan'];

        		$this->model_admin_goods->addPackage($this->request->post);
        	}

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_provider'])) {
				$url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_rule'])) {
	            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
	        }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }

            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getPackage();
    }

    public function getPackageUpload() {
        $data['action'] = $this->url->link('admin/goods/importPackage', 'token=' . $this->session->data['token']);
        $data['template'] = $this->url->link('admin/goods/templatePackage', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('goods/upload_package.tpl', $data));
    }

    public function importPackage() {
        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
        	$this->load->language('import');
            $this->load->model('admin/goods');
            $this->load->model('admin/excel');

            $upload_info = $this->model_admin_excel->upload('packages');

            if (isset($upload_info['name'])) {
                $import_datas = array();
                $provider_no = '';
                $upload_info['error'] = '';

                $import_total = 0;
                $import_total_success = 0;

                $excel_datas = $this->model_admin_excel->import($upload_info['name']);
                $titles = array_shift($excel_datas);

                if (implode(',', $titles) == '产品编码,出库数量') {
                    foreach ($excel_datas as $excel_data) {
                        if (isset($excel_data['A']) && trim($excel_data['A'])) {
                            $import_total++;
                            $import_datas[] = array(
                            	'bsku'  => $excel_data['A'],
                            	'quantity'  => $excel_data['B'],
                            );
                        }
                    }

                    $providers = $this->model_admin_goods->getProviders();

					foreach ($providers as $provider) {
						if ($this->user->real_name == $provider['provider_name']) {
							$provider_no = $provider['provider_no'];
						}
					}

					if (empty($provider_no)) {
			            $upload_info['error'] = '供应商不能为空！';
			        }
                } else {
                    $upload_info['error'] = $this->language->get('error_template');
                }

                @unlink($upload_info['name']);
            }

            if (isset($upload_info['error']) && $upload_info['error']) {
                $json['error'] = $upload_info['error'] . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
            } else {
                if (!empty($import_datas)) {
                    foreach ($import_datas as $import_data) {
                    	$import_data['provider_no'] = $provider_no;
                    	$import_data['action'] = 'out';

                        $this->model_admin_goods->addPackage($import_data);
                        $import_total_success++;
                    }

                    $json['success'] = $this->language->get('text_upload_success') . sprintf($this->language->get('text_upload_total'), $import_total, $import_total_success);
                } else {
                    $json['error'] = $this->language->get('error_upload');
                }
            }
        }

        $this->response->setOutJson($json);
    }

    public function templatePackage() {
        $file = DIR_DOWNLOAD . '包材出库模板.xlsx';

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));

        if (ob_get_level()) {
            ob_end_clean();
        }

        readfile($file, 'rb');

        exit();
    }

    public function getPackage() {
        if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_provider'])) {
			$filter_provider = $this->request->get['filter_provider'];
		} else {
			$filter_provider = '';
		}

		if (isset($this->request->get['filter_rule'])) {
            $filter_rule = $this->request->get['filter_rule'];
        } else {
            $filter_rule = 'allstock';
        }

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = 'date_modified';
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = 'DESC';
		}

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

        $data['add'] = $this->url->link('admin/goods/addPackage', 'token=' . $this->session->data['token'] . $url);
        $data['upload'] = $this->url->link('admin/goods/getPackageUpload', 'token=' . $this->session->data['token'] . $url);
        $data['detail'] = $this->url->link('admin/goods/getPackageDetail', 'token=' . $this->session->data['token'] . $url);

        $this->load->model('admin/goods');
		$data['providers'] = $this->model_admin_goods->getProviders();
		$data['rules'] = $this->model_admin_goods->getPackageRules();

		$providers = array();

		foreach ($data['providers'] as $provider) {
			$providers[$provider['provider_no']] = $provider['provider_name'];

			if ($this->user->real_name == $provider['provider_name']) {
				$this->request->get['filter_provider'] = $provider['provider_no'];
				$filter_provider = $provider['provider_no'];
				$data['isprovider'] = true;
			}
		}

		$data['packages'] = array();

        $filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_provider'	=> $filter_provider,
			'filter_rule'		=> $filter_rule,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );
       
        $results = $this->model_admin_goods->getPackages($filter_data);

        foreach ($results as $result) {
            $data['packages'][] = array(
                'bsku'      => $result['spec_no'],
                'name'      => $result['spec_name'],
                'img'   	=> $result['img_url'],
                'package_id'=> $result['package_id'],
                'provider'  => $providers[$result['provider_no']] ?? '',
                'pending'   => $result['pending_quan'],
                'boxstock'  => $result['boxstock_quan'],
                'packstock' => $result['packstock_quan'],
                'outbox'    => ((int)$result['outbox'] >= 0) ? $result['outbox'] : 0,
                'outpack'   => ((int)$result['outpack'] >= 0) ? $result['outpack'] : 0,
                'ispack'	=> ($result['ispack'] == '0') ? false : true,
                'modified'	=> $result['date_modified']
            );
        }

        $total = $this->model_admin_goods->getTotalPackages($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_provider'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=provider_no' . $url);
        $data['sort_bsku'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=spec_no' . $url);
        $data['sort_name'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=spec_name' . $url);
        $data['sort_pending'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=pending_quan' . $url);
        $data['sort_boxstock'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=boxstock_quan' . $url);
        $data['sort_packstock'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=packstock_quan' . $url);
        $data['sort_ispack'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=ispack' . $url);
        $data['sort_outbox'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=outbox' . $url);
        $data['sort_outpack'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=outpack' . $url);
        $data['sort_modified'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . '&sort=date_modified' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_provider'])) {
			$url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
		$data['filter_provider'] = $filter_provider;
		$data['filter_rule'] = $filter_rule;

        $data['nofilter'] = $this->url->link('admin/goods/getPackage', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('goods/package_list.tpl', $data));
    }

    public function getPackageDetail() {
        if (!isset($this->request->get['package_id'])) {
            $this->request->get['package_id'] = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $data['details'] = array();
        $this->config->set('config_limit', 10);

        $filter_data = array(
            'start'   => ($page - 1) * $this->config->get('config_limit'),
            'limit'   => $this->config->get('config_limit')
        );

        $this->load->model('admin/goods');        
        $results = $this->model_admin_goods->getPackageDetails($this->request->get['package_id'], $filter_data);

        foreach ($results as $result) {
            $data['details'][] = array(
                'bsku'   	=> $result['bsku'],
                'quantity'	=> $result['quantity'],
                'action' 	=> $result['action'],
                'date_added'=> $result['date_added']
            );
        }

        $total = $this->model_admin_goods->getTotalPackageDetails($this->request->get['package_id']);

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/goods/getPackageDetail', 'token=' . $this->session->data['token'] . '&package_id=' . $this->request->get['package_id'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $this->response->setOutput($this->load->view('goods/package_detail.tpl', $data));
    }

    public function labes() {
        if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}
        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $data['add'] = $this->url->link('admin/goods/labesSet', 'token=' . $this->session->data['token']);
        $data['delete'] = $this->url->link('admin/goods/grossProfitRateUserStoreDelete', 'token=' . $this->session->data['token']);

        $this->load->model('admin/goods');
        $data['labels'] = $this->model_admin_goods->getLabels();

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('goods/labels.tpl', $data));
    }

    public function labesSet() {
        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $this->load->model('admin/goods');

            if (isset($this->request->get['label_id'])) {
                $this->model_admin_goods->editLabel($this->request->get['label_id'],$this->request->post);
            } else {
                $this->model_admin_goods->addLabel($this->request->post);
            }

            $this->labes();
        } else {
            $url = '';
            $data['labels'] = [
                'name' => '',
                'tag' => []
            ];
            if (isset($this->request->get['label_id'])) {
                $data['label_id'] = $this->request->get['label_id'];
                $this->load->model('admin/goods');
                $data['labels'] = $this->model_admin_goods->getLabel($this->request->get['label_id']);

                $url .= '&label_id='.$this->request->get['label_id'];
            }

            if (isset($this->error['warning'])) {
                $data['warning'] = $this->error['warning'];
            } else {
                $data['warning'] = '';
            }

            $data['text_form'] = !isset($this->request->get['label_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
            $data['action'] = $this->url->link('admin/goods/labesSet', 'token=' . $this->session->data['token'] . $url);
            $data['cancel'] = $this->url->link('admin/goods/labes', 'token=' . $this->session->data['token']);

            $data['header'] = $this->load->controller('admin/template/header');
            $data['content_top'] = $this->load->controller('admin/template/top');
            $data['content_bottom'] = $this->load->controller('admin/template/bottom');
            $data['footer'] = $this->load->controller('admin/template/footer');
            $this->response->setOutput($this->load->view('goods/labes_form.tpl', $data));
        }

    }

    public function specsTag() {
        if (!isset($this->request->get['goods_id'])) {
            $this->request->get['goods_id'] = '';
        }

        $data['specs'] = [];
        $this->load->model('admin/goods');
        $goods = $this->model_admin_goods->getOneGoods($this->request->get['goods_id']);
        if (!empty($goods)) {
            $data['specs'][] = [
                'spec_id' => $goods['goods_id'],
                'spec_no' => $goods['goods_no'],
                'key' => 'goods',
            ];
        }
        $specs = $this->model_admin_goods->getSpecs($this->request->get['goods_id']);
        if (!empty($specs)) {
            foreach ($specs as $v) {
                $data['specs'][] = [
                    'spec_id' => $v['spec_id'],
                    'spec_no' => $v['spec_no'],
                    'key' => 'specs',
                ];
            }
        }

        $data['getSpecTag'] = $this->url->link('admin/goods/getSpecTag', 'token=' . $this->session->data['token']);
        $data['addSpecTag'] = $this->url->link('admin/goods/addSpecTag', 'token=' . $this->session->data['token']);

        $data['this_spec_id'] = 0;
        $data['this_spec_name'] = '';
        $data['this_spec_img'] = '';
        $data['this_key'] = '';
        if (!empty($goods)) {
            $data['this_spec_id'] = $data['specs'][0]['spec_id'];
            $data['this_spec_name'] = $data['specs'][0]['spec_no'];
            $data['this_spec_img'] = '';
            $data['this_key'] = 'goods';
        }

        $data['labels'] = $this->model_admin_goods->getLabelsArray(1);
        $this->response->setOutput($this->load->view('goods/archives_specs_tag.tpl', $data));
    }

    public function getSpecTag() {
        $json = [];
        $data['this_spec_id'] = 0;
        $data['this_spec_name'] = '';
        $data['this_spec_img'] = '';
        $data['this_key'] = 0;
        if (!empty($this->request->post['spec_id']) && !empty($this->request->post['key'])) {
            $this->load->model('admin/goods');
            if ($this->request->post['key'] == 'goods') {
                $goods = $this->model_admin_goods->getOneGoods($this->request->post['spec_id']);
                $json['this_spec_id'] = $goods['goods_id'];
                $json['this_spec_name'] = $goods['goods_name'];
                $json['this_key'] = 'goods';
            } else if ($this->request->post['key'] == 'specs') {
                $json['this_spec_id'] = $this->request->post['spec_id'];
                $tag = $this->model_admin_goods->getSpecsTag($this->request->post['spec_id']);
                $json['this_spec_name'] = $tag['spec_name'];
                $json['this_spec_img'] = $tag['img_url'];
                $json['this_key'] = 'specs';
                if ($tag['label_bindings']) {
                    $json['label_bindings'] = json_decode($tag['label_bindings']);
                }
            }

        }

        $this->response->setOutJson($json);
    }

    public function addSpecTag() {
        $json = [];

        if (!empty($this->request->post['spec_id']) && !empty($this->request->post['key']) && !empty($this->request->post['tag'])) {
            $this->load->model('admin/goods');
            if ($this->request->post['key'] == 'goods') {
                $specs = $this->model_admin_goods->getSpecs($this->request->post['spec_id']);
                if (!empty($specs)) {
                    foreach ($specs as $v) {
                        $update_data['spec_id'] = $v['spec_id'];
                        $update_data['spec_no'] = $v['spec_no'];
                        $update_data['label_bindings'] = json_encode(array_column($this->request->post['tag'],'value','name'),320);
                        $this->model_admin_goods->setSpecsTag($update_data);
                    }
                }
            } else if ($this->request->post['key'] == 'specs') {
                $update_data['spec_id'] = $this->request->post['spec_id'];
                $getSpec = $this->model_admin_goods->getSpec($this->request->post['spec_id']);
                $update_data['spec_no'] = $getSpec['spec_no'];
                $update_data['label_bindings'] = json_encode(array_column($this->request->post['tag'],'value','name'),320);
                $this->model_admin_goods->setSpecsTag($update_data);
            }

            $json['success'] = $this->language->get('text_add_success');
        }

        $this->response->setOutJson($json);
    }

    public function labesStatistics() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_row'])) {
            $filter_row = $this->request->get['filter_row'];
        } else {
            $filter_row = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['filter_least_labels'])) {
            $filter_least_labels = json_decode(html_entity_decode($this->request->get['filter_least_labels']), true);
        } else {
            $filter_least_labels = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->load->model('admin/goods');
        $labels = $this->model_admin_goods->getLabelsArray();
        $data['labels'] = [];
        foreach ($labels as $v) {
            $data['labels'][$v['label_id']] = $v;
        }
        $data['least_labels'] = count($data['labels']) - 1;

        $data['lists'] = [];
        $data['search'] = 0;
        if (isset($this->request->get['search'])) {
            $search_label = array_diff(array_column($labels,'label_id'),array_column($filter_least_labels,'name'));

            $data['lists'] = array();

            $filter_data = array(
                'filter_name'		=> $filter_name,
                'filter_row'		=> $filter_row,
                'filter_date_start'	=> $filter_date_start,
                'filter_date_end'	=> $filter_date_end,
                'filter_least_labels' => $filter_least_labels,
                'search_label'  => array_values($search_label)[0],
                'start'			=> ($page - 1) * $this->config->get('config_limit'),
                'limit'			=> $this->config->get('config_limit'),
            );

            $data['lists'] = $this->model_admin_goods->getGabesStatistics($filter_data);

            $data['search'] = 1;
        }

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['filter_name'] = $filter_name;
        $data['filter_row'] = $filter_row;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_least_labels'] = !empty($filter_least_labels) ? array_column($filter_least_labels,'value','name') : [];

        $data['nofilter'] = $this->url->link('admin/goods/labesStatistics', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('goods/labes_statistics.tpl', $data));
    }

    protected function validatePackageForm() {
		/*if (!$this->user->hasPermission('modify', 'admin/goods')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}*/

        $providers = $this->model_admin_goods->getProviders();

		foreach ($providers as $provider) {
			if ($this->user->real_name == $provider['provider_name']) {
				$this->request->post['provider_no'] = $provider['provider_no'];
			}
		}

		if (empty($this->request->post['provider_no'])) {
            $this->error['warning'] = '供应商不能为空！';
            return false;
        }

		if (empty($this->request->post['bsku'])) {
            $this->error['warning'] = '商品编码不能为空！';
            return false;
        }

		if (empty($this->request->post['in_quan']) && empty($this->request->post['out_quan'])) {
            $this->error['warning'] = '数量不能为空！';
            return false;
        }

		if (!empty($this->request->post['in_quan']) && empty($this->request->post['in_type'])) {
            $this->error['warning'] = '入库类型不能为空！';
            return false;
        }

		return !$this->error;
	}

    public function getInTransits() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_provider'])) {
            $filter_provider = $this->request->get['filter_provider'];
        } else {
            $filter_provider = '';
        }

        if (isset($this->request->get['filter_rule'])) {
            $filter_rule = $this->request->get['filter_rule'];
        } else {
            $filter_rule = 'allInTransit';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'total_lack_num';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['export'] = $this->url->link('admin/goods/exportInTransits', 'token=' . $this->session->data['token'] . $url);
        $data['loss_url'] = $this->url->link('admin/goods/getInTransitLoss', 'token=' . $this->session->data['token']);
        $data['stock_in_url'] = $this->url->link('admin/goods/getInTransitStockIn', 'token=' . $this->session->data['token']);

        $data['stocks'] = array();

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_provider'	=> $filter_provider,
            'filter_rule'		=> $filter_rule,
            'sort'			=> $sort,
            'order'			=> $order,
            'start'			=> ($page - 1) * $this->config->get('config_limit'),
            'limit'			=> $this->config->get('config_limit')
        );

        $this->load->model('admin/goods');
        $data['providers'] = $this->model_admin_goods->getProviders();
        $data['rules'] = $this->model_admin_goods->getInTransitRules();

        $in_transits = $this->model_admin_goods->getInTransits($filter_data);

        foreach ($in_transits as $in_transit_key => $in_transit) {
            $data['in_transits'][$in_transit_key] = $in_transit;
            $data['in_transits'][$in_transit_key]['total_loss_quantity'] = $this->model_admin_goods->getTotalLossQuantity($in_transit['spec_no'],$in_transit['provider_name']);;
        }

        $total = $this->model_admin_goods->getTotalinTransits($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_lack_num'] = $this->url->link('admin/goods/getInTransits', 'token=' . $this->session->data['token'] . '&sort=total_lack_num' . $url);
        $data['sort_stockin_num'] = $this->url->link('admin/goods/getInTransits', 'token=' . $this->session->data['token'] . '&sort=total_stockin_num' . $url);
        $data['sort_num'] = $this->url->link('admin/goods/getInTransits', 'token=' . $this->session->data['token'] . '&sort=total_num' . $url);
        $data['sort_loss_quantity'] = $this->url->link('admin/goods/getInTransits', 'token=' . $this->session->data['token'] . '&sort=total_loss_quantity' . $url);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_provider'])) {
            $url .= '&filter_provider=' . urlencode(html_entity_decode($this->request->get['filter_provider'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_rule'])) {
            $url .= '&filter_rule=' . urlencode(html_entity_decode($this->request->get['filter_rule'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/goods/getInTransits', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_provider'] = $filter_provider;
        $data['filter_rule'] = $filter_rule;

        $data['nofilter'] = $this->url->link('admin/goods/getInTransits', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('goods/in_transit_list.tpl', $data));
    }

    public function getInTransitLoss() {
        if (isset($this->request->get['spec_no'])) {
            $filter_spec_no = $this->request->get['spec_no'];
        } else {
            $filter_spec_no = '';
        }

        if (isset($this->request->get['provider_name'])) {
            $filter_provider_name = $this->request->get['provider_name'];
        } else {
            $filter_provider_name = '';
        }

        $data['loos_imgs'] = [];
        $this->load->model('admin/goods');
        $loss_imgs = $this->model_admin_goods->getInTransitLoss($filter_spec_no,$filter_provider_name);
        if (!empty($loss_imgs)) {
            foreach ($loss_imgs as $v) {
                $data['loos_imgs'] = array_merge($data['loos_imgs'],explode(',',$v['images']));
            }
        }

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('goods/in_transit_loss.tpl', $data));
    }

    public function getInTransitStockIn() {
        if (isset($this->request->get['spec_no'])) {
            $filter_spec_no = $this->request->get['spec_no'];
        } else {
            $filter_spec_no = '';
        }

        if (isset($this->request->get['provider_name'])) {
            $filter_provider_name = $this->request->get['provider_name'];
        } else {
            $filter_provider_name = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $this->load->model('admin/purchase');

        $data['warehouses'] = [];
        $warehouses = $this->model_admin_purchase->getPushWarehouseList();
        foreach ($warehouses as $v) {
            $data['warehouses'][$v['warehouse_no']] = $v['name'];
        }

        $data['providers'] = [];
        $providers = $this->model_admin_purchase->getPurchaseProviders();
        foreach ($providers as $v) {
            $data['providers'][$v['provider_no']] = $v['provider_name'];
        }

        $filter_data = array(
            'filter_name'   => $filter_spec_no,
            'filter_provider'  => $filter_provider_name,
            'filter_state'  => 1,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $this->load->model('admin/goods');
        $data['pushs'] = $this->model_admin_purchase->getPushs($filter_data);
        foreach ($data['pushs'] as $k => $v) {
            if (!empty($v['push_data'])) {
                $data['pushs'][$k]['push_data'] = json_decode($v['push_data'],true);
            }
        }

        $total = $this->model_admin_purchase->getTotalPushs($filter_data);

        $data['execute_detail'] = $this->model_admin_purchase->getCron();

        $data['push_insufficients'] = $this->model_admin_purchase->getPushInsufficients();

        $url = '';

        if (isset($this->request->get['spec_no'])) {
            $url .= '&spec_no=' . urlencode(html_entity_decode($this->request->get['spec_no'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['provider_name'])) {
            $url .= '&provider_name=' . urlencode(html_entity_decode($this->request->get['provider_name'], ENT_QUOTES, 'UTF-8'));
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/goods/getInTransitStockIn', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));



        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('goods/in_transit_stock_in.tpl', $data));
    }

    public function exportInTransits() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_provider'])) {
            $filter_provider = $this->request->get['filter_provider'];
        } else {
            $filter_provider = '';
        }

        if (isset($this->request->get['filter_rule'])) {
            $filter_rule = $this->request->get['filter_rule'];
        } else {
            $filter_rule = 'allInTransit';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'total_lack_num';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_provider'	=> $filter_provider,
            'filter_rule'		=> $filter_rule,
            'sort'			=> $sort,
            'order'			=> $order
        );

        $export_head = array('产品名称', '产品编码', '供应商', '未到货数量', '订单数', '破损数量', '订单详情');
        $export_data = array();
        $export_data[] = $export_head;

        $this->load->model('admin/goods');

        $in_transits = $this->model_admin_goods->getInTransits($filter_data);

        foreach ($in_transits as $in_transit) {
            $export_data[] = array(
                $in_transit['spec_name'],
                $in_transit['spec_no'],
                $in_transit['provider_name'],
                $in_transit['total_lack_num'],
                $in_transit['order_count'],
                $this->model_admin_goods->getTotalLossQuantity($in_transit['spec_no'],$in_transit['provider_name']),
                str_replace('<br>','；',$in_transit['combined_order_info']),
            );
        }


        if (!empty($export_data)) {
            $this->load->model('admin/excel');
            $this->model_admin_excel->export('在途库存表' . date('Y-m-d'), $export_data, array(3 => 'numbric', 4 => 'numbric'), '.xlsx');
        }
    }
}
