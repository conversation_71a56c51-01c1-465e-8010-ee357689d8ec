<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        关键字管理
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label>类别：</label>
                <div class="">
                  <input type="text" name="filter_name" value="<?php echo $filter_name; ?>" placeholder="请输入类别" id="filter_name" class="form-control pull-right" />
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>执行方式：</label>
                <select class="form-control" name="filter_type">
                  <option value="0">全部方式</option>
                  <option  <?php if (!empty($filter_type) && $filter_type==1) { ?>selected="selected"<?php } ?> value="1">全部符合</option>
                  <option  <?php if (!empty($filter_type) && $filter_type==2) { ?>selected="selected"<?php } ?>  value="2">一条符合</option>
                  <option  <?php if (!empty($filter_type) && $filter_type==3) { ?>selected="selected"<?php } ?>  value="3">没有条件</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">关键字列表</h3>
          <div class="box-tools">
            <a class="btn btn-primary" href="<?php echo $add; ?>">添加</a>
          </div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>类别</th>
              <th>执行方式</th>
              <th>关键字</th>
              <th>
                <?php if ($sort == 'sort') { ?>
                <a href="<?php echo $sort_url; ?>" class="<?php echo strtolower($order); ?>">权重</a>
                <?php } else { ?>
                <a href="<?php echo $sort_url; ?>">权重</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'export_sort') { ?>
                <a href="<?php echo $export_sort_url; ?>" class="<?php echo strtolower($order); ?>">导出权重</a>
                <?php } else { ?>
                <a href="<?php echo $export_sort_url; ?>">导出权重</a>
                <?php } ?>
              </th>
              <th>新建时间</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($keys)) { ?>
              <?php foreach ($keys as $key) { ?>
                <tr data-id="<?php echo $key['alipay_bill_sort_id']; ?>">
                  <td><?php echo $key['name']; ?></td>
                  <td><?php if($key['type'] == 1){ ?>全部符合<?php } else if($key['type'] == 2) { ?>一条符合<?php } else if($key['type'] == 3) { ?>没有条件<?php } ?></td>
                  <td>
                    <?php if($key['type'] != 3){ ?>
                      <?php foreach ($key['keyword_arr'] as $k => $v) { ?>
                        <?php if ($k != 0) { ?> <br> <?php } ?>
                        <?php echo $v; ?>
                      <?php } ?>
                    <?php } ?>
                  </td>
                  <td><?php echo $key['sort']; ?></td>
                  <td><?php echo $key['export_sort']; ?></td>
                  <td><?php echo $key['date_added']; ?></td>
                  <td class="text-right">
                    <a class="btn btn-success" href="<?php echo $key['edit_url']; ?>" title="" target="_blank">修改</a>
                    <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">删除</button>
                  </td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 删除 -->
      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-del">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除此类别吗？</p>
                <input id="del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();

      if (filter_name != '') {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_type = $('select[name=\'filter_type\']').val();

      if (filter_type != '*') {
        url += '&filter_type=' + encodeURIComponent(filter_type);
      }
      location.href = url;
    });
    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
  })()
</script>
<?php echo $footer; ?>