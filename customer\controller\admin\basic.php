<?php
class ControllerAdminBasic extends Controller {
    private $error = array();

    public function getGoods() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'goods_id';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $data['goods'] = array();

        $this->load->model('admin/basic');
        $results = $this->model_admin_basic->getWdtSpecs($filter_data);

        foreach ($results as $result) {
            $data['goods'][] = array(
                'img_url'   => $result['img_url'],
                'spec_name' => $result['spec_name'],
                'spec_no'   => $result['spec_no'],
                'wholesale1'=> $result['wholesale_price'],
                'wholesale2'=> $result['custom_price1'],
                'wholesale3'=> $result['custom_price2'],
                'weight'    => $result['weight'],
                'bat_size'  => $result['prop1'],
                'bat_quan'  => $result['prop2'],
                'box_size'  => $result['prop3'],
                'spec_size' => $result['prop5']
            );
        }

        $total = $this->model_admin_basic->getTotalSpecs($filter_data);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/basic/getGoods', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/basic/getGoods', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('basic/goods_list.tpl', $data));
    }

    public function addAuth() {
        $this->load->model('admin/basic');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateAuthForm()) {
            $this->model_admin_basic->addAuth($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/basic/getAuths', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getAuths();
    }

    public function getAuths() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/basic/addAuth', 'token=' . $this->session->data['token'] . $url);

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $data['auths'] = $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'];
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_store'  => $filter_store,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $this->load->model('admin/basic');
        $results = $this->model_admin_basic->getAuths($filter_data);

        foreach ($results as $result) {
            $data['auths'][] = array(
                'cardid'    => $result['cardid'],
                'realname'  => $result['realname'],
                'telephone' => $result['telephone'],
                'storename' => $stores[$result['store_id']] ?? '',
                'wholesale' => $result['wholesale'],
                'date_added'=> $result['date_added']
            );
        }

        $total = $this->model_admin_basic->getTotalAuths($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/basic/getAuths', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_store'] = $filter_store;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/basic/getAuths', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('basic/auth_list.tpl', $data));
    }

    public function getInfosAdd() {
        $this->load->model('admin/basic');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateInfoForm()) {
            $this->model_admin_basic->addInfo($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_category'])) {
                $url .= '&filter_category=' . $this->request->get['filter_category'];
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getInfosForm();
    }

    public function getInfosEdit() {
        $this->load->model('admin/basic');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && isset($this->request->get['design_id']) && $this->validateDeleteInfo()) {
            $this->model_admin_basic->editInfo($this->request->get['design_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_category'])) {
                $url .= '&filter_category=' . $this->request->get['filter_category'];
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getInfosForm();
    }

    public function getInfosForm() {
        $data['text_form'] = !isset($this->request->get['design_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_category'])) {
            $url .= '&filter_category=' . $this->request->get['filter_category'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        if (!isset($this->request->get['design_id'])) {
            $data['action'] = $this->url->link('admin/basic/getInfosAdd', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/basic/getInfosEdit', 'token=' . $this->session->data['token'] . '&design_id=' . $this->request->get['design_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['design_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $info = $this->model_admin_basic->getInfo($this->request->get['design_id']);
        }

        if (!empty($this->request->post['design_image'])) {
            $data['design_image'] = $this->request->post['design_image'];
        } elseif (!empty($info)) {
            $data['design_image'] = $info['design_image'];
        } else {
            $data['design_image'] = 'no_image.png';
        }

        if (isset($this->request->post['design_name'])) {
            $data['design_name'] = $this->request->post['design_name'];
        } elseif (!empty($info)) {
            $data['design_name'] = $info['design_name'];
        } else {
            $data['design_name'] = '';
        }

        if (isset($this->request->post['design_date'])) {
            $data['design_date'] = $this->request->post['design_date'];
        } elseif (!empty($info)) {
            $data['design_date'] = $info['design_date'];
        } else {
            $data['design_date'] = '';
        }

        if (isset($this->request->post['goods_no'])) {
            $data['goods_no'] = $this->request->post['goods_no'];
        } elseif (!empty($info)) {
            $data['goods_no'] = $info['goods_no'];
        } else {
            $data['goods_no'] = '';
        }

        if (isset($this->request->post['vote'])) {
            $data['vote'] = $this->request->post['vote'];
        } elseif (!empty($info)) {
            $data['vote'] = $info['vote'];
        } else {
            $data['vote'] = '0';
        }

        if (isset($this->request->post['warehousing_date'])) {
            $data['warehousing_date'] = $this->request->post['warehousing_date'];
        } elseif (!empty($info)) {
            $data['warehousing_date'] = $info['warehousing_date'];
        } else {
            $data['warehousing_date'] = '';
        }

        if (isset($this->request->post['category'])) {
            $data['categoryjson'] = json_encode($this->request->post['category']);
        } elseif (!empty($info)) {
            $data['categoryjson'] = json_encode(explode(',',$info['category']));
        } else {
            $data['categoryjson'] = '{}';
        }

        if (isset($this->request->post['store_ids'])) {
            $data['store_ids'] = $this->request->post['store_ids'];
        } elseif (!empty($info)) {
            $data['store_ids'] = explode(',',$info['store_ids']);
        } else {
            $data['store_ids'] = [];
        }

        if (isset($this->request->post['remark'])) {
            $data['remark'] = $this->request->post['remark'];
        } elseif (!empty($info)) {
            $data['remark'] = $info['remark'];
        } else {
            $data['remark'] = '';
        }

        if (isset($this->request->post['type'])) {
            $data['this_type'] = $this->request->post['type'];
        } elseif (!empty($info)) {
            $data['this_type'] = $info['type'];
        } else {
            $data['this_type'] = '1';
        }

        if (isset($this->request->post['designer'])) {
            $data['this_designer'] = $this->request->post['designer'];
        } elseif (!empty($info)) {
            $data['this_designer'] = $info['designer'];
        } else {
            $data['this_designer'] = '1';
        }

        if (isset($this->request->post['texture'])) {
            $data['this_texture'] = $this->request->post['texture'];
        } elseif (!empty($info)) {
            $data['this_texture'] = $info['texture'];
        } else {
            $data['this_texture'] = '1';
        }

        $types = $this->model_admin_basic->getTypes();
        $data['types'] = [];
        if (!empty($types['value'])) {
            $data['types'] = json_decode($types['value'],true);
        }

        $stylists = $this->model_admin_basic->getStylists();
        $data['stylists'] = [];
        if (!empty($stylists['value'])) {
            $data['stylists'] = json_decode($stylists['value'],true);
        }

        $textures = $this->model_admin_basic->getTextures();
        $data['textures'] = [];
        if (!empty($textures['value'])) {
            $data['textures'] = json_decode($textures['value'],true);
        }

        $data['customer_name'] = $this->user->real_name;
        $data['getToken'] = $this->url->link('admin/common/getUploadToken', 'token=' . $this->session->data['token']);

        $data['votes'] = $this->model_admin_basic->votes();

        $this->load->model('admin/purchase');
        $data['categories'] = $this->model_admin_purchase->getStoreClass();

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('basic/design_form.tpl', $data));
    }

    public function deleteInfo() {
        $this->load->model('admin/basic');

        if (isset($this->request->post['selected']) && $this->validateDeleteInfo()) {
            foreach ($this->request->post['selected'] as $design_id) {
                $this->model_admin_basic->deleteInfo($design_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_category'])) {
                $url .= '&filter_category=' . $this->request->get['filter_category'];
            }

            if (isset($this->request->get['filter_store'])) {
                $url .= '&filter_store=' . $this->request->get['filter_store'];
            }

            if (isset($this->request->get['filter_start'])) {
                $url .= '&filter_start=' . $this->request->get['filter_start'];
            }

            if (isset($this->request->get['filter_end'])) {
                $url .= '&filter_end=' . $this->request->get['filter_end'];
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getInfos();
    }

    public function getInfos() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_category'])) {
            $filter_category = $this->request->get['filter_category'];
        } else {
            $filter_category = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_category'])) {
            $url .= '&filter_category=' . $this->request->get['filter_category'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['customer_name'] = $this->user->real_name;

        $data['add'] = $this->url->link('admin/basic/getInfosAdd', 'token=' . $this->session->data['token'] . $url);
        $data['edit'] = $this->url->link('admin/basic/getInfosEdit', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/basic/deleteInfo', 'token=' . $this->session->data['token'] . $url);
        $data['getToken'] = $this->url->link('admin/common/getUploadToken', 'token=' . $this->session->data['token']);
        $data['setType'] = $this->url->link('admin/basic/getInfosType', 'token=' . $this->session->data['token'] . $url);
        $data['setStylist'] = $this->url->link('admin/basic/getInfosStylist', 'token=' . $this->session->data['token'] . $url);
        $data['setTexture'] = $this->url->link('admin/basic/getInfosTexture', 'token=' . $this->session->data['token'] . $url);
        $data['export_url'] = $this->url->link('admin/basic/getInfosExport', 'token=' . $this->session->data['token'] . $url);

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $data['infos'] = $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'] . '<br>';
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_category' => $filter_category,
            'filter_store'  => $filter_store,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $this->load->model('admin/purchase');
        $data['categories'] = $this->model_admin_purchase->getStoreClass();

        $this->load->model('admin/basic');
        $results = $this->model_admin_basic->getInfos($filter_data);

        $votes = $this->model_admin_basic->votes();
        $types = $this->model_admin_basic->getTypes();
        if (!empty($types['value'])) {
            $types = json_decode($types['value'],true);
        }

        $stylists = $this->model_admin_basic->getStylists();
        if (!empty($stylists['value'])) {
            $stylists = json_decode($stylists['value'],true);
        }

        $textures = $this->model_admin_basic->getTextures();
        if (!empty($textures['value'])) {
            $textures = json_decode($textures['value'],true);
        }

        foreach ($results as $result) {
            $storename = '';

            foreach ((array)explode(',', $result['store_ids']) as $store_id) {
                $storename .= $stores[$store_id] ?? '';
            }

            $reorder = 0;
            $warehousing_date = $result['warehousing_date'];
            $reorder_date = $result['reorder_date'];
            $reorder_day = (!empty($result['warehousing_date']) && !empty($result['reorder_date'])) ? $result['reorder_day'] : '';

            if (!empty($result['goods_no'])) {
                if (empty($result['warehousing_date']) || empty($result['reorder_date']) || $result['warehousing_date'] == '0000-00-00' || $result['reorder_date'] == '0000-00-00') {
                    $set_warehousing = $this->model_admin_basic->getWarehousing(substr($result['goods_no'], 0, -1),$result['design_id']);

                    if (!empty($set_warehousing)) {
                        $warehousing_date = $set_warehousing['first_date'];
                        $reorder_date = $set_warehousing['second_date'];
                        $reorder_day = $set_warehousing['date_diff'];
                    }
                }
            }

            if (!empty($reorder_day)) {
                $reorder = 1;
            }

            $data['infos'][] = array(
                'info_id'   => $result['design_id'],
                'image'     => $result['design_image'],
                'type'      => !empty($types[$result['type']]) ? $types[$result['type']] : '',
                'name'      => $result['design_name'],
                'design_date'=> $result['design_date'] == '0000-00-00' ? '' : $result['design_date'],
                'designer'  => !empty($stylists[$result['designer']]) ? $stylists[$result['designer']] : '',
                'texture'      => !empty($textures[$result['texture']]) ? $textures[$result['texture']] : '',
                'vote'      => !empty($votes[$result['vote']]) ? $votes[$result['vote']] : '',
                'reorder'   => $reorder,
                'warehousing_date'=> $warehousing_date == '0000-00-00' ? '' : $warehousing_date,
                'reorder_date'=> $reorder_date == '0000-00-00' ? '' :  $reorder_date,
                'reorder_day'=> ($warehousing_date == '0000-00-00' && $reorder_date == '0000-00-00') ? '' : $reorder_day,
                'remark'    => $result['remark'],
                'category'  => $result['category'],
                'storename' => $storename,
                'date_added'=> $result['date_added'],
                'delete'    => ($result['user_id'] == $this->user->user_id) ? true : false,
            );
        }

        $total = $this->model_admin_basic->getTotalInfos($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_category'])) {
            $url .= '&filter_category=' . $this->request->get['filter_category'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_category'] = $filter_category;
        $data['filter_store'] = $filter_store;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('basic/info_list.tpl', $data));
    }

    public function getInfosType() {
        $data['text_form'] =  $this->language->get('text_edit');

        $this->load->model('admin/basic');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && !empty($this->request->post['types'])) {
            $this->model_admin_basic->setTypes($this->request->post['types']);
        }

        $typess = $this->model_admin_basic->getTypes();
        $data['num'] = 0;
        $data['types'] = [];
        if (!empty($typess['value'])) {
            $data['types'] = json_decode($typess['value'],true);
            $data['num'] = count($data['types']);
        }
        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_category'])) {
            $url .= '&filter_category=' . $this->request->get['filter_category'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['action'] = $this->url->link('admin/basic/getInfosType', 'token=' . $this->session->data['token'] . $url);
        $data['cancel'] = $this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token'] . $url);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('basic/design_type.tpl', $data));
    }

    public function getInfosStylist() {
        $data['text_form'] =  $this->language->get('text_edit');

        $this->load->model('admin/basic');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && !empty($this->request->post['stylists'])) {
            $this->model_admin_basic->setStylists($this->request->post['stylists']);
        }

        $stylists = $this->model_admin_basic->getStylists();
        $data['num'] = 0;
        $data['stylists'] = [];
        if (!empty($stylists['value'])) {
            $data['stylists'] = json_decode($stylists['value'],true);
            $data['num'] = count($data['stylists']);
        }
        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_category'])) {
            $url .= '&filter_category=' . $this->request->get['filter_category'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['action'] = $this->url->link('admin/basic/getInfosStylist', 'token=' . $this->session->data['token'] . $url);
        $data['cancel'] = $this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token'] . $url);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('basic/design_stylist.tpl', $data));
    }

    public function getInfosTexture() {
        $data['text_form'] =  $this->language->get('text_edit');

        $this->load->model('admin/basic');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && !empty($this->request->post['textures'])) {
            $this->model_admin_basic->setTextures($this->request->post['textures']);
        }

        $textures = $this->model_admin_basic->getTextures();
        $data['num'] = 0;
        $data['textures'] = [];
        if (!empty($textures['value'])) {
            $data['textures'] = json_decode($textures['value'],true);
            $data['num'] = count($data['textures']);
        }
        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_category'])) {
            $url .= '&filter_category=' . $this->request->get['filter_category'];
        }

        if (isset($this->request->get['filter_store'])) {
            $url .= '&filter_store=' . $this->request->get['filter_store'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['action'] = $this->url->link('admin/basic/getInfosTexture', 'token=' . $this->session->data['token'] . $url);
        $data['cancel'] = $this->url->link('admin/basic/getInfos', 'token=' . $this->session->data['token'] . $url);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('basic/design_texture.tpl', $data));
    }

    public function getInfosExport() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_category'])) {
            $filter_category = $this->request->get['filter_category'];
        } else {
            $filter_category = '';
        }

        if (isset($this->request->get['filter_store'])) {
            $filter_store = $this->request->get['filter_store'];
        } else {
            $filter_store = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        $this->load->model('admin/setting');
        $data['stores'] = $this->model_admin_setting->getStores();

        $data['infos'] = $stores = array();

        foreach ($data['stores'] as $store) {
            $stores[$store['store_id']] = $store['name'] . '<br>';
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_category' => $filter_category,
            'filter_store'  => $filter_store,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
        );

        $this->load->model('admin/purchase');
        $data['categories'] = $this->model_admin_purchase->getStoreClass();

        $this->load->model('admin/basic');
        $results = $this->model_admin_basic->getInfos($filter_data);

        $votes = $this->model_admin_basic->votes();
        $types = $this->model_admin_basic->getTypes();
        if (!empty($types['value'])) {
            $types = json_decode($types['value'],true);
        }

        $stylists = $this->model_admin_basic->getStylists();
        if (!empty($stylists['value'])) {
            $stylists = json_decode($stylists['value'],true);
        }

        $textures = $this->model_admin_basic->getTextures();
        if (!empty($textures['value'])) {
            $textures = json_decode($textures['value'],true);
        }

        foreach ($results as $result) {
            $storename = '';

            foreach ((array)explode(',', $result['store_ids']) as $store_id) {
                $storename .= $stores[$store_id] ?? '';
            }

            $reorder = 0;
            $warehousing_date = $result['warehousing_date'];
            $reorder_date = $result['reorder_date'];
            $reorder_day = (!empty($result['warehousing_date']) && !empty($result['reorder_date'])) ? $result['reorder_day'] : '';

            if (!empty($result['goods_no'])) {
                if (empty($result['warehousing_date']) || empty($result['reorder_date']) || $result['warehousing_date'] == '0000-00-00' || $result['reorder_date'] == '0000-00-00') {
                    $set_warehousing = $this->model_admin_basic->getWarehousing(substr($result['goods_no'], 0, -1),$result['design_id']);

                    if (!empty($set_warehousing)) {
                        $warehousing_date = $set_warehousing['first_date'];
                        $reorder_date = $set_warehousing['second_date'];
                        $reorder_day = $set_warehousing['date_diff'];
                    }
                }
            }

            if (!empty($reorder_day)) {
                $reorder = 1;
            }

            $data['infos'][] = array(
                'info_id'   => $result['design_id'],
                'image'     => $result['design_image'],
                'type'      => !empty($types[$result['type']]) ? $types[$result['type']] : '',
                'name'      => $result['design_name'],
                'design_date'=> $result['design_date'] == '0000-00-00' ? '' : $result['design_date'],
                'designer'  => !empty($stylists[$result['designer']]) ? $stylists[$result['designer']] : '',
                'texture'      => !empty($textures[$result['texture']]) ? $textures[$result['texture']] : '',
                'vote'      => !empty($votes[$result['vote']]) ? $votes[$result['vote']] : '',
                'reorder'   => $reorder,
                'warehousing_date'=> $warehousing_date == '0000-00-00' ? '' : $warehousing_date,
                'reorder_date'=> $reorder_date == '0000-00-00' ? '' :  $reorder_date,
                'reorder_day'=> ($warehousing_date == '0000-00-00' && $reorder_date == '0000-00-00') ? '' : $reorder_day,
                'remark'    => $result['remark'],
                'category'  => $result['category'],
                'storename' => $storename,
                'date_added'=> $result['date_added'],
                'delete'    => ($result['user_id'] == $this->user->user_id) ? true : false,
            );
        }

        $this->load->helper('office/autoload');

        $styleArray_header = [
            'alignment' => [
                'horizontal' => 'center',
                'vertical' => 'center',
            ],
            'font' => [
                'name' => '宋体',
                'size' => 16,
                'bold' => true,
            ]
        ];

        $styleArray_default2 = [
            'alignment' => [
                'horizontal' => 'center',
                'vertical' => 'center',
                'wrapText' => true,
            ],
            'font' => [
                'name' => '宋体',
                'size' => 12,
                'color' => ['argb' => 'FFFFFF'],
                'bold' => true,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'argb' => '4483bc',
                ]
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '000000'],
                ],
            ],
        ];

        $styleArray_table_default = [
            'alignment' => [
                'horizontal' => 'center',
                'vertical' => 'center',
                'wrapText' => true,
            ],
            'font' => [
                'name' => '宋体',
                'size' => 12,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '000000'],
                ],
            ]
        ];

        $default_line_height = 30;

        // 创建Excel对象
        $phpexcel = new PhpOffice\PhpSpreadsheet\Spreadsheet();
        $phpexcel->getDefaultStyle()->getFont()->setName('宋体');
        $phpexcel->getDefaultStyle()->getFont()->setSize(12);
        $phpexcel->getActiveSheet()->getDefaultRowDimension()->setRowHeight($default_line_height);

        $n = 0;
        $n2 = 1;
        $phpexcel->createSheet();
        $phpexcel->setActiveSheetIndex($n);
        $phpexcel->getActiveSheet($n)->setTitle('产品翻单情况表');

        // 设置列宽
        $columns = ['A' => 9, 'B' => 20, 'C' => 9, 'D' => 33, 'E' => 10, 'F' => 15,
            'G' => 12, 'H' => 12, 'I' => 10, 'J' => 15, 'K' => 15, 'L' => 11, 'M' => 16];
        foreach ($columns as $col => $width) {
            $phpexcel->getActiveSheet($n)->getColumnDimension($col)->setWidth($width);
        }

        // 设置标题行
        $phpexcel->getActiveSheet($n)->mergeCells('A'.$n2.':M'.$n2);
        $phpexcel->getActiveSheet($n)->getStyle($n2)->applyFromArray($styleArray_header);
        $phpexcel->getActiveSheet($n)->getCell('A'.$n2)->setValue('研发图纸登记表');
        $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
        $n2 += 1;

        // 设置表头行
        $headers = [
            'A' => '序号', 'B' => '图片', 'C' => '类别', 'D' => '系列名',
            'E' => '材质', 'F' => '设计时间', 'G' => '设计师', 'H' => '投票情况',
            'I' => '6个月内是否翻单', 'J' => '入仓时间', 'K' => '翻单时间',
            'L' => '翻单天数', 'M' => '备注'
        ];

        foreach ($headers as $col => $value) {
            $phpexcel->getActiveSheet($n)->getCell($col.$n2)->setValue($value);
        }

        // 应用表头样式（一次性应用范围样式）
        $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':M'.$n2)->applyFromArray($styleArray_default2);
        $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight($default_line_height);
        $phpexcel->getActiveSheet($n)->setAutoFilter('A'.$n2.':M'.$n2); // 设置筛选
        $n2 += 1;

        $folderPath = DIR_DOWNLOAD.'/basic_info_image/'.$this->user->union_id;
        if (file_exists($folderPath)) {
            $this->deleteDir($folderPath);
        }
        mkdir($folderPath,0777,true);

        // 填充数据行
        if (!empty($data['infos'])) {
            foreach ($data['infos'] as $info) {
                $values = [
                    'A' => $info['info_id'],
                    'C' => $info['type'],
                    'D' => $info['name'],
                    'E' => $info['texture'],
                    'F' => $info['design_date'],
                    'G' => $info['designer'],
                    'H' => $info['vote'],
                    'I' => $info['reorder'] == 1 ? '是' : '',
                    'J' => $info['warehousing_date'],
                    'K' => $info['reorder_date'],
                    'L' => $info['reorder_day'],
                    'M' => $info['remark']
                ];

                $img_url = HTTP_IMAGE . $info['image'];
                $headers = @get_headers($img_url, 1); // 获取文件的HTTP头信息
                if ($headers && $headers[0] == 'HTTP/1.1 200 OK') {
                    $content_type = $headers['Content-Type']; // 从HTTP头中获取Content-Type字段
                    $file_type = explode('/', $content_type)[1]; // 获取文件类型部分

                    $destination = $folderPath.'/'.$info['info_id'].'.'.$file_type;
                    $file_content = file_get_contents($img_url);
                    file_put_contents($destination, $file_content);

                    $info_img = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                    $info_img->setName($info['info_id']);
                    $info_img->setDescription($info['name']);
                    $info_img->setPath($destination);
//                        $info_img->setWidth(80);
                    $info_img->setHeight(60);
                    $info_img->setCoordinates('B'.$n2);
                    $info_img->setOffsetX(25);
                    $info_img->setOffsetY(6);
                    $info_img->setWorksheet($phpexcel->getActiveSheet());
                }

                foreach ($values as $col => $value) {
                    $phpexcel->getActiveSheet($n)->getCell($col.$n2)->setValue($value);
                }

                // 应用数据行样式（一次性应用范围样式）
                $phpexcel->getActiveSheet($n)->getStyle('A'.$n2.':M'.$n2)->applyFromArray($styleArray_table_default);
                $phpexcel->getActiveSheet($n)->getRowDimension($n2)->setRowHeight(60);
                $n2 += 1;
            }
        }

        // 冻结前两行
        $phpexcel->getActiveSheet($n)->freezePane('A3');

        // 输出Excel文件
        $title = "研发图纸登记表";
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.$title.'.xlsx"');
        header('Cache-Control: max-age=0');
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($phpexcel, 'Xlsx');
        $objWriter->save('php://output');
        $this->deleteDir($folderPath);
        exit;

    }

    function deleteDir($dirPath) {
        if (!is_dir($dirPath)) {
            throw new InvalidArgumentException("$dirPath must be a directory");
        }
        if (substr($dirPath, strlen($dirPath) - 1, 1) != '/') {
            $dirPath .= '/';
        }
        $files = glob($dirPath . '*', GLOB_MARK);
        foreach ($files as $file) {
            if (is_dir($file)) {
                $this->deleteDir($file);
            } else {
                unlink($file);
            }
        }
        rmdir($dirPath);
    }

    public function getFreights() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }
        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/basic/getFreightsFrom', 'token=' . $this->session->data['token'] . $url);
        $data['affirm'] = $this->url->link('admin/basic/getFreightsAffirm', 'token=' . $this->session->data['token'] . $url);
        $data['action'] = $this->url->link('admin/basic/getFreights', 'token=' . $this->session->data['token'] . $url);

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_status' => $filter_status,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $this->load->model('admin/purchase');
        $data['categories'] = $this->model_admin_purchase->getStoreClass();

        $this->load->model('admin/basic');
        $data['freights'] = $this->model_admin_basic->getFreights($filter_data);

        $total = $this->model_admin_basic->getTotalFreights($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/basic/getFreights', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_status'] = $filter_status;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('admin/basic/getFreights', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('basic/freight_list.tpl', $data));
    }

    public function getFreightsFrom() {
        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['warning'] = '';
        $data['res_count'] = 0;
        $data['res_province'] = '';
        $freight_id = 0;

        $data['action'] = $this->url->link('admin/basic/getFreightsFrom', 'token=' . $this->session->data['token'] .$url);
        $data['cancel'] = $this->url->link('admin/basic/getFreights', 'token=' . $this->session->data['token'] .$url);

        $this->load->model('admin/basic');

        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            if (empty($this->request->post['order_no'])) {
                $data['warning'] = '订单号不能为空！';
            } else {
                $res = array_values($this->request->post['provinces']);
                $res_sum = [];
                $res_template = [];
                $status = 0;

                $expressFeeTemplate = $this->model_admin_basic->getExpressFeeTemplate();
                if (!empty($this->request->post['province'])) {
                    $status = 1;
                    foreach ($expressFeeTemplate as $template_key => $template) {
                        $res_template[$template['express_fee_template_id']] = $template;
                        $expressFeeStandard = $this->model_admin_basic->getExpressFeeStandard($template['express_fee_template_id'],date("d",time()),date("d",time()));

                        if (!empty($expressFeeStandard)) {
                            $weight = json_decode($expressFeeStandard['weight'],true);
                            $provinces = json_decode($expressFeeStandard['provinces'],true);
                            if (!empty($provinces)) {
                                foreach ($provinces as $province_key => $province) {
                                    if (!empty($province['province'])) {
                                        foreach ($province['province'] as $province_v) {
                                            if ($province_v == $this->request->post['province']) {
                                                foreach ($res as $key => $val) {
                                                    if (!empty($val['weight']) && !empty($val['long']) && !empty($val['wide']) && !empty($val['high'])) {
                                                        $calculate_weight = $val['weight'];
                                                        if ($expressFeeStandard['type'] == 1 && !empty($expressFeeStandard['volume_ratio'])) {//计算材积重量
                                                            $cj_weight = $val['long'] * $val['wide'] * $val['high'] / $expressFeeStandard['volume_ratio'];
                                                            if ($cj_weight>$val['weight']) {
                                                                $calculate_weight = $cj_weight;
                                                            }
                                                        }

                                                        if ($province['weight_type'] == 2) { //不使用重量区间
                                                            if ($calculate_weight > $expressFeeStandard['first_weight']) {
                                                                if ($province['starting_price_type'] == 2 && $calculate_weight>= $province['first_follow_weight']) {
                                                                    $province['starting_price'][0] = $province['starting_price'][1];
                                                                }
                                                                $price = $province['starting_price'][0] + $province['starting_price'][1] * (ceil(($calculate_weight-$expressFeeStandard['first_weight'])/$expressFeeStandard['continue_weight']));
                                                            } else {
                                                                $price = $province['starting_price'][0];
                                                            }
                                                        } else {
                                                            if ($calculate_weight >= $expressFeeStandard['starting_weight']) {//按首重续重计算价格
                                                                if ($province['starting_price_type'] == 2 && $calculate_weight>= $province['first_follow_weight']) {
                                                                    $province['starting_price'][0] = $province['starting_price'][1];
                                                                }
                                                                $price = $province['starting_price'][0] + $province['starting_price'][1] * (ceil(($calculate_weight-$expressFeeStandard['first_weight'])/$expressFeeStandard['continue_weight']));

                                                            } else {
                                                                foreach ($weight as $weight_k => $weight_v) {
                                                                    if (($calculate_weight >= $weight_v[0]) && ($calculate_weight <= $weight_v[1])) {
                                                                        $price = $province['price'][$weight_k];
                                                                        break;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                        if (!empty($val['num']) && (int)$val['num'] > 1) {
                                                            $price = round($price * $val['num'],2);
                                                        } else {
                                                            $price = round($price,2);
                                                        }
                                                        $res[$key][$template['express_fee_template_id']] = $price;
                                                        $res_sum[$template['express_fee_template_id']] = !empty($res_sum[$template['express_fee_template_id']]) ? round($res_sum[$template['express_fee_template_id']] + $price,2) : $price;
                                                    }

                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                $data['res'] = $add_data['res'] = $res;
                $data['res_count'] = count($res);
                $data['sum_weight'] = 0;
                $data['sum_volume'] = 0;
                $data['sum_num'] = 0;
                foreach ($data['res'] as $v) {
                    if (!empty($v['weight']) && !empty($v['long']) && !empty($v['wide']) && !empty($v['high'])) {
                        if (!empty($v['num']) && (int)$v['num'] > 1) {
                            $data['sum_weight'] += $v['weight'] * $v['num'];
                            $data['sum_volume'] += number_format($v['long'] * $v['wide'] * $v['high'], 2, '.', '') * $v['num'];
                            $data['sum_num'] += $v['num'];
                        } else {
                            $data['sum_weight'] += $v['weight'];
                            $data['sum_volume'] += number_format($v['long'] * $v['wide'] * $v['high'], 2, '.', '');
                            $data['sum_num'] += 1;
                        }
                    }
                }
                $data['sum_volume'] = number_format($data['sum_volume'] / 1000000, 6, '.', '');
                $data['res_sum'] = $add_data['res_sum'] = $res_sum;
                $data['template'] = $add_data['template'] = $res_template;
                $data['res_order_no'] = $add_data['order_no'] = $this->request->post['order_no'];
                $data['res_province'] = $add_data['province_id'] = $this->request->post['province'];
                $data['res_order_address'] = $add_data['order_address'] = $this->request->post['order_address'];
                $add_data['status'] = $status;

                if (empty($this->request->post['freight_id'])) {
                    $freight_id = $this->model_admin_basic->addFreight($add_data);
                } else {
                    $freight_id = $this->request->post['freight_id'];
                    $this->model_admin_basic->setFreight($add_data,$freight_id);
                }
            }
        } else if (!empty($this->request->get['freight_id'])) {
            $freight = $this->model_admin_basic->getFreight($this->request->get['freight_id']);
            $data['res'] = json_decode($freight['res'],true);
            $data['sum_weight'] = 0;
            $data['sum_volume'] = 0;
            $data['sum_num'] = 0;
            foreach ($data['res'] as $v) {
                if (!empty($v['weight']) && !empty($v['long']) && !empty($v['wide']) && !empty($v['high'])) {
                    if (!empty($v['num']) && (int)$v['num'] > 1) {
                        $data['sum_weight'] += $v['weight'] * $v['num'];
                        $data['sum_volume'] += number_format($v['long'] * $v['wide'] * $v['high'], 2, '.', '') * $v['num'];
                        $data['sum_num'] += $v['num'];
                    } else {
                        $data['sum_weight'] += $v['weight'];
                        $data['sum_volume'] += number_format($v['long'] * $v['wide'] * $v['high'], 2, '.', '');
                        $data['sum_num'] += 1;
                    }
                }
            }
            $data['sum_volume'] = number_format($data['sum_volume'] / 1000000, 6, '.', '');
            $data['res_count'] = count($data['res']);
            $data['res_sum'] = json_decode($freight['res_sum'],true);
            $data['template'] = json_decode($freight['template'],true);
            $data['res_order_no'] = $freight['order_no'];
            $data['res_province'] = $freight['province_id'];
            $data['res_order_address'] = $freight['order_address'];

            $freight_id = $this->request->get['freight_id'];
        }

        $this->load->model('admin/billcheck');
        $data['provinces'] = $this->model_admin_billcheck->getExpressFeeProvince();

        $data['freight_id'] = $freight_id;
        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('basic/freight.tpl', $data));
    }

    public function getFreightsAffirm() {
        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $this->load->model('admin/basic');

        if (isset($this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $freight_id) {
                $this->model_admin_basic->setFreightAffirm($freight_id);
            }
        }

        $this->response->redirect($this->url->link('admin/basic/getFreights', 'token=' . $this->session->data['token'] . $url));
    }

    protected function validateAuthForm() {
        if (!$this->user->hasPermission('modify', 'admin/basic')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['store_id']) || empty($this->request->post['cardid']) || empty($this->request->post['realname']) || empty($this->request->post['telephone']) || empty($this->request->post['wholesale'])) {
            $this->error['warning'] = '所有信息不能为空！';
            return false;
        }

        $this->load->model('goods');
        $auth_info = $this->model_goods->authentication($this->request->post['cardid'], $this->request->post['telephone']);

        if (!empty($auth_info)) {
            $this->error['warning'] = '身份证手机号重复记录！';
            return false;
        }

        return !$this->error;
    }

    protected function validateInfoForm() {
        if (!$this->user->hasPermission('modify', 'admin/basic')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['design_image'])) {
            $this->error['warning'] = '设计图不能为空！';
            return false;
        }

        if (empty($this->request->post['design_name'])) {
            $this->error['warning'] = '设计名称不能为空！';
            return false;
        }

        if (empty($this->request->post['designer'])) {
            $this->error['warning'] = '设计师不能为空！';
            return false;
        }

        return !$this->error;
    }

    protected function validateDeleteInfo() {
        if (!$this->user->hasPermission('modify', 'admin/basic')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }
}