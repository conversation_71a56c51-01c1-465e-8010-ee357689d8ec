<?php
class ModelAdminSheetnode extends Model {
    public function addNode($data) {
        $union_id = implode(',',$data['union_id']);
        $is_score = isset($data['is_score']) ? $data['is_score'] : 1;
        $sponsor_id = isset($data['sponsor_id']) ? $data['sponsor_id'] : 0;
        $sponsor_status = isset($data['sponsor_status']) ? $data['sponsor_status'] : 0;

        $this->db->query("INSERT INTO " . "rg_node SET flow_id = '" . $this->db->escape($data['flow_id']) . "', node_name = '" . $data['name'] .  "', sort = '" . (int)$data['sort'] . "', _union_id = '" . $union_id ."', create_time = '" . time() . "', day = '" . (int)$data['day'] . "', is_delete = '0', is_score = '" . (int)$is_score . "', sponsor_id = '" . (int)$sponsor_id . "', sponsor_status = '" . (int)$sponsor_status . "'");
 
        return $this->db->getLastId();
    }

    public function editNode($node_id, $data) {
        $union_id = implode(',',$data['union_id']);
        $is_score = isset($data['is_score']) ? $data['is_score'] : 1;
        $sponsor_id = isset($data['sponsor_id']) ? $data['sponsor_id'] : 0;
        $sponsor_status = isset($data['sponsor_status']) ? $data['sponsor_status'] : 0;

        $this->db->query("UPDATE " . "rg_node SET flow_id = '" . $this->db->escape($data['flow_id']) . "', node_name = '" . $data['name'] .  "', sort = '" . (int)$data['sort'] . "', _union_id = '" . $union_id ."', create_time = '" . time() . "', day = '" . (int)$data['day'] . "', is_delete = '0', is_score = '" . (int)$is_score . "', sponsor_id = '" . (int)$sponsor_id . "', sponsor_status = '" . (int)$sponsor_status . "' WHERE node_id = '" . (int)$node_id . "'");
    }

    public function deleteNode($node_id) {
        $this->db->query("UPDATE " . "rg_node SET is_delete = 1 WHERE node_id = '" . (int)$node_id . "'");
    }

    public function getFlowInfo($node_id) {
        $query = $this->db->query("SELECT * FROM " . "rg_node WHERE node_id = '" . (int)$node_id . "'");
        return $query->row;
    }

    public function userList($union_id = 0) {
        $where = 'status = 1 ';
        if($union_id) $where .=  " and union_id in($union_id) ";
        $query = $this->db->query("SELECT * FROM " . "_union WHERE status = 1");
        return $query->rows;
    }

    public function getNodes($data = array(), $sheet_id = 0)
    {
        $sql = "SELECT * FROM rg_node WHERE is_delete = 0";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  node_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }


        if (!empty($data['filter_date_start'])) {
            $sql .= " AND create_time >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND create_time <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }

        if (!empty($data['filter_flow_id'])) {
            $sql .= " AND flow_id = '" . $this->db->escape($data['filter_flow_id']) . "'";
        }


        $sort_data = array(
            'node_name',
            'node_sort',
            'create_time'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY sort";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }


        $query = $this->db->query($sql);

        return $query->rows;
    }


    public function getFlowList() {
        $query = $this->db->query("SELECT * FROM rg_flow WHERE is_delete = 0");
        return $query->rows;
    }

    public function getTotalNodes($data)
    {
        $sql = "SELECT COUNT(*) AS total FROM  rg_node WHERE is_delete = '0'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  node_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }


        if (!empty($data['filter_date_start'])) {
            $sql .= " AND create_time >= '" . $this->db->escape(strtotime($data['filter_date_start'])) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND create_time <= '" . $this->db->escape(strtotime($data['filter_date_end'])) . "'";
        }

        if (!empty($data['filter_flow_id'])) {
            $sql .= " AND flow_id = '" . $this->db->escape($data['filter_flow_id']) . "'";
        }


        $query = $this->db->query($sql);

        return $query->row['total'];
    }




 
} 