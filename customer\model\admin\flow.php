<?php
class ModelAdminFlow extends Model {
	public function getProduces($data = array()) {
		$sql = "SELECT workflow_id, user_id, workflow_name, state, start_time FROM wf_workflow WHERE flow_id = '6' AND state > '0'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND workflow_id IN (SELECT wn.workflow_id FROM wf_worknode_field wf LEFT JOIN wf_worknode wn ON (wf.worknode_id = wn.worknode_id) WHERE wf.latest = '1' AND wf.field_code IN ('product_name', 'order_no') AND wf.field_value LIKE '%" . $this->db->escape($data['filter_name']) . "%')";
		}

		if (!empty($data['filter_provider'])) {
			$sql .= " AND user_id = '" . (int)$data['filter_provider'] . "'";
		}

		if (!empty($data['filter_state'])) {
			$sql .= " AND state = '" . (int)$data['filter_state'] . "'";
		}

		if (!empty($data['filter_date_start'])) {
            $sql .= " AND start_time >= '" . (int)strtotime($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND start_time <= '" . (int)strtotime($data['filter_date_end']) . "'";
        }
		
		$sql .= " ORDER BY start_time ASC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int) $data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalProduces($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM wf_workflow WHERE flow_id = '6' AND state > '0'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND workflow_id IN (SELECT wn.workflow_id FROM wf_worknode_field wf LEFT JOIN wf_worknode wn ON (wf.worknode_id = wn.worknode_id) WHERE wf.latest = '1' AND wf.field_code IN ('product_name', 'order_no') AND wf.field_value LIKE '%" . $this->db->escape($data['filter_name']) . "%')";
		}

		if (!empty($data['filter_provider'])) {
			$sql .= " AND user_id = '" . (int)$data['filter_provider'] . "'";
		}

		if (!empty($data['filter_state'])) {
			$sql .= " AND state = '" . (int)$data['filter_state'] . "'";
		}

		if (!empty($data['filter_date_start'])) {
            $sql .= " AND start_time >= '" . (int)strtotime($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND start_time <= '" . (int)strtotime($data['filter_date_end']) . "'";
        }

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function getWorkFields($workflow_ids) {
		$fields = array();

		$query = $this->db->query("SELECT wn.workflow_id, wf.worknode_id, wf.field_code, wf.field_value FROM wf_worknode_field wf LEFT JOIN wf_worknode wn ON (wf.worknode_id = wn.worknode_id) WHERE wf.latest = '1' AND FIND_IN_SET(wn.workflow_id, '" . implode(',', $workflow_ids) . "') ORDER BY wf.workfield_id ASC");

		foreach ($query->rows as $row) {
			$fields[$row['workflow_id']][$row['field_code']] = $row['field_value'];
		}

		return $fields;
	}

	public function getWorkSubmit($workflow_ids) {
		$submit = array();

		$query = $this->db->query("SELECT workflow_id, fields FROM cr_worknode_submit WHERE status = '0' AND FIND_IN_SET(workflow_id, '" . implode(',', $workflow_ids) . "') ORDER BY submit_id ASC");

		foreach ($query->rows as $row) {
			foreach ((array)json_decode($row['fields'], true) as $code => $value) {
				$submit[$row['workflow_id']][$code] = $value;
			}
		}

		return $submit;
	}
}
