<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        事项提醒
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-success">
        <div class="box-body">
          <div class="panel panel-primary">
            <div class="panel-heading">员工司日提醒</div>
            <?php if (!empty($jobages)) { ?>
            <ul class="list-group">
              <?php foreach ($jobages as $jobage) { ?>
              <li class="list-group-item"><a href="<?php echo $action . '&action=jobage&employee_id=' . $jobage['employee_id']; ?>">
                <?php echo $jobage['job_department']; ?><strong><?php echo $jobage['fullname']; ?></strong><?php echo $jobage['job_work_date']; ?>入职，当前工龄<?php echo load_class('model_admin_employee')->getAge('%s年%s个月', $jobage['job_work_date']); ?>，司日是<?php echo $jobage['job_age_date']; ?>，<?php echo ($jobage['diff_days'] > 0 ? '还剩下' : '已超过') . abs($jobage['diff_days']); ?>天
              </a></li>
              <?php } ?>
            </ul>
            <?php } else { ?>
            <div class="panel-body">暂无提醒</div>
            <?php } ?>
          </div>
          <div class="panel panel-warning">
            <div class="panel-heading">员工转正提醒</div>
            <?php if (!empty($regulars)) { ?>
            <ul class="list-group">
              <?php foreach ($regulars as $regular) { ?>
              <li class="list-group-item"><a href="<?php echo $action . '&action=regular&employee_id=' . $regular['employee_id']; ?>">
                <?php echo $regular['job_department']; ?><strong><?php echo $regular['fullname']; ?></strong><?php echo $regular['job_work_date']; ?>入职，将于<?php echo $regular['job_estimate_date']; ?>转正，<?php echo ($regular['diff_days'] > 0 ? '还剩下' : '已超过') . abs($regular['diff_days']); ?>天
              </a></li>
              <?php } ?>
            </ul>
            <?php } else { ?>
            <div class="panel-body">暂无提醒</div>
            <?php } ?>
          </div>
          <div class="panel panel-success">
            <div class="panel-heading">劳动合同提醒</div>
            <?php if (!empty($contracts)) { ?>
            <ul class="list-group">
              <?php foreach ($contracts as $contract) { ?>
              <li class="list-group-item"><a href="<?php echo $action . '&action=contract&employee_id=' . $contract['employee_id']; ?>">
                <?php echo $contract['job_department']; ?><strong><?php echo $contract['fullname']; ?></strong><?php echo $contract['job_work_date']; ?>入职，
                <?php if (!empty($contract['expire'])) { ?>
                合同到期日是<?php echo $contract['expire']; ?>，<?php echo ($contract['diff_days'] > 0 ? '还剩下' : '已超过') . abs($contract['diff_days']); ?>天
                <?php } else { ?>
                还没有签订合同
                <?php } ?>
              </a></li>
              <?php } ?>
            </ul>
            <?php } else { ?>
            <div class="panel-body">暂无提醒</div>
            <?php } ?>
          </div>
          <div class="panel panel-danger">
            <div class="panel-heading">保险增减提醒</div>
            <?php if (!empty($insurances)) { ?>
            <ul class="list-group">
              <?php foreach ($insurances as $insurance) { ?>
              <li class="list-group-item"><a href="<?php echo $action . '&action=insurance&employee_id=' . $insurance['employee_id']; ?>">
                <?php echo $insurance['job_department']; ?><strong><?php echo $insurance['fullname']; ?></strong><?php echo $insurance['job_work_date']; ?>入职，
                <?php if ($insurance['job_status'] == '正式') { ?>
                转正日期是<?php echo $insurance['job_regular_date']; ?>，保险还未增员
                <?php } else { ?>
                离职日期是<?php echo $insurance['job_leave_date']; ?>，<?php echo $insurance['insurance_name']; ?>还未减员
                <?php } ?>
              </a></li>
              <?php } ?>
            </ul>
            <?php } else { ?>
            <div class="panel-body">暂无提醒</div>
            <?php } ?>
          </div>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>