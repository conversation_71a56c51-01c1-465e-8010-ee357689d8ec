<?php
class ModelAdminCustomer extends Model {
    public function addCustomer($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "customer SET customer_group_id = '" . (int)$data['customer_group_id'] . "', store_id = '" . (int)$data['store_id'] . "', nickname = '" . $this->db->escape($data['nickname']) . "', realname = '" . $this->db->escape($data['realname']) . "', telephone = '" . $this->db->escape($data['telephone']) . "', address = '" . $this->db->escape($data['address']) . "', channel = '" . $this->db->escape($data['channel']) . "', product = '" . $this->db->escape($data['product']) . "', state = '" . $this->db->escape($data['state']) . "', remark = '" . $this->db->escape($data['remark']) . "', order_times = '0', order_total = '0', date_first = NULL, date_last = NULL, shop_name = '', shop_url = '', shop_platform = '', order_last = '0', date_added = NOW(), date_modified = NOW()");

        $customer_id = $this->db->getLastId();

        if (!empty($data['order_total'])) {
            if (!empty($data['order_date']) && strtotime($data['order_date'])) {
                $order_date = date($this->language->get('datetime_format'), strtotime($data['order_date']));
            } else {
                $order_date = date($this->language->get('date_format'), strtotime('-1 day'));
            }

            $this->addOrder(array('customer_id' => $customer_id, 'order_times' => 1, 'order_total' => $data['order_total'], 'order_date' => $order_date, 'order_product' => ''));
        }
    }

    public function editCustomer($customer_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "customer SET customer_group_id = '" . (int)$data['customer_group_id'] . "', store_id = '" . (int)$data['store_id'] . "', nickname = '" . $this->db->escape($data['nickname']) . "', realname = '" . $this->db->escape($data['realname']) . "', telephone = '" . $this->db->escape($data['telephone']) . "', address = '" . $this->db->escape($data['address']) . "', channel = '" . $this->db->escape($data['channel']) . "', product = '" . $this->db->escape($data['product']) . "', state = '" . $this->db->escape($data['state']) . "', remark = '" . $this->db->escape($data['remark']) . "', date_modified = NOW() WHERE customer_id = '" . (int)$customer_id . "'");
    }

    public function editCustomerShop($data) {
        $this->db->query("UPDATE " . DB_PREFIX . "customer SET shop_name = '" . $this->db->escape($data['shop_name']) . "', shop_url = '" . $this->db->escape($data['shop_url']) . "', shop_platform = '" . $this->db->escape($data['shop_platform']) . "' WHERE customer_id = '" . (int)$data['customer_id'] . "'");
    }

    public function getCustomer($customer_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer WHERE customer_id = '" . (int)$customer_id . "'");

        return $query->row;
    }
    
    public function getRepeatCustomer($store_id, $nickname) {
        $query = $this->db->query("SELECT customer_id FROM " . DB_PREFIX . "customer WHERE store_id = '" . (int)$store_id . "' AND nickname = '" . $this->db->escape($nickname) . "'");

        return $query->row;
    }

    public function editOrderGroup() {
        $sql = "SELECT c.customer_id, c.customer_group_id, (SELECT customer_group_id FROM " . DB_PREFIX . "customer_group WHERE store_id = c.store_id AND order_total <= c.order_total ORDER BY order_total DESC LIMIT 1) AS order_group_id FROM " . DB_PREFIX . "customer c WHERE order_total > '0'";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        $query = $this->db->query($sql);

        foreach ($query->rows as $row) {
            if ($row['order_group_id'] && $row['customer_group_id'] != $row['order_group_id']) {
                $this->db->query("UPDATE " . DB_PREFIX . "customer SET customer_group_id = '" . (int)$row['order_group_id'] . "' WHERE customer_id = '" . (int)$row['customer_id'] . "'");
            }
        }
    }

    public function getCustomers($data) {
        $sql = "SELECT * FROM " . DB_PREFIX . "customer WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(nickname, realname, telephone, shop_name) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_group']) && $data['filter_group'] !== '') {
            $sql .= " AND customer_group_id = '" . (int)$data['filter_group'] . "'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_channel'])) {
            $sql .= " AND channel = '" . $this->db->escape($data['filter_channel']) . "'";
        }

        if (!empty($data['filter_product'])) {
            $sql .= " AND product LIKE '%" . $this->db->escape($data['filter_product']) . "%'";
        }

        if (!empty($data['filter_state'])) {
            $sql .= " AND state = '" . $this->db->escape($data['filter_state']) . "'";
        }

        if (!empty($data['filter_first_start'])) {
            $sql .= " AND DATE(date_first) >= '" . $this->db->escape($data['filter_first_start']) . "'";
        }

        if (!empty($data['filter_first_end'])) {
            $sql .= " AND DATE(date_first) <= '" . $this->db->escape($data['filter_first_end']) . "'";
        }

        if (!empty($data['filter_last_start'])) {
            $sql .= " AND DATE(date_last) >= '" . $this->db->escape($data['filter_last_start']) . "'";
        }

        if (!empty($data['filter_last_end'])) {
            $sql .= " AND DATE(date_last) <= '" . $this->db->escape($data['filter_last_end']) . "'";
        }

        $sort_data = array(
            'customer_id',
            'customer_group_id',
            'store_id',
            'nickname',
            'realname',
            'telephone',
            'channel',
            'product',
            'order_total',
            'state',
            'date_first',
            'date_last',
            'date_added'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY customer_id";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalCustomers($data) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(nickname, realname, telephone) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_group']) && $data['filter_group'] !== '') {
            $sql .= " AND customer_group_id = '" . (int)$data['filter_group'] . "'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_channel'])) {
            $sql .= " AND channel = '" . $this->db->escape($data['filter_channel']) . "'";
        }

        if (!empty($data['filter_product'])) {
            $sql .= " AND product LIKE '%" . $this->db->escape($data['filter_product']) . "%'";
        }

        if (!empty($data['filter_state'])) {
            $sql .= " AND state = '" . $this->db->escape($data['filter_state']) . "'";
        }

        if (!empty($data['filter_first_start'])) {
            $sql .= " AND DATE(date_first) >= '" . $this->db->escape($data['filter_first_start']) . "'";
        }

        if (!empty($data['filter_first_end'])) {
            $sql .= " AND DATE(date_first) <= '" . $this->db->escape($data['filter_first_end']) . "'";
        }

        if (!empty($data['filter_last_start'])) {
            $sql .= " AND DATE(date_last) >= '" . $this->db->escape($data['filter_last_start']) . "'";
        }

        if (!empty($data['filter_last_end'])) {
            $sql .= " AND DATE(date_last) <= '" . $this->db->escape($data['filter_last_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function getSummary($data) {
        $sql = DB_PREFIX . "customer";
        $where = array();

        if ($this->user->store_ids) {
            $where[] = "FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_store'])) {
            $where[] = "store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($where)) {
            $sql .= " WHERE " . implode(' AND ', $where);
        }

        if ($data['filter_report'] == 'group') {
            $sql = "SELECT tmp.*, (SELECT group_name FROM " . DB_PREFIX . "customer_group WHERE customer_group_id = tmp.customer_group_id) AS gname FROM (SELECT customer_group_id, COUNT(customer_id) AS quantity, SUM(order_total) AS total FROM " . $sql . " GROUP BY customer_group_id) AS tmp";
        } elseif ($data['filter_report'] == 'store') {
            $sql = "SELECT tmp.*, (SELECT name FROM " . DB_PREFIX . "store WHERE store_id = tmp.store_id) AS gname FROM (SELECT store_id, COUNT(customer_id) AS quantity, SUM(order_total) AS total FROM " . $sql . " GROUP BY store_id) AS tmp";
        } else {
            $sql = "SELECT " . $data['filter_report'] . " AS gname, COUNT(customer_id) AS quantity, SUM(order_total) AS total FROM " . $sql . " GROUP BY " . $data['filter_report'];
        }

        $option = array(
            'tooltip'   => array('trigger' => 'item', 'formatter' => '{a} <br/>{b} : {c} ({d}%)'),
            'legend'    => array('orient' => 'horizontal', 'left' => 'left', 'top' => 50),
            'series'    => array(array('name' => '客户数量', 'type' => 'pie', 'radius' => '50%', 'center' => array('25%', '50%'), 'data' => array(), 'emphasis' => array('itemStyle' => array('shadowBlur' => 10, 'shadowOffsetX' => 0, 'shadowColor' => 'rgba(0, 0, 0, 0.5)'))), array('name' => '下单金额', 'type' => 'pie', 'radius' => '50%', 'center' => array('75%', '50%'), 'data' => array(), 'emphasis' => array('itemStyle' => array('shadowBlur' => 10, 'shadowOffsetX' => 0, 'shadowColor' => 'rgba(0, 0, 0, 0.5)'))))
        );

        $query = $this->db->query($sql);

        if (!empty($query->rows)) {
            $summary = array('quantity' => 0, 'total' => 0);

            foreach ($query->rows as $row) {
                if (empty($row['gname'])) {
                    $gname = '其他';
                } else {
                    $gname = $row['gname'];
                }

                $option['series'][0]['data'][] = array(
                    'name'  => $gname,
                    'value' => $row['quantity']
                );

                $option['series'][1]['data'][] = array(
                    'name'  => $gname,
                    'value' => $row['total']
                );

                $summary['quantity'] += (int)$row['quantity'];
                $summary['total'] += moneyformat($row['total']);
            }

            $option['title']['text'] = '客户总数量：' . $summary['quantity'] . '，下单总金额：' . $summary['total'];
            $option['title']['left'] = 'center';

            return $option;
        } else {
            return false;
        }
    }

    public function getReportType() {
        return array('group' => '按客户级别统计', 'channel' => '按销售渠道统计', 'state' => '按客户状态统计', 'store' => '按店铺统计');
    }

    public function getChannels() {
        $sql = "SELECT channel FROM " . DB_PREFIX . "customer";

        if ($this->user->store_ids) {
            $sql .= " WHERE FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        $sql .= " GROUP BY channel";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getProducts() {
        $sql = "SELECT product FROM " . DB_PREFIX . "customer";

        if ($this->user->store_ids) {
            $sql .= " WHERE FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        $sql .= " GROUP BY product";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getStates() {
        $sql = "SELECT state FROM " . DB_PREFIX . "customer";

        if ($this->user->store_ids) {
            $sql .= " WHERE FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        $sql .= " GROUP BY state";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function addOrder($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "customer_order SET customer_id = '" . (int)$data['customer_id'] . "', order_times = '" . (int)$data['order_times'] . "', order_total = '" . (float)$data['order_total'] . "', order_date = '" . $this->db->escape($data['order_date']) . "', order_product = '" . $this->db->escape($data['order_product']) . "', date_added = NOW()");

        $sql = "UPDATE " . DB_PREFIX . "customer SET order_times = order_times + '" . (int)$data['order_times'] . "', order_total = order_total + '" . (float)$data['order_total'] . "', order_last = '" . (float)$data['order_total'] . "'";

        $customer_info = $this->getCustomer($data['customer_id']);

        if (!empty($customer_info)) {
            if (empty($customer_info['date_first']) || strtotime($customer_info['date_first']) > strtotime($data['order_date'])) {
                $sql .= ", date_first = '" . $this->db->escape($data['order_date']) . "'";
            }

            if (empty($customer_info['date_last']) || strtotime($customer_info['date_last']) < strtotime($data['order_date'])) {
                $sql .= ", date_last = '" . $this->db->escape($data['order_date']) . "'";
            }

            $query = $this->db->query("SELECT customer_group_id FROM " . DB_PREFIX . "customer_group WHERE store_id = '" . (int)$customer_info['store_id'] . "' AND order_total <= '" . ((float)$customer_info['order_total'] + (float)$data['order_total']) . "' ORDER BY order_total DESC LIMIT 1");

            if (!empty($query->row) && ($query->row['customer_group_id'] != $customer_info['customer_group_id'])) {
                $sql .= ", customer_group_id = '" . (int)$query->row['customer_group_id'] . "'";
            }
        }

        $sql .= " WHERE customer_id = '" . (int)$data['customer_id'] . "'";

        $this->db->query($sql);
    }

    public function getLastOrder($customer_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_order WHERE customer_id = '" . (int)$customer_id . "' ORDER BY order_date DESC LIMIT 1");

        return $query->row;
    }

    public function getOrders($customer_id, $data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "customer_order WHERE customer_id = '" . (int)$customer_id . "' ORDER BY order_date DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalOrders($customer_id) {
        $query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_order WHERE customer_id = '" . (int)$customer_id . "'");

        return $query->row['total'];
    }

    public function getOrderSummary($customer_id) {
        $option = array(
            'tooltip'   => array('trigger' => 'axis', 'axisPointer' => array('type' => 'cross', 'crossStyle' => array('color' => '#999'))),
            'legend'    => array('data' => array('下单次数', '下单金额')),
            'xAxis'     => array('type' => 'category', 'data' => array(), 'axisPointer' => array('type' => 'shadow')),
            'yAxis'     => array(array('type' => 'value', 'name' => '下单次数', 'alignTicks' => true), array('type' => 'value', 'name' => '下单金额', 'alignTicks' => true, 'axisLabel' => array('formatter' => '{value} 元'))),
            'series'    => array(array('name' => '下单次数', 'type' => 'bar', 'barMaxWidth' => 80, 'data' => array()), array('name' => '下单金额', 'type' => 'bar', 'yAxisIndex' => 1, 'barMaxWidth' => 80, 'data' => array()))
        );

        $query = $this->db->query("SELECT YEAR(order_date) AS oyear, SUM(order_times) AS otimes, SUM(order_total) AS ototal FROM " . DB_PREFIX . "customer_order WHERE customer_id = '" . (int)$customer_id . "' GROUP BY YEAR(order_date) ORDER BY oyear");

        if (!empty($query->rows)) {
            foreach ($query->rows as $row) {
                $option['xAxis']['data'][] = $row['oyear'];
                $option['series'][0]['data'][] = $row['otimes'];
                $option['series'][1]['data'][] = $row['ototal'];
            }

            return $option;
        } else {
            return false;
        }
    }

    public function addGroup($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "customer_group SET group_name = '" . $this->db->escape($data['group_name']) . "', order_total = '" . (float)$data['order_total'] . "', store_id = '" . (int)$data['store_id'] . "', date_added = NOW()");
    }

    public function editGroup($customer_group_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "customer_group SET group_name = '" . $this->db->escape($data['group_name']) . "', order_total = '" . (float)$data['order_total'] . "' WHERE customer_group_id = '" . (int)$customer_group_id . "'");
    }

    public function deleteGroup($customer_group_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "customer SET customer_group_id = '0' WHERE customer_group_id = '" . (int)$customer_group_id . "'");

        $this->db->query("DELETE FROM " . DB_PREFIX . "customer_group WHERE customer_group_id = '" . (int)$customer_group_id . "'");
    }

    public function getGroup($customer_group_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_group WHERE customer_group_id = '" . (int)$customer_group_id . "'");

        return $query->row;
    }

    public function getGroupByStore($store_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_group WHERE store_id = '" . (int)$store_id . "'");

        return $query->rows;
    }

    public function getGroups($data = array()) {
        $sql = "SELECT cg.*, (SELECT name FROM " . DB_PREFIX . "store WHERE store_id = cg.store_id) AS storename FROM " . DB_PREFIX . "customer_group cg";

        if ($this->user->store_ids) {
            $sql .= " WHERE FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalGroups() {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_group";

        if ($this->user->store_ids) {
            $sql .= " WHERE FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function addSales($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "customer_sales SET store_id = '" . (int)$data['store_id'] . "', order_no = '" . $this->db->escape($data['order_no']) . "', customer = '" . $this->db->escape($data['customer']) . "', contact = '" . $this->db->escape($data['contact']) . "', sales_manage = '" . $this->db->escape($data['sales_manage']) . "', sales_date = '" . $this->db->escape($data['sales_date']) . "', delivery_date = '" . $this->db->escape($data['delivery_date']) . "', sales_total = '" . (float)$data['sales_total'] . "', sales_paid = '0', paid_date = NULL, state = '0', date_added = NOW()");

        return $this->db->getLastId();
    }

    public function editSales($sales_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "customer_sales SET store_id = '" . (int)$data['store_id'] . "', order_no = '" . $this->db->escape($data['order_no']) . "', customer = '" . $this->db->escape($data['customer']) . "', contact = '" . $this->db->escape($data['contact']) . "', sales_manage = '" . $this->db->escape($data['sales_manage']) . "', sales_date = '" . $this->db->escape($data['sales_date']) . "', delivery_date = '" . $this->db->escape($data['delivery_date']) . "', sales_total = '" . (float)$data['sales_total'] . "' WHERE sales_id = '" . (int)$sales_id . "'");
    }

    public function getSales($sales_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_sales WHERE sales_id = '" . (int)$sales_id . "'");

        return $query->row;
    }

    public function getSalesList($data) {
        $sql = "SELECT * FROM " . DB_PREFIX . "customer_sales WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(order_no, customer, contact, sales_manage) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_state'])) {
            $sql .= " AND state = '" . $this->db->escape($data['filter_state']) . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(sales_date) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(sales_date) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $sort_data = array(
            'store_id',
            'order_no',
            'customer',
            'contact',
            'sales_manage',
            'sales_total',
            'sales_paid',
            'sales_date',
            'paid_date',
            'delivery_date',
            'state',
            'date_added'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY sales_id";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalSales($data) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_sales WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND CONCAT(order_no, customer, contact, sales_manage) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (isset($data['filter_store']) && $data['filter_store'] !== '') {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_state'])) {
            $sql .= " AND state = '" . $this->db->escape($data['filter_state']) . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(sales_date) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(sales_date) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function addSalesBill($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "customer_sales_bill SET sales_id = '" . (int)$data['sales_id'] . "', bill_paid = '" . (float)$data['bill_paid'] . "', bill_date = '" . $this->db->escape($data['bill_date']) . "', bill_account = '" . $this->db->escape($data['bill_account']) . "', date_added = NOW()");

        $this->db->query("UPDATE " . DB_PREFIX . "customer_sales SET sales_paid = sales_paid + '" . (float)$data['bill_paid'] . "', paid_date = '" . $this->db->escape($data['bill_date']) . "', state = '1' WHERE sales_id = '" . (int)$data['sales_id'] . "'");

        $this->db->query("UPDATE " . DB_PREFIX . "customer_sales SET state = '2' WHERE sales_id = '" . (int)$data['sales_id'] . "' AND sales_paid >= sales_total");
    }

    public function getSalesBills($sales_id, $data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "customer_sales_bill WHERE sales_id = '" . (int)$sales_id . "' ORDER BY bill_date DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getTotalSalesBills($sales_id) {
        $query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_sales_bill WHERE sales_id = '" . (int)$sales_id . "'");

        return $query->row['total'];
    }

    public function getSalesStates() {
        return [0 => '未付款', 1 => '部分付款', 2 => '已付全款'];
    }
}
