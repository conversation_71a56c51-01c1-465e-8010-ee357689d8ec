<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        库存借调
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>产品名称编码：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="产品名称编码" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>借调方式：</label>
                  <select class="form-control" name="filter_mode">
                    <option value="*">全部方式</option>
                    <?php if ($filter_mode == 'out') { ?>
                    <option value="out" selected="selected">从申请店铺调出到目标店铺</option>
                    <?php } else { ?>
                    <option value="out">从申请店铺调出到目标店铺</option>
                    <?php } ?>
                    <?php if ($filter_mode == 'in') { ?>
                    <option value="in" selected="selected">从目标店铺调出到申请店铺</option>
                    <?php } else { ?>
                    <option value="in">从目标店铺调出到申请店铺</option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>申请状态：</label>
                  <select class="form-control" name="filter_state">
                    <option value="*">全部状态</option>
                    <?php if ($filter_state === '0') { ?>
                    <option value="0" selected="selected">待审核</option>
                    <?php } else { ?>
                    <option value="0">待审核</option>
                    <?php } ?>
                    <?php if ($filter_state == '1') { ?>
                    <option value="1" selected="selected">已同意</option>
                    <?php } else { ?>
                    <option value="1">已同意</option>
                    <?php } ?>
                    <?php if ($filter_state == '2') { ?>
                    <option value="2" selected="selected">不同意</option>
                    <?php } else { ?>
                    <option value="2">不同意</option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>申请时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header">
          <h3 class="box-title">借调列表</h3>

          <div class="box-tools">
            <a class="btn btn-primary" href="<?php echo $add; ?>">申请借调</a>
          </div>
        </div>
        <!-- /.box-header -->
        <div class="box-body table-responsive no-padding">
          <table class="table table-hover table-striped">
            <tbody><tr>
              <th>借调方式</th>
              <th>编码</th>
              <th>名称</th>
              <th>数量</th>
              <th>申请状态</th>
              <th>申请时间</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($transfers)) { ?>
            <?php foreach ($transfers as $transfer) { ?>
            <tr data-id="<?php echo $transfer['transfer_id']; ?>">
              <td><?php echo ($transfer['mode'] == 'out' ? '从<b>' . $transfer['fromstore'] . '</b>调出到<b>' . $transfer['tostore'] . '</b>' : '从<b>' . $transfer['tostore'] . '</b>调出到<b>' . $transfer['fromstore'] . '</b>'); ?></td>
              <td><?php echo $transfer['bsku']; ?></td>
              <td><?php echo $transfer['spec_name']; ?></td>
              <td><?php echo $transfer['quan']; ?></td>
              <td><?php if ($transfer['state'] == '0') { ?>待审核
                <?php } elseif ($transfer['state'] == '1') { ?>已同意
                <?php } elseif ($transfer['state'] == '2') { ?>不同意
                <?php } else { ?>未知
                <?php } ?></td>
              <td><?php echo $transfer['date_added']; ?></td>
              <td class="text-right">
                <?php if ($transfer['state'] == '0') { ?>
                <?php if ($transfer['operator']) { ?>
                <button class="btn btn-primary" type="button" data-toggle="modal" data-target="#action-modal">审核</button>
                <?php } ?>
                <?php if ($transfer['creater']) { ?>
                <a class="btn btn-success" href="<?php echo $transfer['edit']; ?>" title="">修改</a>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">删除</button>
                <?php } ?>
                <?php } ?>
              </td>
            </tr>
            <?php } ?>
            <?php } else{ ?>
            <td colspan="7" align="center"> 暂无借调记录 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
        <!-- /.box-body -->
      </div>
      <!-- 审核 -->
      <div class="modal modal-primary fade" id="action-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-action">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">确定同意库存借调申请吗？</h4>
              </div>
              <div class="modal-body">
                <div class="radio"><label><input type="radio" name="state" value="1"> 同意</label></div>
                <div class="radio"><label><input type="radio" name="state" value="2"> 不同意</label></div>
                <input id="action-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">关闭</button>
                <button id="action-yes" type="button" class="btn btn-outline">提交</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
      <!-- 删除 -->
      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-del">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除此记录吗？</p>
                <input id="del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_mode = $('select[name=\'filter_mode\']').val();
  
      if (filter_mode != '*') {
        url += '&filter_mode=' + encodeURIComponent(filter_mode);
      }

      var filter_state = $('select[name=\'filter_state\']').val();
  
      if (filter_state != '*') {
        url += '&filter_state=' + encodeURIComponent(filter_state);
      }
  
      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });

    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})

    $('#action-modal').on('show.bs.modal', function(event) {
      $('#action-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#action-yes').on('click', () => {$('#form-action').submit()})
  })()
</script>
<?php echo $footer; ?>