<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        破损表
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-2">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索供应商/商家编码/名称/订单号/客户ID/所在地" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>责任原因：</label>
                  <select class="form-control" name="filter_reason">
                    <option value="*">全部原因</option>
                    <?php foreach($reasons as $reason) { ?>
                    <?php if ($reason['name'] == $filter_reason) { ?>
                    <option value="<?php echo $reason['name']; ?>" selected="selected"><?php echo $reason['name']; ?></option>
                    <?php } else{ ?>
                    <option value="<?php echo $reason['name']; ?>"><?php echo $reason['name']; ?></option>
                    <?php } ?>
                      <?php foreach($reason['subs'] as $sub) { ?>
                      <?php $subname = $reason['name'] . '-' . $sub; ?>
                      <?php if ($subname == $filter_reason) { ?>
                      <option value="<?php echo $subname; ?>" selected="selected"><?php echo $subname; ?></option>
                      <?php } else{ ?>
                      <option value="<?php echo $subname; ?>"><?php echo $subname; ?></option>
                      <?php } ?>
                      <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>处理方案：</label>
                  <select class="form-control" name="filter_solution">
                    <option value="*">全部状态</option>
                    <?php foreach($solutions as $solution) { ?>
                    <?php if ($solution == $filter_solution) { ?>
                    <option value="<?php echo $solution; ?>" selected="selected"><?php echo $solution; ?></option>
                    <?php } else{ ?>
                    <option value="<?php echo $solution; ?>"><?php echo $solution; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach ($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>记录人：</label>
                  <select class="form-control" name="filter_user">
                    <option value="*">全部记录人</option>
                    <?php foreach($users as $user_id => $real_name) { ?>
                    <?php if ($user_id == $filter_user) { ?>
                    <option value="<?php echo $user_id; ?>" selected="selected"><?php echo $real_name; ?></option>
                    <?php } else{ ?>
                    <option value="<?php echo $user_id; ?>"><?php echo $real_name; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label>记录时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right" id="reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right" id="reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start-time" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end-time" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <a class="btn btn-danger" href="<?php echo $export; ?>"><i class="glyphicon glyphicon-download-alt"></i> 下载数据</a>
          <a class="btn btn-success" href="<?php echo $import; ?>"><i class="glyphicon glyphicon-upload"></i> 导入数据</a>

          <div class="pull-right">
            <label class="checkbox-inline"><input type="checkbox" name="filter_reshipno" value="1"<?php if ($filter_reshipno == '1') { ?> checked="checked"<?php } ?>> 待更新补发单号 </label>
            <button type="button" id="button-filter" class="btn bg-purple"><i class="glyphicon glyphicon-search"></i> 筛选</button>
          </div>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">破损列表</p>
          <div class="box-tools">
            <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
          </div>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th style="max-width: 200px;">图片</th>
              <th>
                <?php if ($sort == 'responsible') { ?>
                  <a href="<?php echo $sort_respon; ?>" class="<?php echo strtolower($order); ?>">责任原因</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_respon; ?>">责任原因</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'bsku') { ?>
                  <a href="<?php echo $sort_bsku; ?>" class="<?php echo strtolower($order); ?>">破损产品</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_bsku; ?>">破损产品</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'supplier') { ?>
                  <a href="<?php echo $sort_supplier; ?>" class="<?php echo strtolower($order); ?>">供应商</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_supplier; ?>">供应商</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'buyer_location') { ?>
                  <a href="<?php echo $sort_location; ?>" class="<?php echo strtolower($order); ?>">买家所在地</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_location; ?>">买家所在地</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'solution') { ?>
                  <a href="<?php echo $sort_solution; ?>" class="un<?php echo strtolower($order); ?>">处理方案</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_solution; ?>">处理方案</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'allcost') { ?>
                  <a href="<?php echo $sort_cost; ?>" class="<?php echo strtolower($order); ?>">产品总成本</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_cost; ?>">产品总成本</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'user_id') { ?>
                  <a href="<?php echo $sort_user; ?>" class="<?php echo strtolower($order); ?>">记录人</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_user; ?>">记录人</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'date_added') { ?>
                  <a href="<?php echo $sort_date; ?>" class="<?php echo strtolower($order); ?>">记录时间</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_date; ?>">记录时间</a>
                <?php } ?>
              </th>
              <th>备注</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($losses)) { ?>
            <?php foreach ($losses as $loss) { ?>
            <tr data-id="<?php echo $loss['loss_id']; ?>">
              <td><ul class="list-inline">
                <?php foreach($loss['images'] as $img) { ?>
                <li><a href="<?php echo HTTP_IMAGE . $img; ?>" target="_blank"><img width="50" src="<?php echo HTTP_IMAGE . $img; ?>?imageView2/1/w/100" class="img-thumbnail" data-toggle="popover" data-content="<img src='<?php echo HTTP_IMAGE . $img; ?>' width='480'>"></a></li>
                <?php } ?>
              </ul></td>
              <td><?php echo $loss['responsible']; ?>-<?php echo $loss['reason']; ?></td>
              <td>
                <?php echo $loss['pname']; ?><br>
                <?php echo $loss['bsku']; ?><br>
                数量: <?php echo $loss['quantity']; ?>
              </td>
              <td><?php echo $loss['supplier']; ?></td>
              <td><?php echo $loss['location']; ?></td>
              <td class="h4 no-margin">
                <span class="label label-primary"><?php echo $loss['solution']; ?></span>
              </td>
              <td><?php echo $loss['allcost']; ?></td>
              <td><?php echo $loss['username']; ?></td>
              <td><?php echo $loss['date_added']; ?></td>
              <td><?php echo $loss['remark']; ?></td>
              <td class="text-right">
                <a class="btn btn-info" href="<?php echo $loss['view']; ?>" title="" target="_blank">查看详情</a>
                <a class="btn btn-success" href="<?php echo $loss['edit']; ?>" title="">修改</a>
                <?php if ($loss['responsible'] == '快递责任') { ?>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">删除</button>
                <?php } ?>
              </td>
            </tr>
            <?php } ?>
            <?php } else{ ?>
            <td colspan="10" align="center"> 暂无破损数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 删除 -->
      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-del">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">删除</h4>
              </div>
              <div class="modal-body">
                <p>确定删除此记录吗？</p>
                <input id="del-id" name="selected[]" type="hidden" value="">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="del-yes" type="button" class="btn btn-outline">是</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after, .table a.undesc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after, .table a.unasc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    $('[data-toggle="popover"]').popover({html:true, placement:'auto right', trigger: 'hover', template: '<div class="popover" style="max-width: 640px;"><div class="popover-content"></div></div>'});
    // 日期筛选
    $('#reservation').daterangepicker({
      autoUpdateInput: false,
      daterangepicker: true,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('#reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start-time').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end-time').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('#reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start-time').val('')
      $('#filter-end-time').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_reason = $('select[name=\'filter_reason\']').val();

      if (filter_reason != '*') {
        url += '&filter_reason=' + encodeURIComponent(filter_reason);
      }

      var filter_solution = $('select[name=\'filter_solution\']').val();

      if (filter_solution != '*') {
        url += '&filter_solution=' + encodeURIComponent(filter_solution);
      }

      var filter_user = $('select[name=\'filter_user\']').val();

      if (filter_user != '*') {
        url += '&filter_user=' + encodeURIComponent(filter_user);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_reshipno = $('input[name=\'filter_reshipno\']:checked').val();

      if (filter_reshipno) {
        url += '&filter_reshipno=' + encodeURIComponent(filter_reshipno);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });

    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
  })()
</script>
<?php echo $footer; ?>