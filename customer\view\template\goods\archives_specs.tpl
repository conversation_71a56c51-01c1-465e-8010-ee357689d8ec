<div class="table-responsive">
  <table class="table table-bordered table-hover table-striped">
    <thead>
      <tr>
        <th style="width: 120px" class="text-left">图片</th>
        <th style="width: 300px" class="text-left">产品名称/编码</th>
        <th class="text-left">重量</th>
        <th class="text-left">价格</th>
        <th style="width: 250px" class="text-left">规格尺寸</th>
      </tr>
    </thead>
    <tbody>
      <?php if ($specs) { ?>
      <?php foreach ($specs as $spec) { ?>
      <tr data-id="<?php echo $spec['spec_id']; ?>">
        <td class="text-left"><img width="100" src="<?php echo $spec['img_url']; ?>" class="img-thumbnail"></td>
        <td class="text-left"><?php echo $spec['spec_name']; ?><br><?php echo $spec['spec_no']; ?></td>
        <td class="text-left"><br><?php echo $spec['weight']; ?></td>
        <td class="text-left">最低价：<?php echo $spec['lowest_price']; ?><br>零售价：<?php echo $spec['retail_price']; ?><br>批发价：<?php echo $spec['wholesale_price']; ?>
          <br>会员价：<?php echo $spec['member_price']; ?><br>市场价：<?php echo $spec['market_price']; ?></td>
        <td class="text-left">外箱规格：<?php echo $spec['prop1']; ?><br>装箱数：<?php echo $spec['prop2']; ?><br>内盒尺寸：<?php echo $spec['prop3']; ?>
          <br>美工：<?php echo $spec['prop4']; ?><br>产品尺寸：<?php echo $spec['prop5']; ?><br>分销价：<?php echo $spec['prop6']; ?></td>
      </tr>
      <?php } ?>
      <?php } else { ?>
      <tr>
        <td class="text-center" colspan="5">暂无记录</td>
      </tr>
      <?php } ?>
    </tbody>
  </table>
</div>
<div class="flex ai__c jc__sb">
  <div><?php echo $results; ?></div>
  <?php echo $pagination; ?>
</div>
