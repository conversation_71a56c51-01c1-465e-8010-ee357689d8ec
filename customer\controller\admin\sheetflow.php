<?php


class ControllerAdminSheetflow extends Controller
{
    private $error = array();

    public function flowList()
    {
        $this->load->model('admin/sheetflow');

        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'create_time';
        }


        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

   

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }


        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        $data['add'] = $this->url->link('admin/sheetflow/sheetflow_add', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/sheetflow/sheetflow_del', 'token=' . $this->session->data['token'] . $url);




        $filter_data = array(
            'filter_name' => $filter_name,
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'sort'              => $sort,
            'order'             => $order,
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );


        $results = $this->model_admin_sheetflow->getFlows($filter_data);

        foreach ($results as $k => $result) {
            $data['column'][$k] = array(
                'id' => $result['flow_id'],
                'name' => $result['flow_name'],
                'sort' => $result['flow_sort'],
                'entry' => date('Y-m-d H:i:s', $result['create_time']),
                'edit' => $this->url->link('admin/sheetflow/sheetflow_edit', 'token=' . $this->session->data['token'] . '&flow_id=' . $result['flow_id'] . $url),
                'image' => $this->url->link('admin/sheetflow/image', 'token=' . $this->session->data['token'] . '&flow_id=' . $result['flow_id'] . $url),
            );
        }


        $total = $this->model_admin_sheetflow->getTotalFlows($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['sheet_id'])) {
            $url .= '&sheet_id=' . urlencode(html_entity_decode($this->request->get['sheet_id'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }





        $data['sort_name'] = $this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token'] . '&sort=name' . $url);
        $data['sort_createTime'] = $this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token'] . '&sort=createTime' . $url);
        $data['sort_sort'] = $this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token'] . '&sort=sort' . $url);
        $data['sort_type'] = $this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token'] . '&sort=' . $url);


        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('sheetflow/sheetflow_list.tpl', $data));
    }

    public function sheetflow_add()
    {
        $this->load->model('admin/sheetflow');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateFlowForm()) {
            $this->model_admin_sheetflow->addFlow($this->request->post);

            $this->session->data['success'] = '添加成功';

            $url = '';


            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getFlowForm();
    }

    public function sheetflow_edit()
    {

        $this->load->model('admin/sheetflow');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateFlowForm($this->request->get['flow_id'])) {
            $this->model_admin_sheetflow->editFlow($this->request->get['flow_id'], $this->request->post);


            $this->session->data['success'] = $this->language->get('编辑成功');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }


            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getFlowForm();
    }

    public function sheetflow_del()
    {
        $this->load->model('admin/sheetflow');

        if (isset($this->request->post['selected']) ) {
            foreach ($this->request->post['selected'] as $flow_id) {
                $this->model_admin_sheetflow->deleteFlow(intval($flow_id));
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';
    

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->flowList();
    }


    protected function getFlowForm()
    {

        $this->load->model('admin/sheetflow');
        $data['text_form'] = !isset($this->request->get['flow_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }
  

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }


        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        if (!isset($this->request->get['flow_id'])) {
            $data['action'] = $this->url->link('admin/sheetflow/sheetflow_add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/sheetflow/sheetflow_edit', 'token=' . $this->session->data['token'] . '&flow_id=' . $this->request->get['flow_id'] . $url);
        }


        $data['cancel'] = $this->url->link('admin/sheetflow/flowList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['flow_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $flow_info = $this->model_admin_sheetflow->getFlowInfo($this->request->get['flow_id']);
        }


        if (isset($this->request->post['name'])) {
            $data['name'] = $this->request->post['name'];
        } elseif (!empty($flow_info)) {
            $data['name'] = $flow_info['flow_name'];
        } else {
            $data['name'] = '';
        }
        

        if (isset($this->request->post['sort'])) {
            $data['sort'] = $this->request->post['sort'];
        } elseif (!empty($flow_info)) {
            $data['sort'] = $flow_info['flow_sort'];
        } else {
            $data['sort'] = 0;
        }


   

        $data['flow_id'] = isset($this->request->get['flow_id']) ?? 0;


        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('sheetflow/sheetflow_form.tpl', $data));

    }

    protected function validateFlowForm($flow_id = 0)
    {
        $this->load->model('admin/sheet');
        if (!$this->user->hasPermission('modify', 'admin/sheet')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (empty($this->request->post['name'])) {
            $this->error['warning'] = '名称必填!';
            return false;
        }

        return !$this->error;
    }

    
       // 流程图
       public function image(){
        $this->load->model('admin/sheetnode');
        $this->load->model('admin/sheetnodeact');
        $nodeList = $this->model_admin_sheetnode->getNodes(['filter_flow_id'=>$this->request->get['flow_id']]);
        $nodeIds = $nodeList ? implode(',',array_column($nodeList,'node_id')) : 0;
        $nodeListById = array_column($nodeList,null,'node_id');
        $actList = $this->model_admin_sheetnodeact->getActList($nodeIds);
        $userList = $this->model_admin_sheetnode->userList();
        $userList = array_column($userList,null,'union_id');
        if($actList){
            foreach($actList as $key=>$val){
                $actList[$key]['node_name'] = $nodeListById[$val['child_node_id']]['node_name'] ?? '未设置';
                $actList[$key]['union_id'] = $nodeListById[$val['child_node_id']]['_union_id'] ?? '';
                $actList[$key]['day'] = $nodeListById[$val['child_node_id']]['day'] ?? '';
    
                if(isset($nodeListById[$val['child_node_id']]['_union_id']) && strpos($nodeListById[$val['child_node_id']]['_union_id'],',') !== false){
                    $temp = explode(',',$nodeListById[$val['child_node_id']]['_union_id']);
                    $temp_arr = [];
                    foreach($temp as $v){
                        $temp_arr[] = isset($userList[$v]['real_name']) ? $userList[$v]['real_name'] : '已离职';
                    }
                    $actList[$key]['union_id'] = implode(',',$temp_arr);
                    unset($temp);unset($temp_arr);
                }else{
                    $actList[$key]['union_id'] = isset($userList[$nodeListById[$val['child_node_id']]['_union_id']]['real_name']) ? $userList[$nodeListById[$val['child_node_id']]['_union_id']]['real_name'] : '已离职';
                }
             
            }
        }
        

     

        $data['actList'] = $actList ?? []; 
        $data = json_encode($data,JSON_UNESCAPED_UNICODE);
        print_r($data);die;

        $this->response->setOutput($this->load->view('sheetflow/sheetnodeact.html',$data));
    }

}
