<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      众筹产品同步
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <?php if (empty($products)) { ?>
    <div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 没有详细商品信息！ </div>
    <?php } ?>
    <div class="box box-primary">
      <!-- /.box-header -->
      <div class="box-body">
        <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-order" class="form-horizontal">
          <div class="table-responsive">
            <table id="plans" class="table table-striped table-bordered table-hover">
              <thead>
              <tr>
                <td class="text-left">图片</td>
                <td class="text-left">名称</td>
                <td class="text-left">编码</td>
                <td class="text-left">成本</td>
                <td width="150" class="text-left">数量</td>
              </tr>
              </thead>
              <tbody>
                <?php if (!empty($products)) { ?>
                <?php foreach ($products as $product_id => $product) { ?>
                  <?php foreach ($product as $option_id => $option) { ?>
                  <tr>
                    <td class="text-left"><img width="100" src="<?php echo $option['img']; ?>" class="img-thumbnail"></td>
                    <td class="text-left"><?php echo $option['name']; ?></td>
                    <td class="text-left"><?php echo $option['bsku']; ?></td>
                    <td class="text-left"><?php echo $option['price']; ?></td>
                    <td class="text-left">
                      <input type="number" name="quantity[<?php echo $product_id; ?>][<?php echo $option_id; ?>]" value="<?php echo $option['quan']; ?>" placeholder="数量" class="form-control" />
                    </td>
                  </tr>
                  <?php } ?>
                <?php } ?>
                <?php } ?>
              </tbody>
            </table>
          </div>
          <?php if (!empty($products)) { ?>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" type="submit">同步众筹翻单</button>
            </div>
          </div>
          <?php } ?>
        </form>
      </div>
      <!-- /.box-body -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>