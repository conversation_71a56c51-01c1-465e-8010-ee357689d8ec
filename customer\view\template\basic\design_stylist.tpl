<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      设置设计师
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <div class="box box-warning">
      <div class="box-header">
        <h3 class="box-title"></h3>
      </div>
      <!-- /.box-header -->
      <div class="box-body">
        <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-nickname"></label>
            <div class="col-sm-8">
              <dl class="fieldlist">
                <dd>
                  <ins style="width: 205px;display:inline-block;text-align: center">设计师姓名</ins>
                </dd>
                <div class="keyword">
                  <?php if(!empty($stylists)){ ?>
                    <?php foreach($stylists as $stylist_key => $stylist){ ?>
                    <dd class="form-inline">
                      <input type="text" name="stylists[<?php echo $stylist_key; ?>]" class="form-control" value="<?php echo $stylist; ?>" title="<?php echo $stylist; ?>" placeholder="" style="width: 200px;margin: 5px 10px 5px 0">
                    </dd>
                    <?php } ?>
                  <?php } ?>
                </div>
              </dl>

              <div class="box-tools" style="width: 15%;float: left;margin-left: 20px">
                <a class="btn btn-primary" href="javascript:addSpecKey();">添加</a>
              </div>

            </div>
          </div>

          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" type="submit">提交保存</button>
              <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
            </div>
          </div>
        </form>
      </div>
      <!-- /.box-body -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script>
  var this_num = <?php echo $num; ?>;
  function addSpecKey() {
    this_num += 1;
    var addHtml = '<dd class="form-inline">' +
            '<input type="text" name="stylists['+this_num+']" class="form-control" value="" placeholder="" style="width: 200px;margin: 5px 10px 5px 0">'+
            '<a class="btn btn-danger" href="javascript:;" onclick="delSpecKey($(this))">删除</a>'+
            '</dd>';

    $('.keyword').append(addHtml);
  }

  function delSpecKey(obj) {
    $(obj).parent().remove();
  }
</script>
<?php echo $footer; ?>