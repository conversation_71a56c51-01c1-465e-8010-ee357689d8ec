<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        客户统计数据
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
          <div class="box-body">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>报表类型：</label>
                  <select class="form-control" name="filter_report">
                    <?php foreach($reports as $type => $report) { ?>
                    <?php if ($type == $filter_report) { ?>
                    <option value="<?php echo $type; ?>" selected="selected"><?php echo $report; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $type; ?>"><?php echo $report; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <!-- /.box-body -->
          <div class="box-footer">
            <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 查看</button>
          </div>
      </div>
      <div class="box box-danger">
        <div class="box-header with-border">
          <p class="box-title"><?php echo isset($reports[$filter_report]) ? $reports[$filter_report] : ''; ?></p>
        </div>
        <div class="box-body">
          <div id="chartMain" style="width: 1000px;height:500px;"></div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script src="<?php echo HTTP_SERVER; ?>static/js/echarts.min.js"></script>
<script type="text/javascript">
$(function() {
    <?php if (!empty($summary)) { ?>
    var myChart = echarts.init(document.getElementById('chartMain'));
    myChart.clear();
    myChart.setOption(<?php echo json_encode($summary, 320); ?>);
    <?php } ?>
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }
  
      var filter_report = $('select[name=\'filter_report\']').val();
  
      if (filter_report) {
        url += '&filter_report=' + encodeURIComponent(filter_report);
      }

      location.href = url;
    });
});
</script>
<?php echo $footer; ?>