<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      销售确认
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <?php if (empty($products)) { ?>
    <div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 没有详细商品信息！ </div>
    <?php } ?>
    <div class="box box-primary">
      <!-- /.box-header -->
      <div class="box-body">
          <div class="table-responsive">
            <table id="plans" class="table table-striped table-bordered table-hover">
              <thead>
              <tr>
                <td class="text-left">图片</td>
                <td class="text-left">产品名称</td>
                <td class="text-left">商品编码</td>
                <td class="text-left">销售日期</td>
                <td class="text-left">销售成本</td>
                <td width="150" class="text-left">销售数量</td>
             </tr>
             </thead>
             <tbody>
                <?php if (!empty($products)) { ?>
                <?php $submit = false; ?>
                <?php foreach ($products as $product) { ?>
                <tr>
                  <td class="text-left"><img width="100" src="<?php echo $product['img_url']; ?>" class="img-thumbnail"></td>
                  <td class="text-left"><?php echo $product['spec_name']; ?></td>
                  <td class="text-left"><?php echo $product['bsku']; ?></td>
                  <td class="text-left"><?php echo $product['stockout_month']; ?></td>
                  <td class="text-left"><?php echo $product['stockout_cost']; ?></td>
                  <td class="text-left">
                    <?php if ($product['status'] == '0') { ?>
                    <?php $submit = true; ?>
                    <input type="number" name="quantity[<?php echo $product['stockout_id']; ?>]" value="<?php echo $product['stockout_quan']; ?>" placeholder="出库数量" class="form-control" />
                    <?php } else { ?>
                    <?php echo $product['stockout_quan']; ?>
                    <?php } ?>
                  </td>
                </tr>
                <?php } ?>
                <?php } ?>
             </tbody>
           </table>
         </div>
         <?php if ($submit) { ?>
         <div class="form-group">
           <div class="col-sm-offset-2 col-sm-8">
             <button class="btn btn-primary" id="button-handle" data-loading-text="正在计算..." type="button" onclick="javascript:handleOut();">确认出库</button>
           </div>
         </div>
         <?php } ?>
      </div>
      <!-- /.box-body -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.mask {
  background-color: rgba(25, 24, 26, 0.34);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1998;
}
.loading-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 1999;
  transform: translate(-50%, -50%);
}
.loading-icon {
  width: 160px;
  height: 160px;
  background-image: url('<?php echo HTTP_SERVER; ?>static/loading.gif');
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  vertical-align: middle;
  background-size: contain;
}
</style>
<script type="text/javascript"><!--
function Loading () {
  this.el = $('<div class="mask"></div><div class="loading-modal"><i class="loading-icon"></i></div>')
  if (typeof this.show !== 'function') {
    Loading.prototype.hide = function () {
      $(this.el).remove()
    }
    Loading.prototype.show = function () {
      $('body').append(this.el)
    }
  }
  this.show()
}
var loading = null;
// 处理
function handleOut() {
  if (typeof(loading) == "undefined" || loading == null) {
    loading = new Loading();
  }
  $.ajax({
    url: '<?php echo $handle; ?>',
    type: 'post',
    data: $('input[type="number"]'),
    dataType: 'json',
    beforeSend: function() {
      $('#button-handle').button('loading');
    },
    complete: function() {
      $('#button-handle').button('reset');
    },
    success: function(json) {
      if (json['next']) {
        handleOut();
      }

      if (json['success']) {
        loading.hide();
        alert('确认完成');
        location.href = '<?php echo $list; ?>';
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      // alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
      handleOut();
    }
  });
}
//--></script>
<?php echo $footer; ?>