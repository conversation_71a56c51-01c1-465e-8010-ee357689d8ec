<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        入库数据
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索入库单号/商家编码" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>所属店铺：</label>
                  <select class="form-control" name="filter_store">
                    <option value="*">全部店铺</option>
                    <?php foreach ($stores as $store) { ?>
                    <?php if ($store['store_id'] == $filter_store) { ?>
                    <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>入库时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right" id="reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right" id="reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start-time" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end-time" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">入库列表</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>
                <?php if ($sort == 'store_id') { ?>
                  <a href="<?php echo $sort_store; ?>" class="<?php echo strtolower($order); ?>">店铺</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_store; ?>">店铺</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'stockin_no') { ?>
                <a href="<?php echo $sort_no; ?>" class="<?php echo strtolower($order); ?>">入库单号</a>
                <?php } else { ?>
                <a href="<?php echo $sort_no; ?>">入库单号</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'stockin_count') { ?>
                <a href="<?php echo $sort_count; ?>" class="<?php echo strtolower($order); ?>">入库商品</a>
                <?php } else { ?>
                <a href="<?php echo $sort_count; ?>">入库商品</a>
                <?php } ?>
              </th><th>
                <?php if ($sort == 'stockin_quan') { ?>
                <a href="<?php echo $sort_quan; ?>" class="<?php echo strtolower($order); ?>">入库数量</a>
                <?php } else { ?>
                <a href="<?php echo $sort_quan; ?>">入库数量</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'stockin_date') { ?>
                <a href="<?php echo $sort_date; ?>" class="<?php echo strtolower($order); ?>">入库日期</a>
                <?php } else { ?>
                <a href="<?php echo $sort_date; ?>">入库日期</a>
                <?php } ?>
              </th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($stockins)) { ?>
              <?php foreach ($stockins as $stockin) { ?>
                <tr>
                  <td><?php echo $stockin['in_store']; ?></td>
                  <td><?php echo $stockin['in_no']; ?></td>
                  <td><?php echo $stockin['in_count']; ?></td>
                  <td><?php echo $stockin['in_quan']; ?></td>
                  <td><?php echo $stockin['in_date']; ?></td>
                  <td class="text-right">
                    <?php if ($stockin['status'] == '0') { ?>
                    <a class="btn btn-danger" href="<?php echo $stockin['detail']; ?>">核对入库</a>
                    <?php } else { ?>
                    <a class="btn btn-success" href="<?php echo $stockin['detail']; ?>">查看入库</a>
                    <?php } ?>
                  </td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="6" align="center"> 暂无入库数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('#reservation').daterangepicker({
      autoUpdateInput: false,
      daterangepicker: true,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('#reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start-time').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end-time').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('#reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start-time').val('')
      $('#filter-end-time').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>