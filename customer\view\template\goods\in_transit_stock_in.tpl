<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        入库详情
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">

      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">入库详情</h3>
          <div class="box-tools"></div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>图片</th>
              <th>产品名称/编码</th>
              <th>仓库验收日期</th>
              <th>仓库</th>
              <th>供应商</th>
              <th>入库数量</th>
              <th>状态</th>
              <th>装箱数</th>
              <th>箱数</th>
              <th>尾数</th>
              <th>详情(采购单号--入库单号--入库数量)</th>
              <th>备注</th>
            </tr>
            <?php if (!empty($pushs)) { ?>
            <?php foreach ($pushs as $push) { ?>
            <tr data-id="<?php echo $push['push_id']; ?>">
              <td><img width="100" src="<?php echo $push['img_url']; ?>" class="img-thumbnail"></td>
              <td><?php echo $push['spec_name']; ?><br><?php echo $push['bsku']; ?></td>
              <td><?php echo $push['push_date']; ?></td>
              <td> <?php if (!empty($warehouses[$push['warehouse_no']])) { ?><?php echo $warehouses[$push['warehouse_no']]; ?><?php } ?></td>
              <td> <?php if (!empty($providers[$push['provider_no']])) { ?><?php echo $providers[$push['provider_no']]; ?><?php } ?></td>
              <td><?php echo $push['quantity']; ?></td>
              <td><?php if($push['status']==0){ ?>未处理<?php }else if($push['status']==1){ ?>已处理<?php }else if($push['status']==2){ ?>采购数量不足<?php }else if($push['status']==3){ ?>无采购数量<?php }else if($push['status']==4){ ?>已核对<?php } ?></td>
              <td><?php echo $push['PSC']; ?></td>
              <td><?php echo $push['carton_numbers']; ?></td>
              <td><?php echo $push['mantissa']; ?></td>
              <td>
                <?php if (!empty($push['push_data'])) { ?>
                <?php foreach ($push['push_data'] as $push_data) { ?>
                <?php echo $push_data['purchase_no']; ?>--<?php echo $push_data['outer_no']; ?>--<?php echo $push_data['stockin_num']; ?>
                <br>
                <?php } ?>
                <?php } ?>
              </td>
              <td><?php echo $push['remark']; ?></td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="13" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>

<?php echo $footer; ?>