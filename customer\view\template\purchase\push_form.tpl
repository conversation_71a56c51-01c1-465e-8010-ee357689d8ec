<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        编辑采购入库
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-username">编码：</label>
              <div class="col-sm-8">
                <input type="text" name="bsku" value="<?php echo $push['bsku']; ?>" placeholder="编码" id="input-bsku" class="form-control required" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-real-name">入库数量：</label>
              <div class="col-sm-8">
                <input type="text" name="quantity" value="<?php echo $push['quantity']; ?>" placeholder="入库数量" id="input-quantity" class="form-control required" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-push_date">仓库验收日期：</label>
              <div class="col-sm-8">
                <input type="text" name="push_date" id="input-push_date" class="form-control pull-right reservation required" placeholder="选择仓库验收日期" value="<?php echo $push['push_date']; ?>">
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="select-calculate">选择仓库：</label>
              <div class="col-sm-8">
                <select name="warehouse_no" id="select-warehouse" class="form-control">
                  <?php foreach($warehouses as $warehouse){ ?>
                  <option value="<?php echo $warehouse['warehouse_no']; ?>" <?php if(!empty($push['warehouse_no']) && ($push['warehouse_no'] == $warehouse['warehouse_no'])){ ?>selected="selected"<?php } ?>><?php echo $warehouse['name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="select-calculate">选择供应商：</label>
              <div class="col-sm-8">
                <select name="provider_no" id="select-provider" class="form-control">
                  <?php foreach($providers as $provider){ ?>
                  <option value="<?php echo $provider['provider_no']; ?>" <?php if(!empty($push['provider_no']) && ($push['provider_no'] == $provider['provider_no'])){ ?>selected="selected"<?php } ?>><?php echo $provider['provider_name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-real-name">备注：</label>
              <div class="col-sm-8">
                <input type="text" name="remark" value="<?php echo $push['remark']; ?>" placeholder="备注" id="input-remark" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-real-name">规格名称：</label>
              <div class="col-sm-8">
                <input type="text" name="spec_name" value="<?php echo $push['spec_name']; ?>" placeholder="规格名称" id="input-spec_name" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-real-name">装箱数：</label>
              <div class="col-sm-8">
                <input type="text" name="PSC" value="<?php echo $push['PSC']; ?>" placeholder="入库数量" id="input-PSC" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-real-name">箱数：</label>
              <div class="col-sm-8">
                <input type="text" name="carton_numbers" value="<?php echo $push['carton_numbers']; ?>" placeholder="箱数" id="input-carton_numbers" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-real-name">尾数：</label>
              <div class="col-sm-8">
                <input type="text" name="mantissa" value="<?php echo $push['mantissa']; ?>" placeholder="尾数" id="input-mantissa" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      singleDatePicker:true,
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('YYYY-MM-DD'));
    })
  })()

  function toVaild() {
    var isValid = true;
    $('.required').each(function() {
      if ($.trim($(this).val()) === '') {
        isValid = false;
        $(this).css('border', '1px solid red');
      } else {
        $(this).css('border', '');
      }
    });

    if (isValid) {
      return true;
    } else {
      return false;
    }
  }
</script>
<?php echo $footer; ?>