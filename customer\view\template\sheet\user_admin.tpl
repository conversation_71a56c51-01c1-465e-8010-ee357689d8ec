<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
            <div class="form-group required">
     

            <div class="form-group">
              <label class="col-sm-2 control-label">超级管理员：</label>
              <div class="col-sm-8">
                <div class="well well-sm" style="height: 150px; overflow: auto;">
                  <?php foreach ($users as $user) { ?>
                  <?php if (in_array($user['union_id'], $union_id)) { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="union_id[]" value="<?php echo $user['union_id']; ?>" checked="checked"><?php echo $user['real_name']; ?></label>
                  </div>
                  <?php } else { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="union_id[]" value="<?php echo $user['union_id']; ?>"><?php echo $user['real_name']; ?></label>
                  </div>
                  <?php } ?>
                  <?php } ?>
                </div>
              </div>
            </div>
          
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>