<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        采购合同
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>合同号：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="合同号" value="<?php echo $filter_name; ?>">
                </div>
              </div>

              <div class="col-md-4">
                <div class="form-group">
                  <label>签订时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_start) && !empty($filter_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?> - <?php echo $filter_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_start; ?><?php echo $filter_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_start" id="filter-start" placeholder="" value="<?php echo $filter_start; ?>">
                    <input type="text" class="hidden" name="filter_end" id="filter-end" placeholder="" value="<?php echo $filter_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">合同列表</h3>
          <div class="box-tools">
            <a class="btn btn-primary" href="<?php echo $add; ?>">新增合同</a>
            <button id="button-crowd" class="btn btn-warning" type="button">同步众筹</button>
          </div>
        </div>
        <div class="box-body">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th width="30"></th>
              <th>合同号</th>
              <th>签订时间</th>
              <th>交货时间</th>
              <th>新建时间</th>
              <th class="text-right">操作</th>
              </th>
            </tr>
            <?php if (!empty($purchase_contracts)) { ?>
            <?php foreach ($purchase_contracts as $purchase_contract) { ?>
            <tr>
              <td><?php if (empty($purchase_contract['crowd'])) { ?><input class="flat" type="checkbox" value="<?php echo $purchase_contract['purchase_contract_id']; ?>"><?php } ?></td>
              <td><?php echo $purchase_contract['contract_number']; ?></td>
              <td><?php echo $purchase_contract['sign_date']; ?></td>
              <td><?php echo $purchase_contract['delivery_date']; ?></td>
              <td><?php echo $purchase_contract['date_added']; ?></td>
              <td class="text-right">
                <a class="btn btn-success" href="<?php echo $edit_url; ?><?php echo $purchase_contract['purchase_contract_id']; ?>" title="" target="_blank">修改</a>
                <a class="btn btn-primary" href="<?php echo $export_url; ?><?php echo $purchase_contract['purchase_contract_id']; ?>" title="" target="_blank">导出</a>
                <a class="btn btn-primary" href="<?php echo $documentary_export_url; ?><?php echo $purchase_contract['purchase_contract_id']; ?>" title="" target="_blank">导出跟单表</a>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="7" align="center"> 暂无合同数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }
  
      var filter_start = $('input[name=\'filter_start\']').val();
  
      if (filter_start) {
        url += '&filter_start=' + encodeURIComponent(filter_start);
      }

      var filter_end = $('input[name=\'filter_end\']').val();
  
      if (filter_end) {
        url += '&filter_end=' + encodeURIComponent(filter_end);
      }

      location.href = url;
    });
    $('input.flat').iCheck('uncheck');
  })()
</script>
<script type="text/javascript"><!--
$('#button-crowd').on('click', function() {
  if ($('input.flat:checked').length > 0) {
    var temp = [];
    $('input.flat:checked').each(function() {
      temp.push($(this).val());
    });

    location.href = '<?php echo $crowd; ?>&contract_ids=' + temp.join(',');
  } else {
    alert('请选择对应合同！');
  }
});
//--></script>
<?php echo $footer; ?>