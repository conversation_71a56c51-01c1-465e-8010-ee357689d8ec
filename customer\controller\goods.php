<?php
class ControllerGoods extends Controller {
    public function auth() {
        $data['error_warning'] = '';

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {            
            if (isset($this->request->post['cardid']) && isset($this->request->post['mobile'])) {
                $this->load->model('goods');
                $auth_info = $this->model_goods->authentication($this->request->post['cardid'], $this->request->post['mobile']);

                if (!empty($auth_info)) {
                    $this->session->data['wholesale'] = $auth_info['wholesale'];

                    $this->response->redirect($this->url->link('goods/list'));
                } else {
                    $data['error_warning'] = '验证错误！';
                }
            } else {
                $data['error_warning'] = '输入不能为空！';
            }  
        }

        $data['action'] = $this->url->link('goods/auth');

        if (isset($this->request->post['cardid'])) {
            $data['cardid'] = $this->request->post['cardid'];
        } else {
            $data['cardid'] = '';
        }

        if (isset($this->request->post['mobile'])) {
            $data['mobile'] = $this->request->post['mobile'];
        } else {
            $data['mobile'] = '';
        }

        $this->response->setOutput($this->load->view('goods/auth.tpl', $data));
    }

    public function list() {
        if (!$this->user->isLogged() && !isset($this->session->data['wholesale'])) {
            $this->response->redirect($this->url->link('goods/auth'));
        }

        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_class'])) {
            $filter_class = $this->request->get['filter_class'];
        } else {
            $filter_class = '';
        }

        if (isset($this->request->get['filter_start'])) {
            $filter_start = $this->request->get['filter_start'];
        } else {
            $filter_start = '';
        }

        if (isset($this->request->get['filter_end'])) {
            $filter_end = $this->request->get['filter_end'];
        } else {
            $filter_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'goods_id';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_class'  => $filter_class,
            'filter_start'  => $filter_start,
            'filter_end'    => $filter_end,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit'),
            'limit'         => $this->config->get('config_limit'),
        );

        $this->load->model('goods');
        $data['classes'] = $this->model_goods->getWdtClass();

        $data['goods'] = array();
        $results = $this->model_goods->getWdtGoods($filter_data);

        $wholesale = $this->session->data['wholesale'] ?? '';

        foreach ($results as $result) {
            $price = array((float)$result['market_price']);
            if ($wholesale == '1') $price = array((float)$result['wholesale_price']); // 批发1档
            if ($wholesale == '2') $price = array((float)$result['custom_price1']); // 批发2档
            if ($wholesale == '3') $price = array((float)$result['custom_price2']); // 批发3档
            if ($wholesale == '5') $price = array((float)$result['wholesale_price'], (float)$result['custom_price1'], (float)$result['custom_price2']);
            if ($wholesale == '6') $price = array((float)$result['prop6']);// 分销价
            if ($wholesale == '7') $price = array((float)$result['wholesale_price'], (float)$result['custom_price1'], (float)$result['custom_price2'], (float)$result['prop6']);
            if ($wholesale == '8') $price = array((float)$result['wholesale_price'], (float)$result['custom_price1'], (float)$result['custom_price2'], (float)$result['prop6'], (float)$result['market_price']);

            $data['goods'][] = array(
                'img_url'   => $result['img_url'],
                'spec_name' => $result['spec_name'],
                'name_en'   => $result['name_en'],
                'class_en'  => $result['category_en'],
                'spec_no'   => $result['spec_no'],
                'class_name'=> $result['class_name'],
                'price'     => $price,
                'weight'    => (float)$result['weight'] * (int)$result['prop2'],
                'bat_size'  => $result['prop1'],
                'bat_quan'  => $result['prop2'],
                'box_size'  => $result['prop3'],
                'spec_size' => $result['prop5']
            );
        }

        $total = $this->model_goods->getTotalGoods($filter_data);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_class'])) {
            $url .= '&filter_class=' . $this->request->get['filter_class'];
        }

        if (isset($this->request->get['filter_start'])) {
            $url .= '&filter_start=' . $this->request->get['filter_start'];
        }

        if (isset($this->request->get['filter_end'])) {
            $url .= '&filter_end=' . $this->request->get['filter_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('goods/list', 'page={page}' . $url);

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_class'] = $filter_class;
        $data['filter_start'] = $filter_start;
        $data['filter_end'] = $filter_end;

        $data['nofilter'] = $this->url->link('goods/list');

        $data['sort'] = $sort;
        $data['order'] = $order;

        $this->response->setOutput($this->load->view('goods/list.tpl', $data));
    }
}