<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        库存管理
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="搜索编码/名称" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>供应商：</label>
                  <select class="form-control" name="filter_provider">
                    <option value="*">全部供应商</option>
                    <?php foreach ($providers as $provider) { ?>
                    <?php if ($provider['provider_no'] == $filter_provider) { ?>
                    <option value="<?php echo $provider['provider_no']; ?>" selected="selected"><?php echo $provider['provider_name']; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $provider['provider_no']; ?>"><?php echo $provider['provider_name']; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>筛选产品：</label>
                  <select class="form-control" name="filter_rule">
                    <?php foreach($rules as $rule_code => $rule_name) { ?>
                    <?php if ($rule_code == $filter_rule) { ?>
                    <option value="<?php echo $rule_code; ?>" selected="selected"><?php echo $rule_name; ?></option>
                    <?php } else{ ?>
                    <option value="<?php echo $rule_code; ?>"><?php echo $rule_name; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <a class="btn btn-danger" href="<?php echo $export; ?>"><i class="glyphicon glyphicon-download-alt"></i> 下载数据</a>
          
          <div class="pull-right">
            <button type="button" id="button-filter" class="btn bg-purple"><i class="glyphicon glyphicon-search"></i> 筛选</button>
          </div>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">库存列表</p>
          <div class="box-tools"></div>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th style="max-width: 200px;">图片</th>
              <th>产品名称
                <?php if ($sort == 'spec_no') { ?>
                  <a href="<?php echo $sort_bsku; ?>" class="<?php echo strtolower($order); ?>">编码</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_bsku; ?>">编码</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'stock_num') { ?>
                  <a href="<?php echo $sort_stock; ?>" class="<?php echo strtolower($order); ?>">实际库存</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_stock; ?>">实际库存</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'order_num') { ?>
                  <a href="<?php echo $sort_order; ?>" class="<?php echo strtolower($order); ?>">待审核</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_order; ?>">待审核</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'purchase_num') { ?>
                  <a href="<?php echo $sort_purchase; ?>" class="<?php echo strtolower($order); ?>">采购在途</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_purchase; ?>">采购在途</a>
                <?php } ?>
              </th>
              <th>
                <?php if ($sort == 'out_num') { ?>
                  <a href="<?php echo $sort_out; ?>" class="<?php echo strtolower($order); ?>">缺货数量</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_out; ?>">缺货数量</a>
                <?php } ?>
              </th>
              <th>缺货信息</th>
              <th>
                <?php if ($sort == 'last_sales') { ?>
                  <a href="<?php echo $sort_last; ?>" class="<?php echo strtolower($order); ?>">最后销售日期</a>
                <?php } else { ?>
                  <a href="<?php echo $sort_last; ?>">最后销售日期</a>
                <?php } ?>
              </th>
              <th>供应商</th>
              <th class="text-right">预计到货</th>
            </tr>
            <?php if (!empty($stocks)) { ?>
            <?php foreach ($stocks as $stock) { ?>
            <tr data-id="<?php echo $stock['bsku']; ?>">
              <td><img width="100" src="<?php echo $stock['img_url']; ?>" class="img-thumbnail"></td>
              <td>
                <?php echo $stock['spec_name']; ?><br>
                <?php echo $stock['bsku']; ?>
              </td>
              <td><?php echo $stock['stock']; ?></td>
              <td><?php echo $stock['order']; ?></td>
              <td><?php echo $stock['purchase']; ?></td>
              <td class="h4 no-margin">
                <span class="label label-primary"><?php echo $stock['out']; ?></span>
              </td>
              <td>
                <?php if (!empty($stock['quan'])) { ?>
                零售：<?php echo $stock['quan']['retail']; ?>个<br>
                批发：<?php echo $stock['quan']['wholesale']; ?>个<br>
                <?php } ?>
                <button class="btn btn-primary" type="button" data-toggle="modal" data-target="#info-modal">填写缺货信息</button>
              </td>
              <td><?php echo $stock['last']; ?></td>
              <td><?php echo $stock['providers']; ?></td>
              <td class="text-right">
                <?php if (!empty($stock['arrival'])) { ?>
                <h4><span class="label label-info"><?php echo $stock['arrival']['date']; ?>预计到货<?php echo $stock['arrival']['quan']; ?>个</span><h4>
                <?php } ?>
                <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#del-modal">填写到货信息</button>
              </td>
            </tr>
            <?php } ?>
            <?php } else{ ?>
            <td colspan="11" align="center"> 暂无库存数据 </td>
            <?php } ?>
          </tbody></table>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 缺货 -->
      <div class="modal modal-primary fade" id="info-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $addOutQuan; ?>" method="post" enctype="multipart/form-data" id="form-info" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">请填写缺货信息</h4>
              </div>
              <div class="modal-body">
                <input id="info-id" name="bsku" type="hidden" value="">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-retail">零售数量：</label>
                  <div class="col-sm-8">
                    <input type="number" name="retail_quan" value="" placeholder="请输入零售缺货数量" id="input-retail" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-wholesale">批发数量：</label>
                  <div class="col-sm-8">
                    <input type="number" name="wholesale_quan" value="" placeholder="请输入批发缺货数量" id="input-wholesale" class="form-control" />
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">关闭</button>
                <button id="info-yes" type="button" class="btn btn-outline">提交</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>

      <!-- 到货 -->
      <div class="modal modal-danger fade" id="del-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $addArrival; ?>" method="post" enctype="multipart/form-data" id="form-del" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">请填写预计到货信息</h4>
              </div>
              <div class="modal-body">
                <input id="del-id" name="bsku" type="hidden" value="">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-date-start">到货时间：</label>
                  <div class="col-sm-8">
                    <input type="text" name="arrival_date" value="" placeholder="请选择预计到货时间" id="input-date-start" class="form-control" readonly="readonly" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">到货数量：</label>
                  <div class="col-sm-8">
                    <input type="number" name="arrival_quan" value="" placeholder="请输入预计到货数量" id="input-quan" class="form-control" />
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">关闭</button>
                <button id="del-yes" type="button" class="btn btn-outline">提交</button>
              </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期显示
    $('#input-date-start').daterangepicker({
      autoApply: true,
      autoUpdateInput: false,
      singleDatePicker: true,
      timePicker: false,
      locale: {
        format: 'YYYY-MM-DD',
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    $('#input-date-start').on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('YYYY-MM-DD'))
    })
    $('#input-date-start').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_provider = $('select[name=\'filter_provider\']').val();

      if (filter_provider != '*') {
        url += '&filter_provider=' + encodeURIComponent(filter_provider);
      }

      var filter_rule = $('select[name=\'filter_rule\']').val();

      if (filter_rule != '*') {
        url += '&filter_rule=' + encodeURIComponent(filter_rule);
      }

      location.href = url;
    });

    $('#info-modal').on('show.bs.modal', function(event) {
      $('#info-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#info-yes').on('click', () => {
      if ($('#input-retail').val() || $('#input-wholesale').val()) {
        $('#form-info').submit()
      } else {
        $('#info-modal').modal('toggle')
      }
    })

    $('#del-modal').on('show.bs.modal', function(event) {
      $('#del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#del-yes').on('click', () => {$('#form-del').submit()})
  })()
</script>
<?php echo $footer; ?>