<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-name">名称 ：</label>
              <div class="col-sm-8">
                <input type="text" name="power_name" value="<?php if(!empty($file_power['power_name'])){ ?><?php echo $file_power['power_name']; ?><?php } ?>" placeholder="请输入名称" id="input-name" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label">选择部门：</label>
              <div class="col-sm-8">
                <select id="select-department" class="form-control select-province"  name="department[]"  multiple data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">
                  <?php foreach ($departments as $department) { ?>
                  <option value="<?php echo $department['department_id']; ?>"><?php echo $department['department_name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label">选择角色：</label>
              <div class="col-sm-8">
                <select id="select-role" class="form-control select-province"  name="role[]"  multiple data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">
                  <?php foreach ($roles as $role) { ?>
                  <option value="<?php echo $role['role_id']; ?>"><?php echo $role['role_name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label">选择文件夹 ：</label>
              <div class="col-sm-8" id="jstree_div"></div>
              <input type="hidden" name="folder_ids" id="jstree_value">
            </div>


            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" id="myForm" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<link rel="stylesheet" href="<?php echo HTTP_SERVER; ?>static/js_tree/themes/default/style.min.css" />
<script src="<?php echo HTTP_SERVER; ?>static/js_tree/jstree.js"></script>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>


<script type="text/javascript">
  var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
  window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","placeholder":"请选择","language":"zh-CN"};
  $.when($('#select-department').select2(select2_5eaa6d36)).done(initS2Loading('select-department','s2options_c4acac00'));
  $.when($('#select-role').select2(select2_5eaa6d36)).done(initS2Loading('select-role','s2options_c4acac00'));


  var file_power_id = '<?php echo $file_power_id; ?>';

  $(document).ready(function() {
    $('#jstree_div').jstree({
      'core' : {
        'data' : function (obj, callback) {
          $.ajax({
            'url' : '<?php echo $get_folder; ?>',
            'type' : 'GET',
            'data' : { 'file_power_id' : file_power_id },
            'success' : function (data) {
              callback.call(this, data);
            }
          });

        },
        'check_callback' : true,
        'lazyLoading' : true
      },
      "checkbox" : {
        'three_state' : true,
        'cascade': 'down',
        "keep_selected_style" : false
      },
      "plugins" : [ "checkbox" ]
    });

    $('#jstree_div').jstree(true).close_node("ALL");
  });



  function toVaild() {
    // if ($('.glyphicon-info-sign').length) {
    //   confirm('数据不存在')
    //   return false;
    // }
    var name = document.getElementById("input-name").value;
    if (name == "") {
      confirm('请输入名称')
      return false;
    }
    var department = [];
    $('#select-department').find('option:selected').each(function(){
      department.push($(this).val());
    })
    var role = [];
    $('#select-role').find('option:selected').each(function(){
      role.push($(this).val());
    })
    if (department.length == 0 && role.length == 0) {
      confirm('请选择部门或角色')
      return false;
    }

    var get_checked = $('#jstree_div').jstree("get_checked");
    if (get_checked == "") {
      confirm('请选择文件夹')
      return false;
    }

    $('#jstree_value').val(get_checked);

    return true;
  }

  var department_ids = '<?php echo $department_ids; ?>';
  if (department_ids) {
    var department_ids_arr = department_ids.split(",");
    $.each(department_ids_arr, function(index, value) {
      $('#select-department').find("option[value='" + value + "']").prop('selected', true);
    });
    $('#select-department').trigger('change');
  }

  var role_ids = '<?php echo $role_ids; ?>';
  if (role_ids) {
    var role_ids_arr = role_ids.split(",");
    $.each(role_ids_arr, function(index, value) {
      $('#select-role').find("option[value='" + value + "']").prop('selected', true);
    });
    $('#select-role').trigger('change');
  }



</script>
<?php echo $footer; ?>