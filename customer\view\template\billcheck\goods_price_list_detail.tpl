<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        货款详情-<?php echo $template['name']; ?>-<?php echo $check_date; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">货款详情</h3>
          <div class="box-tools">
          </div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped box-primary2" id="plans">
            <tbody><tr>
              <th>图片</th>
              <th>产品名称</th>
              <th>规格</th>
              <th>数量</th>
              <th>总金额</th>
            </tr>
            <?php if (!empty($details)) { ?>
              <?php foreach ($details as $details) { ?>
                <tr>
                  <td class="text-left"><img width="100" src="<?php echo $details['img_url']; ?>" class="img-thumbnail"></td>
                  <td><?php echo $details['spec_name']; ?></td>
                  <td class="text-left"><input style="width: 200px" type="text" name="detail[]" value="<?php echo $details['bsku']; ?>" data-sku-detail="<?php echo $details['goods_price_id']; ?>,<?php echo $details['supplier']; ?>,<?php echo $details['check_date']; ?>,<?php echo $details['bsku']; ?>" data-org="<?php echo $details['bsku']; ?>" placeholder="请输入规格" class="input-detail form-control" /></td>
                  <td><?php echo $details['quantity']; ?></td>
                  <td><?php echo $details['total_prices']; ?></td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
      </div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    $('#plans').on('blur', '.input-detail', function () {
      var obj = $(this);
      if (!obj.val() || (obj.val() == '0') || (obj.val() == obj.data('org'))) {
        obj.val(obj.data('org'));
        return;
      }
      $.ajax({
        url: '<?php echo $updateSku; ?>',
        type: 'post',
        data: {sku_detail: obj.data('sku-detail'), sku: obj.val()},
        dataType: 'json',
        success: function(json) {
          $('.alert-danger, .alert-success').remove();

          if (json['error']) {
            $('.box-primary2').before('<div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 修改规格失败，请重试！ </div>');

            obj.val(obj.data('org'));
          }

          if (json['success']) {
            $('.box-primary2').before('<div class="alert alert-success alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i>  修改规格成功 </div>');

            obj.data('org', obj.val());
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    });
  })()
</script>
<?php echo $footer; ?>