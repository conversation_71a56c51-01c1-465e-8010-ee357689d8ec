<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        货款列表
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label>所属工厂：</label>
                <select class="form-control" name="filter_template_id">
                  <option value="*">全部工厂</option>
                  <?php foreach($templates as $template) { ?>
                  <?php if ($template['goods_price_template_id'] == $filter_template_id) { ?>
                  <option value="<?php echo $template['goods_price_template_id']; ?>" selected="selected"><?php echo $template['name']; ?></option>
                  <?php } else { ?>
                  <option value="<?php echo $template['goods_price_template_id']; ?>"><?php echo $template['name']; ?></option>
                  <?php } ?>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>账单时间：</label>
                <div class="input-group">
                  <div class="input-group-addon">
                    <i class="glyphicon glyphicon-calendar"></i>
                  </div>
                  <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                  <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                  <?php } else{ ?>
                  <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                  <?php } ?>
                  <input type="text" class="hidden" name="filter_date_start" id="filter-start" placeholder="" value="<?php echo $filter_date_start; ?>">
                  <input type="text" class="hidden" name="filter_date_end" id="filter-end" placeholder="" value="<?php echo $filter_date_end; ?>">
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>状态：</label>
                <select class="form-control" name="filter_status">
                  <option value="0">全部状态</option>
                  <option value="1" <?php if($filter_status==1){ ?>selected="selected"<?php } ?>>已核对</option>
                  <option value="2" <?php if($filter_status==2){ ?>selected="selected"<?php } ?>>待核对</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>

      <div class="box box-success box-primary2">
        <div class="box-header with-border">
          <h3 class="box-title">货款列表</h3>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>工厂</th>
              <th>货款日期</th>
              <th>状态</th>
              <td>图片</td>
              <td>产品名称</td>
              <th>规格</th>
              <th>数量</th>
              <th>总金额</th>
              <th>导入数量</th>
              <th>导入总金额</th>
              <th>操作</th>
            </tr>
            <?php if (!empty($goods_prices)) { ?>
              <?php foreach ($goods_prices as $goods_price) { ?>
                <tr data-id="<?php echo $goods_price['goods_price_statistics_id']; ?>">
                  <td><?php echo $goods_price['name']; ?></td>
                  <td><?php echo $goods_price['check_date']; ?></td>
                  <td><?php if($goods_price['status']==1) { ?>已核对<?php } else if ($goods_price['status']==2) { ?>待核对<?php } ?></td>
                  <td class="text-left"><img width="100" src="<?php echo $goods_price['img_url']; ?>" class="img-thumbnail"></td>
                  <td class="text-left"><?php echo $goods_price['spec_name']; ?></td>
                  <td class="text-left"><?php echo $goods_price['bsku']; ?></td>
                  <td><?php echo $goods_price['quantity']; ?></td>
                  <td><?php echo $goods_price['total_prices']; ?></td>
                  <td><?php echo $goods_price['import_quantity']; ?></td>
                  <td><?php echo $goods_price['import_total_prices']; ?></td>
                  <td class="text-right">
                    <button class="btn btn-success" type="button" data-toggle="modal" data-target="#add-modal">修改数量，总金额</button>
                    <a class="btn btn-info" href="<?php echo $detail; ?>&supplier=<?php echo $goods_price['supplier']; ?>&check_date=<?php echo $goods_price['check_date']; ?>&bsku=<?php echo $goods_price['bsku']; ?>" title="" target="_blank">查看详情</a>
                    <?php if($goods_price['status']==1) { ?>
                   <!-- <a class="btn btn-success" href="<?php echo $detail; ?><?php echo $goods_price['supplier']; ?>" title="" target="_blank">查看详情</a> -->
                    <?php } else if ($goods_price['status']==2) { ?>
                    <a class="btn btn-warning" href="<?php echo $check; ?>&supplier=<?php echo $goods_price['supplier']; ?>&check_date=<?php echo $goods_price['check_date']; ?>&bsku=<?php echo $goods_price['bsku']; ?>" title="" target="_blank">核对货款</a>
                    <?php } ?>
                  </td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>


      <!-- 设置数量总价 -->
      <div class="modal modal-warning fade" id="add-modal">
        <div class="modal-dialog">
          <div class="modal-content">
            <form action="<?php echo $update_import; ?>" method="post" enctype="multipart/form-data" id="form-add" class="form-horizontal">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">请填写退货信息</h4>
              </div>
              <div class="modal-body">
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">数量：</label>
                  <div class="col-sm-8">
                    <input type="number" name="quantity" value="" placeholder="请输入数量" id="input-quantity" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-quan">总价：</label>
                  <div class="col-sm-8">
                    <input type="number" name="total_prices" value="" placeholder="请输入总价" id="input-total_prices" class="form-control" />
                  </div>
                </div>
                <input id="add-id" name="goods_price_statistics_id" type="hidden" value="">
                <div class="modal-footer">
                  <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">取消</button>
                  <button id="add-yes" type="button" class="btn btn-outline">提交保存</button>
                </div>
            </form>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_template_id = $('select[name=\'filter_template_id\']').val();

      if (filter_template_id != '*') {
        url += '&filter_template_id=' + encodeURIComponent(filter_template_id);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();

      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();

      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      var filter_status = $('select[name=\'filter_status\']').val();

      if (filter_status != '*') {
        url += '&filter_status=' + encodeURIComponent(filter_status);
      }
      location.href = url;
    });

    $('#add-modal').on('show.bs.modal', function(event) {
      $('#add-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#add-yes').on('click', () => {$('#form-add').submit()})

  })()
</script>
<?php echo $footer; ?>