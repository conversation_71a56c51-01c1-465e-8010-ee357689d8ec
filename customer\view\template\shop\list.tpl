<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      店铺管理
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <div class="box box-primary">
      <div class="box-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label>关键字：</label>
              <input type="text" class="form-control" name="filter_name" placeholder="平台链接id/管理人" value="<?php echo $filter_name; ?>">
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <label>所属店铺：</label>
              <select id="w1" class="form-control" name="filter_store" multiple>
                <?php foreach($stores as $store) { ?>
                <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                <?php } ?>
              </select>
            </div>
          </div>
        </div>
      </div>
      <!-- /.box-body -->
      <div class="box-footer">
        <a class="btn btn-success" href="<?php echo $import; ?>"><i class="glyphicon glyphicon-upload"></i> 导入数据</a>
        <a class="btn btn-danger" href="<?php echo $export; ?>"><i class="glyphicon glyphicon-download-alt"></i> 下载数据</a>
        <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
      </div>
    </div>
    <?php if ($success) { ?>
    <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
    <?php } ?>
    <?php if ($warning) { ?>
    <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
    <?php } ?>
    <div class="box">
      <div class="box-header">
        <h3 class="box-title">店铺列表</h3>

        <div class="box-tools">
          <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
        </div>
      </div>
      <!-- /.box-header -->
      <div class="box-body table-responsive no-padding">
        <table class="table table-hover table-striped">
          <tbody><tr>
            <th>店铺</th>
            <th>平台链接id</th>
            <th>管理人</th>
            <th class="text-right">操作</th>
          </tr>
          <?php if (!empty($shops)) { ?>
          <?php foreach ($shops as $shop) { ?>
          <tr data-id="<?php echo $shop['shop_platform_id']; ?>">
            <td><?php echo $shop['shop_name']; ?></td>
            <td><?php echo $shop['platform_id']; ?></td>
            <td><?php echo $shop['real_name']; ?></td>
            <td class="text-right">
              <?php if (!empty($shop['edit'])) { ?>
              <a class="btn btn-success" href="<?php echo $shop['edit']; ?>" title="">修改</a>
              <button class="btn btn-danger" type="button" data-toggle="modal" data-target="#user-del-modal">删除</button>
              <?php } ?>
            </td>
          </tr>
          <?php } ?>
          <?php } else{ ?>
          <td colspan="7" align="center"> 暂无店铺 </td>
          <?php } ?>
          </tbody></table>
      </div>
      <div class="box-footer clearfix">
        <div class="flex ai__c jc__sb">
          <div><?php echo $results; ?></div>
          <?php echo $pagination; ?>
        </div>
      </div>
      <!-- /.box-body -->
    </div>
    <!-- 删除 -->
    <div class="modal modal-danger fade" id="user-del-modal">
      <div class="modal-dialog">
        <div class="modal-content">
          <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-user">
            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title">删除</h4>
            </div>
            <div class="modal-body">
              <p>确定删除吗？</p>
              <input id="user-del-id" name="selected[]" type="hidden" value="">
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
              <button id="user-del-yes" type="button" class="btn btn-outline">是</button>
            </div>
          </form>
        </div>
        <!-- /.modal-content -->
      </div>
      <!-- /.modal-dialog -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>

<script>
  var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};
  window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"35px","placeholder":"请选择店铺","language":"zh-CN"};

  if (jQuery('#w1').data('select2')) { jQuery('#w1').select2('destroy'); }
  jQuery.when(jQuery('#w1').select2(select2_5eaa6d36)).done(initS2Loading('w1','s2options_c4acac00'));

  var filter_store = '<?php echo $filter_store_json; ?>'
  $("#w1").val($.parseJSON(filter_store)).trigger("change");
</script>
<style type="text/css">
  .table a.asc:after {
    content: " \f106";
    font-family: FontAwesome;
  }
  .table a.desc:after {
    content: " \f107";
    font-family: FontAwesome;
  }
  .select2-container--krajee .select2-selection--multiple .select2-search--inline .select2-search__field {
    height: 26px;
  }
  /*.select2-selection {*/
/*  height: 34px;*/
/*}*/
  /*.select2-container .select2-selection--multiple .select2-selection__rendered {*/
/*  padding-top: 0;*/
/*}*/
</style>

<script type="text/javascript">
  (function() {
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();

      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      url += '&search=' + encodeURIComponent('1');
      location.href = url;
    });

    $('#user-del-modal').on('show.bs.modal', function(event) {
      $('#user-del-id').val($(event.relatedTarget).parents('tr').data('id'))
    })
    $('#user-del-yes').on('click', () => {$('#form-user').submit()})
  })()
</script>
<?php echo $footer; ?>