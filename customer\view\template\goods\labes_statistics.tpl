<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        标签统计
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label>关键字：</label>
                <input type="text" class="form-control" name="filter_name" placeholder="搜索编码/名称" value="<?php echo $filter_name; ?>">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>选择规格数：</label>
                <select class="form-control" name="filter_row">
                  <option <?php if($filter_row == 5) { ?>selected<?php } ?> value="5">5个</option>
                  <option <?php if($filter_row == 10) { ?>selected<?php } ?> value="10">10个</option>
                  <option <?php if($filter_row == 15) { ?>selected<?php } ?> value="15">15个</option>
                  <option <?php if($filter_row == 20) { ?>selected<?php } ?> value="20">20个</option>
                </select>
              </div>
            </div>

            <div class="col-md-4">
              <div class="form-group">
                <label>账单时间：</label>
                <div class="input-group">
                  <div class="input-group-addon">
                    <i class="glyphicon glyphicon-calendar"></i>
                  </div>
                  <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                  <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                  <?php } else{ ?>
                  <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                  <?php } ?>
                  <input type="text" class="hidden" name="filter_date_start" id="filter-start" placeholder="" value="<?php echo $filter_date_start; ?>">
                  <input type="text" class="hidden" name="filter_date_end" id="filter-end" placeholder="" value="<?php echo $filter_date_end; ?>">
                </div>
              </div>
            </div>

            <?php if(!empty($labels)) { ?>
            <?php foreach($labels as $label_key => $label) { ?>

              <div class="col-md-4">
                <div class="form-group">
                  <label><?php echo $label['name']; ?>：</label>
                  <select class="form-control tag-select" name="filter_label_<?php echo $label['label_id']; ?>" id="label_<?php echo $label['label_id']; ?>">
                    <option value="" data-name="<?php echo $label['label_id']; ?>">请选择标签</option>
                    <?php foreach($label['tag'] as $tag_key => $tag) { ?>
                      <option <?php if(!empty($filter_least_labels[$label['label_id']]) && $filter_least_labels[$label['label_id']] == $tag_key) { ?>selected<?php } ?> value="<?php echo $tag_key; ?>" data-name="<?php echo $label['label_id']; ?>"><?php echo $tag; ?></option>
                    <?php } ?>
                  </select>
                </div>
              </div>
            <?php } ?>
            <?php } ?>

          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">标签统计</h3>
        </div>
        <div class="box-body table-responsive no-padding">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th style="width: 120px" class="text-left">图片</th>
              <th style="width: 300px" class="text-left">产品名称/编码</th>
              <th>销量</th>
              <th>当前标签</th>
              <th>提示标签</th>

            </tr>
            <?php if (!empty($lists)) { ?>
            <?php foreach ($lists as $list) { ?>
            <tr>
              <td class="text-left"><img width="100" src="<?php echo $list['img_url']; ?>" class="img-thumbnail"></td>
              <td class="text-left"><?php echo $list['spec_name']; ?><br><?php echo $list['spec_no']; ?></td>
              <td class="text-left"><?php echo $list['quantity']; ?></td>
              <td class="text-left">
                <?php foreach ($list['label_bindings'] as $bindings_key => $bindings) { ?>
                <?php if (!empty($labels[$bindings_key]['name']) && !empty($labels[$bindings_key]['tag'][$bindings])) { ?>
                  <?php echo $labels[$bindings_key]['name']; ?>：<?php echo $labels[$bindings_key]['tag'][$bindings]; ?><br>
                <?php } ?>
                <?php } ?>
              </td>
              <td class="text-left">
                <?php foreach ($list['labels'] as $label_key => $label) { ?>
                <?php echo $label; ?><br>
                <?php } ?>
              </td>
            </tr>
            <?php } ?>
            <?php } else { ?>
            <td colspan="10" align="center"> <?php if($search == 1) { ?>暂无数据<?php } else { ?>请筛选<?php } ?> </td>
            <?php } ?>
            </tbody></table>
        </div>

      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>

<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      var least_labels = '<?php echo $least_labels; ?>';
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();

      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_row = $('select[name=\'filter_row\']').val();

      if (filter_row != '') {
        url += '&filter_row=' + encodeURIComponent(filter_row);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();

      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();

      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      var selectedTag = $('.tag-select').map(function() {
        var selectedName = $(this).find('option:selected').data('name');
        var selectedValue = $(this).val(); // 获取选中的option的值
        if (selectedName && selectedValue) {
          return { name: selectedName,value: selectedValue };
        }
      }).get();

      if (selectedTag.length == least_labels) {
        console.log(selectedTag)
        console.log(JSON.parse(JSON.stringify(selectedTag)))
        url += '&filter_least_labels=' + encodeURIComponent(JSON.stringify(selectedTag));
        url += '&search=' + encodeURIComponent('1');
        location.href = url;
      } else {
        alert('请选择'+least_labels+'个标签维度')
      }

    });
  })()
</script>
<?php echo $footer; ?>