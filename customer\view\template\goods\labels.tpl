<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <h1>
            标签管理
            <small></small>
        </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
        <?php if ($success) { ?>
        <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
        <?php } ?>
        <?php if ($warning) { ?>
        <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
        <?php } ?>
        
        <div class="box box-success">
            <div class="box-header with-border">
                <p class="box-title">标签列表</p>
                <div class="box-tools">
                    <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
                </div>
            </div>
            <div class="box-body table-responsive no-padding">
                <table class="table text-middle table-bordered table-hover table-striped">
                    <tbody><tr>
                        <th>维度名称</th>
                        <th style="width: 1000px">标签</th>
                        <th class="text-right">操作</th>
                    </tr>
                    <?php if (!empty($labels)) { ?>
                    <?php foreach ($labels as $label) { ?>
                    <tr>
                        <td><?php echo $label['name']; ?></td>
                        <td><?php echo $label['tag']; ?></td>
                        <td class="text-right">
                            <a class="btn btn-success" href="<?php echo $add; ?>&label_id=<?php echo $label['label_id']; ?>" title="" target="_blank">编辑</a>
                        </td>
                    </tr>
                    <?php } ?>
                    <?php } else{ ?>
                    <td colspan="10" align="center"> 暂无数据 </td>
                    <?php } ?>
                    </tbody></table>
            </div>
            
        </div>

    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
    .table a.asc:after {
        content: " \f106";
        font-family: FontAwesome;
    }
    .table a.desc:after {
        content: " \f107";
        font-family: FontAwesome;
    }
</style>
<script type="text/javascript">
    (function () {

    })()
</script>
<?php echo $footer; ?>