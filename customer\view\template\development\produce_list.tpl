<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        新品研发
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($success) { ?>
      <div class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $success; ?></div>
      <?php } ?>
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>关键字：</label>
                  <input type="text" class="form-control" name="filter_name" placeholder="产品名称" value="<?php echo $filter_name; ?>">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>状态：</label>
                  <select class="form-control" name="filter_state">
                    <option value="*">全部状态</option>
                    <?php foreach ($states as $state_id => $state) { ?>
                    <?php if ($state_id == $filter_state) { ?>
                    <option value="<?php echo $state_id; ?>" selected="selected"><?php echo $state; ?></option>
                    <?php } else { ?>
                    <option value="<?php echo $state_id; ?>"><?php echo $state; ?></option>
                    <?php } ?>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>创建时间：</label>
                  <div class="input-group">
                    <div class="input-group-addon">
                      <i class="glyphicon glyphicon-calendar"></i>
                    </div>
                    <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                    <?php } else{ ?>
                    <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                    <?php } ?>
                    <input type="text" class="hidden" name="filter_date_start" id="filter-start" placeholder="" value="<?php echo $filter_date_start; ?>">
                    <input type="text" class="hidden" name="filter_date_end" id="filter-end" placeholder="" value="<?php echo $filter_date_end; ?>">
                  </div>
                </div>
              </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <div class="pull-right">
            <button type="button" id="button-filter" class="btn bg-purple"><i class="glyphicon glyphicon-search"></i> 筛选</button>
          </div>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <p class="box-title">研发列表</p>
          <div class="box-tools">
            <a class="btn btn-sm btn-danger" data-toggle="modal" data-target="#info-modal">批量更新</a>
          </div>
        </div>
        <div class="box-body table-responsive no-padding">
          <form action="<?php echo $submit; ?>" method="post" enctype="multipart/form-data" id="form-flows">
          <table id="flows" class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>产品名称/创建时间</th>
              <th>新品图片</th>
              <th>状态</th>
              <th>建模/雕塑/分配工厂</th>
              <th>设样/视觉</th>
              <th>新品下单</th>
            </tr>
            <?php if (!empty($flows)) { ?>
            <?php foreach ($flows as $flow) { ?>
            <tr data-id="<?php echo $flow['workid']; ?>">
              <td>
                <?php echo $flow['workproduct']; ?>
                <br>
                <?php echo $flow['workdate']; ?>
              </td>
              <td class="text-left"><a href="<?php echo $flow['workproductimg']; ?>" target="_blank"><img width="100" src="<?php echo $flow['workproductimg']; ?>" class="img-thumbnail"></a></td>
              <td class="h4 no-margin">
                <span class="label label-primary"><?php echo $flow['workstate']; ?></span>
              </td>
              <td>
                <?php if (!empty($flow['worknode'][6])) { ?>
                  建模完成：<input type="checkbox" value="1" <?php if ($flow['worknode'][6]['state']==0) { ?>checked disabled<?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][6]"<?php } ?> <?php if (!empty($submit_list[$flow['workid']][6]['fields'])) { ?>checked<?php } ?>>
                <?php } else if (!empty($flow['worknode'][7])) { ?>
                  雕塑完成：<input type="checkbox" value="1" <?php if ($flow['worknode'][7]['state']==0) { ?>checked disabled<?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][7]"<?php } ?> <?php if (!empty($submit_list[$flow['workid']][7]['fields'])) { ?>checked<?php } ?>>
                <?php } ?>
                <br>
                分配工厂：
                <select  <?php if (!empty($flow['worknode'][12]) && $flow['worknode'][12]['state']==0) { ?>disabled<?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][12]"<?php } ?>>
                  <option value="0">请选择模具负责人</option>
                  <?php foreach ($mould_users as $mould_user_id => $mould_user) { ?>
                    <option value="<?php echo $mould_user_id; ?>" <?php if (!empty($submit_list[$flow['workid']][12]['uid']) && $mould_user_id == $submit_list[$flow['workid']][12]['uid']) { ?>selected="selected"<?php } ?>><?php echo $mould_user; ?></option>
                  <?php } ?>
                </select>
                <br>
                样品模具：<input type="checkbox" value="1" <?php if (!empty($flow['worknode'][8]) && $flow['worknode'][8]['state']==0) { ?>checked disabled<?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][8]"<?php } ?> <?php if (!empty($submit_list[$flow['workid']][8]['fields'])) { ?>checked<?php } ?>>
              </td>
              <td>
                新品送样：<input type="checkbox" value="1" <?php if (!empty($flow['worknode'][9]) && $flow['worknode'][9]['state']==0) { ?>checked disabled<?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][9]"<?php } ?> <?php if (!empty($submit_list[$flow['workid']][9]['fields'])) { ?>checked<?php } ?>>
                <br>
                样品模具：<input type="checkbox" value="1" <?php if (!empty($flow['worknode'][10]) && $flow['worknode'][10]['state']==0) { ?>checked disabled<?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][10]"<?php } ?> <?php if (!empty($submit_list[$flow['workid']][10]['fields'])) { ?>checked<?php } ?>>
                <br>
                视觉接单：<input type="text" style="width: 200px" list="workproductname_<?php echo $flow['workid']; ?>" <?php if (!empty($flow['worknode'][11]) && $flow['worknode'][11]['state']==0) { ?>disabled="disabled" value="<?php echo $flow['worknode'][11]['worknode_field'][0]['field_value']; ?>" title="<?php echo $flow['worknode'][11]['worknode_field'][0]['field_value']; ?>" <?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][11]" value="<?php if (!empty($submit_list[$flow['workid']][11]['fields'])) { ?><?php echo $submit_list[$flow['workid']][11]['fields']['product_name']; ?><?php } ?>"<?php } ?>>
                <datalist id="workproductname_<?php echo $flow['workid']; ?>">
                  <option><?php echo $flow['workproduct']; ?></option>
                </datalist>
              </td>
              <td>
                新品名称：<input type="text" style="width: 200px" list="workproductname_<?php echo $flow['workid']; ?>" <?php if (!empty($flow['worknode'][13]) && $flow['worknode'][13]['state']==0) { ?>disabled="disabled" value="<?php echo $flow['worknode'][13]['worknode_field'][0]['field_value']; ?>" title="<?php echo $flow['worknode'][13]['worknode_field'][0]['field_value']; ?>" <?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][13][product_name]" value="<?php if (!empty($submit_list[$flow['workid']][13]['fields'])) { ?><?php echo $submit_list[$flow['workid']][13]['fields']['product_name']; ?><?php } ?>"<?php } ?>>
                <br>
                下单数量：<input type="text" style="width: 200px" <?php if (!empty($flow['worknode'][13]) && $flow['worknode'][13]['state']==0) { ?>disabled="disabled" value="<?php echo $flow['worknode'][13]['worknode_field'][1]['field_value']; ?>" title="<?php echo $flow['worknode'][13]['worknode_field'][1]['field_value']; ?>" <?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][13][order_quantity]" value="<?php if (!empty($submit_list[$flow['workid']][13]['fields'])) { ?><?php echo $submit_list[$flow['workid']][13]['fields']['order_quantity']; ?><?php } ?>"<?php } ?>>
                <br>
                合同编号：<input type="text" style="width: 200px" <?php if (!empty($flow['worknode'][13]) && $flow['worknode'][13]['state']==0) { ?>disabled="disabled" value="<?php echo $flow['worknode'][13]['worknode_field'][2]['field_value']; ?>" title="<?php echo $flow['worknode'][13]['worknode_field'][2]['field_value']; ?>" <?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][13][order_no]" value="<?php if (!empty($submit_list[$flow['workid']][13]['fields'])) { ?><?php echo $submit_list[$flow['workid']][13]['fields']['order_no']; ?><?php } ?>"<?php } ?>>
                <br>
                合同工期：<input type="date" style="width: 200px" <?php if (!empty($flow['worknode'][13]) && $flow['worknode'][13]['state']==0) { ?>disabled="disabled" value="<?php echo $flow['worknode'][13]['worknode_field'][3]['field_value']; ?>" title="<?php echo $flow['worknode'][13]['worknode_field'][3]['field_value']; ?>" <?php } else { ?>name="sumbit[<?php echo $flow['workid']; ?>][13][estimate_date]" value="<?php if (!empty($submit_list[$flow['workid']][13]['fields'])) { ?><?php echo $submit_list[$flow['workid']][13]['fields']['estimate_date']; ?><?php } ?>"<?php } ?>>
                <?php if ((!empty($flow['worknode'][13]) && $flow['worknode'][13]['state']==1) || empty($flow['worknode'][13])) { ?>
                <br>
                下单方式：<input type="radio" name="sumbit[<?php echo $flow['workid']; ?>][13][order_way]" value="1" style="width: 30px;" <?php if (!empty($submit_list[$flow['workid']][13]['uid'])) { ?>checked<?php } ?>>自动下单
                <input type="radio" name="sumbit[<?php echo $flow['workid']; ?>][13][order_way]" value="2" style="width: 30px;" <?php if (empty($submit_list[$flow['workid']][13]['uid'])) { ?>checked<?php } ?>>手动下单
                <br>
                负责人员：
                <select name="sumbit[<?php echo $flow['workid']; ?>][13][order_person]">
                <option value="0">请选择生产负责人员</option>
                <?php foreach ($order_person_users as $order_person_user_id => $order_person_user) { ?>
                <option value="<?php echo $order_person_user_id; ?>" <?php if (!empty($submit_list[$flow['workid']][13]['uid']) && $order_person_user_id == $submit_list[$flow['workid']][13]['uid']) { ?>selected="selected"<?php } ?>><?php echo $order_person_user; ?></option>
                <?php } ?>
                </select>
                <?php } ?>
              </td>
            </tr>
            <?php } ?>
            <?php } else{ ?>
            <td colspan="8" align="center"> 暂无流程数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>

      <!-- 更新 -->
      <div class="modal modal-primary fade" id="info-modal">
        <div class="modal-dialog">
          <div class="modal-content">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">更新</h4>
              </div>
              <div class="modal-body">
                <p>确定批量更新记录吗？</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">否</button>
                <button id="info-yes" type="button" class="btn btn-outline">是</button>
              </div>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
  #flows input {
    display: inline;
    width: 75px;
  }
</style>
<script type="text/javascript">
  (function () {
    // 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_name = $('input[name=\'filter_name\']').val();
  
      if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
      }

      var filter_state = $('select[name=\'filter_state\']').val();

      if (filter_state != '*') {
        url += '&filter_state=' + encodeURIComponent(filter_state);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();
  
      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();
  
      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }

      location.href = url;
    });

    $('#info-yes').on('click', () => {$('#form-flows').submit()})
  })()
</script>
<?php echo $footer; ?>