<style>
  .left-nav-tabs {
    float: none;
  }
  .col-sm-1>.left-nav-tabs>li>a {
    color:#444;
    border-right: 1px solid #ddd;
  }
  .col-sm-1>.left-nav-tabs>li.active>a{
    border-top: 1px solid #f4f4f4;
    border-bottom: 1px solid #f4f4f4;
    border-right-color:transparent;
  }
  .col-sm-1>.left-nav-tabs>li.active {
    width:100%;border-left: 3px solid transparent;border-left-color: #3c8dbc;border-right: 1px solid #fff;
  }

</style>
<?php if(!empty($specs)) { ?>
<div class="" style="overflow-x:auto">
  <div style="display: none" class="alert alert-warning"><i class="glyphicon glyphicon-info-sign"></i> 修改成功！</div>
  <div class="col-sm-1 " style="box-shadow:none">
    <ul class="nav left-nav-tabs" style="border-bottom :none">
      <?php foreach($specs as $spec_key => $spec) { ?>
          <li class="<?php if($spec_key == 0){ ?>active<?php } ?>"><a onclick="tag_spec($(this),'<?php echo $spec['spec_id']; ?>','<?php echo $spec['key']; ?>')" data-toggle="tab"><?php echo $spec['spec_no']; ?></a></li>
      <?php } ?>
    </ul>
  </div>
  <div class="col-sm-9">
    <div class="form-group required">
      <label class="col-sm-2 control-label" for="input-nickname">产品名称：</label>
      <div class="col-sm-8">
        <label style="text-align:left;width:100%" class="col-sm-2 control-label tag-value-name"><?php echo $this_spec_name; ?></label>
      </div>
    </div>

    <div class="form-group required">
      <label class="col-sm-2 control-label" for="input-nickname">图片：</label>
      <div class="col-sm-8">
        <img width="100" src="<?php echo $this_spec_img; ?>" class="img-thumbnail tag-value-img">
      </div>
    </div>

    <?php if(!empty($labels)) { ?>
      <?php foreach($labels as $label_key => $label) { ?>
        <div class="form-group">
          <label class="col-sm-2 control-label" for="input-relation"><?php echo $label['name']; ?>：</label>
          <div class="col-sm-8">
            <select name="label_<?php echo $label['label_id']; ?>" id="label_<?php echo $label['label_id']; ?>" class="form-control tag-select">
              <option value="" data-name="<?php echo $label['label_id']; ?>">请选择标签</option>
              <?php foreach($label['tag'] as $tag) { ?>
              <option value="<?php echo $tag['label_id']; ?>" data-name="<?php echo $label['label_id']; ?>"  <?php if($tag['status']==0) { ?>style="color: red;"<?php } ?>><?php echo $tag['name']; ?></option>
              <?php } ?>
            </select>
          </div>
        </div>
      <?php } ?>
    <?php } ?>

    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-8">
        <a class="btn btn-primary" href="javascript:postSpecTag();">提交保存</a>
      </div>
    </div>
  </div>
</div>
<?php } ?>

<script>
  var this_spec_id = '<?php echo $this_spec_id; ?>';
  var this_key = '<?php echo $this_key; ?>';
  function tag_spec(obj,on_spec_id,on_key) {
    if (this_spec_id != on_spec_id) {
      $(obj).parent().addClass('left-active'); // 使用parent()获取当前按钮的父元素
      this_spec_id = on_spec_id;
      this_key = on_key;
      $.ajax({
        url: '<?php echo $getSpecTag; ?>',
        type: 'post',
        data: {spec_id: on_spec_id,key: on_key},
        dataType: 'json',
        success: function(json) {
          $('.tag-value-name').html(json.this_spec_name);
          $('.tag-value-img').attr('src', json.this_spec_img);
          $('.tag-select option').prop('selected', false);
          if (json.label_bindings) {
            $.each(json.label_bindings, function(index, value) {
              console.log('#label_'+index)
                $('#label_'+index).val(value)
            });
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }
  }


  function postSpecTag() {
    var selectedTag = $('.tag-select').map(function() {
      var selectedName = $(this).find('option:selected').data('name'); // 使用data()获取自定义属性
      var selectedValue = $(this).val(); // 获取选中的option的值
      if (selectedName && selectedValue) {
        return { name: selectedName,value: selectedValue };
      }
    }).get();

    if (selectedTag.length > 0) {
      $.ajax({
        url: '<?php echo $addSpecTag; ?>',
        type: 'post',
        data: {spec_id:this_spec_id,key:this_key,tag:selectedTag},
        dataType: 'json',
        success: function(json) {
          $('.alert-warning').show()
          // 设置3秒后隐藏提示层
          setTimeout(function() {
            $('.alert-warning').hide()
          }, 1500); // 1500毫秒等于1.5秒
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }

  }
</script>


