<?php
class ControllerAdminFilemanagerpower extends Controller {
    private $error = array();

	public function index() {
        $this->load->model('admin/filemanagerpower');
        $data['powers'] = $this->model_admin_filemanagerpower->getList();

        $data['add'] = $this->url->link('admin/filemanagerpower/setPower', 'token=' . $this->session->data['token']);
        $data['edit_url'] = $this->url->link('admin/filemanagerpower/setPower', 'token=' . $this->session->data['token'].'&file_power_id=');
        $data['delete'] = $this->url->link('admin/filemanagerpower/deletePower', 'token=' . $this->session->data['token']);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('filemanagerpower/power_list.tpl', $data));
    }

    public function deletePower() {
        $this->load->model('admin/filemanagerpower');

        if (isset($this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $power_id) {
                $this->model_admin_filemanagerpower->deletePower($power_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/filemanagerpower/index', 'token=' . $this->session->data['token'] . $url));
        }

        $this->index();
    }

    public function setPower() {
        $this->load->model('admin/filemanagerpower');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            !isset($this->request->get['file_power_id']) ? $this->model_admin_filemanagerpower->addFilePower($this->request->post) : $this->model_admin_filemanagerpower->editFilePower($this->request->post,$this->request->get['file_power_id']);
            $this->response->redirect($this->url->link('admin/filemanagerpower/index', 'token=' . $this->session->data['token']));
        }

        $data['text_form'] = !isset($this->request->get['file_power_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }
        $data['department_ids'] = '';
        $data['role_ids']  = '';
        $data['file_power_id']  = '';
        if (isset($this->request->get['file_power_id'])) {
            $power = $this->model_admin_filemanagerpower->getPower($this->request->get['file_power_id']);
            if (empty($power['file_power_id'])) {
                $data['warning'] = '数据不存在！';
            } else {
                $data['file_power_id'] = $power['file_power_id'];
                if (!empty($power['department_ids'])) {
                    $data['department_ids'] = $power['department_ids'];
                }
                if (!empty($power['role_ids'])) {
                    $data['role_ids'] = $power['role_ids'];
                }
                $data['file_power'] = $power;
            }
        }

        $data['get_folder'] = $this->url->link('admin/filemanagerpower/getAllFolder', 'token=' . $this->session->data['token']);

        if (!isset($this->request->get['file_power_id'])) {
            $data['action'] = $this->url->link('admin/filemanagerpower/setPower', 'token=' . $this->session->data['token']);
        } else {
            $data['action'] = $this->url->link('admin/filemanagerpower/setPower', 'token=' . $this->session->data['token'] . '&file_power_id=' . $this->request->get['file_power_id']);
        }
        $data['cancel'] = $this->url->link('admin/filemanagerpower/index', 'token=' . $this->session->data['token']);
        $data['departments'] = $this->model_admin_filemanagerpower->getDepartments();
        $data['roles'] = $this->model_admin_filemanagerpower->getRoles();

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('filemanagerpower/power_form.tpl', $data));
    }

    public function getFolder() {
        $this->load->model('admin/filemanagerpower');
        $json = array();

        if (!empty($this->request->get['folder_id'])) {
            $folder_id = $this->request->get['folder_id'];
        } else {
            $folder_id = 0;
        }
        $folder_ids = [];
        if (!empty($this->request->get['file_power_id'])) {
            $power = $this->model_admin_filemanagerpower->getPower($this->request->get['file_power_id']);
            if ($power['folder_ids']) {
                $folder_ids = explode(',',$power['folder_ids']);
            }
        }

        $folder_list = $this->model_admin_filemanagerpower->getFolder($folder_id);
        if (!empty($folder_list)) {
            foreach ($folder_list as $folder) {
                $folder_val = [];
                $folder_val['id'] = $folder['file_id'];
                $folder_val['text'] = $folder['file_name'];
                $folder_val['parent'] = $folder['folder_id'] == 0 ? '#' : $folder['folder_id'];
                if (in_array($folder['file_id'],$folder_ids)) {
                    $folder_val['state'] = [
                        'expanded' => false,
                        'selected' => true,
                    ];
                }
                $json[] = $folder_val;
            }
        }
        $this->response->setOutJson($json);
    }

    public function getAllFolder() {
        $this->load->model('admin/filemanagerpower');

        $folder_ids = [];
        if (!empty($this->request->get['file_power_id'])) {
            $power = $this->model_admin_filemanagerpower->getPower($this->request->get['file_power_id']);
            if ($power['folder_ids']) {
                $folder_ids = explode(',',$power['folder_ids']);
            }
        }
        $json = array();

        $folder_list = $this->model_admin_filemanagerpower->getAllFolder();
        if (!empty($folder_list)) {
            foreach ($folder_list as $folder) {
                $folder_val = [];
                $folder_val['id'] = $folder['file_id'];
                $folder_val['text'] = $folder['file_name'];
                $folder_val['parent'] = $folder['folder_id'] == 0 ? '#' : $folder['folder_id'];
                if ($folder['file_type'] != 'folder') {
                    $folder_val['icon'] = 'jstree-icon jstree-file';
                }
                if (in_array($folder['file_id'],$folder_ids)) {
                    $folder_val['state'] = [
                        'expanded' => false,
                        'selected' => true,
                    ];
                }
                $json[] = $folder_val;
            }
        }
        $this->response->setOutJson($json);
    }

    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'admin/filemanagerpower')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (!isset($this->request->post['power_name'])) {
            $this->error['warning'] = '名称不能为空！';
            return false;
        }
        if (!isset($this->request->post['role']) && !isset($this->request->post['department'])) {
            $this->error['warning'] = '部门或角色不能为空！';
            return false;
        }
        if (!isset($this->request->post['folder_ids'])) {
            $this->error['warning'] = '文件夹不能为空！';
            return false;
        }
        return !$this->error;
    }
}
