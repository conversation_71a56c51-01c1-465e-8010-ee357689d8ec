<?php
class ModelAdminDepartment extends Model {
	public function addDepartment($data) {
        if (!empty($data['union_id'])) {
            $union_id = implode(',', $data['union_id']);
        } else {
            $union_id = '';
        }
		$this->db->query("INSERT INTO _union_department SET department_name = '" . $this->db->escape($data['department_name']) . "', status = '" . (int)$data['status'] . "',  union_id = '" . $this->db->escape($union_id) . "',date_added = NOW(),competent_id = '" . (int)$data['competent_id'] . "'");
	}

	public function editDepartment($id, $data) {
        if (!empty($data['union_id'])) {
            $union_id = implode(',', $data['union_id']);
        } else {
            $union_id = '';
        }
        $this->db->query("UPDATE  _union_department SET department_name = '" . $this->db->escape($data['department_name']) . "',union_id = '" . $this->db->escape($union_id) . "',status = '" . (int)$data['status'] . "',competent_id = '" . (int)$data['competent_id'] . "' WHERE department_id = '" . (int)$id . "'");
	}

	
	public function deleteDepartment($id) {

		$this->db->query("UPDATE _union_department SET status = '-1' WHERE department_id = '" . (int)$id . "'");
		// $this->db->query("DELETE FROM `" . DB_PREFIX . "union_info` WHERE user_id = '" . (int)$user_id . "'");
	}
	

	public function getDepartment($id) {
		$query = $this->db->query("SELECT * FROM _union_department  WHERE department_id = '" . (int)$id . "'");
		return $query->row;
	}
	
	public function getDepartmentByDepartmentName($departmentname) {
		$query = $this->db->query("SELECT * FROM _union_department WHERE department_name = '" . $this->db->escape($departmentname) . "'");

		return $query->row;
	}

	public function getDepartments($data = array()) {
		$sql = "SELECT * FROM `_union_department` where status != '-1'";

		$sql .= " ORDER BY department_id DESC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalDepartments() {
		$sql = "SELECT COUNT(*) AS total FROM `_union_department`";
		
		$query = $this->db->query($sql);

		return $query->row['total'];
	}
    public function getUser($data = array()){
        $sql = "SELECT * FROM _union  ";
        $query = $this->db->query($sql);
        return $query->rows;
    }
}