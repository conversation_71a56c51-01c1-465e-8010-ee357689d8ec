<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      入库确认
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <?php if (empty($products)) { ?>
    <div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button><i class="glyphicon glyphicon-info-sign"></i> 没有详细商品信息！ </div>
    <?php } ?>
    <div class="box box-primary">
      <!-- /.box-header -->
      <div class="box-body">
        <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-order" class="form-horizontal">
          <div class="table-responsive">
            <table id="plans" class="table table-striped table-bordered table-hover">
              <thead>
              <tr>
                <td class="text-left">图片</td>
                <td class="text-left">产品名称</td>
                <td class="text-left">商品编码</td>
                <td class="text-left">入库单号</td>
                <td class="text-left">入库时间</td>
                <td class="text-left">入库成本</td>
                <td width="150" class="text-left">入库数量</td>
              </tr>
              </thead>
              <tbody>
                <?php if (!empty($products)) { ?>
                <?php $submit = false; ?>
                <?php foreach ($products as $product) { ?>
                <tr>
                  <td class="text-left"><img width="100" src="<?php echo $product['img_url']; ?>" class="img-thumbnail"></td>
                  <td class="text-left"><?php echo $product['spec_name']; ?></td>
                  <td class="text-left"><?php echo $product['bsku']; ?></td>
                  <td class="text-left"><?php echo $product['stockin_no']; ?></td>
                  <td class="text-left"><?php echo $product['stockin_date']; ?></td>
                  <td class="text-left"><?php echo $product['stockin_cost']; ?></td>
                  <td class="text-left">
                    <?php if ($product['status'] == '0') { ?>
                    <?php $submit = true; ?>
                    <input type="number" name="quantity[<?php echo $product['stockin_id']; ?>]" value="<?php echo $product['stockin_quan']; ?>" placeholder="入库数量" class="form-control" />
                    <?php } else { ?>
                    <?php echo $product['stockin_quan']; ?>
                    <?php } ?>
                  </td>
                </tr>
                <?php } ?>
                <?php } ?>
              </tbody>
            </table>
          </div>
          <?php if ($submit) { ?>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" type="submit">确认入库</button>
            </div>
          </div>
          <?php } ?>
        </form>
      </div>
      <!-- /.box-body -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>