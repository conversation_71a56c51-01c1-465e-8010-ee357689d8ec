<?php

class ControllerAdminDepartment extends Controller
{
    private $error = array();

    public function add()
    {
        $this->load->model('admin/department');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_department->addDepartment($this->request->post);

            $this->session->data['success'] = $this->language->get('text_add_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/department/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function edit()
    {
        $this->load->model('admin/department');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_department->editDepartment($this->request->get['department_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/department/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }
    public function delete()
    {
        $this->load->model('admin/department');

        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $id) {
                $this->model_admin_department->deleteDepartment($id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/department/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getList();
    }

    public function getList()
    {
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }
        $data['add'] = $this->url->link('admin/department/add', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/department/delete', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['departments'] = array();

        $filter_data = array(
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );

        $this->load->model('admin/department');
        $results = $this->model_admin_department->getDepartments($filter_data);
        $userList = $this->model_admin_department->getUser();
        $userList = array_column($userList, 'real_name', 'union_id');
        foreach ($results as $result) {
            $data['departments'][] = array(
                'department_id' => $result['department_id'],
                'department_name' => $result['department_name'],
                'competent_name' => $userList[$result['competent_id']] ?? '',
                'date_added' => $result['date_added'],
                'status'     => ($result['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled')),
                'edit' => $this->url->link('admin/department/edit', 'token=' . $this->session->data['token'] . '&department_id=' . $result['department_id'] . $url),
            );
        }
        

        $total = $this->model_admin_department->getTotalDepartments();

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/department/getList', 'token=' . $this->session->data['token'] . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('department/department_list.tpl', $data));
    }

    protected function getForm()
    {
        $data['text_form'] = !isset($this->request->get['department_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        if (!isset($this->request->get['department_id'])) {
            $data['action'] = $this->url->link('admin/department/add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/department/edit', 'token=' . $this->session->data['token'] . '&department_id=' . $this->request->get['department_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/department/getList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['department_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $department_info = $this->model_admin_department->getDepartment($this->request->get['department_id']);
        }

        if (isset($this->request->post['department_name'])) {
            $data['department_name'] = $this->request->post['department_name'];
        } elseif (!empty($department_info)) {
            $data['department_name'] = $department_info['department_name'];
        } else {
            $data['department_name'] = '';
        }

        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($department_info)) {
            $data['status'] = $department_info['status'];
        } else {
            $data['status'] = 1;
        }

        if (isset($this->request->post['competent_id'])) {
            $data['competent_id'] = $this->request->post['competent_id'];
        } elseif (!empty($department_info)) {
            $data['competent_id'] = $department_info['competent_id'];
        } else {
            $data['competent_id'] = 0;
        }

        $data['users'] = $this->model_admin_department->getUser();
        if (isset($this->request->post['union_id'])) {
            $data['union_id'] = $this->request->post['union_id'];
        } elseif (!empty($department_info)) {
            $data['union_id'] = explode(',', $department_info['union_id']);
        } else {
            $data['union_id'] = array();
        }
        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('department/department_form.tpl', $data));
    }

    protected function validateForm()
    {
        if (!$this->user->hasPermission('modify', 'admin/department')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['department_name']) < 1) || (utf8_strlen($this->request->post['department_name']) > 32)) {
            $this->error['warning'] = $this->language->get('error_account_length');
        }

        $department_info = $this->model_admin_department->getDepartmentByDepartmentName($this->request->post['department_name']);

        if (!isset($this->request->get['department_id'])) {
            if (!empty($department_info['department_name'])) {
                $this->error['warning'] = $this->language->get('error_department_name_exists');
            }
        } else {
            if ($department_info && ($this->request->get['department_id'] != $department_info['department_id'])) {
                $this->error['warning'] = $this->language->get('error_department_name_exists');
            }
        }


        return !$this->error;
    }

    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'admin/department')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }


        return !$this->error;
    }
}