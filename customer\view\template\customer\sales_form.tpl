<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal">
          <div class="nav-tabs-custom">
            <ul class="nav nav-tabs">
              <li><a href="#tab-general" data-toggle="tab">订单信息</a></li>
              <?php if (isset($bill)) { ?>
              <li><a href="#tab-orders" data-toggle="tab">付款明细</a></li>
              <?php } ?>
            </ul>
            <div class="tab-content">
              <div class="tab-pane" id="tab-general">
                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-orderno">订单号*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="order_no" value="<?php echo $order_no; ?>" placeholder="请输入订单号" id="input-orderno" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-store">业务归属*：</label>
                  <div class="col-sm-8">
                    <select name="store_id" id="input-store" class="form-control" >
                      <option value="">请选择所属店铺</option>
                      <?php foreach($stores as $store) { ?>
                      <?php if ($store['store_id'] == $store_id) { ?>
                      <option value="<?php echo $store['store_id']; ?>" selected="selected"><?php echo $store['name']; ?></option>
                      <?php } else { ?>
                      <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                      <?php } ?>
                      <?php } ?>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-customer">客户名称*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="customer" value="<?php echo $customer; ?>" placeholder="请输入客户名称" id="input-customer" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-contact">联系方式：</label>
                  <div class="col-sm-8">
                    <input type="text" name="contact" value="<?php echo $contact; ?>" placeholder="请输入联系方式" id="input-contact" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-salesmanage">业务员*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="sales_manage" value="<?php echo $sales_manage; ?>" placeholder="请输入业务员" id="input-salesmanage" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-salestotal">订单金额*：</label>
                  <div class="col-sm-8">
                    <input type="text" name="sales_total" value="<?php echo $sales_total; ?>" placeholder="请输入订单金额" id="input-salestotal" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-salesdate">销售日期*：</label>
                  <div class="col-sm-8">
                    <div class="input-group">
                      <div class="input-group-addon">
                        <i class="glyphicon glyphicon-calendar"></i>
                      </div>
                      <input type="text" name="sales_date" value="<?php echo $sales_date; ?>" placeholder="请选择销售日期" id="input-salesdate" class="inputdate form-control" />
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-deliverydate">交货日期*：</label>
                  <div class="col-sm-8">
                    <div class="input-group">
                      <div class="input-group-addon">
                        <i class="glyphicon glyphicon-calendar"></i>
                      </div>
                      <input type="text" name="delivery_date" value="<?php echo $delivery_date; ?>" placeholder="请选择交货日期" id="input-deliverydate" class="inputdate form-control" />
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <div class="col-sm-offset-2 col-sm-8">
                    <button class="btn btn-primary" type="submit">提交保存</button>
                    <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
                  </div>
                </div>
              </div>
              <?php if (isset($bill)) { ?>
              <div class="tab-pane" id="tab-orders">
                <div id="orders"></div>
                <br />
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-date">付款日期：</label>
                  <div class="col-sm-8">
                    <div class="input-group">
                      <div class="input-group-addon">
                        <i class="glyphicon glyphicon-calendar"></i>
                      </div>
                      <input type="text" name="bill_date" value="" placeholder="请选择付款日期" id="input-date" class="inputdate form-control" />
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-total">付款金额：</label>
                  <div class="col-sm-8">
                    <input type="text" name="bill_paid" value="" placeholder="请输入付款金额" id="input-total" class="form-control" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-account">付款账号：</label>
                  <div class="col-sm-8">
                    <input type="text" name="bill_account" value="" placeholder="请输入付款账号" id="input-account" class="form-control" />
                  </div>
                </div>
                <div class="text-center">
                  <button type="button" id="button-order" data-loading-text="正在添加记录..." class="btn btn-danger"><i class="fa fa-plus-circle"></i> 增加付款记录</button>
                </div>
              </div>
              <?php } ?>
            </div>
          </div>
            
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<script type="text/javascript">
$('.nav-tabs li:first a').tab('show');

// 日期显示
$('.inputdate').daterangepicker({
  autoApply: true,
  autoUpdateInput: false,
  singleDatePicker: true,
  timePicker: false,
  locale: {
    format: 'YYYY-MM-DD',
    applyLabel: '确定',
    cancelLabel: '清除'
  }
})
$('.inputdate').on('apply.daterangepicker', function(ev, picker) {
  $(this).val(picker.startDate.format('YYYY-MM-DD'))
})
$('.inputdate').on('cancel.daterangepicker', function(ev, picker) {
  $(this).val('')
})

<?php if (isset($bill)) { ?>
$('#orders').delegate('.pagination a', 'click', function(e) {
  e.preventDefault();

  $('#orders').load(this.href);
});

$('#orders').load('<?php echo $bill; ?>');

$('#button-order').on('click', function(e) {
  e.preventDefault();

  $.ajax({
    url: '<?php echo $addbill; ?>',
    type: 'post',
    dataType: 'json',
    data: 'bill_paid=' + encodeURIComponent($('#tab-orders input[name=\'bill_paid\']').val()) + '&bill_date=' + encodeURIComponent($('#tab-orders input[name=\'bill_date\']').val()) + '&bill_account=' + encodeURIComponent($('#tab-orders input[name=\'bill_account\']').val()),
    beforeSend: function() {
      $('#button-order').button('loading');
    },
    complete: function() {
      $('#button-order').button('reset');
    },
    success: function(json) {
      $('.alert').remove();

      if (json['error']) {
        $('#tab-orders').prepend('<div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
      }

      if (json['success']) {
        $('#tab-orders').prepend('<div class="alert alert-success"><i class="glyphicon glyphicon-info-sign"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');

        $('#orders').load('<?php echo $bill; ?>');

        $('#tab-orders input[name=\'bill_paid\']').val('');
        $('#tab-orders input[name=\'bill_account\']').val('');
      }
    }
  });
});
<?php } ?>
</script>
<?php echo $footer; ?>