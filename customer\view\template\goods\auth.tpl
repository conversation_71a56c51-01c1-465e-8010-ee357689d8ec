<!DOCTYPE html>

<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
  <title>如果家居货品目录</title>
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
  <base href="http://public.youranjian.cn/dz/" />
  <link rel="stylesheet" href="static/bower_components/bootstrap/dist/css/bootstrap.min.css">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="static/bower_components/font-awesome/css/font-awesome.min.css">
</head>
<body class="fixed">
<style type="text/css" media="screen">
    .sink{background-color: #fbfbfb;}
    .addon-right{border-radius: 50%; background-color: #a0d271; height: 16px; width: 16px; top: 8px; right: 8px; line-height: 16px; color: #fff; font-size: 8px;}
    .addon-error{border-radius: 50%; background-color: #ff5958; height: 16px; width: 16px; top: 8px; right: 8px; line-height: 16px; color: #fff; font-size: 8px;}
</style>
<div id="content">
<div class="container">
    <div class="row">
        <div class="col-sm-8 col-sm-offset-2">
            <div class="row" style="padding:25px 0;">
                <div class="col-sm-3">
                    
                </div>
            </div><br><br>
            <div class="row">
                <div class="col-sm-6 col-sm-offset-3 sink">
                    <div class="row" style="border-top:solid 2px #ff7c5b;border-bottom:solid 1px #fff;">
                        <div class="col-sm-12 text-center sink" style="padding:10px 0;">
                            <h4 class="text-muted" style="margin-bottom:0px;">信息验证</h4>
                        </div>
                    </div>
                    <?php if ($error_warning) { ?>
                    <div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> <?php echo $error_warning; ?>
                      <button type="button" class="close" data-dismiss="alert">&times;</button>
                    </div>
                    <?php } ?>
                    <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-sm-12 sink" style="padding-top:20px;">
                            <div class="form-group has-feedback">
                              <div class="input-group">
                                <span class="input-group-addon" style="background-color:#e1dede;"><i class="fa fa-user" style="color:#fff;"></i></span>
                                <input type="text" name="cardid" class="form-control" placeholder="account：" value="<?php echo $cardid; ?>">
                              </div>
                              <span class="glyphicon form-control-feedback"></span>
                            </div>
                            <div class="form-group has-feedback">
                              <div class="input-group">
                                <span class="input-group-addon" style="background-color:#e1dede;"><i class="fa fa-lock" style="color:#fff;"></i></span>
                                <input type="text" name="mobile" class="form-control" placeholder="password：" value="<?php echo $mobile; ?>">
                              </div>
                              <span class="glyphicon form-control-feedback"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row sink">
                        <div class="col-sm-8 col-sm-offset-2" style="padding-top:30px;padding-bottom:30px;">
                            <button type="submit" class="btn btn-danger btn-block">验&nbsp;&nbsp;证</button>
                        </div>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<script src="static/bower_components/jquery/dist/jquery.min.js"></script>
<script type="text/javascript">
    $('[name=username],[name=password]').on('blur', function() {
        if ($(this).val()) {
            $(this).parent().next().removeClass('glyphicon-remove addon-error').addClass('glyphicon-ok addon-right');
        }else{
            $(this).parent().next().removeClass('glyphicon-ok addon-right').addClass('glyphicon-remove addon-error');
        }
    });
</script>
</body>
</html>