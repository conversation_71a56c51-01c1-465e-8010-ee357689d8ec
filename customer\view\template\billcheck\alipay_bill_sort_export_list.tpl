<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        导出列表
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <div class="box box-primary">
        <div class="box-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label>所属店铺：</label>
                <select class="form-control" name="filter_store">
                  <option value="*">全部店铺</option>
                  <?php foreach($stores as $store) { ?>
                  <option <?php if ($store['type'] == 1) { ?>style="color: #3dd5f3"<?php }else if ($store['type'] == 2) { ?>style="color: #ff0000"<?php } ?> value="<?php echo $store['store_id']; ?>" <?php if ($store['store_id'] == $filter_store) { ?>selected="selected" <?php } ?>><?php echo $store['name']; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>账单时间：</label>
                <div class="input-group">
                  <div class="input-group-addon">
                    <i class="glyphicon glyphicon-calendar"></i>
                  </div>
                  <?php if (!empty($filter_date_start) && !empty($filter_date_end)) { ?>
                  <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?> - <?php echo $filter_date_end; ?>">
                  <?php } else{ ?>
                  <input type="text" class="form-control pull-right reservation" placeholder="起始时间 - 截止时间" value="<?php echo $filter_date_start; ?><?php echo $filter_date_end; ?>">
                  <?php } ?>
                  <input type="text" class="hidden" name="filter_date_start" id="filter-start" placeholder="" value="<?php echo $filter_date_start; ?>">
                  <input type="text" class="hidden" name="filter_date_end" id="filter-end" placeholder="" value="<?php echo $filter_date_end; ?>">
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          <button type="button" id="button-filter" class="btn bg-purple pull-right"><i class="glyphicon glyphicon-search"></i> 筛选</button>
        </div>
      </div>
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">导出列表</h3>
          <div class="box-tools">
            <a class="btn btn-sm btn-primary" href="<?php echo $add; ?>">添加</a>
          </div>
        </div>
        <div class="box-body">
          <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody><tr>
              <th>店铺</th>
              <th>账单开始时间</th>
              <th>账单结束时间</th>
              <th class="text-right">操作</th>
            </tr>
            <?php if (!empty($exports)) { ?>
              <?php foreach ($exports as $export) { ?>
                <tr>
                  <td>
                    <?php foreach ($export['stores'] as $store_key => $store) { ?>
                    <span style="margin-right: 5px;padding: 2px;line-height:1.6;background-color: <?php if($store[1]==1){ ?>#3dd5f3<?php } else { ?>#ff0000<?php } ?>;border-radius: 5px"><?php echo $store[0]; ?></span>
                    <?php } ?>
                  </td>
                  <td><?php echo $export['s_month']; ?></td>
                  <td><?php echo $export['e_month']; ?></td>
                  <td class="text-right">
                    <?php if ($export['status'] == 1) { ?>
                    <a class="btn btn-success" href="<?php echo $export['download']; ?>" title="" target="_blank">下载</a>
                    <?php } ?>
                  </td>
                </tr>
              <?php } ?>
            <?php } else { ?>
              <td colspan="9" align="center"> 暂无数据 </td>
            <?php } ?>
          </tbody></table>
          </form>
        </div>
        <div class="box-footer clearfix">
          <div class="flex ai__c jc__sb">
            <div><?php echo $results; ?></div>
            <?php echo $pagination; ?>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<style type="text/css">
.table a.asc:after {
  content: " \f106";
  font-family: FontAwesome;
}
.table a.desc:after {
  content: " \f107";
  font-family: FontAwesome;
}
</style>
<script type="text/javascript">
  (function () {
// 日期筛选
    $('.reservation').daterangepicker({
      autoUpdateInput: false,
      locale: {
        applyLabel: '确定',
        cancelLabel: '清除'
      }
    })
    // 日期显示
    $('.reservation').on('apply.daterangepicker', function(ev, picker) {
      $('#filter-start').val(picker.startDate.format('YYYY-MM-DD'))
      $('#filter-end').val(picker.endDate.format('YYYY-MM-DD'))
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    })
    $('.reservation').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('')
      $('#filter-start').val('')
      $('#filter-end').val('')
    })
    // 筛选
    $('#button-filter').on('click', function() {
      url = '<?php echo $nofilter; ?>';

      var filter_store = $('select[name=\'filter_store\']').val();

      if (filter_store != '*') {
        url += '&filter_store=' + encodeURIComponent(filter_store);
      }

      var filter_date_start = $('input[name=\'filter_date_start\']').val();

      if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
      }

      var filter_date_end = $('input[name=\'filter_date_end\']').val();

      if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
      }
      location.href = url;
    });
  })()
</script>
<?php echo $footer; ?>