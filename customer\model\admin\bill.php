<?php
class ModelAdminBill extends Model {
	public function addBill($data) {
		$data['fee'] = 0;

		foreach ($data['fee_detail'] as $fee) {
			$fee['amount'] = (float)$fee['total'] + (float)$data['sales'] * (float)$fee['percent'] / 100;
			$data['fee'] += moneyformat($fee['amount']);
		}

		$data['bill_total'] = (float)$data['sales'] - (float)$data['cost'] - (float)$data['advert'] - (float)$data['fee'];

		$data['monthly_total'] = (float)$this->db->query("SELECT IFNULL(SUM(bill_total), 0) AS total FROM " . DB_PREFIX . "store_bill WHERE store_id = '" . (int)$data['store_id'] . "' AND bill_date < '" . date('Y-m-d') . "' AND bill_date >= '" . date('Y-m-01') . "'")->row['total'];
		$data['monthly_total'] += $data['bill_total'];

		$this->db->query("INSERT INTO " . DB_PREFIX . "store_bill SET store_id = '" . (int)$data['store_id'] . "', sales = '" . (float)$data['sales'] . "', cost = '" . (float)$data['cost'] . "', advert = '" . (float)$data['advert'] . "', fee = '" . (float)$data['fee'] . "', fee_detail = '" . $this->db->escape(json_encode($data['fee_detail'])) . "', bill_total = '" . (float)$data['bill_total'] . "', monthly_total = '" . (float)$data['monthly_total'] . "', bill_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY), state = '0', date_added = NOW()");
	}

	public function editBill($bill_id, $data) {
		$bill_info = $this->getBill($bill_id);
		$data['fee'] = 0;

		foreach ($data['fee_detail'] as $fee) {
			$fee['amount'] = (float)$fee['total'] + (float)$data['sales'] * (float)$fee['percent'] / 100;
			$data['fee'] += moneyformat($fee['amount']);
		}

		$data['bill_total'] = (float)$data['sales'] - (float)$data['cost'] - (float)$data['advert'] - (float)$data['fee'];

		$this->db->query("UPDATE " . DB_PREFIX . "store_bill SET store_id = '" . (int)$data['store_id'] . "', sales = '" . (float)$data['sales'] . "', cost = '" . (float)$data['cost'] . "', advert = '" . (float)$data['advert'] . "', fee = '" . (float)$data['fee'] . "', fee_detail = '" . $this->db->escape(json_encode($data['fee_detail'])) . "', bill_total = '" . (float)$data['bill_total'] . "', state = '1' WHERE bill_id = '" . (int)$bill_id . "'");

		$this->db->query("UPDATE " . DB_PREFIX . "store_bill SET monthly_total = monthly_total - '" . (float)$bill_info['bill_total'] . "' + '" . (float)$data['bill_total'] . "' WHERE store_id = '" . (int)$data['store_id'] . "' AND bill_date >= '" . $this->db->escape($bill_info['bill_date']) . "' AND YEAR(bill_date) = '" . (int)date('Y', strtotime($bill_info['bill_date'])) . "' AND MONTH(bill_date) = '" . (int)date('n', strtotime($bill_info['bill_date'])) . "'");
	}

	public function deleteBill($bill_id) {
		$this->db->query("UPDATE " . DB_PREFIX . "store_bill SET state = '-1' WHERE bill_id = '" . (int)$bill_id . "'");
	}

	public function getBill($bill_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "store_bill WHERE bill_id = '" . (int)$bill_id . "'");

		return $query->row;
	}

	public function getBills($data = array()) {
		$sql = "SELECT * FROM " . DB_PREFIX . "store_bill WHERE state >= '0'";

		if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

		if (!empty($data['filter_store'])) {
			$sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
		}

		if (isset($data['filter_state']) && $data['filter_state'] !== '') {
			$sql .= " AND state = '" . (int)$data['filter_state'] . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(bill_date) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(bill_date) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

		$sort_data = array(
			'bill_id',
			'store_id',
			'sales',
			'advert',
			'cost',
			'fee',
			'bill_total',
			'monthly_total',
			'bill_date',
			'state',
			'date_added'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY date_added";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalBills($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "store_bill WHERE state >= '0'";

		if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

		if (!empty($data['filter_store'])) {
			$sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
		}

		if (isset($data['filter_state']) && $data['filter_state'] !== '') {
			$sql .= " AND state = '" . (int)$data['filter_state'] . "'";
		}

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(bill_date) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(bill_date) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

    public function getMonthly($store_id, $bill_date) {
        $query = $this->db->query("SELECT IFNULL(SUM(sales), 0) AS sales, IFNULL(SUM(cost), 0) AS cost, IFNULL(SUM(advert), 0) AS advert, IFNULL(SUM(fee), 0) AS fee, IFNULL(SUM(bill_total), 0) AS total FROM " . DB_PREFIX . "store_bill WHERE store_id = '" . (int)$store_id . "' AND bill_date <= '" . $this->db->escape($bill_date) . "' AND YEAR(bill_date) = '" . (int)date('Y', strtotime($bill_date)) . "' AND MONTH(bill_date) = '" . (int)date('n', strtotime($bill_date)) . "'");

        return $query->row;
    }

	public function addFee($data) {
		if (!empty($data['store_ids'])) {
			$store_ids = implode(',', $data['store_ids']);
		} else {
			$store_ids = '';
		}

		$this->db->query("INSERT INTO " . DB_PREFIX . "operate_fee SET user_id = '" . (int)$this->user->user_id . "', fee_name = '" . $this->db->escape($data['fee_name']) . "', fee_total = '" . (float)$data['fee_total'] . "', fee_percent = '" . (float)$data['fee_percent'] . "', calculate = '" . $this->db->escape($data['calculate']) . "', store_ids = '" . $this->db->escape($store_ids) . "', method = '" . $this->db->escape($data['method']) . "', sort_order = '" . (int)$data['sort_order'] . "', status = '1', date_added = NOW()");
	}

	public function editFee($fee_id, $data) {
		if (!empty($data['store_ids'])) {
			$store_ids = implode(',', $data['store_ids']);
		} else {
			$store_ids = '';
		}

		$this->db->query("UPDATE " . DB_PREFIX . "operate_fee SET fee_name = '" . $this->db->escape($data['fee_name']) . "', fee_total = '" . (float)$data['fee_total'] . "', fee_percent = '" . (float)$data['fee_percent'] . "', calculate = '" . $this->db->escape($data['calculate']) . "', store_ids = '" . $this->db->escape($store_ids) . "', method = '" . $this->db->escape($data['method']) . "', sort_order = '" . (int)$data['sort_order'] . "' WHERE fee_id = '" . (int)$fee_id . "'");
	}

	public function deleteFee($fee_id) {
		$this->db->query("UPDATE " . DB_PREFIX . "operate_fee SET status = '0' WHERE fee_id = '" . (int)$fee_id . "'");
		// $this->db->query("DELETE FROM " . DB_PREFIX . "operate_fee WHERE fee_id = '" . (int)$fee_id . "'");
	}

	public function getFee($fee_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "operate_fee WHERE fee_id = '" . (int)$fee_id . "'");

		return $query->row;
	}

	public function getFees($data = array()) {
		$sql = "SELECT * FROM " . DB_PREFIX . "operate_fee WHERE status = '1'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND fee_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_store'])) {
			$sql .= " AND FIND_IN_SET('" . (int)$data['filter_store'] . "', store_ids)";
		}

		if (!empty($data['filter_calculate'])) {
			$sql .= " AND calculate = '" . $this->db->escape($data['filter_calculate']) . "'";
		}

		if (!empty($data['filter_method'])) {
			$sql .= " AND method = '" . $this->db->escape($data['filter_method']) . "'";
		}

		$sql .= " ORDER BY fee_id DESC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalFees($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "operate_fee WHERE status = '1'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND fee_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_store'])) {
			$sql .= " AND FIND_IN_SET('" . (int)$data['filter_store'] . "', store_ids)";
		}

		if (!empty($data['filter_calculate'])) {
			$sql .= " AND calculate = '" . $this->db->escape($data['filter_calculate']) . "'";
		}

		if (!empty($data['filter_method'])) {
			$sql .= " AND method = '" . $this->db->escape($data['filter_method']) . "'";
		}

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function getFeeDetail($store_id) {
		$fees = array();

		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "operate_fee WHERE status = '1' AND FIND_IN_SET('" . (int)$store_id . "', store_ids) ORDER BY sort_order DESC, fee_id ASC");

		foreach ($query->rows as $row) {
			$allocation = count(explode(',', $row['store_ids']));
			$days = $row['method'] == 'monthly' ? (int)date('t', strtotime('-1 day')) : 1;

			$fees[] = array(
				'fee_id'	=> $row['fee_id'],
				'fee_name'	=> $row['fee_name'],
				'total'		=> moneyformat($row['fee_total'] / ($days * $allocation)),
				'percent'	=> moneyformat($row['fee_percent'] / ($days * $allocation)),
				'amount'	=> 0
			);
		}

		return $fees;
	}

	public function getSalesSummary($store_id, $sale_date) {
		$query = $this->db->query("SELECT IFNULL(SUM(ss.total), 0) AS sales, IFNULL(SUM(wsl.retail_price * ss.quantity), 0) AS cost FROM " . DB_PREFIX . "store_sales ss LEFT JOIN wdt_spec_list wsl ON (ss.bsku = wsl.spec_no) WHERE store_id = '" . (int)$store_id . "' AND sale_date = '" . $this->db->escape($sale_date) . "'");

		return $query->row;
	}

    public function getShipList($data = array()) {
        $sql = "SELECT store_id, month, COUNT(bsku) AS count, SUM(ship_quan) AS quan, SUM(cost_total) AS total FROM " . DB_PREFIX . "store_shipcost WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND bsku LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND month >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND month <= '" . (int)$data['filter_date_end'] . "'";
        }

        $sql .= " GROUP BY store_id, month";

        $sort_data = array(
            'store_id',
            'month',
            'count',
            'quan',
            'total'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY month";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (!empty($data['start']) || !empty($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getShipTotal($data = array()) {
        $sql = "SELECT count(cost_id) AS total FROM " . DB_PREFIX . "store_shipcost WHERE 1";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        if (!empty($data['filter_name'])) {
            $sql .= " AND bsku LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND store_id = '" . (int)$data['filter_store'] . "'";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND month >= '" . (int)$data['filter_date_start'] . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND month <= '" . (int)$data['filter_date_end'] . "'";
        }

        $sql .= " GROUP BY store_id, month";

        $query = $this->db->query($sql);

        return $query->num_rows;
    }

    public function getShipDetail($store_id, $month) {
        $sql = "SELECT cost_id, bsku, month, ship_quan, cost_total, spec_name, img_url FROM " . DB_PREFIX . "store_shipcost sc LEFT JOIN (SELECT spec_no, spec_name, img_url FROM wdt_spec_list WHERE deleted = '0') sl ON (sc.bsku = sl.spec_no) WHERE store_id = '" . (int)$store_id . "' AND month = '" . (int)$month . "'";

        if ($this->user->store_ids) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($this->user->store_ids) . "')";
        }

        $sql .= " ORDER BY cost_id ASC";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getMonths() {
        $months = array();

        for ($i=1; $i <= 24; $i++) { 
            $months[] = date('Ym', strtotime('-' . $i . ' month'));
        }

        return $months;
    }

    public function getGrossProfitRate($data = array()) {
        $sort_data = array(
            'total_sales',
            'total_quantity',
            'total_cost',
            'gross_margin',
            'spec_name'
        );

        if ($data['filter_state'] == 0) {
//            $sql = "SELECT ss.store_id,ss.bsku,SUM(ss.total) AS total_sales,SUM(ss.quantity) AS quantity,SUM(ss.quantity * COALESCE(wsl.retail_price, 0)) AS total_cost FROM cr_store_sales AS ss LEFT JOIN (SELECT spec_no,MAX(retail_price) AS retail_price FROM wdt_spec_list WHERE deleted = 0 GROUP BY spec_no) AS wsl ON ss.bsku = wsl.spec_no WHERE 1";
//
//            if (!empty($data['filter_name'])) {
//                $sql .= " AND ss.bsku like '%" . $data['filter_name'] . "%'";
//            }
//
//            if (!empty($data['filter_store'])) {
//                $sql .= " AND FIND_IN_SET(ss.store_id, '" . $this->db->escape($data['filter_store']) . "')";
//            }
//
//            if (!empty($data['filter_date_start'])) {
//                $sql .= " AND DATE(ss.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
//            }
//
//            if (!empty($data['filter_date_end'])) {
//                $sql .= " AND DATE(ss.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
//            }
//
//            $sql .= " GROUP BY ss.store_id, ss.bsku";
//
//            $query = $this->db->query($sql);
//
//            $result = [];
//            foreach ($query->rows as $item) {
//                $store_id = $item['store_id'];
//
//                if (!isset($result[$store_id])) {
//                    $result[$store_id] = [
//                        'total_sales' => 0,
//                        'quantity' => 0,
//                        'total_cost' => 0,
//                    ];
//                }
//
//                $result[$store_id]['total_sales'] += $item['total_sales'];
//                $result[$store_id]['quantity'] += $item['quantity'];
//                $result[$store_id]['total_cost'] += $item['total_cost'];
//            }
//            echo '<pre>';var_dump($result);exit();

            $sql = "SELECT ss.store_id,SUM(ss.total) AS total_sales,SUM(ss.quantity) AS total_quantity,SUM(ss.quantity * COALESCE(wsl.retail_price, 0)) AS total_cost,ROUND((SUM(ss.total) - SUM(ss.quantity * COALESCE(wsl.retail_price, 0))) / SUM(ss.total) * 100,2) AS gross_margin FROM " . DB_PREFIX . "store_sales AS ss LEFT JOIN (SELECT spec_no,MAX(retail_price) AS retail_price FROM wdt_spec_list WHERE deleted = 0 GROUP BY spec_no) AS wsl ON ss.bsku = wsl.spec_no WHERE 1";

            if (!empty($data['filter_name'])) {
                $sql .= " AND ss.bsku like '%" . $this->db->escape($data['filter_name']) . "%'";
            }

            if (!empty($data['filter_store'])) {
                $sql .= " AND FIND_IN_SET(ss.store_id, '" . $this->db->escape($data['filter_store']) . "')";
            } else {
                $sql .= " AND FIND_IN_SET(ss.store_id, '" . $this->db->escape($data['all_store_id']) . "')";
            }

            if (!empty($data['filter_date_start'])) {
                $sql .= " AND DATE(ss.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
            }

            if (!empty($data['filter_date_end'])) {
                $sql .= " AND DATE(ss.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
            }

            $sql .= " GROUP BY ss.store_id";

            if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
                $sql .= " ORDER BY " . $data['sort'];
            } else {
                $sql .= " ORDER BY gross_margin";
            }

            if (isset($data['order']) && ($data['order'] == 'ASC')) {
                $sql .= " ASC";
            } else {
                $sql .= " DESC";
            }

            $query = $this->db->query($sql);

        } else {

            $sql = "SELECT ss.bsku AS product_code,wsl.spec_name AS spec_name,wsl.img_url AS product_image,SUM(ss.total) AS total_sales,SUM(ss.quantity) AS total_quantity, SUM(ss.quantity * COALESCE(wsl.retail_price, 0)) AS total_cost,ROUND((SUM(ss.total) - SUM(ss.quantity * COALESCE(wsl.retail_price, 0))) / SUM(ss.total) * 100,2) AS gross_margin FROM cr_store_sales AS ss LEFT JOIN (SELECT spec_no,MAX(spec_name) AS spec_name,MAX(img_url) AS img_url,MAX(retail_price) AS retail_price FROM wdt_spec_list WHERE deleted = 0 GROUP BY spec_no) AS wsl ON ss.bsku = wsl.spec_no WHERE 1";

            if (!empty($data['filter_name'])) {
                $sql .= " AND ss.bsku like '%" . $this->db->escape($data['filter_name']) . "%'";
            }

            if (!empty($data['filter_store'])) {
                $sql .= " AND FIND_IN_SET(ss.store_id, '" . $this->db->escape($data['filter_store']) . "')";
            }  else {
                $sql .= " AND FIND_IN_SET(ss.store_id, '" . $this->db->escape($data['all_store_id']) . "')";
            }

            if (!empty($data['filter_date_start'])) {
                $sql .= " AND DATE(ss.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
            }

            if (!empty($data['filter_date_end'])) {
                $sql .= " AND DATE(ss.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
            }

            $sql .= " GROUP BY ss.bsku, wsl.spec_name, wsl.img_url";

            if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
                $sql .= " ORDER BY " . $data['sort'];
            } else {
                $sql .= " ORDER BY gross_margin";
            }

            if (isset($data['order']) && ($data['order'] == 'ASC')) {
                $sql .= " ASC";
            } else {
                $sql .= " DESC";
            }

            if (!empty($data['start']) || !empty($data['limit'])) {
                if ($data['start'] < 0) {
                    $data['start'] = 0;
                }

                if ($data['limit'] < 1) {
                    $data['limit'] = 20;
                }

                $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
            }

            $query = $this->db->query($sql);

        }

        return $query->rows;
    }

    public function getTotalGrossProfitRate($data = array()) {
        if ($data['filter_state'] == 0) {
            $sql = "SELECT ss.store_id,SUM(ss.total) AS total_sales,SUM(ss.quantity) AS total_quantity,SUM(ss.quantity * COALESCE(wsl.retail_price, 0)) AS total_cost,ROUND((SUM(ss.total) - SUM(ss.quantity * COALESCE(wsl.retail_price, 0))) / SUM(ss.total) , 2) AS gross_margin FROM " . DB_PREFIX . "store_sales AS ss LEFT JOIN (SELECT spec_no,MAX(retail_price) AS retail_price FROM wdt_spec_list WHERE deleted = 0 GROUP BY spec_no) AS wsl ON ss.bsku = wsl.spec_no WHERE 1";

            if (!empty($data['filter_name'])) {
                $sql .= " AND ss.bsku like '%" . $this->db->escape($data['filter_name']) . "%'";
            }

            if (!empty($data['filter_store'])) {
                $sql .= " AND FIND_IN_SET(ss.store_id, '" . $this->db->escape($data['filter_store']) . "')";
            } else {
                $sql .= " AND FIND_IN_SET(ss.store_id, '" . $this->db->escape($data['all_store_id']) . "')";
            }

            if (!empty($data['filter_date_start'])) {
                $sql .= " AND DATE(ss.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
            }

            if (!empty($data['filter_date_end'])) {
                $sql .= " AND DATE(ss.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
            }

            $sql .= " GROUP BY ss.store_id";

            $query = $this->db->query($sql);

        } else {

            $sql = "SELECT ss.bsku AS product_code,wsl.spec_name AS spec_name,wsl.img_url AS product_image,SUM(ss.total) AS total_sales,SUM(ss.quantity) AS total_quantity, SUM(ss.quantity * COALESCE(wsl.retail_price, 0)) AS total_cost,ROUND((SUM(ss.total) - SUM(ss.quantity * COALESCE(wsl.retail_price, 0))) / SUM(ss.total) * 100,2) AS gross_margin FROM cr_store_sales AS ss LEFT JOIN (SELECT spec_no,MAX(spec_name) AS spec_name,MAX(img_url) AS img_url,MAX(retail_price) AS retail_price FROM wdt_spec_list WHERE deleted = 0 GROUP BY spec_no) AS wsl ON ss.bsku = wsl.spec_no WHERE 1";

            if (!empty($data['filter_name'])) {
                $sql .= " AND ss.bsku like '%" . $this->db->escape($data['filter_name']) . "%'";
            }

            if (!empty($data['filter_store'])) {
                $sql .= " AND FIND_IN_SET(ss.store_id, '" . $this->db->escape($data['filter_store']) . "')";
            } else {
                $sql .= " AND FIND_IN_SET(ss.store_id, '" . $this->db->escape($data['all_store_id']) . "')";
            }

            if (!empty($data['filter_date_start'])) {
                $sql .= " AND DATE(ss.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
            }

            if (!empty($data['filter_date_end'])) {
                $sql .= " AND DATE(ss.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
            }

            $sql .= " GROUP BY ss.bsku, wsl.spec_name, wsl.img_url";

            $query = $this->db->query($sql);

        }

        return $query->num_rows;
    }

    public function getUserStorePovers($data) {
        $sql = "SELECT * FROM " . DB_PREFIX . "user_store_pover WHERE 1";

        if (!empty($data['filter_user'])) {
            $sql .= " AND (";
            foreach ($data['filter_user'] as $user_key => $user) {
                if ($user_key != 0) {
                    $sql .= " OR";
                }
                $sql .= " FIND_IN_SET('" . $this->db->escape($user) . "',users)";
            }
            $sql .= " )";
        }

        if (!empty($data['filter_store'])) {
            $sql .= " AND (";
            foreach ($data['filter_store'] as $store_key => $store) {
                if ($store_key != 0) {
                    $sql .= " OR";
                }
                $sql .= " FIND_IN_SET('" . $this->db->escape($store) . "',stores)";
            }
            $sql .= " )";
        }
        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function getUserStorePover($user_store_pover_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "user_store_pover WHERE user_store_pover_id = '".(int)$user_store_pover_id."'");

        return $query->row;
    }

    public function addUserStorePover($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "user_store_pover SET name = '" . $this->db->escape($data['name']) . "', users = '" . $this->db->escape(implode(',',$data['users'])) . "', stores = '" . $this->db->escape(implode(',',$data['stores'])) . "'");
    }

    public function editUserStorePover($user_store_pover_id,$data) {
        $this->db->query("UPDATE " . DB_PREFIX . "user_store_pover SET name = '" . $this->db->escape($data['name']) . "', users = '" . $this->db->escape(implode(',',$data['users'])) . "', stores = '" . $this->db->escape(implode(',',$data['stores'])) . "' WHERE user_store_pover_id = '".(int)$user_store_pover_id."'");
    }

    public function deleteUserStorePover($user_store_pover_id) {
        $query = $this->db->query("DELETE FROM " . DB_PREFIX . "user_store_pover WHERE user_store_pover_id = '".(int)$user_store_pover_id."'");
    }

    public function getStores($stores = '') {
        $sql = "SELECT * FROM " . DB_PREFIX . "store WHERE state = '1'";

        if (!empty($stores)) {
            $sql .= " AND FIND_IN_SET(store_id, '" . $this->db->escape($stores) . "')";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }



    public function getPerformance($data = array())
    {
        $sql = "SELECT a.union_id, b.extra_info,a.real_name FROM _union as a left join _union_extra_info as b on a.union_id = b.union_id   and  b.extra_key = 'performance'  where a.status = 1  ";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  a.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }



        $sort_data = array(
            'a.real_name',
            'b.extra_info',
        );

        if (isset($data['sort'])) {
            if($data['sort'] == 'score'){
                $sql .= "ORDER BY CONVERT(b.extra_info, SIGNED)";
            }else{
                $sql .= " ORDER BY a.real_name";
            }
           
        } else {
            $sql .= " ORDER BY a.union_id";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }


        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }


    public function getPerformanceTotal($data = array())
    {
        $sql = "SELECT count(*) as total FROM _union as a left join _union_extra_info as b on a.union_id = b.union_id   and  b.extra_key = 'performance'  where a.status = 1   ";

        if (!empty($data['filter_name'])) {
            $sql .= " AND  a.real_name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }      
        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    public function editPerformance($score,$union_id){
        $key = 'performance';
        $info = $this->db->query(" select * from _union_extra_info where union_id = $union_id and extra_key = 'performance' ");
        if($info->row){
            $this->db->query("UPDATE " ."_union_extra_info SET extra_info = '" . $this->db->escape($score) . "' WHERE union_id = $union_id and extra_key = '$key'");
        }else{
            $this->db->query("INSERT INTO " . "_union_extra_info SET extra_key = '" . $this->db->escape($key) . "', union_id = '" . $this->db->escape($union_id) . "', extra_info = '" . $this->db->escape($score) . "'");
        }


        
    }



    
}
