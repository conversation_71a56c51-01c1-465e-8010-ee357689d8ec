<?php echo $header; ?>
<?php echo $content_top; ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo $text_form; ?>
        <small></small>
      </h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">
      <?php if ($warning) { ?>
      <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
      <?php } ?>
      <div class="box box-warning">
        <div class="box-header">
          <h3 class="box-title"></h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
          <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" class="form-horizontal">
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-name">名称：</label>
              <div class="col-sm-8">
                <input type="text" name="item_name" value="<?php echo $item_name; ?>" placeholder="名称" id="input-name" class="form-control" />
              </div>
            </div>  
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-score">分值：</label>
              <div class="col-sm-8">
                <input type="number" name="item_score" value="<?php echo $item_score; ?>" placeholder="分值" id="input-score" class="form-control" />
              </div>
            </div>   
            <div class="form-group">
              <label class="col-sm-2 control-label">评分人：</label>
              <div class="col-sm-8">
                <div class="well well-sm" style="height: 250px; overflow: auto;">
                  <?php foreach ($users as $user) { ?>
                  <?php if (in_array($user['user_id'], $rater_ids)) { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="rater_ids[]" value="<?php echo $user['user_id']; ?>" checked="checked"><?php echo $user['real_name']; ?></label>
                  </div>
                  <?php } else { ?>
                  <div class="checkbox">
                    <label><input type="checkbox" name="rater_ids[]" value="<?php echo $user['user_id']; ?>"><?php echo $user['real_name']; ?></label>
                  </div>
                  <?php } ?>
                  <?php } ?>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-desc">描述说明：</label>
              <div class="col-sm-8">
                <textarea name="item_desc" rows="5" id="input-desc" class="form-control" placeholder="描述说明："><?php echo $item_desc; ?></textarea>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-status">状态：</label>
              <div class="col-sm-8">
                <select name="status" id="input-status" class="form-control">
                  <?php if ($status) { ?>
                  <option value="0"><?php echo $text_disabled; ?></option>
                  <option value="1" selected="selected"><?php echo $text_enabled; ?></option>
                  <?php } else { ?>
                  <option value="0" selected="selected"><?php echo $text_disabled; ?></option>
                  <option value="1"><?php echo $text_enabled; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-8">
                <button class="btn btn-primary" type="submit">提交保存</button>
                <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
              </div>
            </div>
          </form>
        </div>
        <!-- /.box-body -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php echo $content_bottom; ?>
<?php echo $footer; ?>