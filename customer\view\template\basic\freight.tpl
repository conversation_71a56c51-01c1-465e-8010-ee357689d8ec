<?php echo $header; ?>
<?php echo $content_top; ?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <h1>
      运费试算
      <small></small>
    </h1>
  </section>

  <!-- Main content -->
  <section class="content container-fluid">
    <?php if ($warning) { ?>
    <div class="alert alert-danger"><i class="glyphicon glyphicon-info-sign"></i> <?php echo $warning; ?></div>
    <?php } ?>
    <div class="box box-warning">
      <div class="box-header">
        <h3 class="box-title"></h3>
      </div>
      <!-- /.box-header -->
      <div class="box-body">
        <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-user" class="form-horizontal" onsubmit="return toVaild()">
          <input type="hidden" name="freight_id" value="<?php echo $freight_id; ?>">
          <div class="all-provinces-condition">
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-order_no">订单号：</label>
              <div class="col-sm-8">
                <input type="text" name="order_no" value="<?php if(!empty($res_order_no)){ ?><?php echo $res_order_no; ?><?php } ?>" placeholder="请输入订单号" id="input-order_no" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-mode">选择省份：</label>
              <div class="col-sm-8">
                <select id="w1" class="form-control select-province"  name="province" data-s2-options="s2options_c4acac00" data-krajee-select2="select2_5eaa6d36">
                  <option value="0">请选择省份</option>
                  <?php foreach ($provinces as $key=>$province) { ?>
                  <option value="<?php echo $key; ?>" <?php if ($res_province == $key) { ?>selected<?php } ?>><?php echo $province; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-order_no">地址：</label>
              <div class="col-sm-8">
                <input type="text" name="order_address" value="<?php if(!empty($res_order_address)){ ?><?php echo $res_order_address; ?><?php } ?>" placeholder="请输入地址" id="input-order_address" class="form-control" />
              </div>
            </div>
            <div class="provinces-condition" style="padding-top: 20px;border-top: 1px solid #f39c12;position: relative">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-quan">请输入：</label>
                <div class="col-sm-10 keyword">
                  <?php if(!empty($res)){ ?>
                  <?php foreach ($res as $key=>$val) { ?>
                  <div data-id="<?php echo $key; ?>">
                    <input type="text" name="provinces[<?php echo $key; ?>][weight]" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" value="<?php echo $val['weight']; ?>" placeholder="请输入重量（KG）" class="form-control required weight" style="width: 16%;display: inline-block;margin: 10px" />
                    <input type="text" name="provinces[<?php echo $key; ?>][long]" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" value="<?php echo $val['long']; ?>" placeholder="请输入长（CM）" class="form-control required long" style="width: 16%;display: inline-block" /> -
                    <input type="text" name="provinces[<?php echo $key; ?>][wide]" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" value="<?php echo $val['wide']; ?>" placeholder="请输入宽（CM）" class="form-control required wide" style="width: 16%;display: inline-block" /> -
                    <input type="text" name="provinces[<?php echo $key; ?>][high]" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" value="<?php echo $val['high']; ?>" placeholder="请输入高（CM）" class="form-control required high" style="width: 16%;display: inline-block" />
                    <input type="number" name="provinces[<?php echo $key; ?>][num]" min="1" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" value="<?php if(!empty($val['num'])){ ?><?php echo $val['num']; ?><?php } else { ?>1<?php } ?>" placeholder="请输入箱数" class="form-control required num" style="width: 14%;display: inline-block" >
                    <a class="btn btn-warning" href="javascript:;" onclick="copyweight($(this))">复制体积</a>
                    <?php if($key != 0){ ?>
                    <a class="btn btn-danger" href="javascript:;" onclick="delweight($(this))">删除</a>
                    <?php } ?>
                  </div>
                  <?php } ?>
                  <?php } else { ?>
                  <div data-id="0">
                    <input type="text" name="provinces[0][weight]" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" value="" placeholder="请输入重量（KG）" class="form-control required weight" style="width: 16%;display: inline-block;margin: 10px" />
                    <input type="text" name="provinces[0][long]" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" value="" placeholder="请输入长（CM）" class="form-control required long" style="width: 16%;display: inline-block" /> -
                    <input type="text" name="provinces[0][wide]" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" value="" placeholder="请输入宽（CM）" class="form-control required wide" style="width: 16%;display: inline-block" /> -
                    <input type="text" name="provinces[0][high]" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" value="" placeholder="请输入高（CM）" class="form-control required high" style="width: 16%;display: inline-block" />
                    <input type="number" name="provinces[0][num]" min="1" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" value="1" placeholder="请输入箱数" class="form-control required num" style="width: 14%;display: inline-block" />
                    <a class="btn btn-warning" href="javascript:;" onclick="copyweight($(this))">复制体积</a>
                  </div>
                  <?php } ?>
                </div>
              </div>

              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-mode"></label>
                <div class="box-tools" style="width: 15%;float: left;margin-left: 20px">
                  <a class="btn btn-primary" href="javascript:addweight();">添加</a>
                </div>
              </div>

            </div>
          </div>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-8">
              <button class="btn btn-primary" id="myForm" type="submit">试算运费</button>
              <a href="<?php echo $cancel; ?>" class="btn btn-default">取消返回</a>
            </div>
          </div>
        </form>

        <?php if(!empty($res)){ ?>
        <form method="post" enctype="multipart/form-data" id="form-order">
          <table class="table text-middle table-bordered table-hover table-striped">
            <tbody>
            <tr>
              <th>
                重量(KG)
              </th>
              <th>
                长(CM)
              </th>
              <th>
                宽(CM)
              </th>
              <th>
                高(CM)
              </th>
              <th>
                箱数
              </th>
              <?php foreach ($template as $v) { ?>
              <th>
                <?php echo $v['name']; ?>
              </th>
              <?php } ?>
            </tr>

            <?php foreach ($res as $v) { ?>
            <tr>
              <td><?php echo $v['weight']; ?></td>
              <td><?php echo $v['long']; ?></td>
              <td><?php echo $v['wide']; ?></td>
              <td><?php echo $v['high']; ?></td>
              <td><?php if(!empty($v['num'])){ ?><?php echo $v['num']; ?><?php } else { ?>1<?php } ?></td>
              <?php foreach ($template as $val) { ?>
              <td><?php if(!empty($v[$val['express_fee_template_id']])){ ?><?php echo $v[$val['express_fee_template_id']]; ?><?php } ?></td>
              <?php } ?>
            </tr>
            <?php } ?>
            <tr>
              <td>合计（KG）：<?php echo $sum_weight; ?></td>
              <td colspan="3" align="center">总体积（m³）：<?php echo $sum_volume; ?></td>
              <td>总箱数：<?php echo $sum_num; ?></td>
              <?php foreach ($template as $val) { ?>
              <td><?php if(!empty($res_sum[$val['express_fee_template_id']])){ ?><?php echo $res_sum[$val['express_fee_template_id']]; ?><?php } ?></td>
              <?php } ?>
            </tr>
            </tbody></table>
        </form>
        <?php } ?>
      </div>
      <!-- /.box-body -->
    </div>
  </section>
  <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<?php echo $content_bottom; ?>

<script src="<?php echo HTTP_SERVER; ?>static/js/select2.full.min.js"></script>
<script src="<?php echo HTTP_SERVER; ?>static/js/select2-krajee.min.js"></script>

<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-addl.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select-krajee.min.css?v=2"/>
<link rel="stylesheet" type="text/css" href="<?php echo HTTP_SERVER; ?>static/css/select.min.css"/>

<script>
  var num = <?php echo $res_count; ?>;
  if (num > 0) {
    num -= 1;
  }
  function addweight() {
    num += 1;
    var addHtml =
            '<div data-id="'+num+'">'+
            '<input type="text" name="provinces['+num+'][weight]" onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')" value="" placeholder="请输入重量（KG）" class="form-control required weight" style="width: 16%;display: inline-block;margin: 10px" />'+
            '<input type="text" name="provinces['+num+'][long]" onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')" value="" placeholder="请输入长（CM）" class="form-control required long" style="width: 16%;display: inline-block" /> - '+
            '<input type="text" name="provinces['+num+'][wide]" onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')" value="" placeholder="请输入宽（CM）" class="form-control required wide" style="width: 16%;display: inline-block" /> - '+
            '<input type="text" name="provinces['+num+'][high]" onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')" value="" placeholder="请输入高（CM）" class="form-control required high" style="width: 16%;display: inline-block;margin-right: 5px" />'+
            '<input type="number" name="provinces['+num+'][num]"  min="1" onkeyup="value=value.replace(/^(0+)|[^\\d]+/g,\'\')" value="1" placeholder="请输入箱数" class="form-control required num" style="width: 14%;display: inline-block;margin-right: 10px" />'+
            '<a class="btn btn-warning" href="javascript:;" onclick="copyweight($(this))" style="margin:0 5px 0 -5px">复制体积</a>' +
            '<a class="btn btn-danger" href="javascript:;" onclick="delweight($(this))">删除</a>' +
            '</div>';
    $('.keyword').append(addHtml);
  }

  function copyweight(obj) {
    num += 1;
    var copynum = parseInt($(obj).parent().attr('data-id'), 10) + 1;
    var addHtml =
            '<div data-id="'+copynum+'">'+
            '<input type="text" name="provinces['+copynum+'][weight]" onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')" value="" placeholder="请输入重量（KG）" class="form-control required weight" style="width: 16%;display: inline-block;margin: 10px" />'+
            '<input type="text" name="provinces['+copynum+'][long]" onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')" value="'+$(obj).siblings('.long').val()+'" placeholder="请输入长（CM）" class="form-control required long" style="width: 16%;display: inline-block" /> - '+
            '<input type="text" name="provinces['+copynum+'][wide]" onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')" value="'+$(obj).siblings('.wide').val()+'" placeholder="请输入宽（CM）" class="form-control required wide" style="width: 16%;display: inline-block" /> - '+
            '<input type="text" name="provinces['+copynum+'][high]" onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')" value="'+$(obj).siblings('.high').val()+'" placeholder="请输入高（CM）" class="form-control required high" style="width: 16%;display: inline-block;margin-right: 5px" />'+
            '<input type="number" name="provinces['+copynum+'][num]"  min="1" onkeyup="value=value.replace(/^(0+)|[^\\d]+/g,\'\')" value="1" placeholder="请输入箱数" class="form-control required num" style="width: 14%;display: inline-block;margin-right: 10px" />'+
            '<a class="btn btn-warning" href="javascript:;" onclick="copyweight($(this))"  style="margin:0 5px 0 -5px">复制体积</a>' +
            '<a class="btn btn-danger" href="javascript:;" onclick="delweight($(this))">删除</a>' +
            '</div>';

    $(obj).parent().nextAll().each(function(){
      // 在这里处理每个选中的元素
      nexnum = parseInt($(this).attr('data-id'), 10) + 1;
      $(this).attr('data-id', nexnum);
      $($(this).find('.weight')).attr('name', 'provinces['+nexnum+'][weight]');
      $($(this).find('.long')).attr('name', 'provinces['+nexnum+'][long]');
      $($(this).find('.wide')).attr('name', 'provinces['+nexnum+'][wide]');
      $($(this).find('.high')).attr('name', 'provinces['+nexnum+'][high]');
      $($(this).find('.num')).attr('name', 'provinces['+nexnum+'][num]');
    });

    $(obj).parent().after(addHtml);
  }

  function delweight(obj) {
    $(obj).parent().remove();
  }

  // function toVaild() {
  //   var isValid = true;
  //   $('.required').each(function() {
  //     if ($.trim($(this).val()) === '') {
  //       isValid = false;
  //       $(this).css('border', '1px solid red');
  //     } else {
  //       $(this).css('border', '');
  //     }
  //   });
  //
  //   if (isValid) {
  //     return true;
  //   } else {
  //     return false;
  //   }
  // }
</script>

<script>
  {*var s2options_c4acac00 = {"themeCss":".select2-container--krajee","sizeCss":"","doReset":true,"doToggle":true,"doOrder":false};*}
  {*window.select2_5eaa6d36 = {"theme":"krajee","width":"100%","heaght":"35px","placeholder":"请选择省份","language":"zh-CN"};*}

  {*if (jQuery('#w1').data('select2')) { jQuery('#w1').select2('destroy'); }*}
  {*jQuery.when(jQuery('#w1').select2(select2_5eaa6d36)).done(initS2Loading('w1','s2options_c4acac00'));*}

</script>
<?php echo $footer; ?>